package us.zoom.cube.lib.common;

/**
 * <AUTHOR>
 */
public enum AlarmMonitorTypeEnum {
    /**
     *
     */
    loadSysParaError,
    sendIm,
    sendEmail,
    sendPagerDuty,
    retrySendPagerDuty,
    sendCubeDuty,
    sendIncident,
    sendWebhook,
    retrySendIncident,
    retrySendCubeDuty,
    sendAsyncMQ,
    retrySendIm,
    storeToDb,
    discardAlarmInfo,
    retrySendEmail,
    aaCheck,
    aaCommand,
    aaSwitch,
    alarmTrace,
    aaRecover,
    cacheStat,

    inputStat,
    inputIgnoreStat,
    retrieveLastPeriodStat,
    retrieveLastPeriodCacheStat,
    retrieveLastPeriodHitCacheStat,
    retrieveLastPeriodMissCacheStat,
    retrieveLastPeriodFromDBStat,
    searchSpcDataStat,
    matchThresholdStat,
    matchLastCountLargeOneStat,
    matchLastCountLargeOneCacheStat,
    matchLastCountLargeOneHitCacheStat,
    matchLastCountLargeOneMissCacheStat,
    matchLastCountLargeOneFromDBStat,
    matchLastCountStat,
    queryNoticeRecordsStat,
    queryNoticeRecordsCacheStat,
    queryNoticeRecordsMissCacheStat,
    queryNoticeRecordsHitCacheStat,
    queryNoticeRecordsFromDBStat,
    silencedStat,
    setIncidentCache,
    setIncidentCacheEnvNotMatch,
    setIncidentCacheBlock,
    actionStat,
    actionIMStat,
    actionMQStat,
    actionEmailStat,
    actionPageDutyStat,
    actionCubeDutyStat,
    actionIncidentStat,
    actionWebhookStat,
    processEndStat,
    statMetricsMapCount,
    statAlarmMapCount,
    statAlarmMapRemoveCount,
    statMetricsMapRemoveCount,
    aggStatMetricsModelTotalCount,
    aggStatMetricsModelAggCount,
    statSelf,
    checkRateLimit,
    getNoticePermission,
    notGetNoticePermission,
    acquirePermissionLock,
    rcaAnalysis;
}
