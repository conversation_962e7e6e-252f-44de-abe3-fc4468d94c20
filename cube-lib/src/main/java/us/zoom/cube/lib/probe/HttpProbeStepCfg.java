package us.zoom.cube.lib.probe;

import java.util.Map;

/**
 * @authoer: eason.jia
 * @createDate: 2022/6/30
 * @description:
 */
public class HttpProbeStepCfg extends BaseProbeStepCfg {
    private String url;
    private String method;
    private String contentType;
    private Map<String, String> headers;
    private String body;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public Map<String, String> getHeaders() {
        return headers;
    }

    public void setHeaders(Map<String, String> headers) {
        this.headers = headers;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }
}
