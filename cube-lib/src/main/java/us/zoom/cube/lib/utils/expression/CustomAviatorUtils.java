package us.zoom.cube.lib.utils.expression;

import com.googlecode.aviator.*;
import com.googlecode.aviator.runtime.type.AviatorFunction;
import org.apache.commons.lang3.StringUtils;
import us.zoom.cube.lib.utils.expression.customFunction.*;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Aviator tools
 * @ref:  <a href="https://www.yuque.com/boyan-avfmj/aviatorscript/xbdgg2">...</a>
 * @author: canyon.li
 * @date: 2023/12/06
 **/
public class CustomAviatorUtils {

    private static final AviatorEvaluatorInstance INSTANCE = AviatorEvaluator.newInstance();

    private volatile static boolean useSandbox = false;

    private static final Pattern MODEL_PATTERN = Pattern.compile("(require|load)\\((.+)\\)");

    private static final Pattern EXPORTS_PATTERN = Pattern.compile("exports\\.");

    /**
     * Actions need to do when use sandbox
     * @param useSandbox boolean
     */
    public static void setUseSandbox(boolean useSandbox) {
        CustomAviatorUtils.useSandbox = useSandbox;
        if (useSandbox) {
            //disable feature
            INSTANCE.disableFeature(Feature.NewInstance);
            INSTANCE.disableFeature(Feature.Use);
            INSTANCE.disableFeature(Feature.Module);
        } else {
            //enable feature
            INSTANCE.enableFeature(Feature.NewInstance);
            INSTANCE.enableFeature(Feature.Use);
            INSTANCE.enableFeature(Feature.Module);
        }
    }

    /**
     * options control by sysParam.
     * @param customAviatorOption options
     */
    public static void setOption(CustomAviatorOption customAviatorOption) {
        int maxLoopCount = Optional.ofNullable(customAviatorOption).map(CustomAviatorOption::getMaxLoopCount).orElse(0);
        INSTANCE.setOption(Options.MAX_LOOP_COUNT, maxLoopCount);
    }

    static {
        setUseSandbox(useSandbox);
        INSTANCE.setOption(Options.ENABLE_PROPERTY_SYNTAX_SUGAR, false);
        //function list.
        List<AviatorFunction> functions = Arrays.asList(
                new GetByPathFunc(),
                new FormatJsonStrFunc(),
                new StringToLowerCaseFunc(),
                new MentionConvertFunc(),
                new GetTopNFunc(),
                new FormatAndPruneJsonFunc(),
                new NumberRoundFunc()
        );
        //register function
        functions.forEach(INSTANCE::addFunction);
        functions.forEach(AviatorEvaluator::addFunction);
    }

    public static void main(String[] args){
        String expression = "numberA + (numberB == nil ? 0 : numberB) > 0";
        Map<String, Object> params = new HashMap<>(5);
        params.put("numberA", 10);
        //params.put("numberB", null);
        Object res = execute(expression, params, false);
        System.out.println(res);
    }

    public static Object execute(String expression, Map<String, Object> params, boolean cached) {
        return useSandbox ? INSTANCE.execute(expression, params, cached) : AviatorEvaluator.execute(expression, params, cached);
    }

    public static Expression compile(String expression,boolean cached) {
        return useSandbox ? INSTANCE.compile(expression, cached) : AviatorEvaluator.compile(expression, cached);
    }

    public static Expression compile(String expression) {
        return useSandbox ? INSTANCE.compile(expression) : AviatorEvaluator.compile(expression);
    }

    public static void check(String expression, Map<String, Object> fieldMap, boolean isBooleanCondition) {
        if (StringUtils.isEmpty(expression)) {
            return;
        }
        try {
            if (isBooleanCondition
                    && !(execute(expression, fieldMap, true) instanceof Boolean)) {
                throw new RuntimeException("expression[" + expression + "] result is not Boolean");
            } else {
                execute(expression, fieldMap, true);
            }
        } catch (Exception e) {
            throw new RuntimeException("expression[" + expression + "] is invalid, the error msg is " + e.getMessage());
        }
    }

    public static void compileCheck(String expression) {
        if (StringUtils.isEmpty(expression)) {
            return;
        }
        try {
            //require/load function disable
            Matcher matcher = MODEL_PATTERN.matcher(expression);
            if (matcher.find()) {
                throw new IllegalArgumentException("model function(require/load) is not allowed");
            }
            //exports function disable
            Matcher exportsMatcher = EXPORTS_PATTERN.matcher(expression);
            if (exportsMatcher.find()) {
                throw new IllegalArgumentException("exports function is not allowed");
            }
            CustomAviatorUtils.compile(expression, true).getVariableFullNames();
        } catch (Exception e) {
            throw new IllegalArgumentException("get params error, expression: " + expression + ", error:" + e.getMessage());
        }
    }
}
