package us.zoom.cube.lib.config.hub.processor;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/3/10
 */
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "type")
@Data
@NoArgsConstructor
public class FilterProcessorCfg extends BaseProcessorCfg {

    private String filterRule;

    private Map<String, String> filterRuleMap;

    public FilterProcessorCfg(String name, String type, Integer order, String filterRule) {
        super(name, type, order);
        setFilterRule(filterRule);
    }
}
