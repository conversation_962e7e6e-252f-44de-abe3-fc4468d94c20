package us.zoom.cube.sink.core.filter;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import us.zoom.monitor.auto.DefaultServletRequestMetricsFilter;


@Configuration
public class ApmServletRequestMetricsFilter {
    @Bean
    public DefaultServletRequestMetricsFilter defaultServletRequestMetricsFilter() {
        return new DefaultServletRequestMetricsFilter(12345);
    }
}
