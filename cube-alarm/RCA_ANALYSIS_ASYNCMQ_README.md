# RCA Analysis AsyncMQ Integration

## 概述

该功能在告警通知成功发送到Zoom Chat后，会异步生成一个RcaAnalysisEvent事件并发送到AsyncMQ的指定Topic。这样可以触发后续的RCA（根因分析）流程，而不影响主流程的性能。

## 功能特点

1. **异步处理**：使用独立的线程池异步发送事件，不阻塞主流程
2. **高性能**：复用AsyncMQInstance的producer连接池，避免创建额外连接
3. **容错设计**：发送失败不影响告警主流程
4. **可配置**：通过配置文件控制启用/禁用和相关参数

## 配置说明

在`application.properties`中添加以下配置：

```properties
# 启用/禁用RCA分析事件发送功能
# 启用后会使用AsyncMQInstance的producer发送事件
rca.analysis.asyncmq.enabled=true

# RCA分析事件的目标Topic
rca.analysis.asyncmq.topic=cube-rca-analysis

# RCA分析事件的任务类型
rca.analysis.asyncmq.task.type=cube_rca_analysis
```

注意：该功能复用了cube-alarm模块的AsyncMQInstance连接池，无需单独配置endpoint、username和password。

## RcaAnalysisEvent数据结构

发送到AsyncMQ的事件包含以下字段：

```json
{
  "task": "smartRCA-alarm",
  "service": "服务名称（即tenantName）",
  "alarmId": "告警ID",
  "beginTs": "开始时间戳（告警时间前5分钟）",
  "endTs": "结束时间戳（告警时间）",
  "groupKey": "告警分组键",
  "channelId": "通知渠道ID"
}
```

## 架构设计

- **RcaAnalysisEventSender**：独立的服务类，负责RCA分析事件的发送
- **AlarmNoticeAction**：在发送告警通知后调用RcaAnalysisEventSender
- **解耦设计**：RCA分析逻辑与通知渠道实现完全解耦

## 工作流程

1. AlarmNoticeAction调用ChannelEngine发送告警通知
2. 告警通知成功发送后，调用RcaAnalysisEventSender
3. RcaAnalysisEventSender检查是否为Zoom Chat渠道且功能已启用
4. 如果满足条件，创建RcaAnalysisEvent事件
5. 使用线程池异步发送事件到AsyncMQ
6. 发送成功或失败都记录日志，但不影响主流程

## 性能优化

- **线程池配置**：
  - 核心线程数：2
  - 最大线程数：4
  - 线程空闲时间：60秒
  - 队列容量：1000

- **拒绝策略**：当队列满时，直接丢弃新任务，避免影响主流程

- **资源管理**：应用关闭时会优雅地关闭线程池和AsyncMQ连接

## 监控和日志

- 成功发送：记录DEBUG级别日志
- 发送失败：记录WARN级别日志
- 队列满：记录WARN级别日志
- 初始化失败：记录ERROR级别日志

## 使用场景

该功能适用于需要对告警进行智能分析的场景，例如：
- 自动根因分析
- 告警聚合分析
- 智能告警回复
- 告警趋势分析

## 注意事项

1. 确保AsyncMQInstance已正确初始化（cube-alarm启动时自动初始化）
2. 确保目标Topic已在AsyncMQ中创建
3. 监控线程池和队列状态，避免积压
4. 该功能仅对Zoom Chat渠道生效
5. 需要在cube-alarm模块中启用该功能 