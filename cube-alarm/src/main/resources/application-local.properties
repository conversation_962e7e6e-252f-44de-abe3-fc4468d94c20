#Log Config
log.filePath=cube-alarm/logs
log.sampling.count.info=1
log.sampling.count.warn=1
log.sampling.count.error=1

#cache config
cache.load.interval.minute=1

#alarm inhibition
alarm.inhibit.interval.second=600
alarm.inhibit.threshold.count=2

#MySql data source

dataSource.url=****************************************************************************************************************************************************************************************************************************************************************
# To set in CSMS
#cube.dataSource.username=
# To set in CSMS
#cube.dataSource.password=
dataSource.csms_rotate_username_key= cube.dataSource.username
dataSource.csms_rotate_password_key= cube.dataSource.password
dataSourceStandby.csms_rotate_username_key= cube.dataSourceStandby.username
dataSourceStandby.csms_rotate_password_key= cube.dataSourceStandby.password

spring.jpa.show-sql=false
spring.jpa.hibernate.ddl-auto=none
spring.jpa.hibernate.naming.implicit-strategy=org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
spring.jpa.database=MYSQL
spring.jpa.properties.hibernate.enable_lazy_load_no_trans=true

#last count cache config
last.alarm.schedule.seconds=600
last.alarm.maximumSize=1000000

#notify cache
notify.schedule.seconds=36000
notify.maximumSize=1000000



# CSMS configs only used for testing environments: dev, hz, local, stable, etc.
# For prod environment, configs will be set in csms.properties instead of application-{active_profile}.properties.

#true enable, false disable
csms.enable=true
#csms domain
csms.endpoints=https://csmsdev.zoomdev.us
#config in csms console path
csms.app.path=ds01/cube
# non-ec2 bind credential file
# For local debug, use the credential file directory. Apply for the access key from the CSMS maintainer!
# Do not upload the credential to Git repository!!!
csms.app.credential.file=/usr/local/credentials

### Incident
#cube.incident.AK=
#cube.incident.SK=
cube.incident.url.batch=https://devincident.zoomdev.us/api/batch/common
cube.incident.url.single=https://devincident.zoomdev.us/api/incident
cube.incident.url.alert=https://cube-debug.zoomdev.us:8443/
cube.incident.interval=0

#alarm detail url
alarm.details.endpoint=https://cube-perf.zoomdev.us:8443/alarmInsight

#alarm email
alarm.channel.email.sender=cube-team
alarm.channel.email.from=<EMAIL>
alarm.channel.email.smtp.server=smtp.gmail.com
alarm.channel.email.smtp.port=587
#alarm.channel.email.password=

need.mask.log=false

#AsyncMQ
alarm.async.mq.endpoint=https://asyncmq.zoomdev.us
alarm.async.mq.username=app_cube
alarm.event.topic=us_cube_alarm_event

#Clickhouse
cube.dataSource.clickhouse.servers=**************
cube.dataSource.clickhouse.username=zoom_cube
cube.alarm.clickhouse.flush.interval.millsecond=5000
cube.alarm.clickhouse.flush.max.batch.size=100000
cube.alarm.clickhouse.thread.pool.size=10

alarm.dashboard.endpoint=https://cube-perf.zoomdev.us:8443/dashboards
alarm.pii-redirect.dashboard.endpoint=https://cube-perf.zoomdev.us:8443/alarm-redirect/dashboard
cube.config.client.endpoint=http://localhost:8089

#apm
apm.log.home=/data/logs/zoom_middleware/apm/Infra_Monitor_Cube-Alarm
monitor.executorsReplace.enabled=false

# RCA Analysis AsyncMQ Configuration
# Enable/disable RCA analysis event sending
# When enabled, will use AsyncMQInstance's producer to send events
rca.analysis.asyncmq.enabled=false

# AsyncMQ topic for RCA analysis events
rca.analysis.asyncmq.topic=cube-alarm-rca-analysis

# AsyncMQ task type for RCA analysis
rca.analysis.asyncmq.task.type=cube_rca_analysis