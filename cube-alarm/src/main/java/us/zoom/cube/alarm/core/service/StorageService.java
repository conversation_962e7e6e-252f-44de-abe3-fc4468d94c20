package us.zoom.cube.alarm.core.service;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Joiner;
import com.zoom.op.monitor.domain.alarm.AlarmExtensionRelation;
import com.zoom.op.monitor.domain.alarm.Channel;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.alarm.core.config.ClickhouseHandlerFactory;
import us.zoom.cube.alarm.core.config.ConfigCache;
import us.zoom.cube.alarm.core.model.alarm.config.AlarmCfg;
import us.zoom.cube.alarm.core.model.alarm.model.AlarmServiceParaService;
import us.zoom.cube.alarm.core.model.cache.NotifyImg;
import us.zoom.cube.alarm.core.model.cache.NotifyImgList;
import us.zoom.cube.alarm.core.model.metrics.MetricsCfg;
import us.zoom.cube.alarm.core.model.metrics.MetricsFieldCfg;
import us.zoom.cube.alarm.core.service.syspara.AlarmParaService;
import us.zoom.cube.alarm.core.service.syspara.SysParaService;
import us.zoom.cube.alarm.util.ChannelUtils;
import us.zoom.cube.lib.integrations.Metrics;
import us.zoom.cube.lib.integrations.MetricsFieldTypeEnum;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.infra.clickhouse.ClickhouseConst;
import us.zoom.infra.clickhouse.ClickhouseSqlUtil;
import us.zoom.infra.clickhouse.QueryBuilder;
import us.zoom.infra.enums.AlarmRecordStatusEnum;
import us.zoom.infra.enums.AlarmSendingStatusEnum;
import us.zoom.infra.influx.model.alarm.*;
import us.zoom.infra.influx.service.alarm.InfluxAlarmService;
import us.zoom.infra.model.alarm.AlarmLevel;
import us.zoom.infra.utils.DateUtils;

import java.net.URL;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import static com.zoom.op.monitor.domain.alarm.AlarmExtensionRelationType.dashboard;
import static us.zoom.cube.alarm.core.service.syspara.AlarmParaService.*;
import static us.zoom.infra.clickhouse.ClickhouseConst.*;

/**
 * <AUTHOR> Wang
 * @date 2022-06-14 10:11 
 */
@Component
@Slf4j
public class StorageService {

    @Autowired
    private SysParaService sysParaService;

    @Autowired
    private InfluxAlarmService influxAlarmService;

    @Autowired
    private ClickhouseHandlerFactory clickhouseHandlerFactory;

    @Autowired
    private ConfigCache configCache;

    @Autowired
    private AlarmServiceParaService alarmServiceParaService;

    @Autowired
    private AlarmParaService alarmParaService;

    private String alarmDashboardEndpoint;

    public static final int ACK_RECORD_RECOVERY_PERIOD = 8 * 60 * 60 * 1000;

    @Value("${alarm.dashboard.endpoint:}")
    public void setAlarmDashboardEndpoint(String alarmDashboardEndpoint) {
        this.alarmDashboardEndpoint = alarmDashboardEndpoint;
    }

    private ObjectMapper objectMapper;

    @PostConstruct
    public void init() {
        objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    /**
     * add new event record
     */
    public void addAlarmRecordEvent(AlarmMatchRecord alarmMatchRecord) {
        String tenantName = alarmMatchRecord.getTenantName();
        if (sysParaService.isClickhouseTenantWriteEnable(tenantName)) {
            try {
                Map<String, Object> columns = new HashMap<>(5);
                columns.put("id", (alarmMatchRecord.getId()));
                columns.put("gmtCreate", new Timestamp(System.currentTimeMillis()));
                columns.put("alarmStatus", alarmMatchRecord.getStatus().getWithNoPrefix());
                columns.put("ackExpireTime", Optional.ofNullable(alarmMatchRecord.getAckExpireTime()).map(Timestamp::new).orElse(null));
                columns.put("alarmId", alarmMatchRecord.getAlarmId());
                columns.put("alarmName", alarmMatchRecord.getAlarmName());
                columns.put("alarmLevel", alarmMatchRecord.getAlarmLevel().getLevel());
                columns.put("metricsId", alarmMatchRecord.getMetricsId());
                columns.put("metricsName", alarmMatchRecord.getMetricsName());
                columns.put("event", alarmMatchRecord.getAlarmEventType().name());
                columns.put("tagKey", alarmMatchRecord.getTagKey());
                Timestamp metricTs = new Timestamp(alarmMatchRecord.getTime() == 0 ? System.currentTimeMillis() : alarmMatchRecord.getTime());
                long recordMemorySize = ClickhouseSqlUtil.approximateSize(columns);
                String dbName = ClickhouseSqlUtil.toClickhouseName(tenantName);
                boolean isHighSpeed = alarmServiceParaService.isHighSpeedTable(tenantName, ALARM_CUBE_MATCH_RECORD_EVENT_TABLE);
                clickhouseHandlerFactory.getClickhouseWriter(isHighSpeed).write(dbName, ALARM_CUBE_MATCH_RECORD_EVENT_TABLE, columns, metricTs, recordMemorySize, false);
            } catch (Exception e) {
                log.error("Error when addAlarmRecordEvent, record:{}", JsonUtils.toJsonStringIgnoreExp(alarmMatchRecord), e);
            }
        }
    }

    public void addOriginAiDetectResult(AiMetricsV2 aiMetricsV2) {

        if (Objects.isNull(aiMetricsV2)) {
            return;
        }

        String tenantName = aiMetricsV2.getTenantName();
        boolean writeClickhouse = alarmParaService.getParamBooleanValue(AI_ORIGIN_RESULT_CLICKHOUSE_ENABLED, false);
        if (!writeClickhouse) {
            return ;
        }
        try {
            Map<String, Object> columns = new HashMap<>(32);

            Timestamp metricTs = new Timestamp(System.currentTimeMillis());
            columns.put("metricsId", aiMetricsV2.getMetricsId());
            columns.put("metricsName", aiMetricsV2.getMetricsName());
            columns.put("tenantId", aiMetricsV2.getTenantId());
            columns.put("tenantName", aiMetricsV2.getTenantName());
            columns.put("fieldName", aiMetricsV2.getFieldName());
            columns.put("id", aiMetricsV2.getId());
            columns.put("adName", aiMetricsV2.getAdName());
            columns.put("aiAlarmConditionCfgList", objectMapper.writeValueAsString(aiMetricsV2.getAiAlarmConditionCfgList()));
            columns.put("gmtCreate", metricTs);

            long recordMemorySize = ClickhouseSqlUtil.approximateSize(columns);
            String dbName = ClickhouseSqlUtil.toClickhouseName(tenantName);
            String allAlarmStoreInThisService = sysParaService.getServiceForAllAlarmRecords();
            if (StringUtils.isNoneBlank(allAlarmStoreInThisService)) {
                clickhouseHandlerFactory.getClickhouseWriter().write(allAlarmStoreInThisService, AI_ORIGIN_DETECT_RESULT_ALL_SERVICE_RECORD_TABLE, columns, metricTs, recordMemorySize, false);
            }

        } catch (Exception e) {
            log.error("Error when addOriginAiDetectResult, aiMetricsV2:{}", JsonUtils.toJsonStringIgnoreExp(aiMetricsV2), e);
        }
    }

    public void addAiMetric(AlarmCfg alarm, AiMetricsV2 aiMetricsV2, AiRuleConditionV2 aiRuleCondition, String tagKey) {

        if (Objects.isNull(aiMetricsV2)) {
            return;
        }

        String tenantName = aiMetricsV2.getTenantName();
        boolean writeClickhouse = alarmParaService.getParamBooleanValue(AI_METRICS_CLICKHOUSE_ENABLED, false);
        if (!writeClickhouse) {
            return ;
        }
        try {
            Map<String, Object> columns = new HashMap<>(32);

            Timestamp metricTs = new Timestamp(aiRuleCondition.getTime() == 0 ? System.currentTimeMillis() : aiRuleCondition.getTime());
            columns.put("metricsId", aiMetricsV2.getMetricsId());
            columns.put("metricsName", aiMetricsV2.getMetricsName());
            columns.put("tenantId", aiMetricsV2.getTenantId());
            columns.put("tenantName", aiMetricsV2.getTenantName());
            columns.put("fieldName", aiMetricsV2.getFieldName());
            columns.put("id", aiMetricsV2.getId());
            columns.put("adName", aiMetricsV2.getAdName());
            columns.put("latestTime", aiRuleCondition.getLatestTime());
            columns.put("anomalyScore", Optional.of(aiRuleCondition).map(AiRuleConditionV2::getFilterVar).map(AiFilterVarV2::getAnomalyScore).orElse(null));
            columns.put("absPredRatio", Optional.of(aiRuleCondition).map(AiRuleConditionV2::getFilterVar).map(AiFilterVarV2::getAbsPredRatio).orElse(null));
            columns.put("anomalyDirection", Optional.of(aiRuleCondition).map(AiRuleConditionV2::getFilterVar).map(AiFilterVarV2::getAnomalyDirection).orElse(null));
            columns.put("tagKey", tagKey);
            columns.put("columns", aiRuleCondition.getColumns());
            columns.put("adTagCfgDataList", aiRuleCondition.getAdTagCfgDataList());
            columns.put("fieldMap", aiRuleCondition.getFieldMap());
            columns.put("alarmId", Optional.of(alarm).map(AlarmCfg::getId).orElse(null));
            columns.put("gmtCreate", System.currentTimeMillis());

            long recordMemorySize = ClickhouseSqlUtil.approximateSize(columns);
            String dbName = ClickhouseSqlUtil.toClickhouseName(tenantName);
            if (StringUtils.isNoneBlank(dbName)) {
                clickhouseHandlerFactory.getClickhouseWriter().write(dbName, AI_ORIGIN_DETECT_RESULT_SERVICE_RECORD_TABLE, columns, metricTs, recordMemorySize, false);
            }
        } catch (Exception e) {
            log.error("Error when addAiMetric, aiMetricsV2:{}, aiRuleCondition:{}", JsonUtils.toJsonStringIgnoreExp(aiMetricsV2), JsonUtils.toJsonStringIgnoreExp(aiRuleCondition), e);
        }
    }

    /**
     * add new alarm record
     */
    public void addAlarmRecordV2(AlarmMatchRecord alarmMatchRecord) {
        String tenantName = alarmMatchRecord.getTenantName();
        if (sysParaService.isClickhouseTenantWriteEnable(tenantName)) {
            try {
                Map<String, Object> columns = new HashMap<>(32);
                Map<String, Object> columnsForAll = new HashMap<>(32);

                //service table
                columns.put("id", alarmMatchRecord.getId());
                columns.put("alarmId", alarmMatchRecord.getAlarmId());
                columns.put("alarmName", alarmMatchRecord.getAlarmName());
                columns.put("alarmLevel", alarmMatchRecord.getAlarmLevel().getLevel());
                columns.put("alarmLevelPriority", alarmMatchRecord.getAlarmLevel().getValue());
                columns.put("alarmRuleId", alarmMatchRecord.getAlarmRuleId());
                columns.put("alarmSourceType", alarmMatchRecord.getAlarmSourceType().getValue());
                columns.put("metricsId", alarmMatchRecord.getMetricsId());
                columns.put("metricsName", alarmMatchRecord.getMetricsName());
                columns.put("serviceName", alarmMatchRecord.getTenantName());
                columns.put("gmtCreate", new Timestamp(System.currentTimeMillis()));
                columns.put("hittedRule", objectMapper.writeValueAsString(alarmMatchRecord.getHittedRule()));
                columns.put("preRecordId", alarmMatchRecord.getPreRecordId());
                if (!CollectionUtils.isEmpty(alarmMatchRecord.getMetricsTags())) {
                    columns.put("tagNames", String.join(",", alarmMatchRecord.getMetricsTags().keySet()));
                }
                if (!CollectionUtils.isEmpty(alarmMatchRecord.getMetricFields())) {
                    columns.put("fieldNames", String.join(",", alarmMatchRecord.getMetricFields().keySet()));
                }
                columns.put("tagKey", alarmMatchRecord.getTagKey());
                columns.put("initialStatus", Optional.ofNullable(alarmMatchRecord.getStatus()).map(AlarmRecordStatusEnum::getWithNoPrefix).orElse(null));
                columns.put("tagMap", alarmMatchRecord.getMetricsTags());
                columns.put("hittedValue", objectMapper.writeValueAsString(alarmMatchRecord.getHittedValue()));
                columns.put("alarmSilenceName", alarmMatchRecord.getAlarmSilenceName());
                columns.put("alarmSilenceId", alarmMatchRecord.getAlarmSilenceId());
                columns.put("extendInfo", JsonUtils.toJsonStringIgnoreExp(alarmMatchRecord.getRecordExtendInfo()));
                if (!CollectionUtils.isEmpty(alarmMatchRecord.getMetricFields())) {
                    addFieldMap(columns, alarmMatchRecord);
                }

                //all table
                columnsForAll.put("id", alarmMatchRecord.getId());
                columnsForAll.put("alarmId", alarmMatchRecord.getAlarmId());
                columnsForAll.put("alarmName", alarmMatchRecord.getAlarmName());
                columnsForAll.put("alarmLevel", alarmMatchRecord.getAlarmLevel().getLevel());
                columnsForAll.put("metricsId", alarmMatchRecord.getMetricsId());
                columnsForAll.put("metricsName", alarmMatchRecord.getMetricsName());
                columnsForAll.put("serviceName", alarmMatchRecord.getTenantName());
                columnsForAll.put("gmtCreate", new Timestamp(System.currentTimeMillis()));
                columnsForAll.put("initialStatus", alarmMatchRecord.getStatus().getWithNoPrefix());
                columnsForAll.put("metricsType", alarmMatchRecord.getMetricsType());
                columnsForAll.put("tagKey", alarmMatchRecord.getTagKey());

                Timestamp metricTs = new Timestamp(alarmMatchRecord.getTime() == 0 ? System.currentTimeMillis() : alarmMatchRecord.getTime());
                long recordMemorySize = ClickhouseSqlUtil.approximateSize(columns);
                String dbName = ClickhouseSqlUtil.toClickhouseName(tenantName);
                //for each service
                boolean isHighSpeed = alarmServiceParaService.isHighSpeedTable(tenantName, ALARM_CUBE_MATCH_SERVICE_RECORD_TABLE);
                clickhouseHandlerFactory.getClickhouseWriter(isHighSpeed).write(dbName, ALARM_CUBE_MATCH_SERVICE_RECORD_TABLE, columns, metricTs, recordMemorySize, false);
                //for all
                String allAlarmStoreInThisService = sysParaService.getServiceForAllAlarmRecords();
                if (StringUtils.isNotBlank(allAlarmStoreInThisService)) {
                    clickhouseHandlerFactory.getClickhouseWriter().write(allAlarmStoreInThisService, ClickhouseConst.ALARM_CUBE_MATCH_ALL_SERVICE_RECORD_TABLE, columnsForAll, metricTs, recordMemorySize, false);
                }
                addAlarmExtensionRelation(alarmMatchRecord);
            } catch (Exception e) {
                log.error("Error when addAlarmRecordV2, record:{}", JsonUtils.toJsonStringIgnoreExp(alarmMatchRecord), e);
            }
        }
    }

    private void addFieldMap(Map<String, Object> columns, AlarmMatchRecord alarmMatchRecord) {
        String metricsId = alarmMatchRecord.getMetricsId();
        Map<String, Object> fieldMap = alarmMatchRecord.getMetricFields();
        Map<String, Object> fieldStringMap = new HashMap<>(10);
        Map<String, Object> fieldNumberMap = new HashMap<>(10);
        List<MetricsFieldCfg> fields = Optional.ofNullable(configCache.getMetricsCfgCache()).map(m -> m.get(metricsId)).map(MetricsCfg::getFields)
                .orElse(new ArrayList<>());
        Map<String, MetricsFieldTypeEnum> fieldTypeEnumMap = fields.stream().collect(Collectors.toMap(MetricsFieldCfg::getFieldName, MetricsFieldCfg::getFieldType));
        for (String fk : fieldMap.keySet()) {
            MetricsFieldTypeEnum typeEnum = fieldTypeEnumMap.get(fk);
            if (Objects.isNull(typeEnum) || Objects.isNull(fieldMap.get(fk))) {
                continue;
            }
            if ( MetricsFieldTypeEnum.number.equals(typeEnum)) {
                fieldNumberMap.put(fk, fieldMap.get(fk));
            } else {
                fieldStringMap.put(fk, String.valueOf(fieldMap.get(fk)));
            }
        }
        columns.put("fieldStringMap", fieldStringMap);
        columns.put("fieldNumberMap", fieldNumberMap);
    }

    public void addAlarmExtensionRelation(AlarmMatchRecord alarmMatchRecord) {
        List<AlarmExtensionRelation> alarmExtensionRelations = alarmMatchRecord.getAlarmExtensionRelations();
        if (!CollectionUtils.isEmpty(alarmExtensionRelations)) {
            alarmExtensionRelations.forEach(a -> {
                if (Objects.requireNonNull(a.getRelationType()) == dashboard) {
                    dashBoardWrite(a, alarmMatchRecord);
                }
            });
        }
    }

    private void dashBoardWrite(AlarmExtensionRelation a, AlarmMatchRecord alarmMatchRecord) {
        String dbName = ClickhouseSqlUtil.toClickhouseName(alarmMatchRecord.getTenantName());
        Map<String, Object> columns = new HashMap<>();
        columns.put("id", UUID.randomUUID().toString());
        columns.put("alarmMatchRecordId", alarmMatchRecord.getId());
        columns.put("alarmId", alarmMatchRecord.getAlarmId());
        columns.put("relatationId", a.getRelationId());
        columns.put("relatationName", a.getRelationName());
        columns.put("gmtCreate", new Timestamp(System.currentTimeMillis()));
        columns.put("configValue", buildConfigValue(alarmMatchRecord.getMetricsTags(), a.getRelationId()));
        columns.put("type", dashboard);
        Timestamp ts = new Timestamp(alarmMatchRecord.getTime() == 0 ? System.currentTimeMillis() : alarmMatchRecord.getTime());
        long recordMemorySize = ClickhouseSqlUtil.approximateSize(columns);
        clickhouseHandlerFactory.getClickhouseWriter().write(dbName, ClickhouseConst.ALARM_MATCH_RECORD_EXTENSION_RELATION_TABLE_NAME, columns, ts, recordMemorySize, false);
    }

    public String buildConfigValue(Map<String, String> metricsTags, String dashboardId) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(alarmDashboardEndpoint).append("/").append(dashboardId);
        if (!metricsTags.isEmpty()) {
            stringBuilder.append("?").append(Joiner.on("&")
                    .useForNull("")
                    .withKeyValueSeparator("=")
                    .join(metricsTags));
        }
        String link = stringBuilder.toString();
        try {
            URL url = new URL(link);
            link = url.getFile();
        } catch (Exception e) {
            log.error("URL substring error", e);
        }
        return link;
    }

    private String convertMapToJson(Map map) {
        try {
            return null == map ? objectMapper.createObjectNode().toString() : objectMapper.writeValueAsString(map);
        } catch (JsonProcessingException e) {
            log.error("error in converting Map to Json", e);
            return objectMapper.createObjectNode().toString();
        }
    }


    public void addAlarmNotificationRecords(AlarmMatchRecord alarmMatchRecord, Channel channel,String title, AlarmSendingStatusEnum alarmSendingStatus) {
        addAlarmNotificationRecords(alarmMatchRecord, channel.getId(), channel.getName(), channel.getEngineName(), title, alarmSendingStatus);
    }

    public void addAlarmNotificationRecords(AlarmMatchRecord alarmMatchRecord, String channelId, String channelName, String engineName, String title, AlarmSendingStatusEnum alarmSendingStatus) {
        String dbName = ClickhouseSqlUtil.toClickhouseName(alarmMatchRecord.getTenantName());
        Map<String, Object> columns =new HashMap<>();
        columns.put("id", UUID.randomUUID().toString());
        columns.put("alarmMatchRecordId", alarmMatchRecord.getId());
        columns.put("channelId", channelId);
        columns.put("channelName", channelName);
        columns.put("channelPriority", ChannelUtils.getPriorityByChannel(engineName));
        columns.put("status", alarmSendingStatus.getStatus());
        columns.put("title", title);
        columns.put("content", alarmMatchRecord.getNoticeContent());
        columns.put("gmtCreate", new Timestamp(System.currentTimeMillis()));
        Timestamp ts = new Timestamp(alarmMatchRecord.getTime() == 0 ? System.currentTimeMillis() : alarmMatchRecord.getTime());
        long recordMemorySize = ClickhouseSqlUtil.approximateSize(columns);
        clickhouseHandlerFactory.getClickhouseWriter().write(dbName, ClickhouseConst.ALARM_MATCH_RECORD_NOTIFICATION_TABLE_NAME, columns, ts, recordMemorySize, false);

    }

    private Map<String, Object>transColumns(Map<String, ?> t){
        Map<String, Object>ret=new HashMap<>();
        if(t!=null){
            for(Map.Entry<String,?>en:t.entrySet()){
                 String key=en.getKey();
                 if(ClickhouseConst.ALARM_RECORD_FIELD_NAME_TRANSFER.containsKey(key)){
                     key=ClickhouseConst.ALARM_RECORD_FIELD_NAME_TRANSFER.get(key);
                 }
                ret.put(key,en.getValue());
            }
        }
        return ret;
    }

    /**
     *  select * from (
     *  	select id, alarmLevel, gmtCreate
     *  	from Canyon_test_1.alarm_cube_match_service_record_table
     *  	where time >= toDateTime('2023-07-03 08:49:27')
     *  	and time <= toDateTime('2023-07-04 08:49:27')
     *  	and alarmId = '36264600-1568-49cd-80c1-ebdf574cf3a7'
     *  	and tagKey = 'c71685b06b240d64be773591d378158d'
     *  ) amsr global join(
     * 	    select * from (
     * 	 	    select id,alarmStatus, ackExpireTime, gmtCreate, row_number() over(partition by id order by gmtCreate desc) r
     * 	        from Canyon_test_1.alarm_cube_match_record_event_table
     * 	        where time >= toDateTime('2023-07-03 08:49:27')
     * 	        and time <= toDateTime('2023-07-04 08:49:27')
     * 	        and alarmId = '36264600-1568-49cd-80c1-ebdf574cf3a7'
     * 	 	    and tagKey = 'c71685b06b240d64be773591d378158d'
     * 	    )
     * 	    where r = 1 and alarmStatus = 'triggered'
     * ) acmre on amsr.id = acmre.id;
     */
    private static final String RECORD_SQL_TEMPLATE = "select * from ( select id, alarmLevel, gmtCreate from %s.%s where time >= toDateTime('%s') and time <= toDateTime('%s') and alarmId = '%s' and tagKey = '%s') amsr global join " +
            "(select * from (select id, alarmStatus, ackExpireTime, gmtCreate, row_number() over(partition by id order by gmtCreate desc) r " +
            "from %s.%s where time >= toDateTime('%s') and time <= toDateTime('%s') and alarmId = '%s' and tagKey = '%s') " +
            "where r = 1 and alarmStatus = '%s') acmre on amsr.id = acmre.id";

    /**
     * rebuild NotifyImgList from clickhouse
     */
    @SuppressWarnings("rawtypes")
    public NotifyImgList getNotifyImgListFromClickhouse(AlarmMatchRecordRequest request) {

        NotifyImgList notifyImgList = new NotifyImgList();

        long end = request.getEndTimestamp();
        long start = request.getStartTimestamp();
        //ack recovery start limit 8h
        long ackStart = end - ACK_RECORD_RECOVERY_PERIOD;
        String dbName = ClickhouseSqlUtil.encodeClickhouseName(request.getTenantName());
        String alarmId = request.getAlarmId();
        String tagKey = request.getTagKey();

        String triggeredSql = String.format(RECORD_SQL_TEMPLATE, dbName, ALARM_CUBE_MATCH_SERVICE_RECORD_TABLE, DateUtils.fromLongToDate(start), DateUtils.fromLongToDate(end), alarmId, tagKey
                , dbName, ALARM_CUBE_MATCH_RECORD_EVENT_TABLE, DateUtils.fromLongToDate(start), DateUtils.fromLongToDate(end), alarmId, tagKey, AlarmRecordStatusEnum.new_triggered.getWithNoPrefix());
        String ackSql = String.format(RECORD_SQL_TEMPLATE, dbName, ALARM_CUBE_MATCH_SERVICE_RECORD_TABLE, DateUtils.fromLongToDate(ackStart), DateUtils.fromLongToDate(end), alarmId, tagKey
                , dbName, ALARM_CUBE_MATCH_RECORD_EVENT_TABLE, DateUtils.fromLongToDate(ackStart), DateUtils.fromLongToDate(end), alarmId, tagKey, AlarmRecordStatusEnum.new_acknowledged.getWithNoPrefix());
        String sql = triggeredSql + " union all " + ackSql + ";";

        try {
            List<Map<String, Object>> queryResult = clickhouseHandlerFactory.get().queryInSameRegion(dbName,sql);
            for(Map<String, Object> q:queryResult){
                notifyImgList.addImg(transfer2NotifyImg(q));
            }
        }catch (Exception e){
            log.error("Error when getNotifyImgListFromClickhouse, serviceName = {}, alarmId = {}, tagKey = {}, sql={}", dbName, alarmId, tagKey, sql, e);
        }
        return notifyImgList;
    }

    /**
     * covert ch record into NotifyImg
     */
    private NotifyImg transfer2NotifyImg(Map<String, Object> map) {

        NotifyImg img = new NotifyImg();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            switch (entry.getKey()) {
                case "id":
                    img.setRecordId((String) entry.getValue());
                    break;
                case "alarmLevel":
                    img.setLevel(AlarmLevel.fromLevel((String) entry.getValue()));
                    break;
                case "alarmStatus":
                    img.setStatus(AlarmRecordStatusEnum.getStatusFromNoPrefixName((String)entry.getValue()));
                    break;
                case "ackExpireTime":
                    img.setAckExpireTime(((Timestamp) entry.getValue()).getTime());
                    break;
                case "gmtCreate": {
                    img.setSendTime(((Timestamp) entry.getValue()).getTime());
                    break;
                }
                default:
                    break;
            }
        }

        //ack img set sendTime equals ackExpireTime
        if (img.getStatus() == AlarmRecordStatusEnum.new_acknowledged.getStatus() && Objects.nonNull(img.getAckExpireTime())) {
            img.setSendTime(img.getAckExpireTime());
        }
        return img;
    }

    public Integer getAlarmMatchCount(AlarmMatchRecordRequest request) {
        String tenantName=request.getTenantName();
        InfluxAlarmService.validateRequest(request);
        if(sysParaService.isClickhouseTenantQueryEnable(tenantName)){
            return getAlarmMatchCountFromClickhouse(request);
        }
        return influxAlarmService.getAlarmMatchCount(request);
    }

    private int getAlarmMatchCountFromClickhouse(AlarmMatchRecordRequest request ) {
        String tenantName=request.getTenantName();
        String alarmName= request.getAlarmName();
        Boolean isLastCountMatched = request.getIsLastCountMatched();
        Boolean isNoticed = request.getIsNoticed();
        String tableName= ClickhouseConst.ALARM_RECORD_TABLE_PREFIX+alarmName;
        //remove tenant name and alarm name from query conditions
        request.setTenantName(null);
        request.setAlarmName(null);
        String selectField=ClickhouseConst.toV2Name(InfluxAlarmService.getCountField(request));
        String selectClause=" count("+selectField+") as c";
        long end = request.getEndTimestamp()/1000;
        long start = request.getStartTimestamp()/1000;
        int limit= request.getSize();
        Map<String, String> tagConditions = request.fetchTags();
        Map<String, Object> fieldCondtions = request.fetchFields();
        QueryBuilder queryBuilder = new QueryBuilder()
                .setDbName(tenantName).setTableName(tableName)
                .setStartTime(start).setEndTime(end).addConditions(tagConditions).addConditions(fieldCondtions)
                .setLimit(limit);
        String where= queryBuilder.genConditionsIgnoreNull();
        String from=queryBuilder.genFrom();
        String sql="select "+selectClause+from+where+ queryBuilder.genLimit();
        try {
            List<Map<String, Object>> queryResult = clickhouseHandlerFactory.get().queryInSameRegion(tenantName,sql);
            return Integer.parseInt(queryResult.get(0).get("c").toString());
        }catch (Exception e){
            log.error("Error when getting alarm match count from clickhouse, serviceName = {}, alarmName = {}, sql={}", tenantName, alarmName, sql, e);
            return 0;
        }finally {
            request.setTenantName(tenantName);
            request.setAlarmName(alarmName);
            request.setIsLastCountMatched(isLastCountMatched);
            request.setIsNoticed(isNoticed);
        }
    }

}
