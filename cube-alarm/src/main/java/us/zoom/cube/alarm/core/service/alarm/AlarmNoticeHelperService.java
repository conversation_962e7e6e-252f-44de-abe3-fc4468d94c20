package us.zoom.cube.alarm.core.service.alarm;

import com.google.common.collect.Lists;
import com.zoom.op.monitor.domain.alarm.AlarmRule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.alarm.biz.service.AlarmHandler;
import us.zoom.cube.alarm.biz.service.selfmonitor.StaticsDetailService;
import us.zoom.cube.alarm.core.config.ConfigCache;
import us.zoom.cube.alarm.core.config.EnvironmentCacheLoader;
import us.zoom.cube.alarm.core.model.alarm.config.SubEnvCfg;
import us.zoom.cube.alarm.core.model.cache.AlarmImgCacheOutput;
import us.zoom.cube.alarm.core.model.cache.NotifyImgKey;
import us.zoom.cube.alarm.core.model.cache.NotifyImgList;
import us.zoom.cube.alarm.core.service.StorageService;
import us.zoom.cube.alarm.core.service.cache.LocalCacheHolder;
import us.zoom.cube.alarm.core.service.syspara.AlarmParaService;
import us.zoom.cube.alarm.core.service.syspara.SysParaService;
import us.zoom.cube.alarm.infra.redis.MultiRedisService;
import us.zoom.cube.alarm.util.AlarmTraceUtil;
import us.zoom.cube.lib.common.AlarmMonitorTypeEnum;
import us.zoom.infra.enums.AlarmEventTypeEnum;
import us.zoom.infra.enums.AlarmRecordStatusEnum;
import us.zoom.infra.enums.EnvironmentIsForceEnum;
import us.zoom.infra.influx.model.alarm.AlarmMatchRecord;
import us.zoom.infra.influx.model.alarm.AlarmMatchRecordRequest;
import us.zoom.infra.influx.model.alarm.BaseAlarmMatch;
import us.zoom.infra.model.LogResult;
import us.zoom.infra.model.alarm.AlarmLevel;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.infra.utils.IpUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static us.zoom.cube.alarm.core.model.alarm.config.SubEnvCfg.ALARM_ACTION_ENV_CONVERT;
import static us.zoom.cube.alarm.core.service.syspara.AlarmParaService.ALARM_NOTIFY_GROUP_BY_TAGS;
import static us.zoom.cube.alarm.core.service.syspara.AlarmParaService.ALL;
import static us.zoom.cube.alarm.core.service.syspara.SysParaService.PK_ALARM_RECORD_QUERY_CH;

/**
 * @author: canyon.li
 * @date: 2023/06/29
 **/
@Service
@Slf4j
public class AlarmNoticeHelperService {


    @Autowired
    private ConfigCache configCache;

    @Autowired
    protected StorageService storageService;

    @Autowired
    protected SysParaService sysParaService;

    @Autowired
    EnvironmentCacheLoader environmentCacheLoader;

    @Autowired
    private LocalCacheHolder localCacheHolder;

    @Autowired
    private MultiRedisService redisService;

    @Autowired
    private StaticsDetailService staticsDetailService;

    protected static final String SERVICE_ENV_CACHE_PREFIX = "service_";

    protected Logger monitorLog = LoggerFactory.getLogger("Monitor");

    protected static final String PERF = "perf";

    protected static final int QUERY_MAX_SIZE = 20;

    protected static final List<AlarmLevel> ORDERED_ALARM_LEVEL_LIST = Lists.newArrayList(AlarmLevel.FATAL, AlarmLevel.ERROR, AlarmLevel.WARN, AlarmLevel.INFO);
    @Autowired
    private AlarmParaService alarmParaService;

    @Autowired
    private AlarmNoticeRateLimitService alarmNoticeRateLimitService;

    public boolean isSendAlarmUnderThisEnv(String serviceId) {

        //find service-level env
        String subEnvCacheKey = SERVICE_ENV_CACHE_PREFIX + serviceId;
        Set<String> alarmEnvs = Optional.ofNullable(configCache.getSubEnvCfgCache()).map(m -> m.get(subEnvCacheKey)).map(SubEnvCfg::getAlarmActionEnvs).orElse(new HashSet<>());
        if (environmentCacheLoader.getCurrConfigEnv() != null && environmentCacheLoader.getCurrConfigEnv().startsWith(PERF)) {
            alarmEnvs = alarmEnvs.stream().map(e -> ALARM_ACTION_ENV_CONVERT.getOrDefault(e, e)).collect(Collectors.toSet());
        }
        //check if the curr env is executable
        return sendAlarmUnderThisEnv(configCache.getEnv(), configCache.getWorkingEnv(), configCache.getMainEnv(), configCache.getIsForce(), alarmEnvs);
    }

    private boolean sendAlarmUnderThisEnv(String env, String workingEnv, String mainEnv, int isForce, Set<String> alarmEnvs) {

        boolean result = false;
        //service alarm env has config, first execute and ignore other env
        if (!CollectionUtils.isEmpty(alarmEnvs)) {
            return alarmEnvs.contains(env);
        }
        if (isForce == EnvironmentIsForceEnum.TRUE.getCode()){
            if (env.equals(workingEnv)){
                result = true;
            }
        }else {
            if (env.equals(mainEnv) || env.equals(workingEnv)){
                result = true;
            }
        }
        return result;
    }

    @SuppressWarnings("rawtypes")
    public Map<AlarmLevel, List<AlarmMatchRecord>> getNoticeRecordsByLevel(List<AlarmMatchRecord> alarmMatchRecordList) {
        long startTime = System.currentTimeMillis();
        AlarmMatchRecord alarmMatchRecordSample = alarmMatchRecordList.get(0);
        long inhibitInterval = alarmMatchRecordSample.getInhibitInterval();
        long endTimestamp = System.currentTimeMillis();
        long startTimestamp = endTimestamp - 1000L * inhibitInterval;
        boolean needGroupByTags = alarmParaService.getParamBooleanValue(ALARM_NOTIFY_GROUP_BY_TAGS, false)
                && StringUtils.isNotBlank(alarmMatchRecordSample.getGroupTagKey());
        boolean groupByTagNeedPermission = alarmParaService.getAlarmSwitch().isGroupByTagNeedPermission();
        List<String> checkPermissionServiceList = alarmParaService.getAlarmSwitch().getGroupByTagCheckPermissionServiceList();
        boolean needGetPermission = groupByTagNeedPermission && (checkPermissionServiceList.contains(alarmMatchRecordSample.getTenantName()) || checkPermissionServiceList.contains(ALL));
        AlarmHandler.AlarmNewStatContext alarmNewStatContext = AlarmHandler.alarmNewStatContextThreadLocal.get();
        long startGetPermission = System.currentTimeMillis();
        // Try to acquire notification permission before starting processing
        if (needGroupByTags && needGetPermission && !acquireNotificationPermission(alarmMatchRecordSample)) {
            for (AlarmMatchRecord record : alarmMatchRecordList) {
                record.setStatus(AlarmRecordStatusEnum.new_suppressed);
                record.setAlarmEventType(AlarmEventTypeEnum.sys_suppressed_by_not_get_notice_permission);
                storageService.addAlarmRecordV2(record);
                storageService.addAlarmRecordEvent(record);
            }
            // No permission acquired, return empty result directly, no alarm notification sent
            if (alarmNewStatContext != null) {
                staticsDetailService.statics(AlarmMonitorTypeEnum.notGetNoticePermission, alarmNewStatContext.getTaskEntity(), alarmMatchRecordSample, alarmNewStatContext.getMetrics(), 1, startGetPermission);
            }
            return new HashMap<>(1);
        }

        if (alarmNewStatContext != null) {
            staticsDetailService.statics(AlarmMonitorTypeEnum.getNoticePermission, alarmNewStatContext.getTaskEntity(), alarmMatchRecordSample, alarmNewStatContext.getMetrics(), 1, startGetPermission);
        }

        long startCache = System.currentTimeMillis();

        NotifyImgKey notifyImgKey;
        NotifyImgList notifyImgList;


        if (needGroupByTags && StringUtils.isNoneBlank(alarmMatchRecordSample.getGroupTagKey())) {
            notifyImgKey = new NotifyImgKey(alarmMatchRecordSample.getAlarmId(), alarmMatchRecordSample.getGroupTagKey());
            notifyImgList = redisService.getCastValue(notifyImgKey.generateKey(), NotifyImgList.class, alarmParaService.isNewRedisSwitchForGroupByTag());
        } else {
            notifyImgKey = new NotifyImgKey(alarmMatchRecordSample.getAlarmId(), alarmMatchRecordSample.getTagKey());
            notifyImgList = localCacheHolder.getAlarmNoticeCache().getIfPresent(notifyImgKey);
        }

        if(alarmNewStatContext != null){
            staticsDetailService.statics(AlarmMonitorTypeEnum.queryNoticeRecordsCacheStat,alarmNewStatContext.getTaskEntity(),alarmMatchRecordSample,alarmNewStatContext.getMetrics(),1,startCache);
        }
        //load from db
        if (Objects.isNull(notifyImgList)) {
            if(alarmNewStatContext != null){
                staticsDetailService.statics(AlarmMonitorTypeEnum.queryNoticeRecordsMissCacheStat,alarmNewStatContext.getTaskEntity(),alarmMatchRecordSample,alarmNewStatContext.getMetrics(),1,startCache);
            }
            if(Boolean.TRUE.equals(sysParaService.getAlarmSwitchBooleanValue(PK_ALARM_RECORD_QUERY_CH))){
                long startDB = System.currentTimeMillis();
                notifyImgList = getNotifyImgFromDB(alarmMatchRecordSample);
                if(alarmNewStatContext != null){
                    staticsDetailService.statics(AlarmMonitorTypeEnum.queryNoticeRecordsFromDBStat,alarmNewStatContext.getTaskEntity(),alarmMatchRecordSample,alarmNewStatContext.getMetrics(),1,startDB);
                }
            }else {
                notifyImgList = new NotifyImgList();
            }
            localCacheHolder.getAlarmNoticeCache().put(notifyImgKey, notifyImgList);
            if (needGroupByTags && StringUtils.isNoneBlank(alarmMatchRecordSample.getGroupTagKey())) {
                redisService.set(notifyImgKey.generateKey(), JsonUtils.toJsonString(notifyImgList),1, TimeUnit.DAYS, alarmParaService.isNewRedisSwitchForGroupByTag());
            }
        } else {
            if(alarmNewStatContext != null){
                staticsDetailService.statics(AlarmMonitorTypeEnum.queryNoticeRecordsHitCacheStat,alarmNewStatContext.getTaskEntity(),alarmMatchRecordSample,alarmNewStatContext.getMetrics(),1,startCache);
            }
        }
        AlarmImgCacheOutput alarmImgCacheOutput = notifyImgList.isEmpty() ?
                new AlarmImgCacheOutput() : notifyImgList.listValid(startTimestamp, endTimestamp);

        //calc need notify records, and process suppressed records
        Map<AlarmLevel, List<AlarmMatchRecord>> alarmMatchRecordByLevel = alarmMatchRecordList.stream().collect(Collectors.groupingBy(BaseAlarmMatch::getAlarmLevel));
        Map<AlarmLevel, List<AlarmMatchRecord>> noticeRecordByLevel = calcNoticeRecords(alarmMatchRecordByLevel, alarmMatchRecordSample, alarmImgCacheOutput);
        if(alarmNewStatContext != null){
            staticsDetailService.statics(AlarmMonitorTypeEnum.queryNoticeRecordsStat,alarmNewStatContext.getTaskEntity(),alarmMatchRecordSample,alarmNewStatContext.getMetrics(),1,startTime);
        }
        return noticeRecordByLevel;
    }

    /**
     * Use Redis atomic operation to ensure only one machine sends alarm notification
     * @param alarmMatchRecord alarm match record
     * @return whether permission to send was acquired
     */
    private boolean acquireNotificationPermission(AlarmMatchRecord alarmMatchRecord) {
        String permissionKey = generateNotificationPermissionKey(alarmMatchRecord);
        if (StringUtils.isBlank(permissionKey)) {
            return true;
        }
        String ip = IpUtils.getLocalIP();
        // 5 seconds expiration to prevent deadlock and cover time window boundary
        int expireSeconds = 5;
        long startGetPermission = System.currentTimeMillis();
        try {
            // Use SET NX EX command to atomically set permission
            Boolean result = redisService.setIfAbsent(permissionKey, ip, expireSeconds, alarmParaService.isNewRedisSwitchForGroupByTag());
            return result != null && result;
        } catch (Exception e) {
            log.error("Error acquiring notification permission for key: {}", permissionKey, e);
            return false;
        }
    }

    /**
     * Generate notification permission key
     */
    private String generateNotificationPermissionKey(AlarmMatchRecord alarmMatchRecord) {

        if (StringUtils.isBlank(alarmMatchRecord.getGroupTagKey())) {
            return null;
        }

        // Use the actual timestamp from alarm data instead of current system time
        // This ensures that delayed data and normal data fall into the same time window
        long dataTimestamp = alarmMatchRecord.getTime();
        if (dataTimestamp <= 0) {
            // Fallback to current time if timestamp is invalid
            dataTimestamp = System.currentTimeMillis();
        }

        // Group by minute based on data timestamp to ensure the same alarm is only sent once within the same minute
        // Using 60-second window to avoid boundary issues and ensure proper locking
        long timeWindow = dataTimestamp / 60000;
        return String.format("notify:permission:%s:%s:%d",
                alarmMatchRecord.getAlarmId(), alarmMatchRecord.getGroupTagKey(), timeWindow);
    }



    /**
     * build NotifyImg cache from db
     */
    @SuppressWarnings("rawtypes")
    protected NotifyImgList getNotifyImgFromDB(AlarmMatchRecord sampleRecord) {

        AlarmMatchRecordRequest request = new AlarmMatchRecordRequest();
        long endTimestamp = System.currentTimeMillis();
        long inhibitInterval = sampleRecord.getInhibitInterval();
        long startTimestamp = endTimestamp - 1000L * inhibitInterval;
        request.setEndTimestamp(System.currentTimeMillis());
        request.setStartTimestamp(startTimestamp);
        request.setSize(QUERY_MAX_SIZE);
        request.setTagKey(sampleRecord.getTagKey());
        request.setAlarmId(sampleRecord.getAlarmId());


        request.setMetricsTags(sampleRecord.getMetricsTags());
        request.setTenantName(sampleRecord.getTenantName());
        request.setAlarmName(sampleRecord.getAlarmName());
        request.setIsLastCountMatched(true);
        request.setIsNoticed(true);

        return storageService.getNotifyImgListFromClickhouse(request);
    }

    /**
     * Calculate the records that need to be notified (Judgment of inhibiting conditions)
     *
     * @param input upload from metrics
     * @param cache history cache
     * @return Notification records grouped by AlarmLevel
     */
    protected Map<AlarmLevel, List<AlarmMatchRecord>> calcNoticeRecords(Map<AlarmLevel, List<AlarmMatchRecord>> input, AlarmMatchRecord alarmMatchRecordSample, AlarmImgCacheOutput cache) {


        int inhibitThreshold = alarmMatchRecordSample.getInhibitThreshold();

        Map<AlarmLevel, List<AlarmMatchRecord>> noticeRecordByLevel = new HashMap<>(10);
        //Suppression flags for current input
        boolean suppressedByCurrInputHighLevel = false;
        String currInputPreId = null;
        AlarmHandler.AlarmNewStatContext alarmNewStatContext = AlarmHandler.alarmNewStatContextThreadLocal.get();
        for (AlarmLevel alarmLevel : ORDERED_ALARM_LEVEL_LIST) {
            List<AlarmMatchRecord> currLevelInput = input.get(alarmLevel);
            if (CollectionUtils.isEmpty(currLevelInput)) {
                continue;
            }
            int currLevelInputCount = 0;
            for (AlarmMatchRecord record : currLevelInput) {

                /*
                   Judge whether to suppress from current input
                       1. high level exist notify records.
                       2. curr level notify records count > threshold
                 */
                if (suppressedByCurrInputHighLevel || currLevelInputCount >= inhibitThreshold) {
                    record.setStatus(AlarmRecordStatusEnum.new_suppressed);
                    record.setAlarmEventType(AlarmEventTypeEnum.sys_suppressed_by_triggered);
                    record.setPreRecordId(currInputPreId);
                    storageService.addAlarmRecordV2(record);
                    storageService.addAlarmRecordEvent(record);
                    continue;
                }

                //Judge whether to suppress from cache
                AlarmImgCacheOutput.JudgeResult result = cache.isSuppress(alarmLevel, inhibitThreshold);
                if (Optional.ofNullable(result).map(AlarmImgCacheOutput.JudgeResult::isSuppress).orElse(false)) {
                    //change alarmMatchRecord status -> suppressed
                    trace(alarmMatchRecordSample,"inhibited");
                    record.setStatus(AlarmRecordStatusEnum.new_suppressed);
                    record.setAlarmEventType(result.getEventType());
                    record.setPreRecordId(result.getPreId());
                    storageService.addAlarmRecordV2(record);
                    storageService.addAlarmRecordEvent(record);
                    continue;
                }

                // Check notification rate limiting
                String ruleId = Optional.ofNullable(record.getHittedRule()).map(AlarmRule::getId).orElse(null);
                String serviceName = record.getTenantName();
                long startTime = System.currentTimeMillis();
                if (!alarmNoticeRateLimitService.isAllowedToNotify(ruleId, serviceName, null)) {
                    // Rate limited, mark as suppressed status
                    trace(alarmMatchRecordSample, "rate_limited", "ruleId: " + ruleId + ", serviceName: " + serviceName);
                    record.setStatus(AlarmRecordStatusEnum.new_suppressed);
                    record.setAlarmEventType(AlarmEventTypeEnum.sys_suppressed_by_rate_limit);
                    record.setPreRecordId(currInputPreId);
                    storageService.addAlarmRecordV2(record);
                    storageService.addAlarmRecordEvent(record);
                    staticsDetailService.statics(AlarmMonitorTypeEnum.checkRateLimit, alarmNewStatContext.getTaskEntity(), record, alarmNewStatContext.getMetrics(), 1, startTime);
                    continue;
                }
                currLevelInputCount++;
                List<AlarmMatchRecord> recordList = noticeRecordByLevel.getOrDefault(alarmLevel, new ArrayList<>());
                recordList.add(record);
                noticeRecordByLevel.put(alarmLevel, recordList);
                currInputPreId = record.getId();
            }
            //if notice record in current level is not empty, set flag as true, means that low-level record input needs suppressed
            if (!CollectionUtils.isEmpty(noticeRecordByLevel.get(alarmLevel))) {
                suppressedByCurrInputHighLevel = true;
                currInputPreId = noticeRecordByLevel.get(alarmLevel).get(0).getId();
            }
        }

        return noticeRecordByLevel;
    }

    protected void trace(AlarmMatchRecord alarmMatchRecordSample,String action ) {
        trace(alarmMatchRecordSample,action,null);
    }

    protected void trace(AlarmMatchRecord alarmMatchRecordSample,String action,String addition ) {
        try{
            if(AlarmTraceUtil.shouldTrace(sysParaService.getAlarmTracePara(),alarmMatchRecordSample.getAlarmId(),alarmMatchRecordSample.fetchTags())){
                Map<String,Object> metricsMap = new HashMap<>();
                metricsMap.put("action",action);
                if(StringUtils.isNotBlank(addition)){
                    metricsMap.put("addition",addition);
                }
                monitorLog.info(LogResult.getSuccessLogResult(AlarmMonitorTypeEnum.alarmTrace.name(), JsonUtils.toJsonStringIgnoreExp(alarmMatchRecordSample),metricsMap));
            }
        }catch (Exception ignored){
        }
    }
}
