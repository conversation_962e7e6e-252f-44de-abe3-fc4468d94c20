package us.zoom.cube.alarm.infra.redis;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;
import us.zoom.cube.alarm.core.service.syspara.AlarmParaService;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Multi-Redis service that supports both primary and secondary Redis instances.
 * All methods accept a boolean parameter to specify which Redis to use:
 * - true = use SECONDARY Redis
 * - false = use PRIMARY Redis
 * 
 * Additionally, this service provides automatic Redis instance selection based on
 * alarmParaService.isNewRedisSwitch() configuration:
 * - If isNewRedisSwitch() returns true, use SECONDARY Redis
 * - If isNewRedisSwitch() returns false, use PRIMARY Redis
 * 
 * Usage examples:
 * 1. Boolean selection: multiRedisService.get("key", true) // true = SECONDARY, false = PRIMARY
 * 2. Auto selection: multiRedisService.get("key") // Uses switch configuration
 * 3. Specific instance: multiRedisService.getPrimary("key") or multiRedisService.getSecondary("key")
 * 
 * <AUTHOR> Alarm Team
 * @since 2.0.17
 */
@Service
public class MultiRedisService {

    private static final Logger logger = LoggerFactory.getLogger(MultiRedisService.class);

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    @Qualifier("primaryRedisTemplate")
    private RedisTemplate<String, Object> primaryRedisTemplate;

    @Autowired
    @Qualifier("secondaryRedisTemplate")
    private RedisTemplate<String, Object> secondaryRedisTemplate;

    @Autowired
    private AlarmParaService alarmParaService;

    /**
     * Get the appropriate Redis template based on boolean flag.
     * 
     * @param useSecondary true to use secondary Redis, false to use primary Redis
     * @return the Redis template
     */
    private RedisTemplate<String, Object> getRedisTemplate(boolean useSecondary) {
        return useSecondary ? secondaryRedisTemplate : primaryRedisTemplate;
    }

    /**
     * Get the appropriate Redis template based on the new Redis switch configuration.
     * If isNewRedisSwitch() returns true, use SECONDARY Redis, otherwise use PRIMARY Redis.
     * 
     * @return the Redis template
     */
    private RedisTemplate<String, Object> getRedisTemplateBySwitch() {
        return alarmParaService.isNewRedisSwitch() ? secondaryRedisTemplate : primaryRedisTemplate;
    }

    /**
     * Single get operation.
     * 
     * @param key the key to get
     * @param useSecondary true to use secondary Redis, false to use primary Redis
     * @return the value associated with the key
     */
    public Object get(String key, boolean useSecondary) {
        try {
            return getRedisTemplate(useSecondary).opsForValue().get(key);
        } catch (Exception e) {
            logger.error("Failed to execute get command, key: {}, useSecondary: {}", key, useSecondary, e);
        }
        return null;
    }

    /**
     * Get value and set expiration time.
     * 
     * @param key the key to get
     * @param expireTime expiration time in seconds
     * @param useSecondary true to use secondary Redis, false to use primary Redis
     * @return the value associated with the key
     */
    public Object getAndExpire(String key, long expireTime, boolean useSecondary) {
        try {
            return getRedisTemplate(useSecondary).opsForValue().getAndExpire(key, expireTime, TimeUnit.SECONDS);
        } catch (Exception e) {
            logger.error("Failed to execute getAndExpire command, key: {}, expireTime: {}, useSecondary: {}", 
                key, expireTime, useSecondary, e);
        }
        return null;
    }

    /**
     * Single set operation.
     * 
     * @param key the key to set
     * @param value the value to set
     * @param useSecondary true to use secondary Redis, false to use primary Redis
     */
    public void set(String key, Object value, boolean useSecondary) {
        try {
            getRedisTemplate(useSecondary).opsForValue().set(key, value);
        } catch (Exception e) {
            logger.error("Failed to execute set command, key: {}, useSecondary: {}, error: {}", 
                key, useSecondary, e.getMessage());
        }
    }

    /**
     * Single set operation with expiration.
     * 
     * @param key the key to set
     * @param value the value to set
     * @param timeout expiration timeout
     * @param timeUnit time unit for expiration
     * @param useSecondary true to use secondary Redis, false to use primary Redis
     */
    public void set(String key, Object value, long timeout, TimeUnit timeUnit, boolean useSecondary) {
        try {
            getRedisTemplate(useSecondary).opsForValue().set(key, value, timeout, timeUnit);
        } catch (Exception e) {
            logger.error("Failed to execute set command with expire time, key: {}, timeout: {}, timeUnit: {}, useSecondary: {}, error: {}", 
                key, timeout, timeUnit, useSecondary, e.getMessage());
        }
    }

    /**
     * Set Redis lock using SET NX EX command.
     * 
     * @param key the lock key
     * @param value the lock value
     * @param timeInMinutes lock duration in minutes
     * @param useSecondary true to use secondary Redis, false to use primary Redis
     * @return true if lock was acquired, false otherwise
     */
    public boolean setRedisLock(String key, Object value, long timeInMinutes, boolean useSecondary) {
        try {
            Boolean result = getRedisTemplate(useSecondary).opsForValue().setIfAbsent(key, value, timeInMinutes, TimeUnit.MINUTES);
            return result != null && result;
        } catch (Exception e) {
            logger.error("Failed to execute setRedisLock command, key: {}, useSecondary: {}, error: {}", 
                key, useSecondary, e.getMessage());
            throw e;
        }
    }

    /**
     * Set value only if key doesn't exist.
     * 
     * @param key the key to set
     * @param value the value to set
     * @param seconds expiration time in seconds
     * @param useSecondary true to use secondary Redis, false to use primary Redis
     * @return true if key was set, false if key already exists
     */
    public Boolean setIfAbsent(String key, Object value, long seconds, boolean useSecondary) {
        long start = System.currentTimeMillis();
        try {
            return getRedisTemplate(useSecondary).opsForValue().setIfAbsent(key, value, seconds, TimeUnit.SECONDS);
        } catch (Exception e) {
            logger.error("Failed to execute setIfAbsent command, key: {}, useSecondary: {}, cost:{}, error: {}",
                key, useSecondary, System.currentTimeMillis() - start, e.getMessage());
            throw e;
        }
    }

    /**
     * Single delete operation.
     * 
     * @param key the key to delete
     * @param useSecondary true to use secondary Redis, false to use primary Redis
     * @return true if key was deleted, false otherwise
     */
    public Boolean delete(String key, boolean useSecondary) {
        try {
            return getRedisTemplate(useSecondary).delete(key);
        } catch (Exception e) {
            logger.error("Failed to execute delete command, key: {}, useSecondary: {}", key, useSecondary, e);
        }
        return false;
    }

    /**
     * Batch insert operation.
     * 
     * @param keyValuePairs map of key-value pairs to insert
     * @param useSecondary true to use secondary Redis, false to use primary Redis
     */
    public void batchInsert(Map<String, Object> keyValuePairs, boolean useSecondary) {
        getRedisTemplate(useSecondary).executePipelined((RedisCallback<?>) (redisConnection) -> {
            for (Map.Entry<String, Object> entry : keyValuePairs.entrySet()) {
                getRedisTemplate(useSecondary).opsForValue().set(entry.getKey(), entry.getValue());
            }
            return null;
        });
    }

    /**
     * Batch delete operation.
     * 
     * @param keys list of keys to delete
     * @param useSecondary true to use secondary Redis, false to use primary Redis
     */
    public void batchDelete(List<String> keys, boolean useSecondary) {
        getRedisTemplate(useSecondary).executePipelined((RedisCallback<?>) (redisConnection) -> {
            for (String key : keys) {
                getRedisTemplate(useSecondary).delete(key);
            }
            return null;
        });
    }

    /**
     * Get values with certain prefix and cast to specified type.
     * 
     * @param prefix the key prefix
     * @param clazz the target class type
     * @param useSecondary true to use secondary Redis, false to use primary Redis
     * @return list of values with the specified type
     * @param <T> the target type
     */
    public <T> List<T> getValuesByPrefix(String prefix, Class<T> clazz, boolean useSecondary) {
        List<T> result = new ArrayList<>();
        try {
            Set<String> keys = scanForAllKeys(prefix.concat("*"), 100, useSecondary);
            for (String k : keys) {
                T value = objectMapper.readValue(String.valueOf(getRedisTemplate(useSecondary).opsForValue().get(k)), clazz);
                result.add(value);
            }
        } catch (Exception e) {
            logger.error("Failed to execute getValuesByPrefix command, prefix: {}, useSecondary: {}, error: {}", 
                prefix, useSecondary, e.getMessage());
        }
        return result;
    }

    /**
     * Get value and cast to specified type.
     * 
     * @param key the key to get
     * @param clazz the target class type
     * @param useSecondary true to use secondary Redis, false to use primary Redis
     * @return the value cast to the specified type
     * @param <T> the target type
     */
    public <T> T getCastValue(String key, Class<T> clazz, boolean useSecondary) {
        long startTime = System.currentTimeMillis();
        try {
            Object value = getRedisTemplate(useSecondary).opsForValue().get(key);
            if (Objects.nonNull(value)) {
                return objectMapper.readValue(String.valueOf(value), clazz);
            }
        } catch (Exception e) {
            logger.error("getModel error, time: {}, key: {}, useSecondary: {}, {}", 
                System.currentTimeMillis() - startTime, key, useSecondary, clazz.getName(), e);
        }
        return null;
    }

    /**
     * Scan for keys matching a pattern.
     * 
     * @param pattern the key pattern
     * @param count the scan count
     * @param useSecondary true to use secondary Redis, false to use primary Redis
     * @return set of matching keys
     */
    private Set<String> scanForAllKeys(String pattern, long count, boolean useSecondary) {
        Set<String> keys = new HashSet<>();
        Cursor<String> cursor = getRedisTemplate(useSecondary).scan(ScanOptions.scanOptions().match(pattern).count(count).build());
        while (cursor.hasNext()) {
            keys.add(cursor.next());
        }
        cursor.close();
        return keys;
    }

    /**
     * Set field value in hash structure.
     * 
     * @param key Redis key
     * @param field hash field
     * @param value field value
     * @param useSecondary true to use secondary Redis, false to use primary Redis
     */
    public void hset(String key, String field, Object value, boolean useSecondary) {
        try {
            getRedisTemplate(useSecondary).opsForHash().put(key, field, value);
        } catch (Exception e) {
            logger.error("Failed to execute hset command, key: {}, field: {}, useSecondary: {}, error: {}", 
                key, field, useSecondary, e.getMessage());
        }
    }

    /**
     * Set field value in hash structure with expiration time.
     * 
     * @param key Redis key
     * @param field hash field
     * @param value field value
     * @param timeout expiration time
     * @param timeUnit time unit
     * @param useSecondary true to use secondary Redis, false to use primary Redis
     */
    public void hset(String key, String field, Object value, long timeout, TimeUnit timeUnit, boolean useSecondary) {
        try {
            getRedisTemplate(useSecondary).opsForHash().put(key, field, value);
            getRedisTemplate(useSecondary).expire(key, timeout, timeUnit);
        } catch (Exception e) {
            logger.error("Failed to execute hset command with expire time, key: {}, field: {}, timeout: {}, timeUnit: {}, useSecondary: {}, error: {}", 
                key, field, timeout, timeUnit, useSecondary, e.getMessage());
        }
    }

    /**
     * Batch set multiple field values in hash structure.
     * 
     * @param key Redis key
     * @param fieldValueMap field-value mapping
     * @param useSecondary true to use secondary Redis, false to use primary Redis
     */
    public void hmset(String key, Map<String, Object> fieldValueMap, boolean useSecondary) {
        try {
            getRedisTemplate(useSecondary).opsForHash().putAll(key, fieldValueMap);
        } catch (Exception e) {
            logger.error("Failed to execute hmset command, key: {}, useSecondary: {}, error: {}", 
                key, useSecondary, e.getMessage());
        }
    }

    /**
     * Batch set multiple field values in hash structure with expiration time.
     * 
     * @param key Redis key
     * @param fieldValueMap field-value mapping
     * @param timeout expiration time
     * @param timeUnit time unit
     * @param useSecondary true to use secondary Redis, false to use primary Redis
     */
    public void hmset(String key, Map<String, Object> fieldValueMap, long timeout, TimeUnit timeUnit, boolean useSecondary) {
        try {
            getRedisTemplate(useSecondary).opsForHash().putAll(key, fieldValueMap);
            getRedisTemplate(useSecondary).expire(key, timeout, timeUnit);
        } catch (Exception e) {
            logger.error("Failed to execute hmset command with expire time, key: {}, timeout: {}, timeUnit: {}, useSecondary: {}, error: {}", 
                key, timeout, timeUnit, useSecondary, e.getMessage());
        }
    }

    /**
     * Get field value from hash structure.
     * 
     * @param key Redis key
     * @param field hash field
     * @param useSecondary true to use secondary Redis, false to use primary Redis
     * @return field value
     */
    public Object hget(String key, String field, boolean useSecondary) {
        try {
            return getRedisTemplate(useSecondary).opsForHash().get(key, field);
        } catch (Exception e) {
            logger.error("Failed to execute hget command, key: {}, field: {}, useSecondary: {}, error: {}", 
                key, field, useSecondary, e.getMessage());
        }
        return null;
    }

    /**
     * Get all field values from hash structure.
     * 
     * @param key Redis key
     * @param useSecondary true to use secondary Redis, false to use primary Redis
     * @return mapping of all field values
     */
    public Map<String, Object> hgetAll(String key, boolean useSecondary) {
        try {
            Map<Object, Object> entries = getRedisTemplate(useSecondary).opsForHash().entries(key);
            Map<String, Object> result = new HashMap<>();
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                result.put(String.valueOf(entry.getKey()), entry.getValue());
            }
            return result;
        } catch (Exception e) {
            logger.error("Failed to execute hgetAll command, key: {}, useSecondary: {}, error: {}", 
                key, useSecondary, e.getMessage());
        }
        return new HashMap<>();
    }

    /**
     * Get multiple field values from hash structure.
     * 
     * @param key Redis key
     * @param fields list of hash fields
     * @param useSecondary true to use secondary Redis, false to use primary Redis
     * @return list of field values
     */
    public List<Object> hmget(String key, List<String> fields, boolean useSecondary) {
        try {
            return getRedisTemplate(useSecondary).opsForHash().multiGet(key, Collections.unmodifiableList(fields));
        } catch (Exception e) {
            logger.error("Failed to execute hmget command, key: {}, fields: {}, useSecondary: {}, error: {}", 
                key, fields, useSecondary, e.getMessage());
        }
        return new ArrayList<>();
    }

    /**
     * Delete fields from hash structure.
     * 
     * @param key Redis key
     * @param useSecondary true to use secondary Redis, false to use primary Redis
     * @param fields fields to delete
     * @return number of successfully deleted fields
     */
    public Long hdel(String key, boolean useSecondary, String... fields) {
        try {
            return getRedisTemplate(useSecondary).opsForHash().delete(key, (Object[]) fields);
        } catch (Exception e) {
            logger.error("Failed to execute hdel command, key: {}, fields: {}, useSecondary: {}, error: {}", 
                key, Arrays.toString(fields), useSecondary, e.getMessage());
        }
        return 0L;
    }

    /**
     * Check if field exists in hash structure.
     * 
     * @param key Redis key
     * @param field hash field
     * @param useSecondary true to use secondary Redis, false to use primary Redis
     * @return whether the field exists
     */
    public Boolean hexists(String key, String field, boolean useSecondary) {
        try {
            return getRedisTemplate(useSecondary).opsForHash().hasKey(key, field);
        } catch (Exception e) {
            logger.error("Failed to execute hexists command, key: {}, field: {}, useSecondary: {}, error: {}", 
                key, field, useSecondary, e.getMessage());
        }
        return false;
    }

    /**
     * Get all fields from hash structure.
     * 
     * @param key Redis key
     * @param useSecondary true to use secondary Redis, false to use primary Redis
     * @return list of fields
     */
    public Set<Object> hkeys(String key, boolean useSecondary) {
        try {
            return getRedisTemplate(useSecondary).opsForHash().keys(key);
        } catch (Exception e) {
            logger.error("Failed to execute hkeys command, key: {}, useSecondary: {}, error: {}", 
                key, useSecondary, e.getMessage());
        }
        return new HashSet<>();
    }

    /**
     * Get all values from hash structure.
     * 
     * @param key Redis key
     * @param useSecondary true to use secondary Redis, false to use primary Redis
     * @return list of values
     */
    public List<Object> hvals(String key, boolean useSecondary) {
        try {
            return getRedisTemplate(useSecondary).opsForHash().values(key);
        } catch (Exception e) {
            logger.error("Failed to execute hvals command, key: {}, useSecondary: {}, error: {}", 
                key, useSecondary, e.getMessage());
        }
        return new ArrayList<>();
    }

    /**
     * Get number of fields in hash structure.
     * 
     * @param key Redis key
     * @param useSecondary true to use secondary Redis, false to use primary Redis
     * @return number of fields
     */
    public Long hlen(String key, boolean useSecondary) {
        try {
            return getRedisTemplate(useSecondary).opsForHash().size(key);
        } catch (Exception e) {
            logger.error("Failed to execute hlen command, key: {}, useSecondary: {}, error: {}", 
                key, useSecondary, e.getMessage());
        }
        return 0L;
    }

    /**
     * Add element to sorted set.
     * 
     * @param key Redis key
     * @param value element value
     * @param score element score
     * @param useSecondary true to use secondary Redis, false to use primary Redis
     * @return true if element was added, false if element already exists
     */
    public Boolean zadd(String key, Object value, double score, boolean useSecondary) {
        try {
            return getRedisTemplate(useSecondary).opsForZSet().add(key, value, score);
        } catch (Exception e) {
            logger.error("Failed to execute zadd command, key: {}, value: {}, score: {}, useSecondary: {}, error: {}", 
                key, value, score, useSecondary, e.getMessage());
        }
        return false;
    }

    /**
     * Add multiple elements to sorted set.
     * 
     * @param key Redis key
     * @param valueScoreMap map of value-score pairs
     * @param useSecondary true to use secondary Redis, false to use primary Redis
     * @return number of elements added
     */
    public Long zadd(String key, Map<Object, Double> valueScoreMap, boolean useSecondary) {
        try {
            Set<ZSetOperations.TypedTuple<Object>> tuples = valueScoreMap.entrySet().stream()
                .map(entry -> ZSetOperations.TypedTuple.of(entry.getKey(), entry.getValue()))
                .collect(Collectors.toSet());
            return getRedisTemplate(useSecondary).opsForZSet().add(key, tuples);
        } catch (Exception e) {
            logger.error("Failed to execute zadd command with multiple elements, key: {}, useSecondary: {}, error: {}", 
                key, useSecondary, e.getMessage());
        }
        return 0L;
    }

    /**
     * Get score of element in sorted set.
     * 
     * @param key Redis key
     * @param value element value
     * @param useSecondary true to use secondary Redis, false to use primary Redis
     * @return score of the element, null if element doesn't exist
     */
    public Double zscore(String key, Object value, boolean useSecondary) {
        try {
            return getRedisTemplate(useSecondary).opsForZSet().score(key, value);
        } catch (Exception e) {
            logger.error("Failed to execute zscore command, key: {}, value: {}, useSecondary: {}, error: {}", 
                key, value, useSecondary, e.getMessage());
        }
        return null;
    }

    /**
     * Get elements from sorted set by range.
     * 
     * @param key Redis key
     * @param start start index (inclusive)
     * @param end end index (inclusive)
     * @param useSecondary true to use secondary Redis, false to use primary Redis
     * @return list of elements in the range
     */
    public Set<Object> zrange(String key, long start, long end, boolean useSecondary) {
        try {
            return getRedisTemplate(useSecondary).opsForZSet().range(key, start, end);
        } catch (Exception e) {
            logger.error("Failed to execute zrange command, key: {}, start: {}, end: {}, useSecondary: {}, error: {}", 
                key, start, end, useSecondary, e.getMessage());
        }
        return new HashSet<>();
    }

    /**
     * Get elements with scores from sorted set by range.
     * 
     * @param key Redis key
     * @param start start index (inclusive)
     * @param end end index (inclusive)
     * @param useSecondary true to use secondary Redis, false to use primary Redis
     * @return set of elements with their scores in the range
     */
    public Set<ZSetOperations.TypedTuple<Object>> zrangeWithScores(String key, long start, long end, boolean useSecondary) {
        try {
            return getRedisTemplate(useSecondary).opsForZSet().rangeWithScores(key, start, end);
        } catch (Exception e) {
            logger.error("Failed to execute zrangeWithScores command, key: {}, start: {}, end: {}, useSecondary: {}, error: {}", 
                key, start, end, useSecondary, e.getMessage());
        }
        return new HashSet<>();
    }

    /**
     * Get elements from sorted set by score range.
     * 
     * @param key Redis key
     * @param min minimum score (inclusive)
     * @param max maximum score (inclusive)
     * @param useSecondary true to use secondary Redis, false to use primary Redis
     * @return set of elements in the score range
     */
    public Set<Object> zrangeByScore(String key, double min, double max, boolean useSecondary) {
        try {
            return getRedisTemplate(useSecondary).opsForZSet().rangeByScore(key, min, max);
        } catch (Exception e) {
            logger.error("Failed to execute zrangeByScore command, key: {}, min: {}, max: {}, useSecondary: {}, error: {}", 
                key, min, max, useSecondary, e.getMessage());
        }
        return new HashSet<>();
    }

    /**
     * Get elements with scores from sorted set by score range.
     * 
     * @param key Redis key
     * @param min minimum score (inclusive)
     * @param max maximum score (inclusive)
     * @param useSecondary true to use secondary Redis, false to use primary Redis
     * @return set of elements with their scores in the score range
     */
    public Set<ZSetOperations.TypedTuple<Object>> zrangeByScoreWithScores(String key, double min, double max, boolean useSecondary) {
        try {
            return getRedisTemplate(useSecondary).opsForZSet().rangeByScoreWithScores(key, min, max);
        } catch (Exception e) {
            logger.error("Failed to execute zrangeByScoreWithScores command, key: {}, min: {}, max: {}, useSecondary: {}, error: {}", 
                key, min, max, useSecondary, e.getMessage());
        }
        return new HashSet<>();
    }

    /**
     * Remove elements from sorted set by rank range.
     * 
     * @param key Redis key
     * @param start start index (inclusive)
     * @param end end index (inclusive)
     * @param useSecondary true to use secondary Redis, false to use primary Redis
     * @return number of elements removed
     */
    public Long zremrangeByRank(String key, long start, long end, boolean useSecondary) {
        try {
            return getRedisTemplate(useSecondary).opsForZSet().removeRange(key, start, end);
        } catch (Exception e) {
            logger.error("Failed to execute zremrangeByRank command, key: {}, start: {}, end: {}, useSecondary: {}, error: {}", 
                key, start, end, useSecondary, e.getMessage());
        }
        return 0L;
    }

    /**
     * Remove elements from sorted set by score range.
     * 
     * @param key Redis key
     * @param min minimum score (inclusive)
     * @param max maximum score (inclusive)
     * @param useSecondary true to use secondary Redis, false to use primary Redis
     * @return number of elements removed
     */
    public Long zremrangeByScore(String key, double min, double max, boolean useSecondary) {
        try {
            return getRedisTemplate(useSecondary).opsForZSet().removeRangeByScore(key, min, max);
        } catch (Exception e) {
            logger.error("Failed to execute zremrangeByScore command, key: {}, min: {}, max: {}, useSecondary: {}, error: {}", 
                key, min, max, useSecondary, e.getMessage());
        }
        return 0L;
    }

    /**
     * Get size of sorted set.
     * 
     * @param key Redis key
     * @param useSecondary true to use secondary Redis, false to use primary Redis
     * @return number of elements in the sorted set
     */
    public Long zcard(String key, boolean useSecondary) {
        try {
            return getRedisTemplate(useSecondary).opsForZSet().size(key);
        } catch (Exception e) {
            logger.error("Failed to execute zcard command, key: {}, useSecondary: {}, error: {}", 
                key, useSecondary, e.getMessage());
        }
        return 0L;
    }

    /**
     * Count elements in sorted set with scores between min and max.
     * 
     * @param key Redis key
     * @param min minimum score (inclusive)
     * @param max maximum score (inclusive)
     * @param useSecondary true to use secondary Redis, false to use primary Redis
     * @return number of elements in the score range
     */
    public Long zcount(String key, double min, double max, boolean useSecondary) {
        try {
            return getRedisTemplate(useSecondary).opsForZSet().count(key, min, max);
        } catch (Exception e) {
            logger.error("Failed to execute zcount command, key: {}, min: {}, max: {}, useSecondary: {}, error: {}", 
                key, min, max, useSecondary, e.getMessage());
        }
        return 0L;
    }

    /**
     * Get rank of element in sorted set.
     * 
     * @param key Redis key
     * @param value element value
     * @param useSecondary true to use secondary Redis, false to use primary Redis
     * @return rank of the element (0-based), null if element doesn't exist
     */
    public Long zrank(String key, Object value, boolean useSecondary) {
        try {
            return getRedisTemplate(useSecondary).opsForZSet().rank(key, value);
        } catch (Exception e) {
            logger.error("Failed to execute zrank command, key: {}, value: {}, useSecondary: {}, error: {}", 
                key, value, useSecondary, e.getMessage());
        }
        return null;
    }

    /**
     * Get reverse rank of element in sorted set.
     * 
     * @param key Redis key
     * @param value element value
     * @param useSecondary true to use secondary Redis, false to use primary Redis
     * @return reverse rank of the element (0-based), null if element doesn't exist
     */
    public Long zrevrank(String key, Object value, boolean useSecondary) {
        try {
            return getRedisTemplate(useSecondary).opsForZSet().reverseRank(key, value);
        } catch (Exception e) {
            logger.error("Failed to execute zrevrank command, key: {}, value: {}, useSecondary: {}, error: {}", 
                key, value, useSecondary, e.getMessage());
        }
        return null;
    }

    /**
     * Store data in primary Redis and backup to secondary Redis.
     * 
     * @param key the key to store
     * @param value the value to store
     */
    public void storeWithBackup(String key, Object value) {
        set(key, value, false);
        set(key, value, true);
    }

    /**
     * Get data from primary Redis, fallback to secondary if primary fails.
     * 
     * @param key the key to get
     * @return the value associated with the key
     */
    public Object getWithFallback(String key) {
        Object value = get(key, false);
        if (value == null) {
            value = get(key, true);
        }
        return value;
    }
} 