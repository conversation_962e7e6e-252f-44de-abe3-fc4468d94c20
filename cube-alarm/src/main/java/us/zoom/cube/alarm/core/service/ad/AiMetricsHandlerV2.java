package us.zoom.cube.alarm.core.service.ad;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import com.zoom.op.monitor.domain.alarm.AlarmExtensionRelation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.alarm.biz.service.selfmonitor.StaticAiDetailService;
import us.zoom.cube.alarm.constat.LogConstant;
import us.zoom.cube.alarm.core.config.ConfigCache;
import us.zoom.cube.alarm.core.model.ad.AiMonitorTypeEnum;
import us.zoom.cube.alarm.core.model.ad.TagGroupRecords;
import us.zoom.cube.alarm.core.model.alarm.config.AlarmCfg;
import us.zoom.cube.alarm.core.model.alarm.config.AlarmRuleCfg;
import us.zoom.cube.alarm.core.model.alarm.match.AlarmRuleMatchRequest;
import us.zoom.cube.alarm.core.model.alarm.match.AlarmRuleMatchResult;
import us.zoom.cube.alarm.core.model.alarm.model.AlarmEndpointParaService;
import us.zoom.cube.alarm.core.model.cache.*;
import us.zoom.cube.alarm.core.model.metrics.MetricsCfg;
import us.zoom.cube.alarm.core.model.metrics.MetricsFieldCfg;
import us.zoom.cube.alarm.core.service.StorageService;
import us.zoom.cube.alarm.core.service.action.AlarmAction;
import us.zoom.cube.alarm.core.service.alarm.AlarmNoticeHelperService;
import us.zoom.cube.alarm.core.service.cache.LocalCacheHolder;
import us.zoom.cube.alarm.core.service.match.AlarmRuleMatcher;
import us.zoom.cube.alarm.core.service.match.LastCountMatcher;
import us.zoom.cube.alarm.core.service.syspara.AlarmParaService;
import us.zoom.cube.alarm.infra.LogSampling;
import us.zoom.cube.alarm.infra.redis.MultiRedisService;
import us.zoom.cube.alarm.util.LogUtils;
import us.zoom.cube.lib.agent.AlarmSelfMonitorTypeEnum;
import us.zoom.cube.lib.integrations.Metrics;
import us.zoom.cube.lib.integrations.MetricsField;
import us.zoom.cube.lib.integrations.MetricsFieldTypeEnum;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.sdk.util.MonitorLogReporter;
import us.zoom.infra.enums.AlarmEventTypeEnum;
import us.zoom.infra.enums.AlarmRecordStatusEnum;
import us.zoom.infra.enums.AlarmSourceTypeEnum;
import us.zoom.infra.enums.MetricsTypeEnum;
import us.zoom.infra.influx.model.alarm.*;
import us.zoom.infra.influx.util.TagUtils;
import us.zoom.infra.loader.PiiTableLoader;
import us.zoom.infra.model.alarm.AlarmLevel;
import us.zoom.infra.model.alarm.AlarmRuleSingleContent;
import us.zoom.infra.model.alarm.IncidentItem;
import us.zoom.infra.monitor.AlarmSelfMonitor;
import us.zoom.infra.notification.NotificationHelper;
import us.zoom.infra.utils.DateUtils;
import us.zoom.mq.client.clients.consumer.RetryableStraw;
import us.zoom.mq.common.client.task.PayloadType;
import us.zoom.mq.common.entity.TaskEntity;
import us.zoom.mq.common.util.JsonUtil;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static java.util.Map.entry;
import static us.zoom.cube.alarm.core.service.syspara.AlarmParaService.ALARM_NOTIFY_GROUP_BY_TAGS;
import static us.zoom.cube.lib.common.CubeConstants.*;

/**
 * @author: canyon.li
 * @date: 2025/01/14
 **/
@Component
@Slf4j
public class AiMetricsHandlerV2 implements RetryableStraw<String> {

    @Getter
    public static Boolean needMaskLog;

    @Autowired
    private ConfigCache configCache;

    @Autowired
    private List<AlarmAction> actionPacks;

    @Autowired
    private AlarmNoticeHelperService alarmNoticeHelperService;
    
    @Autowired
    private StaticAiDetailService staticAiDetailService;

    @Autowired
    private StorageService storageService;

    @Autowired
    private LocalCacheHolder localCacheHolder;

    @Autowired
    private AlarmRuleMatcher alarmRuleMatcher;

    @Autowired
    private LastCountMatcher lastCountMatcher;

    @Value("${cube.incident.interval:0}")
    @Setter
    private int incidentInterval;

    @Value("${cube.incident.level.threshold:0}")
    @Setter
    private int levelThreshold;

    @Autowired
    private AlarmParaService alarmParaService;

    @Autowired
    private AlarmEndpointParaService alarmEndpointParaService;

    @Autowired
    private NotificationHelper notificationHelper;

    @Autowired
    private PiiTableLoader piiTableLoader;

    @Autowired
    private MultiRedisService redisService;

    private static final String AI_OPERATOR = "AI";

    private static final String THRESHOLD = "threshold";

    private static final Map<Integer, Double> ANOMALY_SCORE_MAPPING;

    static {
        ANOMALY_SCORE_MAPPING = new HashMap<>();
        ANOMALY_SCORE_MAPPING.put(5, 0.95);
        ANOMALY_SCORE_MAPPING.put(4, 0.85);
        ANOMALY_SCORE_MAPPING.put(3, 0.75);
        ANOMALY_SCORE_MAPPING.put(2, 0.65);
        ANOMALY_SCORE_MAPPING.put(1, 0.55);
    }

    private final Logger monitorLog = LoggerFactory.getLogger("Monitor");

    private static final AtomicLong COMMON_ERROR_ALARM_LOG_INDEX = new AtomicLong();

    public static final ThreadLocal<AiMetricsHandlerV2.AiAlarmStatContext> AI_ALARM_STAT_CONTEXT_V2 = ThreadLocal.withInitial(() -> null);

    private final static TypeReference<String> TYPE_REFERENCE = new TypeReference<>() {
    };

    private final int DEFAULT_LAST_COUNT_CACHE_SINGLE_KEY_MAX_CAPACITY = 1000;

    @Override
    public boolean onMessage(List<TaskEntity<String>> taskEntities) {

        if (CollectionUtils.isEmpty(taskEntities)) {
            return false;
        }

        for (TaskEntity<String> taskEntity : taskEntities) {
            try {
                //1. parse message and filter
                long start = System.currentTimeMillis();
                String record = transferMessageToString(taskEntity.getPayloadType(), taskEntity.getPayload());
                AiMetricsV2 aiMetrics = parseMessage(record);
                if (Objects.isNull(aiMetrics)) {
                    continue;
                }
                //add origin detect result record.
                storageService.addOriginAiDetectResult(aiMetrics);
                if (!checkAiMetricsValid(aiMetrics)) {
                    log.warn("AiMetricsHandlerV2 checkAiMetricsValid failed, record:{}", JsonUtils.toJsonStringIgnoreExp(aiMetrics));
                    continue;
                }
                MetricsCfg metricsCfg = Optional.ofNullable(configCache.getMetricsCfgCache()).map(m -> m.get(aiMetrics.getMetricsId())).orElse(null);
                if (Objects.isNull(metricsCfg)) {
                    log.warn("AiMetricsHandlerV2 metrics config is null, record:{}", JsonUtils.toJsonStringIgnoreExp(aiMetrics));
                    continue;
                }

                List<AlarmCfg> alarmCfgList = configCache.getAlarmsByAdId(aiMetrics.getMetricsId(), aiMetrics.getId());
                if (CollectionUtils.isEmpty(alarmCfgList)) {
                    continue;
                }
                long metricsTs = Optional.ofNullable(aiMetrics.getAiAlarmConditionCfgList()).map(l -> l.get(0)).map(AiRuleConditionV2::getTime).orElse(start);
                staticAiDetailService.statics(AiMonitorTypeEnum.inputStat, taskEntity, null, aiMetrics, 1, metricsTs);

                AiMetricsHandlerV2.AiAlarmStatContext aiAlarmStatContext = new AiMetricsHandlerV2.AiAlarmStatContext(record, aiMetrics, metricsCfg, null, null, alarmCfgList, taskEntity);
                AI_ALARM_STAT_CONTEXT_V2.set(aiAlarmStatContext);

                //2. split metrics by time
                List<AiMetricsV2> aiMetricsList = splitAiMetricsByTime(aiMetrics);

                //3. deal with metrics order by time.
                for (AiMetricsV2 item : aiMetricsList) {

                    //3.1 generate AlarmMatchRecord, group by alarmId. (alarmId -> tagKey -> List<AlarmMatchRecord>)
                    long alarmCalcStart = System.currentTimeMillis();
                    Map<String, TagGroupRecords> alarmCalcResults = alarmCalcV2(item, metricsCfg);
                    if (CollectionUtils.isEmpty(alarmCalcResults)) {
                        staticAiDetailService.statics(AiMonitorTypeEnum.alarmCalcNoResultStat, taskEntity, null, aiMetrics, 1, alarmCalcStart);
                        continue;
                    }
                    staticAiDetailService.statics(AiMonitorTypeEnum.alarmCalcSuccessStat, taskEntity, null, aiMetrics, 1, alarmCalcStart);

                    // 3.2 do actions
                    long doActionStart = System.currentTimeMillis();
                    for (TagGroupRecords tagGroupRecords : alarmCalcResults.values()) {
                        if (CollectionUtils.isEmpty(tagGroupRecords.getTagMap())) {
                            continue;
                        }
                        for (List<AlarmMatchRecord> actionAlarmRecords : tagGroupRecords.getTagMap().values()) {
                            if (CollectionUtils.isEmpty(actionAlarmRecords)) {
                                continue;
                            }
                            boolean isSend = alarmNoticeHelperService.isSendAlarmUnderThisEnv(actionAlarmRecords.get(0).getTenantId());
                            Map<AlarmLevel, List<AlarmMatchRecord>> alarmMatchRecordByLevel = alarmNoticeHelperService.getNoticeRecordsByLevel(actionAlarmRecords);
                            List<AlarmMatchRecord> realActionRecordList = new ArrayList<>();
                            for (List<AlarmMatchRecord> list : alarmMatchRecordByLevel.values()) {
                                realActionRecordList.addAll(list);
                            }
                            actionPacks.forEach(e -> {
                                if (e.matchingCheck(actionAlarmRecords)) {
                                    try {
                                        if (CollectionUtils.isEmpty(actionAlarmRecords)) {
                                            return;
                                        }
                                        e.doAction(actionAlarmRecords, alarmMatchRecordByLevel, isSend);
                                    } catch (Exception exp) {
                                        logError("[AiMetricsHandlerV2#onMessage] execute action-{} error! exception: {}", e.getClass().getSimpleName(), exp);
                                    }
                                }
                            });
                            updateIsNoticed(realActionRecordList);

                            if (!CollectionUtils.isEmpty(actionAlarmRecords)) {
                                staticAiDetailService.statics(AiMonitorTypeEnum.actionStat, taskEntity, actionAlarmRecords.get(0), aiMetrics, 1, doActionStart);
                            }
                        }
                    }
                }
                staticAiDetailService.statics(AiMonitorTypeEnum.processEndStat, taskEntity, null, aiMetrics, 1, start);
            } catch (Exception e) {
                log.error("AiMetricsHandlerV2 alarm event doAction error", e);
            } finally {
                AI_ALARM_STAT_CONTEXT_V2.remove();
            }
        }
        return false;
    }

    /**
     * split input aiMetrics by time
     * @param aiMetrics ai metrics
     * @return split result, order by time asc.
     */
    private List<AiMetricsV2> splitAiMetricsByTime(AiMetricsV2 aiMetrics) {

        // get input list
        List<AiRuleConditionV2> aiAlarmConditionCfgList = aiMetrics.getAiAlarmConditionCfgList();

        // group by time, time -> aiDetectResultList.
        Map<Long, List<AiRuleConditionV2>> groupedByTime = aiAlarmConditionCfgList.stream()
                .collect(Collectors.groupingBy(AiRuleConditionV2::getTime));

        // generate new AiMetricsV2 obj for each group, order by time asc.
        List<AiMetricsV2> result = groupedByTime.values().stream()
                .map(aiRuleConditionV2s -> {
                    AiMetricsV2 newMetrics = new AiMetricsV2();
                    newMetrics.setMetricsId(aiMetrics.getMetricsId());
                    newMetrics.setMetricsName(aiMetrics.getMetricsName());
                    newMetrics.setTenantId(aiMetrics.getTenantId());
                    newMetrics.setTenantName(aiMetrics.getTenantName());
                    newMetrics.setFieldName(aiMetrics.getFieldName());
                    newMetrics.setId(aiMetrics.getId());
                    newMetrics.setAdName(aiMetrics.getAdName());
                    newMetrics.setAiAlarmConditionCfgList(aiRuleConditionV2s);
                    return newMetrics;
                })
                .sorted(Comparator.comparingLong(metrics -> metrics.getAiAlarmConditionCfgList().get(0).getTime()))
                .collect(Collectors.toList());

        return result;
    }

    private Map<String, TagGroupRecords> alarmCalcV2(AiMetricsV2 aiMetrics, MetricsCfg metricsCfg) {

        Map<String, TagGroupRecords> aiCalcResultMap = new HashMap<>();

        //Query related alarms, one metrics may have multiple alarms.
        List<AlarmCfg> alarms = Optional.ofNullable(AiMetricsHandlerV2.AI_ALARM_STAT_CONTEXT_V2.get())
                .map(AiMetricsHandlerV2.AiAlarmStatContext::getAlarmCfgList)
                .orElse(null);
        if (CollectionUtils.isEmpty(alarms)) {
            return aiCalcResultMap;
        }

        Objects.requireNonNull(alarms).forEach(alarm -> {
            // --------------------- calc single alarm (Core Function) ------------------------------------
            TagGroupRecords tagGroupRecords = calcSingleAlarmV2(alarm, aiMetrics, metricsCfg);
            if (Objects.nonNull(tagGroupRecords)) {
                aiCalcResultMap.put(alarm.getId(), tagGroupRecords);
            }
        });
        return aiCalcResultMap;
    }

    private TagGroupRecords calcSingleAlarmV2(AlarmCfg alarm, AiMetricsV2 aiMetrics, MetricsCfg metricsCfg) {
        //AlarmCfgAlarmRuleCfg
        List<AlarmRuleCfg> alarmRuleCfgs = alarm.getAlarmRuleCfgs();
        if (CollectionUtils.isEmpty(alarmRuleCfgs)) {
            return null;
        }
        TagGroupRecords tagGroupRecords = new TagGroupRecords();

        //process each data with same ts (different in tagValues)
        for (AiRuleConditionV2 aiRuleCondition : aiMetrics.getAiAlarmConditionCfgList()) {

            //1. build Metrics
            //build field -> fieldType map
            Map<String, MetricsFieldTypeEnum> fieldTypeMap = Maps.newHashMap();
            for (MetricsFieldCfg fieldCfg : metricsCfg.getFields()) {
                fieldTypeMap.put(fieldCfg.getFieldName(), fieldCfg.getFieldType());
            }
            //build metrics info
            Metrics metrics = new Metrics();
            metrics.setTags(aiRuleCondition.getAdTagCfgDataList());
            //metrics field
            List<MetricsField> metricsFields = new ArrayList<>();
            for (Map.Entry<String, Object> entry : aiRuleCondition.getFieldMap().entrySet()) {
                MetricsField field = new MetricsField(entry.getKey(), entry.getValue(), fieldTypeMap.get(entry.getKey()));
                metricsFields.add(field);
            }
            metrics.setFields(metricsFields);
            metrics.setTs(aiRuleCondition.getTime());
            metrics.setMetricsId(aiMetrics.getMetricsId());
            metrics.setMetricsName(aiMetrics.getMetricsName());

            //AI Var Map
            Map<String, Object> filterVar = new HashMap<>(aiRuleCondition.getFilterVar().convertToMap());
            filterVar.putAll(convertColumnsToVarMap(aiRuleCondition.getColumns()));
            metrics.setFilterVar(filterVar);

            Long latestTime = aiRuleCondition.getLatestTime();

            String tagKey = TagUtils.createKey(aiRuleCondition.getAdTagCfgDataList());

            //add origin record in service-level table (ai_origin_detect_result_service_record_table)
            storageService.addAiMetric(alarm, aiMetrics, aiRuleCondition, tagKey);

            //2. check each alarm rule
            for (AlarmRuleCfg alarmRuleCfg : alarmRuleCfgs) {
                try {
                    // 2.1 check if match alarm rule.
                    AlarmRuleMatchRequest alarmRuleMatchRequest = new AlarmRuleMatchRequest();
                    alarmRuleMatchRequest.setAlarmRuleCfg(alarmRuleCfg);
                    alarmRuleMatchRequest.setMetricsId(aiMetrics.getMetricsId());
                    alarmRuleMatchRequest.setMetricsList(Collections.singletonList(metrics));
                    alarmRuleMatchRequest.setIgnoreAiCondition(false);
                    // --------------------- Core Function --------------------
                    AlarmRuleMatchResult alarmRuleMatchResult = alarmRuleMatcher.isMatch(alarmRuleMatchRequest);
                    boolean isAlarmRuleMatched = alarmRuleMatchResult.isMatched();
                    // 2.2 if required continuous hits, delete expired data in lastCountCache (according to latestTime or alarm rule matched.)
                    cleanupExpiredCache(alarmRuleCfg, aiRuleCondition, latestTime, aiMetrics.getTenantName(), alarm.getAlarmName(), isAlarmRuleMatched, alarm);
                    if (!isAlarmRuleMatched) {
                        continue;
                    }
                    // 2.3 build alarm record.
                    AlarmMatchRecord alarmMatchRecord = new AlarmMatchRecord();
                    alarmMatchRecord.setTime(aiRuleCondition.getTime());
                    alarmMatchRecord.setAlarmSourceType(AlarmSourceTypeEnum.AI);
                    alarmMatchRecord.setMetricsId(metricsCfg.getId());
                    alarmMatchRecord.setMetricsType(Optional.of(metricsCfg).map(MetricsCfg::getType).map(MetricsTypeEnum::getValue).orElse(null));
                    alarmMatchRecord.setMetricsName(metrics.getMetricsName());
                    alarmMatchRecord.setAlarmId(alarm.getId());
                    alarmMatchRecord.setAlarmName(alarm.getAlarmName());
                    alarmMatchRecord.setInhibitInterval(alarm.getInhibitInterval());
                    alarmMatchRecord.setInhibitThreshold(alarm.getInhibitThreshold());
                    //add threshold var
                    for (AlarmRuleSingleContent singleContent : alarmRuleCfg.getAlarmRuleSingleContents()) {
                        if (!AI_OPERATOR.equals(singleContent.getOperator())) {
                            continue;
                        }
                        Optional.ofNullable(singleContent.getCmpValue())
                                .map(Integer::valueOf)
                                .map(ANOMALY_SCORE_MAPPING::get)
                                .ifPresent(thresholdValue -> aiRuleCondition.getColumns().put(THRESHOLD, thresholdValue));
                    }
                    alarmMatchRecord.setAiResult(new LinkedHashMap<>(aiRuleCondition.getColumns()));
                    alarmMatchRecord.setAiVarMap(filterVar);
                    alarmMatchRecord.setMetricFields(aiRuleCondition.getFieldMap());
                    alarmMatchRecord.setTitle(alarm.getTitle());
                    alarmMatchRecord.setIsNoticed(false);
                    alarmMatchRecord.setAlarmLevel(alarmRuleMatchResult.getAlarmLevel());
                    alarmMatchRecord.setAlarmRuleId(alarmRuleCfg.getId());
                    alarmMatchRecord.setHittedRule(alarmRuleCfg.getAlarmRule());
                    //add tag
                    alarmMatchRecord.setMetricsTags(aiRuleCondition.getAdTagCfgDataList());
                    alarmMatchRecord.setTags(new HashMap<>(aiRuleCondition.getAdTagCfgDataList()));
                    //set tagKey, groupTagKey and notifyGroupKey
                    alarmMatchRecord.setKeys(aiRuleCondition.getAdTagCfgDataList(), alarm.getGroupTags());
                    alarmMatchRecord.setTenantName(aiMetrics.getTenantName());
                    List<AlarmExtensionRelation> alarmExtensionRelations = alarm.getAlarmExtensionRelations().stream().map(AlarmExtensionRelation::copy).toList();
                    alarmMatchRecord.setAlarmExtensionRelations(alarmExtensionRelations);
                    alarmMatchRecord.setLastCount(alarmRuleCfg.getLastCount());
                    alarmMatchRecord.setTenantId(alarm.getTenantId());
                    alarmMatchRecord.setStatus(AlarmRecordStatusEnum.new_alarming);
                    //add hitValue
                    AlarmRuleHittedValue alarmRuleHitValue = convertAlertResultToHitValue(aiRuleCondition);
                    alarmRuleHitValue.setAdId(aiMetrics.getId());
                    alarmRuleHitValue.setAdName(aiMetrics.getAdName());
                    alarmMatchRecord.setHittedValue(alarmRuleHitValue);
                    //validate pii
                    if(piiTableLoader.existPii(aiMetrics.getTenantName(), metricsCfg.getMetricsName())) {
                        alarmMatchRecord.setContainsPii(true);
                    }
                    //generate id
                    alarmMatchRecord.generateId();
                    //add histogram result->AlarmAction.getVarMapFromRecord
                    alarmMatchRecord.setHistogramResult(alarmRuleMatchResult.getHistogramResult());

                    //4. check if last count number is matched.
                    long matchLastCountStart = System.currentTimeMillis();
                    if (lastCountMatcher.isMatch(alarmMatchRecord)) {
                        //change alarmMatchRecord status -> alarming
                        alarmMatchRecord.setIsLastCountMatched(true);
                        alarmMatchRecord.setStatus(AlarmRecordStatusEnum.new_alarming);
                        tagGroupRecords.addRecord(alarmMatchRecord.getTagKey(), alarmMatchRecord);
                    } else {
                        //change alarmMatchRecord status -> pending
                        alarmMatchRecord.setStatus(AlarmRecordStatusEnum.new_pending);
                        alarmMatchRecord.setAlarmEventType(AlarmEventTypeEnum.sys_pending);
                        storageService.addAlarmRecordV2(alarmMatchRecord);
                        storageService.addAlarmRecordEvent(alarmMatchRecord);
                    }

                    staticAiDetailService.statics(AiMonitorTypeEnum.matchLastCountStat,
                            Optional.ofNullable(AI_ALARM_STAT_CONTEXT_V2.get()).map(AiAlarmStatContext::getTaskEntity).orElse(null),
                            alarmMatchRecord, aiMetrics, 1, matchLastCountStart);

                } catch (Exception e) {
                    LogUtils.logCalcError("AI Alarm single rule match calc error, alarm rule:{}", JsonUtils.toJsonStringIgnoreExp(alarmRuleCfg), e);
                }
            }
        }
        return tagGroupRecords;
    }

    private static final Map<String, String> AI_VAR_MAPPING = Map.ofEntries(
            entry("Anomaly Score", ANOMALY_SCORE),
            entry("Predicted Value/Rate", PREDICTED_VALUE_RATE),
            entry("Point-to-Point Value/Rate", POINT_TO_POINT_VALUE_RATE),
            entry("Day-to-Day Value/Rate", DAY_TO_DAY_VALUE_RATE),
            entry("Week-on-Week Value/Rate", WEEK_ON_WEEK_VALUE_RATE),
            entry("Debug Info", DEBUG_INFO),
            entry("sparse_type", SPARSE_TYPE),
            entry("hist_min_value", HIST_MIN_VALUE),
            entry("hist_max_value", HIST_MAX_VALUE)
    );

    private Map<String, Object> convertColumnsToVarMap(Map<String, Object> columns) {
        if (columns == null || columns.isEmpty()) {
            return Collections.emptyMap();
        }
        return columns.entrySet().stream()
                .filter(entry -> AI_VAR_MAPPING.containsKey(entry.getKey()))
                .collect(Collectors.toMap(
                        entry -> AI_VAR_MAPPING.get(entry.getKey()),
                        Map.Entry::getValue,
                        (oldVal, newVal) -> oldVal
                ));
    }

    private AlarmImgKey generateAlarmImgKey(AlarmRuleCfg alarmRuleCfg, Map<String, String> metricTags, AlarmCfg alarm) {
        Map<String, String> tags = BaseAlarmMatch.createFetchTags(
                Optional.ofNullable(alarmRuleCfg).map(AlarmRuleCfg::getAlarmLevel).map(AlarmLevel::getLevel).orElse(null),
                alarm.getId(), Objects.requireNonNull(alarmRuleCfg).getId(), metricTags);
        return new AlarmImgKey(alarmRuleCfg.getId(), TagUtils.createKey(tags));
    }


    /**
     * Clean up expired cache data with continuous hit rules
     * @param alarmRuleCfg     alarm rule config
     * @param aiRuleCondition  metric data (from AI input)
     * @param latestTime       last metric time (from AI input)
     */
    private void cleanupExpiredCache(AlarmRuleCfg alarmRuleCfg, AiRuleConditionV2 aiRuleCondition, long latestTime, String serviceName, String alarmName, boolean isAlarmRuleMatched, AlarmCfg alarm) {
        // 1. Build cache key.
        AlarmImgKey alarmImgKey = generateAlarmImgKey(alarmRuleCfg, aiRuleCondition.getAdTagCfgDataList(), alarm);

        //default clear exceed time window records(round down).
        long ts = aiRuleCondition.getTime() - alarmRuleCfg.getTimeWindow() * 1000;

        // 2. Get data from cache.
        AlarmImgList alarmImgList = localCacheHolder.getLastCountCache().getIfPresent(alarmImgKey);
        if (alarmImgList == null) {
            alarmImgList = new AlarmImgList();
        }
        // 3. need continuous and not hit alarm rule, clear records by current metric time.
        if (alarmRuleCfg.isContinuous()) {
            AlarmImg lastImg = alarmImgList.getLast();
            if (!isAlarmRuleMatched) {
                ts = aiRuleCondition.getTime();
            } else if ((lastImg != null) && (lastImg.getAlarmTime() < latestTime)) {
                ts = aiRuleCondition.getTime();
            }
        }

        // 4. Perform expired data cleanup
        alarmImgList.deleteBeforeTime(ts);

        // 5. if hit, add to lastCountCache.
        if (isAlarmRuleMatched) {
            alarmImgList.add(new AlarmImg(aiRuleCondition.getTime()));
        }
        localCacheHolder.getLastCountCache().put(alarmImgKey, alarmImgList);

        //Check current capacity, clean up and monitor if it exceeds the limit
        if (alarmImgList.getSize() > alarmParaService.getParamIntValue(AlarmParaService.LAST_COUNT_CACHE_SINGLE_KEY_MAX_CAPACITY, DEFAULT_LAST_COUNT_CACHE_SINGLE_KEY_MAX_CAPACITY)) {
            alarmImgList.deleteBeforeTime(System.currentTimeMillis());
            String monitorInfo = String.format("Clean lastCountCache service = %s, alarm = %s, alarmRuleId = %s, currentImgSize = %s", serviceName, alarmName, alarmRuleCfg.getId(), alarmImgList.getSize());
            MonitorLogReporter.report(monitorLog, new AlarmSelfMonitor(AlarmSelfMonitorTypeEnum.clearLastCountCache.name(), monitorInfo));
        }
    }

    private AlarmRuleHittedValue convertAlertResultToHitValue(AiRuleConditionV2 aiRuleCondition) {

        AlarmRuleHittedValue alarmRuleHittedValue = new AlarmRuleHittedValue();
        List<AlarmRuleHittedValue.AiColumnUnit> columns = new ArrayList<>();
        alarmRuleHittedValue.setColumns(columns);
        aiRuleCondition.getColumns().forEach((k, v) -> {
            AlarmRuleHittedValue.AiColumnUnit unit = new AlarmRuleHittedValue.AiColumnUnit(k, v);
            String description = Optional.ofNullable(aiRuleCondition.getDescription()).map(m -> m.get(k)).orElse(null);
            unit.setDescription(description);
            columns.add(unit);
        });
        return alarmRuleHittedValue;
    }

    private void updateIsNoticed(List<AlarmMatchRecord> realActionRecordList) {
        for (AlarmMatchRecord record : realActionRecordList) {
            if(record.getIsNoticed()) {
                updateTriggered(record);
                storeToCache(record);
            }
        }
    }

    private void updateTriggered(AlarmMatchRecord currentAlarmMatchRecord) {
        currentAlarmMatchRecord.setStatus(AlarmRecordStatusEnum.new_triggered);
        currentAlarmMatchRecord.setAlarmEventType(AlarmEventTypeEnum.sys_triggered);
        storageService.addAlarmRecordV2(currentAlarmMatchRecord);
        storageService.addAlarmRecordEvent(currentAlarmMatchRecord);
    }

    private void storeToCache(AlarmMatchRecord currentAlarmMatchRecord) {
        long startTime = System.currentTimeMillis();
        NotifyImgKey notifyImgKey = new NotifyImgKey(currentAlarmMatchRecord.getAlarmId(), TagUtils.createKey(currentAlarmMatchRecord.getMetricsTags()));
        NotifyImgList<NotifyImg> notifyImgList = localCacheHolder.getAlarmNoticeCache().getIfPresent(notifyImgKey);
        if (null == notifyImgList) {
            notifyImgList = new NotifyImgList<>();
            localCacheHolder.getAlarmNoticeCache().put(notifyImgKey, notifyImgList);
        }
        notifyImgList.addImg(new NotifyImg(currentAlarmMatchRecord.getId(), System.currentTimeMillis(), currentAlarmMatchRecord.getStatus().getStatus(), currentAlarmMatchRecord.getAlarmLevel()));

        //add to redis cache
        boolean needGroupByTags = alarmParaService.getParamBooleanValue(ALARM_NOTIFY_GROUP_BY_TAGS, false);
        if (needGroupByTags && StringUtils.isNoneBlank(currentAlarmMatchRecord.getGroupTagKey())) {
            NotifyImgKey groupNotifyImgKey = new NotifyImgKey(currentAlarmMatchRecord.getAlarmId(), currentAlarmMatchRecord.getGroupTagKey());
            String redisKey = groupNotifyImgKey.generateKey();
            NotifyImgList<NotifyImg> groupNotifyImgList = Optional.ofNullable(redisService.getCastValue(redisKey, NotifyImgList.class, alarmParaService.isNewRedisSwitchForGroupByTag())).orElse(new NotifyImgList());
            groupNotifyImgList.addImg(new NotifyImg(currentAlarmMatchRecord.getId(), System.currentTimeMillis(), currentAlarmMatchRecord.getStatus().getStatus(), currentAlarmMatchRecord.getAlarmLevel()));
            redisService.set(redisKey, JsonUtils.toJsonString(groupNotifyImgList), 1, TimeUnit.DAYS, alarmParaService.isNewRedisSwitchForGroupByTag());
        }

        //need send incident, set cache
        String alarmId = currentAlarmMatchRecord.getAlarmId();
        List<String> levelsSendIncident = Optional.ofNullable(configCache.getAlarmCache().get(alarmId)).map(AlarmCfg::getLevelsSendIncident).orElse(new ArrayList<>());

        // 1. check alarm level
        boolean isLevelSend = levelsSendIncident.contains(currentAlarmMatchRecord.getAlarmLevel().name())
                || (incidentInterval > 0 && currentAlarmMatchRecord.getAlarmLevel().getValue() <= levelThreshold);
        if(!isLevelSend){
            return;
        }
        // 2. check env
        boolean isEnvSend = alarmNoticeHelperService.isSendAlarmUnderThisEnv(currentAlarmMatchRecord.getTenantId());
        if(!isEnvSend){
            return;
        }
        // 3. check block list
        boolean isBlock = alarmParaService.needToBlockIncident(currentAlarmMatchRecord.getTenantName(), currentAlarmMatchRecord.getAlarmName());
        if(isBlock){
            return;
        }
        AlarmImgKey alarmImgKey = new AlarmImgKey(currentAlarmMatchRecord.getAlarmRuleId(), TagUtils.createKey(currentAlarmMatchRecord.fetchTags()));

        String incidentCache = localCacheHolder.getIncidentSendCache().getIfPresent(alarmImgKey);
        if (null == incidentCache) {
            localCacheHolder.getIncidentSendCache().put(alarmImgKey, currentAlarmMatchRecord.getAlarmRuleId());
            localCacheHolder.getSendIncidentQueue().offer(transIncidentItem(alarmImgKey, currentAlarmMatchRecord));
        }
    }

    public IncidentItem transIncidentItem(AlarmImgKey alarmKey, AlarmMatchRecord alarmRecord) {
        return IncidentItem.builder()
                .service(alarmRecord.getTenantName())
                .description(alarmRecord.getAlarmName() + "-" + alarmRecord.getId())
                .check(alarmKey.getAlarmRuleId() + "_" + alarmKey.getTagKey())
                .status(String.valueOf((alarmRecord.getAlarmLevel().getValue() - 1)))
                .time(DateUtils.changeUTC2Date(alarmRecord.getTime()))
                .tags(alarmRecord.getMetricsTags())
                .fields(alarmRecord.getMetricFields())
                .alertUrl(notificationHelper.buildAlarmDetailUrl(alarmEndpointParaService.getNewEndpoint(), alarmRecord))
                .notifyTitle(alarmRecord.getTitle())
                .notifyContent(alarmRecord.getNoticeContent())
                .build();
    }

    private String transferMessageToString(PayloadType payloadType, Object messageObj) {
        if (Objects.isNull(payloadType)) {
            if (messageObj == null) {
                throw new IllegalArgumentException("messageObj cannot be null when payloadType is null");
            }
            return (String) messageObj;
        }
        if (messageObj == null) {
            throw new IllegalArgumentException("messageObj cannot be null for payloadType " + payloadType);
        }
        return switch (payloadType) {
            case BYTES -> new String((byte[]) messageObj, StandardCharsets.UTF_8);
            case STRING -> (String) messageObj;
            case OBJECT -> JsonUtil.toJson(messageObj);
            default ->
                    throw new IllegalArgumentException("Only supports bytes, string, and object type async mq messages");
        };
    }

    private AiMetricsV2 parseMessage(String record) {

        if (StringUtils.isBlank(record)) {
            log.warn("AiMetricsHandlerV2 finds blank record");
            return null;
        }
        AiMetricsV2 aiMetrics = null;
        try {
            aiMetrics = JsonUtils.toOrderObject(record, AiMetricsV2.class);
        } catch (Exception e) {
            logError("AiMetricsHandlerV2 parse error! value = {}", record, e);
        }
        return aiMetrics;
    }

    /**
     * check method
     */
    private boolean checkAiMetricsValid(AiMetricsV2 aiMetrics) {
        List<AiRuleConditionV2> alarmDefinitonRuleConditionDOList =
                Optional.ofNullable(aiMetrics)
                        .filter(m -> StringUtils.isNotBlank(m.getMetricsId()))
                        .filter(m -> StringUtils.isNotBlank(m.getFieldName()))
                        .filter(m -> StringUtils.isNotBlank(m.getTenantId()))
                        .map(AiMetricsV2::getAiAlarmConditionCfgList)
                        .orElse(null);
        return !CollectionUtils.isEmpty(alarmDefinitonRuleConditionDOList);
    }

    private static void logError(String format, Object... obj){
        if(COMMON_ERROR_ALARM_LOG_INDEX.incrementAndGet()% LogSampling.getErrorSamplingCount() ==0){
            if(needMaskLog){
                log.error(format, LogConstant.MASK_LOG);
            }else {
                log.error(format,obj);
            }
        }
        COMMON_ERROR_ALARM_LOG_INDEX.compareAndSet(Long.MAX_VALUE,0);
    }

    @Override
    public TypeReference<String> type() {
        return TYPE_REFERENCE;
    }

    @Data
    @AllArgsConstructor
    public static class AiAlarmStatContext{
        private String consumeRecord;

        private AiMetricsV2 aiMetrics;


        private MetricsCfg metricsCfg;

        private String channelName;

        private AlarmAction.AlarmSilenceRes alarmSilenceRes;

        private List<AlarmCfg> alarmCfgList;

        private TaskEntity<String> taskEntity;
    }
}
