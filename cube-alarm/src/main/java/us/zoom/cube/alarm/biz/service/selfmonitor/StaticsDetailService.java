package us.zoom.cube.alarm.biz.service.selfmonitor;

import jakarta.annotation.PostConstruct;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.cube.alarm.biz.service.AlarmHandler;
import us.zoom.cube.alarm.core.config.ConfigCache;
import us.zoom.cube.alarm.core.model.trace.AlarmLogSwitchPara;
import us.zoom.cube.alarm.core.service.syspara.SysParaService;
import us.zoom.cube.alarm.infra.LogSampling;
import us.zoom.cube.lib.common.AlarmMonitorTypeEnum;
import us.zoom.cube.lib.common.CubeConstants;
import us.zoom.cube.lib.common.StaticsDetailModel;
import us.zoom.cube.lib.integrations.Metrics;
import us.zoom.infra.influx.model.alarm.AlarmMatchRecord;
import us.zoom.infra.model.aggregation.MetricsTenant;
import us.zoom.infra.thread.CommonScheduler;
import us.zoom.infra.utils.DateUtils;
import us.zoom.infra.utils.IpUtils;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.mq.common.entity.Metadata;
import us.zoom.mq.common.entity.TaskContext;
import us.zoom.mq.common.entity.TaskEntity;

import java.io.Serializable;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

import static us.zoom.cube.lib.common.CubeConstants.MONITOR_KEY_SPLIT;

/**
 * <AUTHOR>
 */
@Component
public class StaticsDetailService implements Serializable {

    private Logger monitorLog = LoggerFactory.getLogger("Monitor");

    private transient static final Logger LOG= LoggerFactory.getLogger(StaticsDetailService.class.getName());

    private static final AtomicLong LOG_INDEX = new AtomicLong();

    private transient ThreadLocal<Map<String, StaticsDetailModel>> staticsDetailModelMetricsThreadLocalMap = ThreadLocal.withInitial(() -> new LinkedHashMap<>(100));
    private transient  ThreadLocal<Map<String, StaticsDetailModel>> staticsDetailModelAlarmThreadLocalMap = ThreadLocal.withInitial(() -> new LinkedHashMap<>(10));

    private transient  ThreadLocal<StaticsDetailModel> selfStaticsDetailModelLocal = ThreadLocal.withInitial(() -> null);
    private transient ThreadLocal<Long> periodLocal = ThreadLocal.withInitial(() -> 0L);
    private transient static final Integer schedulerPeriodInSeconds = 5;
    private transient static final Long schedulerPeriodInMs = schedulerPeriodInSeconds * 1000L;

    private transient static final Integer staticsPeriodInSeconds = 60;
    private transient static final Long staticsPeriodInMs = staticsPeriodInSeconds * 1000L;

    private transient Map<String, ConcurrentLinkedQueue<StaticsDetailModel>> aggStaticsDetailModelMap = new ConcurrentHashMap<>(200);

    @Autowired
    private ConfigCache configCache;

    @Autowired
    private SysParaService sysParaService;

    @PostConstruct
    public void init(){
        CommonScheduler.getInstance().getScheduler().scheduleAtFixedRate(new Runnable() {
            @Override
            public void run() {
                aggStaticsMetrics();
            }
        },0,schedulerPeriodInSeconds, TimeUnit.SECONDS);
    }

    private void aggStaticsMetrics(){
        try{
            long start = System.currentTimeMillis();
            AtomicInteger aggModelTotalCount = new AtomicInteger(0);
            AtomicInteger aggModelAggCount = new AtomicInteger(0);

            aggStaticsDetailModelMap.forEach((k,queue) -> {
                List<StaticsDetailModel> modelList = new ArrayList<>();
                queue.forEach(model -> {
                    aggModelTotalCount.addAndGet(1);
                    long timeDiff = start - model.getPeriod();
                    if(timeDiff >= schedulerPeriodInMs && timeDiff <= staticsPeriodInMs){
                        modelList.add(model);
                        queue.remove(model);
                        aggModelAggCount.getAndAdd(1);
                    }else if(timeDiff > staticsPeriodInMs){
                        // just remove it, there's no value to calculate
                        queue.remove(model);
                    }
                });
                StaticsDetailModel aggModel = StaticsDetailModel.agg(modelList);

                if(aggModel != null){
                    aggModel.avg();
                    monitorLog.info(JsonUtils.toJsonString(aggModel));
                }

                if(queue.isEmpty()){
                    aggStaticsDetailModelMap.remove(k);
                }
            });

            String env = configCache.getEnv();
            if(StringUtils.isBlank(env)){
                env = CubeConstants.UNDEFINED;
            }
            String ip = configCache.getIp();
            String unitTag = configCache.getUnitTagMap().get(ip);
            if(StringUtils.isBlank(unitTag)){
                unitTag = CubeConstants.UNDEFINED;
            }

            String component = "alarm";
            String tid = Long.toString(Thread.currentThread().getId());

            StaticsDetailModel totalCountStaticsDetailModel = new StaticsDetailModel(System.currentTimeMillis(), env, component, AlarmMonitorTypeEnum.aggStatMetricsModelTotalCount.name(), ip, tid);
            totalCountStaticsDetailModel.setUnitTag(unitTag);
            totalCountStaticsDetailModel.add(start, aggModelTotalCount.get());
            totalCountStaticsDetailModel.avg();

            StaticsDetailModel aggCountStaticsDetailModel = new StaticsDetailModel(System.currentTimeMillis(), env, component, AlarmMonitorTypeEnum.aggStatMetricsModelAggCount.name(), ip, tid);
            aggCountStaticsDetailModel.setUnitTag(unitTag);
            aggCountStaticsDetailModel.add(start, aggModelAggCount.get());
            aggCountStaticsDetailModel.avg();

            monitorLog.info(JsonUtils.toJsonString(totalCountStaticsDetailModel));
            monitorLog.info(JsonUtils.toJsonString(aggCountStaticsDetailModel));
            // LOG.info("aggStaticsDetailModel cost = {} ms", System.currentTimeMillis() - start);

        }catch (Exception e){
            LOG.error("printAggMetrics error",e);
        }

    }

    public void statics(AlarmMonitorTypeEnum monitorType, TaskEntity<byte[]> taskEntity, AlarmMatchRecord alarmMatchRecord, Metrics metrics, Integer count, long inputTime) {
        try {
            AlarmLogSwitchPara.MonitorLogSwitch monitorLogSwitch = sysParaService.getAlarmLogSwitchPara().getMonitorLogSwitch();
            if (monitorLogSwitch == null) {
                return;
            }
            if (monitorLogSwitch.isReset()) {
                staticsDetailModelMetricsThreadLocalMap.get().clear();
                staticsDetailModelAlarmThreadLocalMap.get().clear();
                return;
            }
            if (!monitorLogSwitch.isEnable()) {
                return;
            }
            long startTime = System.currentTimeMillis();
            if (configCache == null || !configCache.isValidForMonitor()) {
                return;
            }
            String topicName = Optional.ofNullable(taskEntity).map(TaskEntity::getTaskContext).map(TaskContext::getTopicName).orElse(null);
            String partition = Optional.ofNullable(taskEntity).map(TaskEntity::getMetadata).map(Metadata::getPartition).map(String::valueOf).orElse(null);
            Long offset = Optional.ofNullable(taskEntity).map(TaskEntity::getMetadata).map(Metadata::getOffset).orElse(null);
            if (metrics == null || StringUtils.isBlank(metrics.getMetricsId())) {
                return;
            }
            MetricsTenant metricsTenant = configCache.getMetricsTenantByMetricsId(metrics.getMetricsId());
            if (StringUtils.isBlank(metricsTenant.getMetricsName()) || StringUtils.isBlank(metricsTenant.getTenantName())) {
                return;
            }
            AlarmHandler.AlarmNewStatContext alarmNewStatContext = AlarmHandler.alarmNewStatContextThreadLocal.get();
            if (alarmNewStatContext == null) {
                return;
            }

            String channelName = alarmNewStatContext.getChannelName();

            String env = configCache.getEnv();
            if (StringUtils.isBlank(env)) {
                env = CubeConstants.UNDEFINED;
            }
            String ip = configCache.getIp();
            String unitTag = configCache.getUnitTagMap().get(ip);
            if (StringUtils.isBlank(unitTag)) {
                unitTag = CubeConstants.UNDEFINED;
            }

            String component = "alarm";
            String tid = Long.toString(Thread.currentThread().getId());

            long currentPeriod = DateUtils.getPeriodEndTime(startTime, staticsPeriodInSeconds);
            if (currentPeriod - periodLocal.get() >= staticsPeriodInMs) {
                StaticsDetailModel metricsCountStaticsDetailModel = new StaticsDetailModel(startTime, env, component, AlarmMonitorTypeEnum.statMetricsMapCount.name(), ip, tid);
                metricsCountStaticsDetailModel.setTopic(topicName);
                metricsCountStaticsDetailModel.setUnitTag(unitTag);
                metricsCountStaticsDetailModel.setCount((long) staticsDetailModelMetricsThreadLocalMap.get().size());

                StaticsDetailModel alarmCountStaticsDetailModel = new StaticsDetailModel(startTime, env, component, AlarmMonitorTypeEnum.statAlarmMapCount.name(), ip, tid);
                alarmCountStaticsDetailModel.setTopic(topicName);
                alarmCountStaticsDetailModel.setUnitTag(unitTag);
                alarmCountStaticsDetailModel.setCount((long) staticsDetailModelAlarmThreadLocalMap.get().size());
                periodLocal.set(currentPeriod);
                monitorLog.info(JsonUtils.toJsonString(metricsCountStaticsDetailModel));
                monitorLog.info(JsonUtils.toJsonString(alarmCountStaticsDetailModel));
            }

            String monitorKey = monitorType.name() + MONITOR_KEY_SPLIT + env + MONITOR_KEY_SPLIT + unitTag + MONITOR_KEY_SPLIT + ip;
            if (monitorLogSwitch.isEnableService()) {
                monitorKey = monitorKey + MONITOR_KEY_SPLIT + metricsTenant.getTenantName();
            }
            if (monitorLogSwitch.isEnableMetrics()) {
                monitorKey = monitorKey + MONITOR_KEY_SPLIT + metricsTenant.getMetricsName();
            }
            if (monitorLogSwitch.isEnablePartition()) {
                monitorKey = monitorKey + MONITOR_KEY_SPLIT + partition;
            }

            boolean alarmRelated = false;
            boolean alarmRuleRelated = false;
            boolean channelRelated = false;
            boolean silenceRelated = false;
            switch (monitorType) {
                case actionEmailStat:
                case actionIMStat:
                case actionMQStat:
                case actionPageDutyStat:
                case actionIncidentStat:
                case actionCubeDutyStat:
                    alarmRelated = true;
                    alarmRuleRelated = true;
                    channelRelated = true;
                    break;
                case matchLastCountStat:
                case matchLastCountLargeOneStat:
                case matchLastCountLargeOneCacheStat:
                case matchLastCountLargeOneHitCacheStat:
                case matchLastCountLargeOneMissCacheStat:
                case matchLastCountLargeOneFromDBStat:
                case queryNoticeRecordsStat:
                case queryNoticeRecordsCacheStat:
                case queryNoticeRecordsMissCacheStat:
                case queryNoticeRecordsHitCacheStat:
                case queryNoticeRecordsFromDBStat:
                case getNoticePermission:
                case notGetNoticePermission:
                    alarmRelated = true;
                    alarmRuleRelated = true;
                    break;
                case silencedStat:
                    alarmRelated = true;
                    alarmRuleRelated = true;
                    silenceRelated = true;
                    break;
                case setIncidentCache:
                case setIncidentCacheBlock:
                case setIncidentCacheEnvNotMatch:
                    alarmRelated = true;
                    alarmRuleRelated = true;
                    break;
                case actionStat:
                    alarmRelated = true;
                    break;
                default:
                    break;
            }

            if (alarmRelated && alarmMatchRecord != null && StringUtils.isNotBlank(alarmMatchRecord.getAlarmName()) && StringUtils.isNotBlank(alarmMatchRecord.getAlarmRuleId()) && alarmMatchRecord.getAlarmLevel() != null) {
                if (monitorLogSwitch.isEnableAlarm()) {
                    monitorKey = monitorKey + MONITOR_KEY_SPLIT + alarmMatchRecord.getAlarmName();
                }

                if (monitorLogSwitch.isEnableAlarmLevel()) {
                    monitorKey = monitorKey + MONITOR_KEY_SPLIT + alarmMatchRecord.getAlarmLevel().getPeril();
                }

                if (alarmRuleRelated) {
                    if (monitorLogSwitch.isEnableAlarmRule()) {
                        monitorKey = monitorKey + MONITOR_KEY_SPLIT + alarmMatchRecord.getAlarmRuleId();
                    }
                }

                if (channelRelated) {
                    if (monitorLogSwitch.isEnableChannel() && StringUtils.isNotBlank(channelName)) {
                        monitorKey = monitorKey + MONITOR_KEY_SPLIT + channelName;
                    }
                }
            }

            if (silenceRelated) {
                if (monitorLogSwitch.isEnableAlarmSilence() && alarmNewStatContext.hasValidAlarmSilence()) {
                    monitorKey = monitorKey + MONITOR_KEY_SPLIT + alarmNewStatContext.getAlarmSilenceRes().getAlarmSilenceId();
                }
            }
            Map<String, StaticsDetailModel> staticsDetailModelMap;
            if (alarmRelated) {
                staticsDetailModelMap = staticsDetailModelAlarmThreadLocalMap.get();
            } else {
                staticsDetailModelMap = staticsDetailModelMetricsThreadLocalMap.get();
            }

            if (staticsDetailModelMap.containsKey(monitorKey)) {
                StaticsDetailModel staticsDetailModel = staticsDetailModelMap.get(monitorKey);
                Long now = System.currentTimeMillis();
                //create new statics
                if ((now > staticsDetailModel.getPeriod())) {
                    //staticsDetailModel.avg();
                    ConcurrentLinkedQueue<StaticsDetailModel> queue = aggStaticsDetailModelMap.computeIfAbsent(monitorKey, v -> new ConcurrentLinkedQueue());
                    queue.add(staticsDetailModel);
                    // must remove here, keep the insert order by time
                    staticsDetailModelMap.remove(monitorKey);
                    //monitorLog.info(JsonUtils.toJsonString(staticsDetailModel));
                    //staticsQueue.offer(staticsDetailModel);
                    StaticsDetailModel newStaticsDetailModel = new StaticsDetailModel(DateUtils.getPeriodEndTime(now, staticsPeriodInSeconds), env, component, monitorType.name(), ip, null);
                    newStaticsDetailModel.setTopic(topicName);
                    newStaticsDetailModel.setUnitTag(unitTag);
                    if (monitorLogSwitch.isEnableService()) {
                        newStaticsDetailModel.setServiceName(metricsTenant.getTenantName());
                    }
                    if (monitorLogSwitch.isEnableMetrics()) {
                        newStaticsDetailModel.setMetricsName(metricsTenant.getMetricsName());
                        newStaticsDetailModel.setMetricsType(alarmNewStatContext.getMetricsCfg().getType().name());
                    }
                    if (monitorLogSwitch.isEnablePartition()) {
                        newStaticsDetailModel.setPartitionId(partition);
                    }
                    if (alarmRelated) {
                        if (monitorLogSwitch.isEnableAlarm()) {
                            newStaticsDetailModel.setAlarmName(alarmMatchRecord.getAlarmName());
                        }
                        if (monitorLogSwitch.isEnableAlarmLevel()) {
                            newStaticsDetailModel.setAlarmLevel(alarmMatchRecord.getAlarmLevel().getPeril());
                        }
                        if (monitorLogSwitch.isEnableAlarmRule()) {
                            newStaticsDetailModel.setAlarmRuleId(alarmMatchRecord.getAlarmRuleId());
                        }

                        if (monitorLogSwitch.isEnableChannel()) {
                            if (StringUtils.isNotBlank(channelName)) {
                                newStaticsDetailModel.setChannelName(channelName);
                            }
                        }

                    }
                    if (silenceRelated) {
                        if (monitorLogSwitch.isEnableAlarmSilence() && alarmNewStatContext.hasValidAlarmSilence()) {
                            newStaticsDetailModel.setAlarmSilenceId(alarmNewStatContext.getAlarmSilenceRes().getAlarmSilenceId());
                            newStaticsDetailModel.setAlarmSilenceName(alarmNewStatContext.getAlarmSilenceRes().getAlarmSilenceName());
                        }
                    }
                    staticsDetailModelMap.put(monitorKey, newStaticsDetailModel);
                }

            } else {
                StaticsDetailModel newStaticsDetailModel = new StaticsDetailModel(DateUtils.getPeriodEndTime(System.currentTimeMillis(), staticsPeriodInSeconds), env, component, monitorType.name(), ip, null);
                newStaticsDetailModel.setTopic(topicName);
                newStaticsDetailModel.setUnitTag(unitTag);
                if (monitorLogSwitch.isEnableService()) {
                    newStaticsDetailModel.setServiceName(metricsTenant.getTenantName());
                }
                if (monitorLogSwitch.isEnableMetrics()) {
                    newStaticsDetailModel.setMetricsName(metricsTenant.getMetricsName());
                    newStaticsDetailModel.setMetricsType(alarmNewStatContext.getMetricsCfg().getType().name());
                }
                if (monitorLogSwitch.isEnablePartition()) {
                    newStaticsDetailModel.setPartitionId(partition);
                }
                if (alarmRelated) {
                    if (monitorLogSwitch.isEnableAlarm()) {
                        newStaticsDetailModel.setAlarmName(alarmMatchRecord.getAlarmName());
                    }
                    if (monitorLogSwitch.isEnableAlarmLevel()) {
                        newStaticsDetailModel.setAlarmLevel(alarmMatchRecord.getAlarmLevel().getPeril());
                    }
                    if (monitorLogSwitch.isEnableAlarmRule()) {
                        newStaticsDetailModel.setAlarmRuleId(alarmMatchRecord.getAlarmRuleId());
                    }
                    if (monitorLogSwitch.isEnableChannel()) {
                        if (StringUtils.isNotBlank(channelName)) {
                            newStaticsDetailModel.setChannelName(channelName);
                        }
                    }
                }
                if (silenceRelated) {
                    if (monitorLogSwitch.isEnableAlarmSilence() && alarmNewStatContext.hasValidAlarmSilence()) {
                        newStaticsDetailModel.setAlarmSilenceId(alarmNewStatContext.getAlarmSilenceRes().getAlarmSilenceId());
                        newStaticsDetailModel.setAlarmSilenceName(alarmNewStatContext.getAlarmSilenceRes().getAlarmSilenceName());
                    }
                }
                staticsDetailModelMap.put(monitorKey, newStaticsDetailModel);
            }

            staticsDetailModelMap.get(monitorKey).add(inputTime, count);
            staticsDetailModelMap.get(monitorKey).setPartitionOffset(offset);

            Map<String, StaticsDetailModel> staticsDetailAlarmModelMap = staticsDetailModelAlarmThreadLocalMap.get();
            Iterator<Map.Entry<String, StaticsDetailModel>> alarmIterator = staticsDetailAlarmModelMap.entrySet().iterator();
            int alarmRemoveSize = 0;
            while(alarmIterator.hasNext()){
                Map.Entry<String, StaticsDetailModel> kv = alarmIterator.next();
                if(System.currentTimeMillis() > kv.getValue().getPeriod()){
                    //staticsQueue.offer(value);
                    //kv.getValue().avg();
                    ConcurrentLinkedQueue<StaticsDetailModel> queue = aggStaticsDetailModelMap.computeIfAbsent(kv.getKey(), v -> new ConcurrentLinkedQueue());
                    queue.add(kv.getValue());
                    //monitorLog.info(JsonUtils.toJsonString(value));
                    alarmIterator.remove();
                    alarmRemoveSize ++;
                } else {
                    break;
                }
            }
            if(alarmRemoveSize > 0){
                StaticsDetailModel alarmRemoveCountStaticsDetailModel = new StaticsDetailModel(System.currentTimeMillis(), env, component, AlarmMonitorTypeEnum.statAlarmMapRemoveCount.name(), ip, tid);
                alarmRemoveCountStaticsDetailModel.setTopic(topicName);
                alarmRemoveCountStaticsDetailModel.setUnitTag(unitTag);
                alarmRemoveCountStaticsDetailModel.setCount((long) alarmRemoveSize);
                monitorLog.info(JsonUtils.toJsonString(alarmRemoveCountStaticsDetailModel));
            }

            Map<String, StaticsDetailModel> staticsDetailMetricsModelMap = staticsDetailModelMetricsThreadLocalMap.get();
            Iterator<Map.Entry<String, StaticsDetailModel>> metricsIterator = staticsDetailMetricsModelMap.entrySet().iterator();
            int metricsRemoveSize = 0;
            while(metricsIterator.hasNext()){
                Map.Entry<String, StaticsDetailModel> kv = metricsIterator.next();
                if(System.currentTimeMillis() > kv.getValue().getPeriod()){
                    //staticsQueue.offer(value);
                    //kv.getValue().avg();
                    ConcurrentLinkedQueue<StaticsDetailModel> queue = aggStaticsDetailModelMap.computeIfAbsent(kv.getKey(), v -> new ConcurrentLinkedQueue());
                    queue.add(kv.getValue());
                    //monitorLog.info(JsonUtils.toJsonString(value));
                    metricsIterator.remove();
                    metricsRemoveSize ++;
                }else {
                    break;
                }
            }
            if(metricsRemoveSize > 0){
                StaticsDetailModel metricsRemoveCountStaticsDetailModel = new StaticsDetailModel(System.currentTimeMillis(), env, component, AlarmMonitorTypeEnum.statMetricsMapRemoveCount.name(), ip, tid);
                metricsRemoveCountStaticsDetailModel.setTopic(topicName);
                metricsRemoveCountStaticsDetailModel.setUnitTag(unitTag);
                metricsRemoveCountStaticsDetailModel.setCount((long) metricsRemoveSize);
                monitorLog.info(JsonUtils.toJsonString(metricsRemoveCountStaticsDetailModel));
            }


            StaticsDetailModel selfStaticsDetailModel = selfStaticsDetailModelLocal.get();
            long now = System.currentTimeMillis();
            if (selfStaticsDetailModel == null) {
                selfStaticsDetailModel = new StaticsDetailModel(DateUtils.getPeriodEndTime(now, staticsPeriodInSeconds), env, component, AlarmMonitorTypeEnum.statSelf.name(), ip, tid);
                selfStaticsDetailModel.setUnitTag(unitTag);
                selfStaticsDetailModel.setTopic(topicName);
                selfStaticsDetailModelLocal.set(selfStaticsDetailModel);
            }

            if(now > selfStaticsDetailModel.getPeriod()){
                selfStaticsDetailModel.avg();
                monitorLog.info(JsonUtils.toJsonString(selfStaticsDetailModel));
                StaticsDetailModel newSelfStaticsDetailModel = new StaticsDetailModel(DateUtils.getPeriodEndTime(now, staticsPeriodInSeconds), env, component, AlarmMonitorTypeEnum.statSelf.name(), ip, tid);
                newSelfStaticsDetailModel.setUnitTag(unitTag);
                newSelfStaticsDetailModel.setTopic(topicName);
                selfStaticsDetailModelLocal.set(newSelfStaticsDetailModel);
            }

            selfStaticsDetailModelLocal.get().add(startTime, 1);

        } catch (Exception e) {
            logError("statics error", e);
        }
    }

    private void logError(String msg, Exception e) {
        if (LOG_INDEX.incrementAndGet() % LogSampling.getErrorSamplingCount() == 0) {
            LOG.info(msg, e);
        }
        LOG_INDEX.compareAndSet(Long.MAX_VALUE, 0);
    }

    public static void main(String[] args){
        Map<String, ConcurrentLinkedQueue<StaticsDetailModel>> aggStaticsDetailModelMap = new ConcurrentHashMap<>(200);
        ConcurrentLinkedQueue<StaticsDetailModel> queue0 = new ConcurrentLinkedQueue<>();
        queue0.add(new StaticsDetailModel());
        aggStaticsDetailModelMap.put("a",queue0);
        aggStaticsDetailModelMap.forEach((k,queue) -> {
            List<StaticsDetailModel> modelList = new ArrayList<>();
            queue.forEach(model -> {
                //queue.remove(model);
            });

            if(queue.isEmpty()){
                //aggStaticsDetailModelMap.remove(k);
            }
        });
        System.out.println(JsonUtils.toJsonString(aggStaticsDetailModelMap));
        System.out.println(IpUtils.getLocalIP());
    }
}
