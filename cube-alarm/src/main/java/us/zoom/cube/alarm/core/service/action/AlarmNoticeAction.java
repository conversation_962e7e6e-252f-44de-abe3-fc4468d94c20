package us.zoom.cube.alarm.core.service.action;

import com.google.common.collect.Maps;
import com.zoom.op.monitor.domain.alarm.AlarmExtensionRelation;
import com.zoom.op.monitor.domain.alarm.AlarmExtensionRelationType;
import com.zoom.op.monitor.domain.alarm.Channel;
import com.zoom.op.monitor.domain.alarm.ChannelParameter;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.alarm.biz.service.AlarmHandler;
import us.zoom.cube.alarm.biz.service.selfmonitor.StaticsDetailService;
import us.zoom.cube.alarm.constat.LogConstant;
import us.zoom.cube.alarm.core.config.ConfigCache;
import us.zoom.cube.alarm.core.config.EnvironmentCacheLoader;
import us.zoom.cube.alarm.core.config.RsaService;
import us.zoom.cube.alarm.core.model.alarm.config.AlarmCfg;
import us.zoom.cube.alarm.core.model.alarm.model.AlarmServiceParaService;
import us.zoom.cube.alarm.core.service.aa.RdsCheckService;
import us.zoom.cube.alarm.core.service.syspara.AlarmParaService;
import us.zoom.cube.alarm.infra.LogSampling;
import us.zoom.cube.alarm.util.ChannelUtils;
import us.zoom.cube.lib.common.AlarmMonitorTypeEnum;
import us.zoom.cube.lib.common.CubeConstants;
import us.zoom.cube.lib.utils.ExceptionStackUtils;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.notice.biz.service.AlarmIMSender;
import us.zoom.infra.dao.model.AlarmMentionGroupDO;
import us.zoom.infra.enums.AlarmEventTypeEnum;
import us.zoom.infra.enums.AlarmRecordStatusEnum;
import us.zoom.infra.enums.AlarmSendingStatusEnum;
import us.zoom.infra.influx.model.alarm.AlarmMatchRecord;
import us.zoom.infra.influx.model.alarm.AlarmMatchRecordRequest;
import us.zoom.infra.loader.PiiTableLoader;
import us.zoom.infra.model.alarm.AlarmLevel;
import us.zoom.infra.model.alarm.InstanceMessagingChannel;
import us.zoom.infra.notification.channel.ChannelEngine;
import us.zoom.infra.notification.channel.ChannelSendModel;
import us.zoom.infra.notification.channel.cubeduty.CubeDutyEngine;
import us.zoom.infra.notification.channel.email.EmailChannelEngine;
import us.zoom.infra.notification.channel.incident.IncidentEngine;
import us.zoom.infra.notification.channel.pagerduty.PagerDutyEngine;
import us.zoom.infra.notification.channel.webhook.WebhookChannelEngine;
import us.zoom.infra.notification.channel.zoomchat.IMChannelEngine;
import us.zoom.infra.utils.*;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static us.zoom.cube.alarm.core.model.alarm.model.AlarmServiceParaService.INCLUDE_AI_RESULT;
import static us.zoom.cube.alarm.core.service.syspara.AlarmParaService.NOTIFY_CONTAINS_PREFIX;
import static us.zoom.cube.lib.common.CubeConstants.*;
import us.zoom.cube.alarm.core.service.rca.RcaAnalysisEventSender;

/**
 * <AUTHOR> Junjian
 * @create 2020/4/24 9:21 AM
 * <p>
 * ：
 */

@Service
@Slf4j
public class AlarmNoticeAction extends AlarmAction {

    private static final AtomicLong LOG_INDEX = new AtomicLong();

    public static final String RETRY_SEND_IM = "retrySendIM";

    private static final String NOTIFY_TITLE_PREFIX = "[Cube Alarm] ";

    private static final String NO_DATA_TITLE_PREFIX = "🚨[Cube No Data Alarm] ";
    @Autowired
    private ConfigCache configCache;

    @Autowired
    private AlarmIMSender alarmIMSender;

    @Autowired
    private AlarmServiceParaService alarmServiceParaService;

    @Autowired
    EnvironmentCacheLoader environmentCacheLoader;

    @Autowired
    RdsCheckService rdsCheckService;

    @Autowired
    private RsaService rsaService;

    @Autowired
    private PiiTableLoader piiTableLoader;

    @Autowired
    private AlarmParaService alarmParaService;
    
    @Autowired
    private RcaAnalysisEventSender rcaAnalysisEventSender;

    @Autowired
    private StaticsDetailService staticsDetailService;

    private Map<String, ChannelEngine> channelEngines;

    private Map<String, Set<String>> secretParameterMap;

    private static final Set<String> SYS_VAR_SET = new HashSet<>(
            Arrays.asList("alarmTime", "alarmName", "alarmMetricName", "alarmLevel", "alarmDetailUrl", CUBE_ALARM_UNIT_TAG, CUBE_ALARM_SERVICE_NAME));

    @Getter
    public static Boolean needMaskLog;

    @Value("${need.mask.log:false}")
    public void setNeedMaskLog(Boolean maskLog) {
        needMaskLog = maskLog;
    }

    @Autowired
    public void setChannelEngines(List<ChannelEngine> channelEngines) {

        this.channelEngines =
                channelEngines.stream().collect(
                        Collectors.toMap(ChannelEngine::getName, e -> e));

        this.secretParameterMap =
                channelEngines.stream().collect(Collectors.toMap(
                        ChannelEngine::getName,
                        c -> c.getParameters()
                                .stream()
                                .filter(ChannelEngine.Parameter::isSecret)
                                .map(ChannelEngine.Parameter::getName)
                                .collect(Collectors.toSet())
                ));
    }

    @Override
    public void doAction(List<AlarmMatchRecord> alarmMatchRecord, Map<AlarmLevel, List<AlarmMatchRecord>> alarmMatchRecordByLevel, boolean isSend) {
        sendAlarmNoticeWithInhibitionV2(alarmMatchRecord, alarmMatchRecordByLevel, isSend);
    }

    /**
     * Alarm action with lifecycle logic
     */
    private void sendAlarmNoticeWithInhibitionV2(List<AlarmMatchRecord> alarmMatchRecordList, Map<AlarmLevel, List<AlarmMatchRecord>> alarmMatchRecordByLevel, boolean isSend) {

        AlarmMatchRecord alarmMatchRecordSample = alarmMatchRecordList.get(0);

        //Do notify action, processing by alarm level: fatal -> error -> warn
        for (AlarmLevel alarmLevel : ORDERED_ALARM_LEVEL_LIST) {
            List<AlarmMatchRecord> leveledAlarmMatchRecordList = alarmMatchRecordByLevel.get(alarmLevel);
            if (CollectionUtils.isEmpty(leveledAlarmMatchRecordList)) {
                trace(alarmMatchRecordSample, "noNoticeAndNoMatch");
                continue;
            }
            // Sending alarm notice
            for (AlarmMatchRecord currentAlarmMatchRecord : leveledAlarmMatchRecordList) {
                try {
                    //Send IM info and update record
                    long startTime = System.currentTimeMillis();
                    AlarmSilenceRes alarmSilenceRes = computeSilenced(currentAlarmMatchRecord);
                    if (alarmSilenceRes.getIsSilenced()) {
                        AlarmHandler.AlarmNewStatContext alarmNewStatContext = AlarmHandler.alarmNewStatContextThreadLocal.get();
                        if (alarmNewStatContext != null) {
                            alarmNewStatContext.setAlarmSilenceRes(alarmSilenceRes);
                            staticsDetailService.statics(AlarmMonitorTypeEnum.silencedStat, alarmNewStatContext.getTaskEntity(), currentAlarmMatchRecord, alarmNewStatContext.getMetrics(), 1, startTime);
                        }
                        currentAlarmMatchRecord.setAlarmSilenceId(alarmSilenceRes.getAlarmSilenceId());
                        currentAlarmMatchRecord.setAlarmSilenceName(alarmSilenceRes.getAlarmSilenceName());
                        currentAlarmMatchRecord.setStatus(AlarmRecordStatusEnum.new_silenced);
                        storageService.addAlarmRecordV2(currentAlarmMatchRecord);

                        currentAlarmMatchRecord.setAlarmEventType(AlarmEventTypeEnum.sys_silenced);
                        storageService.addAlarmRecordEvent(currentAlarmMatchRecord);
                    } else {
                        sendNotice(currentAlarmMatchRecord, isSend);
                    }
                    printAlarmActionLog(currentAlarmMatchRecord);
                } catch (Exception e) {
                    log.error("processInhibition error! AlarmMatchRecord: {} ", (needMaskLog ? LogConstant.MASK_LOG : JsonUtils.toJsonStringIgnoreExp(currentAlarmMatchRecord)), e);
                }
            }
            // Break out loop after sending alarm notice
            break;
        }
    }

    private Map<AlarmLevel, Long> getAlarmNoticeCountFromDB(AlarmMatchRecord exampleMatchRecord) {
        AlarmMatchRecordRequest request = new AlarmMatchRecordRequest();
        request.setAlarmId(exampleMatchRecord.getAlarmId());
        request.setIsLastCountMatched(true);
        request.setIsNoticed(true);
        long endTimestamp = System.currentTimeMillis();
        long inhibitInterval = exampleMatchRecord.getInhibitInterval();
        long startTimestamp = endTimestamp - 1000L * inhibitInterval;
        request.setEndTimestamp(System.currentTimeMillis());
        request.setStartTimestamp(startTimestamp);
        request.setSize(QUERY_MAX_SIZE);
        request.setMetricsTags(exampleMatchRecord.getMetricsTags());
        request.setTenantName(exampleMatchRecord.getTenantName());
        request.setAlarmName(exampleMatchRecord.getAlarmName());
        Map<AlarmLevel, Long> alarmLevelCount = new HashMap<>(AlarmLevel.values().length);
        try {
            for (AlarmLevel alarmLevel : AlarmLevel.values()) {
                if (alarmLevelCount.containsKey(alarmLevel)) {
                    continue;
                }
                request.fetchTags().put("alarmLevel", alarmLevel.getLevel());
                long influxSearchStart = System.currentTimeMillis();
                Integer count = storageService.getAlarmMatchCount(request);
                trace(exampleMatchRecord,"hitChData-getAlarmNoticeCount",String.valueOf(count));
                long influxSearchEnd = System.currentTimeMillis();
                printSearchTime(influxSearchEnd - influxSearchStart);
                alarmLevelCount.put(alarmLevel, null == count ? 0L : (long) count);
            }
        } catch (Exception e) {
            log.error("processInhibition searchAlarmMatch error! AlarmMatchRecordRequest: {} ", (needMaskLog ? LogConstant.MASK_LOG : JsonUtils.toJsonStringIgnoreExp(request)), e);
        }
        trace(exampleMatchRecord,"hitChData-returnAlarmNoticeCount",JsonUtils.toJsonStringIgnoreExp(alarmLevelCount));
        return alarmLevelCount;
    }

    private String sendNotice(AlarmMatchRecord currentAlarmMatchRecord, boolean isSend) {
        String alarmId = currentAlarmMatchRecord.getAlarmId();
        AlarmCfg alarm = configCache.getAlarmCache().get(alarmId);
        String noticeContent = currentAlarmMatchRecord.getNoticeContent();
        InstanceMessagingChannel instanceMessagingChannel = alarm.getInstanceMessagingChannel();
        if (instanceMessagingChannel != null) {
            if(!sysParaService.isImClose(currentAlarmMatchRecord.getTenantName(),instanceMessagingChannel.getName())){
                alarmIMSender.sendWithServicName(null, noticeContent, alarm.getTitle(), alarm.getAlarmName(), currentAlarmMatchRecord.getAlarmLevel(), instanceMessagingChannel.getUrl(),
                        instanceMessagingChannel.getAuthorization(),currentAlarmMatchRecord.getTenantName(),instanceMessagingChannel.getName());
            }
        }
        Optional.ofNullable(alarm.getNotifications()).ifPresent(
                notifications -> notifications.forEach(notification -> {
                    Channel channel = notification.getChannel();
                    boolean isMarkdown = notification.getIsMarkdown();

                    //routing rule filter
                    if (notContains(currentAlarmMatchRecord, notification.getWhichLevels(), notification.getRoutingRule(), channel.getName(), currentAlarmMatchRecord.getAlarmLevel())) {
                        return;
                    }

                    trace(currentAlarmMatchRecord,"sendImGetChannel",channel.getEngineName());

                    Optional<ChannelEngine> channelEngine = Optional.ofNullable(getChannelEngine(channel.getEngineName()));

                    channelEngine.ifPresent(engine -> {

                        if(engine instanceof EmailChannelEngine &&sysParaService.isEmailClose(currentAlarmMatchRecord.getTenantName(),channel.getName())){
                            return;
                        }else if(engine instanceof IMChannelEngine &&sysParaService.isImClose(currentAlarmMatchRecord.getTenantName(),channel.getName())){
                            return;
                        }

                        AlarmMonitorTypeEnum alarmMonitorTypeEnum = null;
                        Map<String,Object> paras = new HashMap<>();
                        if(engine instanceof EmailChannelEngine){
                            paras.put("type", AlarmMonitorTypeEnum.sendEmail.name());
                            alarmMonitorTypeEnum = AlarmMonitorTypeEnum.actionEmailStat;
                        }else  if(engine instanceof  IMChannelEngine){
                            paras.put("type", AlarmMonitorTypeEnum.sendIm.name());
                            alarmMonitorTypeEnum = AlarmMonitorTypeEnum.actionIMStat;
                        }else  if(engine instanceof  PagerDutyEngine){
                            paras.put("type", AlarmMonitorTypeEnum.sendPagerDuty.name());
                            alarmMonitorTypeEnum = AlarmMonitorTypeEnum.actionPageDutyStat;
                        }else  if(engine instanceof IncidentEngine){
                            paras.put("type", AlarmMonitorTypeEnum.sendIncident.name());
                            alarmMonitorTypeEnum = AlarmMonitorTypeEnum.actionIncidentStat;
                        } else  if(engine instanceof WebhookChannelEngine){
                            paras.put("type", AlarmMonitorTypeEnum.sendWebhook.name());
                            alarmMonitorTypeEnum = AlarmMonitorTypeEnum.sendWebhook;
                        } else if (engine instanceof CubeDutyEngine) {
                            paras.put("type", AlarmMonitorTypeEnum.sendCubeDuty.name());
                            alarmMonitorTypeEnum = AlarmMonitorTypeEnum.actionCubeDutyStat;
                        }
                        paras.put("tenantName", currentAlarmMatchRecord.getTenantName());
                        paras.put("channelName", channel.getName());
                        paras.put("status",true);
                        long begin = System.currentTimeMillis();
                        try {
                            if (ChannelUtils.getMQChannels().contains(engine.getName()) || ChannelUtils.getAsyncMQChannels().contains(engine.getName())) {
                                return;
                            }
                            //parse notice info, support variable
                            //1.mention
                            AlarmMentionGroupDO alarmMentionGroup = new AlarmMentionGroupDO();
                            alarmMentionGroup.setMentionList(JsonUtils.parseArray(notification.getMentions(), AlarmMentionGroupDO.AlarmMentionUnit.class));
                            String mentions = buildMentionInfo(alarmMentionGroup);
                            //2.userLink
                            handleExtensionRelations(currentAlarmMatchRecord, isMarkdown);
                            //3.notice content
                            String content = buildNoticeContent(currentAlarmMatchRecord, notification.getContent(),false, false, isMarkdown, true);
                            currentAlarmMatchRecord.setNoticeContent(content);
                            String maskContent = buildNoticeContent(currentAlarmMatchRecord, notification.getContent(),true, true, isMarkdown, true);
                            //4.notice title
                            String title = buildNoticeContent(currentAlarmMatchRecord, notification.getTitle(),false, false, false, true);
                            currentAlarmMatchRecord.setTitle(title);
                            String maskTitle = buildNoticeContent(currentAlarmMatchRecord, notification.getTitle(),true, true, false, true);
                            //5.Update isNoticed
                            currentAlarmMatchRecord.setIsNoticed(true);
                            currentAlarmMatchRecord.setPdGroupTags(StringUtils.isBlank(notification.getPdGroupTags()) ? "" : notification.getPdGroupTags());
                            Map<String, String> parameters = mapParameters(engine, channel);

                            Map retrySendIM = sysParaService.getSwitchFilterDataAndTimeLimitAndRetrySendIM(RETRY_SEND_IM);
                            trace(currentAlarmMatchRecord,"beforeSendIm");
                            Map<String,Object> extendInfo = new HashMap<>(2);
                            if(Boolean.TRUE.equals(sysParaService.getAlarmSwitchBooleanValue(TURN_ON_PAGER_DUTY_DEDUP))){
                                extendInfo.put(TURN_ON_PAGER_DUTY_DEDUP,Boolean.TRUE);
                            }
                            long startTime = System.currentTimeMillis();
                            boolean includeAiResult = alarmServiceParaService.getParamBooleanValue(INCLUDE_AI_RESULT, false);


                            String sendTitle = maskTitle;
                            if (alarmParaService.getParamBooleanValue(NOTIFY_CONTAINS_PREFIX, false)) {
                                sendTitle = currentAlarmMatchRecord.isNoData()
                                        ? NO_DATA_TITLE_PREFIX + maskTitle
                                        : NOTIFY_TITLE_PREFIX + maskTitle;
                            }

                            ChannelSendModel sendModel = ChannelSendModel.builder()
                                    .parameters(parameters)
                                    .title(sendTitle)
                                    .message(maskContent)
                                    .record(currentAlarmMatchRecord)
                                    .retrySendIM(retrySendIM)
                                    .extendInfo(extendInfo)
                                    .newEndpoint(getEndpoint())
                                    .mentions(mentions)
                                    .isMarkdown(isMarkdown)
                                    .includeAiResult(includeAiResult)
                                    .channel(channel)
                                    .runBook(notification.getRunBook())
                                    .build();
                            AlarmHandler.AlarmNewStatContext alarmNewStatContext = AlarmHandler.alarmNewStatContextThreadLocal.get();
                            if (isSend) {
                                engine.send(sendModel);
                                // Send RCA analysis event after successful notification
                                long rcaAnalysisStart = System.currentTimeMillis();
                                rcaAnalysisEventSender.sendRcaAnalysisEvent(currentAlarmMatchRecord, channel, engine.getName(), sendModel);
                                if(alarmNewStatContext != null) {
                                    staticsDetailService.statics(AlarmMonitorTypeEnum.rcaAnalysis, alarmNewStatContext.getTaskEntity(), currentAlarmMatchRecord, alarmNewStatContext.getMetrics(), 1, rcaAnalysisStart);
                                }
                            }
                            addAlarmNotificationRecords(currentAlarmMatchRecord, channel, title, AlarmSendingStatusEnum.SUCCESS);

                            if(alarmNewStatContext != null && alarmMonitorTypeEnum != null){
                                alarmNewStatContext.setChannelName(channel.getName());
                                staticsDetailService.statics(alarmMonitorTypeEnum, alarmNewStatContext.getTaskEntity(),currentAlarmMatchRecord, alarmNewStatContext.getMetrics(), 1, startTime);
                            }
                            trace(currentAlarmMatchRecord,"endSendIm");

                        } catch (Exception e) {
                            log.error("Send notification error! Channel: {}, Alarm Rule: {}, Time: {}",
                                    channel.getName(), currentAlarmMatchRecord.getAlarmRuleId(),
                                    currentAlarmMatchRecord.getTime(), e);
                            paras.put("status",false);
                            paras.put("msg",e.getMessage());
                            paras.put("stack", ExceptionStackUtils.parseExceptionStackToString(e));
                            trace(currentAlarmMatchRecord,"expSendIm");
                            addAlarmNotificationRecords(currentAlarmMatchRecord, channel, currentAlarmMatchRecord.getTitle(), AlarmSendingStatusEnum.FAIL);
                        }
                        paras.put("cost",System.currentTimeMillis() -begin);
                        paras.put("ts",System.currentTimeMillis());
                        monitorLog.info(JsonUtils.toJsonStringIgnoreExp(paras));
                    });
                })
        );

        return noticeContent;
    }

    /**
     * process extension relations, replace variable in userLink
     */
    private void handleExtensionRelations(AlarmMatchRecord alarmMatchRecord, boolean isMarkdown) {
        if (CollectionUtils.isEmpty(alarmMatchRecord.getAlarmExtensionRelations())) {
            return;
        }
        for (AlarmExtensionRelation alarmExtensionRelation : alarmMatchRecord.getAlarmExtensionRelations()) {
            if (AlarmExtensionRelationType.userlink.equals(alarmExtensionRelation.getRelationType())
                    && StringUtils.isNotBlank(alarmExtensionRelation.getConfigValue())) {
                String newVal = buildNoticeContent(alarmMatchRecord, alarmExtensionRelation.getConfigValue(), true, true, isMarkdown, false);
                alarmExtensionRelation.setConfigValue(newVal);
            }
        }
    }

    /**
     * build mention info
     */
    public String buildMentionInfo(AlarmMentionGroupDO alarmMentionGroup) {
        StringBuilder mentionsBuilder = new StringBuilder();
        List<AlarmMentionGroupDO.AlarmMentionUnit> list = Optional.ofNullable(alarmMentionGroup).map(AlarmMentionGroupDO::getMentionList).orElse(new ArrayList<>());
        for (AlarmMentionGroupDO.AlarmMentionUnit unit : list) {
            String displayName = StringUtils.defaultIfBlank(unit.getDisplayName(), unit.getEmail());

            if (StringUtils.isNotBlank(unit.getGroupId())) {
                String groupMention = String.format("<group:%s|%s> ", unit.getGroupId(), displayName);
                log.info("buildMentionInfo adding group mention: {}", groupMention);
                mentionsBuilder.append(groupMention);
            } else if (StringUtils.isNotBlank(unit.getEmail())) {
                mentionsBuilder.append(String.format("<!%s|%s> ", unit.getEmail(), displayName));
            }
        }
        return mentionsBuilder.toString();
    }

    private void addAlarmNotificationRecords(AlarmMatchRecord alarmMatchRecord, Channel channel, String title, AlarmSendingStatusEnum alarmSendingStatus) {
        try{
            storageService.addAlarmNotificationRecords(alarmMatchRecord, channel, title, alarmSendingStatus);
        }catch (Exception e) {
            log.error("Error when insert alarm record to clickhouse",e);
        }
    }


    private Map<String, String> mapParameters(ChannelEngine engine, Channel channel) {

        Set<String> secretParameters = secretParameterMap.get(engine.getName());

        return channel.getParameters().
                stream()
                .collect(Collectors.toMap(
                        ChannelParameter::getName,
                        p -> getParameterValue(p, secretParameters), (a, b) -> a
                ));
    }


    private String getParameterValue(ChannelParameter p, Set<String> secretParameters) {

        if (secretParameters.contains(p.getName())) {

            try {
                return rsaService.decrypt(p.getValue());
            } catch (Exception e) {
                log.warn("Failed to decrypt secret parameter.", e);
                return p.getValue();
            }

        } else {
            return p.getValue();
        }
    }

    private ChannelEngine getChannelEngine(String engineName) {

        return channelEngines.get(engineName);
    }

    public String buildNoticeContent(AlarmMatchRecord alarmMatchRecord, String alarmContent, boolean needPiiMask, boolean needVarMask, boolean isMarkdown, boolean escapeVariable) {

        StringBuilder builder = new StringBuilder();
        try {

            return builder.append(buildContent(alarmMatchRecord, alarmContent, needPiiMask, needVarMask, isMarkdown, escapeVariable)).toString();

        } catch (Exception e) {
            log.error("build content error,content ={}", alarmContent, e);
        }

        builder.append("[Content]").append(alarmContent).append("; ");
        builder.append("[Tags]").append(alarmMatchRecord.getMetricsTags().toString()).append("; ");
        builder.append("[FieldValues]").append(JsonUtils.toJsonStringIgnoreExp(alarmMatchRecord.getMetricFields()));

        return builder.toString();
    }


    private void setExtraFailedOrTag(Map<String, Object> model, String contentTemplate, AlarmMatchRecord alarmMatchRecord) {
        try {
            if (contentTemplate.contains(CUBE_ALARM_IP)) {
                model.put(CUBE_ALARM_IP, IpUtils.getLocalIP());
            }
            if (contentTemplate.contains(CUBE_ALARM_ENV)) {
                model.put(CUBE_ALARM_ENV, rdsCheckService.getEnvInfor());
            }
            if (contentTemplate.contains(CubeConstants.CUBE_ALARM_UNIT_TAG)) {
                model.put(CubeConstants.CUBE_ALARM_UNIT_TAG, getUnitTagName());
            }
            if (contentTemplate.contains(CubeConstants.CUBE_ALARM_SERVICE_NAME)) {
                model.put(CubeConstants.CUBE_ALARM_SERVICE_NAME, alarmMatchRecord.getTenantName());
            }
            if (contentTemplate.contains(CubeConstants.SERVICE_NAME)) {
                model.put(CubeConstants.SERVICE_NAME, alarmMatchRecord.getTenantName());
            }
        } catch (Exception e) {
            log.error("get cube alarm ip and unit tag error : error = {}", e.getMessage());
        }
    }

    private String buildContent(AlarmMatchRecord alarmMatchRecord, String contentTemplate, boolean needPiiMask,boolean needVarMask, boolean isMarkdown, boolean escapeVariable) throws Exception {

        long time = alarmMatchRecord.getTime();
        Map<String, Object> varMap = getVarMapFromRecord(alarmMatchRecord);
        String alarmTimeRange="{\"type\":\"absolute\",\"value\":[\""+DateUtils.timeStamp2ISODate(time - (1000000))+"\",\""+DateUtils.timeStamp2ISODate(time + (250000))+"\"]}";
        varMap.put("_alarmTimeRange", URLEncoder.encode(alarmTimeRange, StandardCharsets.UTF_8));
        //mask var
        boolean varMask = needVarMask && alarmServiceParaService.isMaskVar(alarmMatchRecord.getTenantName());
        contentTemplate = maskFieldInContent(contentTemplate, alarmMatchRecord, varMask, needPiiMask, varMap);
        if (isMarkdown && escapeVariable) {
            escapeVariable(varMap);
        }
        return TempUtils.convert(contentTemplate, varMap);
    }

    private void escapeVariable(Map<String, Object> varMap) {
        for (Map.Entry<String, Object> entry : varMap.entrySet()) {
            if (entry.getValue() instanceof String) {
                String newValue = String.valueOf(entry.getValue()).replaceAll("_", "\\\\_");
                entry.setValue(newValue);
            }
        }
    }

    private void printAlarmActionLog(AlarmMatchRecord currentAlarmMatchRecord) {
        if (LOG_INDEX.incrementAndGet() % LogSampling.getInfoSamplingCount() == 0) {
            log.info("Matched! Send AlarmMatchRecord: {}", (needMaskLog ? LogConstant.MASK_LOG : JsonUtils.toJsonStringIgnoreExp(currentAlarmMatchRecord)));
        }
        LOG_INDEX.compareAndSet(Long.MAX_VALUE, 0);
    }

    private void printSearchTime(long costTime) {
        if (LOG_INDEX.incrementAndGet() % LogSampling.getInfoSamplingCount() == 0) {
            log.info("AlarmNoticeAction search InfluxDB cost time: " + costTime + " ms");
        }
        LOG_INDEX.compareAndSet(Long.MAX_VALUE, 0);
    }

    @Override
    public boolean matchingCheck(List<AlarmMatchRecord> alarmMatchRecord) {
        boolean result = false;
        if (!CollectionUtils.isEmpty(alarmMatchRecord) && alarmMatchRecord.get(0) != null) {
            AlarmCfg alarm = configCache.getAlarmCache().get(alarmMatchRecord.get(0).getAlarmId());
            result = alarm != null && !CollectionUtils.isEmpty(alarm.getNotifications()) && alarm.getNotifications().stream().anyMatch(e -> e.getChannel() != null && ChannelUtils.getAlarmChannels().contains(e.getChannel().getEngineName()));
        }
        return result;
    }

    private String maskFieldInContent(String contentTemplate, AlarmMatchRecord alarmMatchRecord, boolean varMask, boolean piiMask, Map<String, Object> varMap) throws Exception {
        if (!piiMask && !varMask) {
            return contentTemplate;
        }
        Set<String> thisMetricPiiFields = piiTableLoader.getPiiField(alarmMatchRecord.getTenantName(), alarmMatchRecord.getMetricsName());
        //maskModel just for re-use TempUtils.convert
        Map<String,Object> maskModel = Maps.newHashMap();
        for(String columnName : varMap.keySet()) {
            if(thisMetricPiiFields.contains(columnName)) {
                maskModel.put(columnName, CommonSplitConstants.MASKING);
            }
            if (varMask && !SYS_VAR_SET.contains(columnName)) {
                maskModel.put(columnName, CommonSplitConstants.MASKING);
            }
        }
        return TempUtils.convert(contentTemplate, maskModel, false, false);
    }
}
