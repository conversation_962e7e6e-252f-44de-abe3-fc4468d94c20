package us.zoom.cube.alarm.infra;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import us.zoom.infra.asyncmq.AsyncMQRotateHandler;
import us.zoom.mq.client.AsyncMQ;
import us.zoom.mq.client.DefaultAsyncMQ;
import us.zoom.mq.client.clients.consumer.Consumer;
import us.zoom.mq.client.clients.consumer.RetryableStraw;
import us.zoom.mq.client.clients.producer.Producer;
import us.zoom.mq.client.pojo.Subscriber;
import us.zoom.mq.common.enums.ProtocolStrategy;
import java.util.concurrent.ThreadLocalRandom;
import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import us.zoom.mq.common.client.job.*;
import us.zoom.mq.common.client.task.Task;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class AsyncMQInstance {

    private volatile static Consumer consumer;

    private volatile static AsyncMQ asyncMQ;

    @Getter
    private volatile static AsyncMQInstance instance;

    private static final List<Producer> producers = new ArrayList<>();

    private static final List<AsyncMQ> asyncMQS = new ArrayList<>();

    private static final int producerPoolSize = 4;

    public static final String ASYNC_MQ_TYPE = "cube_alarm_async";

    @Autowired
    private Environment environment;

    @Value("${alarm.async.mq.endpoint}")
    private String alarmAsyncMqEndpoint;

    @Value("${alarm.async.mq.username}")
    private String alarmAsyncMqUsername;

    //support hot reload in listener already
    @Value("${alarm.async.mq.password}")
    private String alarmAsyncMqPassword;

    private AsyncMQInstance() {
    }

    @PostConstruct
    public void initAsyncMQ() {
        init(alarmAsyncMqEndpoint, alarmAsyncMqUsername, alarmAsyncMqPassword);
    }

    private static void init(String endpoint, String username, String password) {
        if (instance == null) {
            synchronized (AsyncMQInstance.class) {
                if (instance == null) {
                    log.info("init AsyncMQInstance.");
                    if (StringUtils.isBlank(endpoint) || StringUtils.isBlank(username) || StringUtils.isBlank(password)) {
                        throw new RuntimeException("endpoint, username and password cannot be empty");
                    }
                    instance = new AsyncMQInstance();
                    asyncMQ = new DefaultAsyncMQ(endpoint, username, password);
                    asyncMQS.add(asyncMQ);
                    consumer = asyncMQ.consumer();

                    AsyncMQRotateHandler.addMQListener(asyncMQ, "alarm.async.mq.password", username);

                    for (int i = 0; i < producerPoolSize; i++) {
                        AsyncMQ asyncMQ = new DefaultAsyncMQ(endpoint, username, password);
                        asyncMQ.setClientId("async-client-" + i);
                        Producer producer = asyncMQ.producer();
                        producer.setProtocolStrategy(ProtocolStrategy.SIMPLE);
                        producers.add(producer);
                        asyncMQS.add(asyncMQ);

                        AsyncMQRotateHandler.addMQListener(asyncMQ, "alarm.async.mq.password", username);
                    }

                    consumer.start();
                    log.info("init AsyncMQInstance success.");
                }
            }
        }
    }

    public void registerConsumer(String topic, String groupId, int receiveCount, RetryableStraw<String> batchHandler) {
        try {
            Subscriber subscriber = consumer.registerSubscriber(topic, groupId);
            consumer.setStraw(subscriber, batchHandler);
            //Straw，consumeCount
            consumer.start(subscriber, receiveCount, 1);
            log.info("start consumer, topic:{}, group id:{}, receiveCount:{}", topic, groupId, receiveCount);
        } catch (Exception e) {
            throw new RuntimeException(String.format("register consumer fail, topic:%s, groupId:%s, theadCount:%d", topic, groupId, receiveCount), e);
        }
    }

    public void shutdown(String topic, String groupId) {
        try {
            consumer.shutdownCleanStartParam(consumer.registerSubscriber(topic, groupId));
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage(), e);
        }
    }
    public Producer getProducer() {
        return producers.get(ThreadLocalRandom.current().nextInt(producerPoolSize));
    }

    @SuppressWarnings("rawtypes")
    public static DelayJob<String> createDelayJob(Task task, Integer delay) {

        DelayJob<String> job = new DelayJob<>();
        job.setDelay(delay);
        job.setTimeUnit(TimeUnit.MINUTES);
        job.setTask(task);
        return job;
    }
}
