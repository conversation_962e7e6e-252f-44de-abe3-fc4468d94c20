package us.zoom.cube.alarm.core.service.rca;

import com.zoom.op.monitor.domain.alarm.Channel;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import us.zoom.cube.alarm.core.service.syspara.AlarmParaService;
import us.zoom.cube.alarm.infra.AsyncMQInstance;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.infra.influx.model.alarm.AlarmMatchRecord;
import us.zoom.infra.influx.model.alarm.RcaAnalysisEvent;
import us.zoom.infra.notification.channel.ChannelSendModel;
import us.zoom.mq.client.clients.producer.Producer;
import us.zoom.mq.common.Result;
import us.zoom.mq.common.client.task.Task;
import us.zoom.mq.common.response.JobResult;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * Service for sending RCA analysis events to AsyncMQ
 * This service handles the asynchronous sending of RCA analysis events after alarm notifications
 * 
 * <AUTHOR>
 * @date 2024/01/10
 */
@Service
@Slf4j
public class RcaAnalysisEventSender {

    private static final String ZOOM_CHAT = "ZOOM_CHAT";

    public static final String PARAM_NAME_ENDPOINT = "Endpoint";

    public static final String PARAM_NAME_VERIFICATION_TOKEN = "Verification Token";

    @Value("${rca.analysis.asyncmq.enabled:false}")
    private boolean rcaAnalysisAsyncMqEnabled;
    
    @Value("${rca.analysis.asyncmq.topic:cube-alarm-rca-analysis}")
    private String rcaAnalysisTopic;
    
    @Value("${rca.analysis.asyncmq.task.type:cube_rca_analysis}")
    private String rcaAnalysisTaskType;
    
    private ThreadPoolExecutor rcaAnalysisExecutor;
    
    @Autowired
    private AlarmParaService alarmParaService;
    
    @PostConstruct
    public void init() {
        if (rcaAnalysisAsyncMqEnabled) {
            // Initialize thread pool for async sending
            rcaAnalysisExecutor = new ThreadPoolExecutor(
                2,
                4,
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1000),
                r -> {
                    Thread t = new Thread(r);
                    t.setName("rca-analysis-sender-" + t.getId());
                    t.setDaemon(true);
                    return t;
                },
                // discard when queue is full
                new ThreadPoolExecutor.DiscardPolicy()
            );
            
            log.info("RCA Analysis event sender initialized. Topic: {}", rcaAnalysisTopic);
        }
    }
    
    @PreDestroy
    public void shutdown() {
        if (rcaAnalysisExecutor != null) {
            rcaAnalysisExecutor.shutdown();
            try {
                if (!rcaAnalysisExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    rcaAnalysisExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                rcaAnalysisExecutor.shutdownNow();
            }
        }
    }
    
    /**
     * Send RCA analysis event for alarm notification
     * 
     * @param record the alarm match record
     * @param channel the channel used for notification
     * @param engineName the engine name (e.g., "Zoom Chat")
     * @param sendModel the notification model
     */
    public void sendRcaAnalysisEvent(AlarmMatchRecord record, Channel channel, String engineName, ChannelSendModel sendModel) {
        if (!rcaAnalysisAsyncMqEnabled) {
            return;
        }
        
        // Check if we need to send RCA analysis event for this channel
        if (!ZOOM_CHAT.equals(engineName)) {
            return;
        }
        
        // Check if RCA analysis should be performed for this alarm
        boolean shouldAnalyze = alarmParaService.shouldAnalyzeRca(
            record.getTenantName(), 
            record.getAlarmName(),
            record.getAlarmLevel(), 
            record.getMetricsTags()
        );
        
        if (!shouldAnalyze) {
            log.debug("RCA analysis is not required for alarm: {}, service: {}", 
                record.getAlarmId(), record.getTenantName());
            return;
        }
        
        try {
            rcaAnalysisExecutor.submit(() -> {
                try {
                    // Create RCA analysis event
                    String endpoint = null;
                    String verifyToken = null;
                    
                    if (sendModel != null && sendModel.getParameters() != null) {
                        endpoint = sendModel.getParameters().get(PARAM_NAME_ENDPOINT);
                        verifyToken = sendModel.getParameters().get(PARAM_NAME_VERIFICATION_TOKEN);
                    }
                    
                    RcaAnalysisEvent event = RcaAnalysisEvent.builder()
                        .task("smartRCA-alarm")
                        .service(record.getTenantName())
                        .alarmId(record.getAlarmId())
                        .beginTs(record.getTime() - 1000L)
                        .endTs(record.getTime() + 1000L)
                        .groupKey(record.getNotifyGroupKey())
                        .channelId(channel != null ? channel.getId() : null)
                        .endpoint(endpoint)
                        .verifyToken(verifyToken)
                        .build();
                    
                    // Create task for AsyncMQ
                    Task<String> task = new Task<>();
                    task.setTopicName(rcaAnalysisTopic);
                    task.setTaskType(rcaAnalysisTaskType);
                    task.setPayload(JsonUtils.toJsonStringIgnoreExp(event));
                    //partition key
                    task.setKey(record.getNotifyGroupKey());
                    // Get producer from AsyncMQInstance
                    Producer producer = AsyncMQInstance.getInstance().getProducer();
                    
                    // Send to AsyncMQ with delay 5 minutes
                    Result<JobResult> result = producer.createDelayJob(AsyncMQInstance.createDelayJob(task, 5));
                    
                    if (result.isSuccess()) {
                        log.debug("RCA analysis event sent successfully for alarm: {}, service: {}", 
                            record.getAlarmId(), record.getTenantName());
                    } else {
                        log.warn("Failed to send RCA analysis event for alarm: {}, error:{}",
                            record.getAlarmId(), result.getException().getMessage());
                    }
                } catch (Exception e) {
                    log.error("Error sending RCA analysis event for alarm: {}", record.getAlarmId(), e);
                }
            });
        } catch (RejectedExecutionException e) {
            log.warn("RCA analysis event queue is full, dropping event for alarm: {}", record.getAlarmId());
        } catch (Exception e) {
            log.error("Unexpected error submitting RCA analysis event for alarm: {}", record.getAlarmId(), e);
        }
    }
} 