package us.zoom.cube.alarm.core.service.match;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.alarm.core.config.ConfigCache;
import us.zoom.cube.alarm.core.model.alarm.config.AlarmCfg;
import us.zoom.cube.alarm.core.model.alarm.match.SingleContentMatchRequest;
import us.zoom.cube.alarm.core.model.alarm.model.AlarmServiceParaService;
import us.zoom.cube.alarm.core.service.ad.AiMetricsHandlerV2;
import us.zoom.cube.alarm.infra.LogSampling;
import us.zoom.cube.lib.common.CubeConstants;
import us.zoom.cube.lib.integrations.Metrics;
import us.zoom.cube.lib.integrations.MetricsField;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.infra.enums.FieldTypeEnum;
import us.zoom.infra.influx.model.alarm.AiFilterVarV2;
import us.zoom.infra.influx.model.alarm.AiMetricsV2;
import us.zoom.infra.model.aggregation.MetricsTenant;
import us.zoom.infra.model.alarm.AlarmRuleSingleContent;
import us.zoom.infra.model.alarm.CompareType;
import us.zoom.infra.utils.Instance;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static us.zoom.cube.alarm.core.model.alarm.model.AlarmServiceParaService.PRE_VAR_PREFIX;

/**
 * <AUTHOR> Junjian
 * @create 2019/10/29 1:05
 */

@Component
@Slf4j
public class AlarmRuleSingleContentMatcher {

    private static final int CHAIN_RATIO_MIN_SIZE = 2;

    private static final AtomicLong LOG_INDEX = new AtomicLong();

    private static final String AI_OPERATOR = "AI";

    private static final int ANOMALY_SCORE_LOWER = 1;

    private static final int ANOMALY_SCORE_UPPER = 5;

    @Autowired
    private ConfigCache configCache;

    @Autowired
    private AlarmServiceParaService alarmServiceParaService;


    private static final Map<Integer, Double> ANOMALY_SCORE_MAPPING;

    static {
        ANOMALY_SCORE_MAPPING = new HashMap<>();
        ANOMALY_SCORE_MAPPING.put(5, 0.95);
        ANOMALY_SCORE_MAPPING.put(4, 0.85);
        ANOMALY_SCORE_MAPPING.put(3, 0.75);
        ANOMALY_SCORE_MAPPING.put(2, 0.65);
        ANOMALY_SCORE_MAPPING.put(1, 0.55);
    }

    /**
     * Are tag Rules match.
     * A typical tag rule is like: {"cmpValue":"dev_web001","expression":"host_name == 'dev_web001'","operator":"==","tag":"host_name","type":3}
     */
    public boolean areTagsMatch(SingleContentMatchRequest request) {
        List<Metrics> metricsList = request.getMetricsList();
        int listSize = metricsList.size();
        Metrics metrics = metricsList.get(listSize - 1);
        Map<String, String> itemMap = metrics.getTags();
        List<AlarmRuleSingleContent> filterRules = request.getTagRules();
        for (AlarmRuleSingleContent filterRule : filterRules) {
            String tag = filterRule.getTag();
            String tagValue = itemMap.get(tag);
            boolean isMatchTag = filterRule.isMatchTag(tagValue);
            if (!isMatchTag) {
                return false;
            }
        }
        return true;
    }

    /**
     * alarm_rule_content
     * ：{"expression":"count(url) >= 200","field":"url","type":0 }
     *
     * @param request requestDetail
     * @return isMatched
     */
    public boolean isCompareRuleMatch(SingleContentMatchRequest request) {
        boolean isAlarmRuleSingleContentMatch = false;

        AlarmRuleSingleContent compareRule = request.getCompareRule();
        CompareType compareType = compareRule.getType();
        try {
            if (AI_OPERATOR.equals(compareRule.getOperator())) {
                isAlarmRuleSingleContentMatch = isMatchAi(request);
            } else if (CompareType.THRESHOLD.equals(compareType)) {
                isAlarmRuleSingleContentMatch = isMatchThreshold(request);
            } else if (CompareType.CHAIN_RATIO.equals(compareType)) {
                isAlarmRuleSingleContentMatch = isMatchChainRatio(request);
            } else if (CompareType.FUNC_COMPARE.equals(compareType)) {
                isAlarmRuleSingleContentMatch = this.isMatchFuncCmp(request);
            } else if (CompareType.NODATA_COMPARE.equals(compareType)) {
                isAlarmRuleSingleContentMatch = isMatchNoData(request);
            }
        } catch (Exception e) {
            try {
                if (AI_OPERATOR.equals(compareRule.getOperator())) {
                    log.error("isCompareRuleMatch AI Exception:{}", JsonUtils.toJsonStringIgnoreExp(request), e);
                }
            } catch (Exception ignored) {
            }
        }

        printSingleContentMatch(request, isAlarmRuleSingleContentMatch);
        return isAlarmRuleSingleContentMatch;
    }

    /**
     * AI Condition Matcher
     * @param request request
     * @return match result.
     */
    private boolean isMatchAi(SingleContentMatchRequest request) {
        if (request.isIgnoreAiCondition()) {
            return true;
        }

        AiFilterVarV2 filterVar = Optional.of(request).map(SingleContentMatchRequest::getMetricsList).map(l -> l.get(0)).map(Metrics::getFilterVar).map(AiFilterVarV2::fromMap).orElse(null);
        if (Objects.isNull(filterVar) || !filterVar.isValid()) {
            log.warn("isMatchAi filterVar is invalid, request:{}, filterVar:{}", JsonUtils.toJsonStringIgnoreExp(request), JsonUtils.toJsonStringIgnoreExp(filterVar));
            return false;
        }

        String adId = Optional.of(request)
                .map(SingleContentMatchRequest::getCompareRule)
                .map(AlarmRuleSingleContent::getExpression)
                .orElse(null);
        if (StringUtils.isBlank(adId)) {
            log.warn("isMatchAi adId is empty, request:{}", JsonUtils.toJsonStringIgnoreExp(request));
            return false;
        }
        AiMetricsHandlerV2.AiAlarmStatContext context = AiMetricsHandlerV2.AI_ALARM_STAT_CONTEXT_V2.get();
        if (context == null || !Objects.equals(context.getAiMetrics().getId(), adId)) {
            log.warn("isMatchAi adId context not match, request:{}, context:{}", JsonUtils.toJsonStringIgnoreExp(request), JsonUtils.toJsonStringIgnoreExp(context));
            return false;
        }

        Integer threshold = Optional.of(request).map(SingleContentMatchRequest::getCompareRule).map(AlarmRuleSingleContent::getCmpValue).map(Integer::valueOf).orElse(null);
        if (Objects.isNull(threshold) || threshold < ANOMALY_SCORE_LOWER || threshold > ANOMALY_SCORE_UPPER) {
            log.warn("isMatchAi threshold is not valid, request:{}, threshold:{}", JsonUtils.toJsonStringIgnoreExp(request), threshold);
            return false;
        }

        AlarmRuleSingleContent.DetectModeEnum detectMode = Optional.of(request).map(SingleContentMatchRequest::getCompareRule)
                .map(AlarmRuleSingleContent::getExtension)
                .map(AlarmRuleSingleContent.AlarmRuleConditionExtension::getDetectMode)
                .orElse(null);
        if (Objects.isNull(detectMode)) {
            log.warn("isMatchAi detectMode is not valid, request:{}", JsonUtils.toJsonStringIgnoreExp(request));
            return false;
        }

        // if not match detect mode.
        if (!detectMode.isMatch(filterVar.getAnomalyDirection())) {
            return false;
        }

        return isMatchAnomalyScore(filterVar.getAnomalyScore(), threshold);
    }

    private boolean isMatchAnomalyScore(Double anomalyScore, Integer threshold) {
        if (Objects.isNull(threshold) || threshold < ANOMALY_SCORE_LOWER || threshold > ANOMALY_SCORE_UPPER || Objects.isNull(anomalyScore)) {
            return false;
        }
        return anomalyScore > ANOMALY_SCORE_MAPPING.get(threshold);
    }

    private boolean isMatchFuncCmp(SingleContentMatchRequest request) {

        List<Metrics> metricsList = request.getMetricsList();
        int listSize = metricsList.size();
        Metrics metrics = metricsList.get(listSize - 1);
        if (request.getMatchContext() == null) {
            request.setMatchContext(new HashMap<>(5));
        }

        //process with _pre_ var in expression
        String expression = request.getCompareRule().getExpression();
        String varPrefix = alarmServiceParaService.getParamValue(PRE_VAR_PREFIX, "_pre_");
        if (expression.contains(varPrefix)) {
            //chain ratio need as least two result
            if (listSize < CHAIN_RATIO_MIN_SIZE) {
                return false;
            }
            Metrics lastMetrics = metricsList.get(listSize - 2);
            //TODO if field has no data, need to add map too.
            Map<String, Object> lastFieldMap = lastMetrics.getFields().stream()
                    .filter(metricsField -> null != metricsField.getFieldValue())
                    .collect(Collectors.toMap(e -> varPrefix + e.getFieldName(), MetricsField::getFieldValue));
            request.getMatchContext().putAll(lastFieldMap);
        }


        if (!CollectionUtils.isEmpty(metrics.getFields())) {
            Map<String, Object> collect = new HashMap<>(metrics.getFields().size());
            metrics.getFields().forEach(f -> {
                if (org.apache.commons.lang3.StringUtils.isNotBlank(f.getFieldName())) {
                    collect.put(f.getFieldName(), f.getFieldValue());
                }
            });
            request.getMatchContext().putAll(collect);
        }

        if (MapUtils.isNotEmpty(metrics.getTags())) {
            request.getMatchContext().putAll(metrics.getTags());
        }

        //add AI Var
        if (MapUtils.isNotEmpty(metrics.getFilterVar())) {
            request.getMatchContext().putAll(metrics.getFilterVar());
        }

        AlarmRuleSingleContent compareRule = request.getCompareRule();

        if (null != request.getMatchContext()) {
            request.getMatchContext().put(CubeConstants.TAG_TIMESTAMP, metrics.getTs());
        }
        return compareRule.isMatchMetrics(request.getMatchContext(), request.getMetricsId());
    }

    private boolean isMatchThreshold(SingleContentMatchRequest request) {
        List<Metrics> aggResultItemMaps = request.getMetricsList();
        int listSize = aggResultItemMaps.size();
        Metrics currentMetrics = aggResultItemMaps.get(listSize - 1);

        AlarmRuleSingleContent compareRule = request.getCompareRule();
        Map<String, Object> currentFieldMap = new HashMap<>();
        Instance.ofNullable(currentMetrics.getFields()).forEach(metricsField -> currentFieldMap.put(metricsField.getFieldName(), metricsField.getFieldValue()));

        Object currentObj = currentFieldMap.get(compareRule.getField());
        //AggType，
        if (currentObj == null) {
            return false;
        }

        if (FieldTypeEnum.string.name().equals(compareRule.getFieldType().name())) {
            return compareRule.isMatchMetrics(currentObj);
        }
        if (FieldTypeEnum.number.name().equals(compareRule.getFieldType().name())) {
            Double currentValue = Double.parseDouble(currentObj.toString());
            //AggType，
            if (currentValue == null) {
                return false;
            }
            return compareRule.isMatchMetrics(currentValue);
        }
        if (FieldTypeEnum.histogram.name().equals(compareRule.getFieldType().name())) {
            Double histogramValue = histogramQuntile(compareRule, currentObj);
            setHistogramContext(request, compareRule.getField(), compareRule.getHistogramQuantile(), histogramValue);
            return compareRule.isMatchMetrics(histogramValue);
        }
        if (FieldTypeEnum.mapString.name().equals(compareRule.getFieldType().name())
                || FieldTypeEnum.mapNumber.name().equals(compareRule.getFieldType().name())) {
            if (currentObj instanceof Map) {
                return compareRule.isMatchMetrics(currentObj);
            }

            return false;
        }

        return false;
    }

    private void setHistogramContext(SingleContentMatchRequest request, String field, String histogramQuantile, Double histogramValue) {
        if (request.getHistogramContext() == null) {
            request.setHistogramContext(new TreeMap<>(Comparator.reverseOrder()));
        }
        if (request.getHistogramContext().get(field) == null) {
            request.getHistogramContext().put(field, new TreeMap<>(Comparator.reverseOrder()));
        }
        Map<String, Object> histogramMap = request.getHistogramContext().get(field);
        histogramMap.put(histogramQuantile, histogramValue);
    }



    public Double histogramQuntile(AlarmRuleSingleContent compareRule, Object cumulative) {
        List<Double> dataList = (List<Double>) cumulative;
        String histogramBucket = compareRule.getHistogramBucket();
        List<Double> bucket = JSON.parseArray(histogramBucket, Double.class);
        String percentileStr = compareRule.getHistogramQuantile();
        Double parseDouble = Double.parseDouble(percentileStr) / 100d;
        return calculatePercentile(dataList, bucket, parseDouble);
    }

    public static double calculatePercentile(List<Double> cumulative, List<Double> bucket, Double percentile) {
        if (CollectionUtils.isEmpty(cumulative) || CollectionUtils.isEmpty(bucket) || Objects.isNull(percentile)) {
            return 0;
        }

        double total = cumulative.get(cumulative.size() - 1);
        if (total == 0) {
            return 0;
        }

        double rank = percentile * total;
        double bucketStart = 0;
        double ans = 0;
        double bucketEnd = bucket.get(bucket.size() - 1);
        int b = bucket.size() - 1;
        double lastBucketCount = 0;
        double thisBucketCount = 0;

        for (int i = 0; i < cumulative.size(); i++) {
            if (cumulative.get(i) >= rank) {
                b = i;
                if (i > 0) {
                    bucketStart = bucket.get(i - 1);
                    rank = rank - cumulative.get(i - 1);
                    lastBucketCount = cumulative.get(i - 1);
                    thisBucketCount = cumulative.get(i);
                } else {
                    bucketStart = 0;
                    thisBucketCount = cumulative.get(i);
                }
                break;
            }
        }

        if (b == bucket.size()) {
            ans = bucket.get(b - 1);
        } else {
            bucketEnd = bucket.get(b);
            ans = bucketStart + (bucketEnd - bucketStart) * (rank / (thisBucketCount - lastBucketCount));
        }

        return ans;
    }

    public static final String LAST_FIELD_PREFIX = "last_";

    private boolean isMatchChainRatio(SingleContentMatchRequest request) {
        List<Metrics> aggResultItemMaps = request.getMetricsList();
        int listSize = aggResultItemMaps.size();
        if (listSize < CHAIN_RATIO_MIN_SIZE) {
            return false;
        }
        Metrics currentMetrics = aggResultItemMaps.get(listSize - 1);
        Metrics lastMetrics = aggResultItemMaps.get(listSize - 2);
        AlarmRuleSingleContent compareRule = request.getCompareRule();
        //Ignore out-of-order data
        if (currentMetrics.getTs() < lastMetrics.getTs()) {
            return false;
        }
        Map<String, Object> currentFieldMap = currentMetrics.getFields().stream().filter(metricsField -> null != metricsField.getFieldValue()).collect(Collectors.toMap(MetricsField::getFieldName, MetricsField::getFieldValue));
        Map<String, Object> lastFieldMap = lastMetrics.getFields().stream().filter(metricsField -> null != metricsField.getFieldValue()).collect(Collectors.toMap(MetricsField::getFieldName, MetricsField::getFieldValue));

        Double ratioValue = null;
        Double lastValue = null;
        Double currentValue = null;
        Object currentObj = currentFieldMap.get(compareRule.getField());
        Object lastObject = lastFieldMap.get(compareRule.getField());
        if (currentObj == null || lastObject == null) {
            return false;
        }
        if (Objects.equals(compareRule.getFieldType(), FieldTypeEnum.histogram)) {
            currentValue = histogramQuntile(compareRule, currentObj);
            lastValue = histogramQuntile(compareRule, lastObject);
        } else {
            currentValue = Double.parseDouble(currentObj.toString());
            lastValue = Double.parseDouble(lastObject.toString());
        }
        setLastObjToContext(request, lastObject, compareRule.getField());
        //，0，
        if (currentValue == 0 && lastValue == 0) {
            //1
            ratioValue = 1d;
        } else {
            //lastValue == 0 && currentValue > 0, Infinity（
            ratioValue = currentValue / lastValue;
        }
        if (Objects.equals(compareRule.getFieldType(), FieldTypeEnum.histogram)) {
            setHistogramContext(request, compareRule.getField(), compareRule.getHistogramQuantile(), ratioValue);
        }
        return compareRule.isMatchMetrics(ratioValue);
    }

    private void setLastObjToContext(SingleContentMatchRequest request, Object lastObject, String field) {

        if (null == request) {
            return;
        }

        if (request.getMatchContext() == null) {
            request.setMatchContext(new HashMap<>(5));
        }
        request.getMatchContext().put(LAST_FIELD_PREFIX + field, lastObject);
    }

    /**
     * No Data Condition Matcher
     * Determines if the metrics data meets the no-data condition
     *
     * @param request The match request containing metrics data
     * @return true if the no-data condition is met, false otherwise
     */
    private boolean isMatchNoData(SingleContentMatchRequest request) {

        List<Metrics> metricsList = request.getMetricsList();
        if (CollectionUtils.isEmpty(metricsList)) {
            return false;
        }
        return metricsList.get(0).isNoData();
    }

    private void printSingleContentMatch(SingleContentMatchRequest request, boolean isAlarmRuleSingleContentMatch) {
        if (LOG_INDEX.incrementAndGet() % LogSampling.getInfoSamplingCount() == 0) {
            log.info("[AlarmRuleSingleContentMatcher#isMatch] request is: {}, result is: {}", request, isAlarmRuleSingleContentMatch);
        }
        LOG_INDEX.compareAndSet(Long.MAX_VALUE, 0);
    }
}
