package us.zoom.cube.alarm.core.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Alarm notification rate limiting configuration
 * Supports default global rate limiting parameters and service-based rate limiting parameters
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
@Slf4j
@Data
@Component
public class AlarmRateLimitConfig {

    /**
     * Whether to enable rate limiting functionality
     */
    private boolean enabled = true;

    /**
     * Service name whitelist, only services in this list will have rate limiting enabled
     * If empty, rate limiting will be enabled for all services
     */
    private List<String> serviceWhitelist;

    /**
     * Default global rate limiting parameters
     */
    private RateLimitParams defaultParams = new RateLimitParams();

    /**
     * Service-based rate limiting parameter configuration
     * key: service name, value: rate limiting parameters for that service
     */
    private Map<String, RateLimitParams> serviceParams = new HashMap<>();

    /**
     * Get rate limiting parameters for the specified service
     * If the service has specific configuration, return the specific configuration; otherwise return default configuration
     * 
     * @param serviceName service name
     * @return rate limiting parameters
     */
    public RateLimitParams getRateLimitParams(String serviceName) {
        if (serviceName == null || serviceName.trim().isEmpty()) {
            return defaultParams;
        }
        
        RateLimitParams serviceSpecificParams = serviceParams.get(serviceName);
        if (serviceSpecificParams != null) {
            return serviceSpecificParams;
        }
        
        return defaultParams;
    }

    /**
     * Rate limiting parameter configuration class
     */
    @Data
    public static class RateLimitParams {
        /**
         * Maximum notification count within 5 minutes
         */
        private int rule5Min = 10;

        /**
         * Maximum notification count within 30 minutes
         */
        private int rule30Min = 50;

        /**
         * Maximum notification count within 1 hour
         */
        private int rule1Hour = 100;

        /**
         * local cache expiration time, default 1h
         */
        private Long localCacheExpireTime = 60000L;

        /**
         * Redis key expiration time (hours)
         */
        private long redisKeyExpireHours = 2;

        /**
         * Data cleanup time window (hours)
         */
        private long dataCleanupHours = 1;

        /**
         * Whether to enable rate limiting for this service
         * If false, skip rate limiting check for this service
         */
        private boolean enabled = true;

        /**
         * Create default rate limiting parameters
         */
        public static RateLimitParams createDefault() {
            return new RateLimitParams();
        }

        /**
         * Create custom rate limiting parameters
         */
        public static RateLimitParams create(int rule5Min, int rule30Min, int rule1Hour) {
            RateLimitParams params = new RateLimitParams();
            params.setRule5Min(rule5Min);
            params.setRule30Min(rule30Min);
            params.setRule1Hour(rule1Hour);
            return params;
        }

        /**
         * Create complete custom rate limiting parameters
         */
        public static RateLimitParams create(int rule5Min, int rule30Min, int rule1Hour, 
                                           long redisKeyExpireHours, long dataCleanupHours, boolean enabled) {
            RateLimitParams params = new RateLimitParams();
            params.setRule5Min(rule5Min);
            params.setRule30Min(rule30Min);
            params.setRule1Hour(rule1Hour);
            params.setRedisKeyExpireHours(redisKeyExpireHours);
            params.setDataCleanupHours(dataCleanupHours);
            params.setEnabled(enabled);
            return params;
        }
    }
} 