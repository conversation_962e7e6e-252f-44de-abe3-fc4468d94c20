package us.zoom.cube.alarm.core.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import us.zoom.monitor.auto.DefaultServletRequestMetricsFilter;

/**
 * @author: canyon.li
 * @date: 2024/07/08
 **/
@Configuration
public class ApmServletRequestMetricsFilter {
    @Bean
    public DefaultServletRequestMetricsFilter defaultServletRequestMetricsFilter() {
        return new DefaultServletRequestMetricsFilter(12345);
    }
}