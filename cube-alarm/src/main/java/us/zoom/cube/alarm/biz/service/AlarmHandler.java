package us.zoom.cube.alarm.biz.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zoom.op.monitor.domain.alarm.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import us.zoom.cube.alarm.biz.service.selfmonitor.StaticsDetailService;
import us.zoom.cube.alarm.constat.LogConstant;
import us.zoom.cube.alarm.core.config.ConfigCache;
import us.zoom.cube.alarm.core.model.alarm.config.AlarmCfg;
import us.zoom.cube.alarm.core.model.alarm.config.AlarmRuleCfg;
import us.zoom.cube.alarm.core.model.alarm.model.AlarmServiceParaService;
import us.zoom.cube.alarm.core.model.cache.AlarmImgKey;
import us.zoom.cube.alarm.core.model.cache.NotifyImg;
import us.zoom.cube.alarm.core.model.cache.NotifyImgKey;
import us.zoom.cube.alarm.core.model.cache.NotifyImgList;
import us.zoom.cube.alarm.core.model.metrics.MetricsCfg;
import us.zoom.cube.alarm.core.model.metrics.MetricsFieldCfg;
import us.zoom.cube.alarm.core.model.trace.AlarmLogSwitchPara;
import us.zoom.cube.alarm.core.service.StorageService;
import us.zoom.cube.alarm.core.service.action.AlarmAction;
import us.zoom.cube.alarm.core.service.action.AlarmNoticeAction;
import us.zoom.cube.alarm.core.service.alarm.AlarmNoticeHelperService;
import us.zoom.cube.alarm.core.service.cache.LocalCacheHolder;
import us.zoom.cube.alarm.core.service.match.LastCountMatcher;
import us.zoom.cube.alarm.core.service.match.TotalAlarmMatcher;
import us.zoom.cube.alarm.core.service.syspara.AlarmParaService;
import us.zoom.cube.alarm.core.service.syspara.SysParaService;
import us.zoom.cube.alarm.infra.LogSampling;
import us.zoom.cube.alarm.infra.redis.MultiRedisService;
import us.zoom.cube.lib.common.AlarmMonitorTypeEnum;
import us.zoom.cube.lib.integrations.Metrics;
import us.zoom.cube.lib.integrations.MetricsField;
import us.zoom.cube.lib.integrations.MetricsFieldTypeEnum;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.infra.enums.AlarmEventTypeEnum;
import us.zoom.infra.enums.AlarmRecordStatusEnum;
import us.zoom.infra.influx.model.alarm.AlarmMatchRecord;
import us.zoom.infra.influx.util.TagUtils;
import us.zoom.infra.model.alarm.AlarmLevel;
import us.zoom.infra.model.alarm.IncidentItem;
import us.zoom.infra.notification.NotificationHelper;
import us.zoom.infra.utils.DateUtils;
import us.zoom.mq.common.entity.TaskEntity;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static com.zoom.op.monitor.domain.alarm.AlarmMatchModeEnum.tag_match;
import static com.zoom.op.monitor.domain.alarm.AlarmMatchModeEnum.traversal;
import static us.zoom.cube.alarm.core.service.syspara.AlarmParaService.ALARM_NOTIFY_GROUP_BY_TAGS;
import static us.zoom.cube.lib.common.CubeConstants.TURN_ON_TAG_MATCH_ALARM;
import static us.zoom.cube.lib.common.TagConditionOperatorEnum.IN;
import static us.zoom.cube.lib.common.TagConditionOperatorEnum.NOT_IN;

/**
 * @author: canyon.li
 * @date: 2023/09/18
 **/
@Service
public class AlarmHandler {

    private static final Logger log = LoggerFactory.getLogger(AlarmHandler.class);
    private static final AtomicLong COMMON_ERROR_ALARM_LOG_INDEX = new AtomicLong();

    @Value("${cube.incident.interval:0}")
    @Setter
    private int incidentInterval;

    @Value("${cube.incident.level.threshold:0}")
    @Setter
    private int levelThreshold;

    @Autowired
    private TotalAlarmMatcher totalAlarmMatcher;

    @Autowired
    private LastCountMatcher lastCountMatcher;

    @Autowired
    private List<AlarmAction> actionPacks;

    @Autowired
    private StorageService storageService;

    @Autowired
    private SysParaService sysParaService;

    @Autowired
    private LocalCacheHolder localCacheHolder;

    @Autowired
    private NotificationHelper notificationHelper;

    @Autowired
    private StaticsDetailService staticsDetailService;

    @Autowired
    private ConfigCache configCache;

    @Autowired
    private AlarmParaService alarmParaService;

    @Autowired
    private AlarmNoticeHelperService alarmNoticeHelperService;

    @Autowired
    private AlarmNoticeAction alarmNoticeAction;

    @Autowired
    private MultiRedisService redisService;

    @Autowired
    private AlarmServiceParaService alarmServiceParaService;


    public static final String SWITCH_FILTER_DATA = "switchFilterData";

    public static final String TIME_LIMIT = "timeLimit";

    public static final Long TIME_UNIT = 1000L;

    public static final String SWITCH_FILTER_DATA_AND_TIME_LIMIT = "switchFilterDataAndTimeLimit";

    public static final ThreadLocal<AlarmNewStatContext> alarmNewStatContextThreadLocal = ThreadLocal.withInitial(() -> null);

    @Getter
    public static Boolean needMaskLog;

    @Value("${need.mask.log:false}")
    public void setNeedMaskLog(Boolean maskLog) {
        needMaskLog = maskLog;
    }

    public void processSingleMsg(TaskEntity<byte[]> entity, String record) {

        if (StringUtils.isBlank(record)) {
            return;
        }

        // 1. parse message, and check metric properties
        long start = System.currentTimeMillis();
        Metrics metrics = parseMessage(record);
        if (Objects.isNull(metrics)) {
            return;
        }
        MetricsCfg metricsCfg = Optional.ofNullable(configCache.getMetricsCfgCache()).map(m -> m.get(metrics.getMetricsId())).orElse(null);
        if (Objects.isNull(metricsCfg)) {
            return;
        }
        metrics.setTags(adjustTags(metrics.getTags(), metricsCfg));
        // adjust metrics
//        adjustMetrics(metrics, metricsCfg);
        List<AlarmCfg> alarmCfgList = calAlarmCfgForMetrics(metrics);
        AlarmNewStatContext alarmNewStatContext = new AlarmNewStatContext(entity, metrics, metricsCfg, null, null, alarmCfgList, System.currentTimeMillis());
        alarmNewStatContextThreadLocal.set(alarmNewStatContext);
        if (CollectionUtils.isEmpty(alarmCfgList)) {
            AlarmLogSwitchPara.MonitorLogSwitch monitorLogSwitch = sysParaService.getAlarmLogSwitchPara().getMonitorLogSwitch();
            if (monitorLogSwitch != null && monitorLogSwitch.isEnableNoAlarmMetrics()) {
                staticsDetailService.statics(AlarmMonitorTypeEnum.processEndStat, entity, null, metrics, 1, start);
            }
            return;
        }
        staticsDetailService.statics(AlarmMonitorTypeEnum.inputStat, entity, null, metrics, 1, metrics.getTs());

        // 2. filter invalid data
        Map filterMap = sysParaService.getSwitchFilterDataAndTimeLimitAndRetrySendIM(SWITCH_FILTER_DATA_AND_TIME_LIMIT);
        if (!filterMap.isEmpty() && Objects.nonNull(filterMap.get(SWITCH_FILTER_DATA)) && (Boolean) filterMap.get(SWITCH_FILTER_DATA)) {
            if (Objects.nonNull(filterMap.get(TIME_LIMIT))) {
                long timeLimit = Long.parseLong(String.valueOf(filterMap.get(TIME_LIMIT)));
                Date expireTime = new Date(metrics.getTs() + timeLimit * TIME_UNIT);
                Date futureTime = new Date(metrics.getTs() - timeLimit * TIME_UNIT);
                Date currTime = new Date();
                if (expireTime.before(currTime) || futureTime.after(currTime)) {
                    staticsDetailService.statics(AlarmMonitorTypeEnum.inputIgnoreStat, entity, null, metrics, 1, metrics.getTs());
                    staticsDetailService.statics(AlarmMonitorTypeEnum.processEndStat, entity, null, metrics, 1, start);
                    return;
                }
            }
        }

        // 3. match alarm for metrics, the AlarmMatchRecord list is grouped by alarmId
        long calculationStart = System.currentTimeMillis();
        Map<String, List<AlarmMatchRecord>> alarmMatchRecords = totalAlarmMatcher.calculate(metrics);
        staticsDetailService.statics(AlarmMonitorTypeEnum.matchThresholdStat, entity, null, metrics, 1, calculationStart);

        // map: alarmId -> List<AlarmMatchRecord>
        for (Map.Entry<String, List<AlarmMatchRecord>> entry : alarmMatchRecords.entrySet()) {
            // 4. check if lasting count number is matched
            List<AlarmMatchRecord> actionAlarmRecordList = Lists.newArrayList();
            for (AlarmMatchRecord alarmMatchRecord : entry.getValue()) {
                long matchLastCountStart = System.currentTimeMillis();
                if (lastCountMatcher.isMatch(alarmMatchRecord)) {
                    //change alarmMatchRecord status -> alarming
                    alarmMatchRecord.setIsLastCountMatched(true);
                    alarmMatchRecord.setStatus(AlarmRecordStatusEnum.new_alarming);
                    actionAlarmRecordList.add(alarmMatchRecord);
                } else {
                    //change alarmMatchRecord status -> pending
                    alarmMatchRecord.setStatus(AlarmRecordStatusEnum.new_pending);
                    alarmMatchRecord.setAlarmEventType(AlarmEventTypeEnum.sys_pending);
                    storageService.addAlarmRecordV2(alarmMatchRecord);
                    storageService.addAlarmRecordEvent(alarmMatchRecord);
                }
                staticsDetailService.statics(AlarmMonitorTypeEnum.matchLastCountStat, entity, alarmMatchRecord, metrics, 1, matchLastCountStart);
            }
            // 5. do actions like sending alarm notice or sending alarm event or both type
            // actions can be extended, e.g, auto-scaling, rate-limit, etc.
            if (org.springframework.util.CollectionUtils.isEmpty(actionAlarmRecordList)) {
                continue;
            }
            long doActionStart = System.currentTimeMillis();
            boolean isSend = alarmNoticeHelperService.isSendAlarmUnderThisEnv(actionAlarmRecordList.get(0).getTenantId());
            Map<AlarmLevel, List<AlarmMatchRecord>> alarmMatchRecordByLevel = alarmNoticeHelperService.getNoticeRecordsByLevel(actionAlarmRecordList);
            List<AlarmMatchRecord> realActionRecordList = new ArrayList<>();
            for (List<AlarmMatchRecord> list : alarmMatchRecordByLevel.values()) {
                realActionRecordList.addAll(list);
            }
            actionPacks.forEach(e -> {
                if (e.matchingCheck(actionAlarmRecordList)) {
                    try {
                        if (org.springframework.util.CollectionUtils.isEmpty(actionAlarmRecordList)) {
                            return;
                        }
                        e.doAction(actionAlarmRecordList, alarmMatchRecordByLevel, isSend);

                    } catch (Exception exp) {
                        logError("[MetricsListener#listen] execute action-{} error! exception: {}", e.getClass().getSimpleName(), exp);
                    }
                }
            });
            updateIsNoticed(realActionRecordList);

            if (!org.springframework.util.CollectionUtils.isEmpty(actionAlarmRecordList)) {
                staticsDetailService.statics(AlarmMonitorTypeEnum.actionStat, entity, actionAlarmRecordList.get(0), metrics, 1, doActionStart);
            }
        }
        staticsDetailService.statics(AlarmMonitorTypeEnum.processEndStat, entity, null, metrics, 1, start);
    }

    /**
     * Parse a JSON string into a Metrics object
     * 
     * @param record JSON string to parse
     * @return parsed Metrics object or null if parsing fails
     */
    private Metrics parseMessage(String record) {
        // Validate input
        if (StringUtils.isBlank(record)) {
            log.warn("MetricsListener finds blank record");
            return null;
        }
        
        try {
            // Parse JSON to Metrics object
            Metrics metrics = JsonUtils.toObject(record, Metrics.class);
            if (metrics == null) {
                return null;
            }
            
            // Handle derived metrics with map fields
            if (metrics.isDerived()) {
                return processDerivedMetrics(metrics);
            }
            
            // Process regular metrics fields
            processRegularMetricsFields(metrics);
            return metrics;
            
        } catch (Exception e) {
            logError("MetricsListener parse error! value = {}", record, e);
            return null;
        }
    }
    
    /**
     * Process derived metrics by converting map fields to individual metrics fields
     */
    private Metrics processDerivedMetrics(Metrics metrics) {
        List<MetricsField> originalFields = new ArrayList<>(metrics.getFields());
        List<MetricsField> newFields = new ArrayList<>();
        
        for (MetricsField field : originalFields) {
            if (field.getFieldValue() == null) {
                newFields.add(field);
                continue;
            }
            
            if (MetricsFieldTypeEnum.mapString.equals(field.getFieldType())) {
                processMapStringField(field, newFields);
            } else if (MetricsFieldTypeEnum.mapNumber.equals(field.getFieldType())) {
                processMapNumberField(field, newFields);
            } else {
                newFields.add(field);
            }
        }
        
        metrics.setFields(newFields);
        return metrics;
    }
    
    /**
     * Process mapString field by converting it to individual string fields
     */
    private void processMapStringField(MetricsField field, List<MetricsField> targetFields) {
        try {
            Map<String, String> fieldMap = convertToMap(field.getFieldValue());
            
            for (Map.Entry<String, String> entry : fieldMap.entrySet()) {
                MetricsField newField = new MetricsField();
                newField.setFieldName(entry.getKey());
                newField.setFieldValue(entry.getValue());
                newField.setFieldType(MetricsFieldTypeEnum.string);
                targetFields.add(newField);
            }
        } catch (Exception e) {
            logError("Failed to convert mapString field. Field: {}, Value: {}", 
                    field.getFieldName(), field.getFieldValue(), e);
            // Add original field to preserve data
            targetFields.add(field);
        }
    }
    
    /**
     * Process mapNumber field by converting it to individual number fields
     */
    private void processMapNumberField(MetricsField field, List<MetricsField> targetFields) {
        try {
            Map<String, Double> fieldMap = convertToMap(field.getFieldValue());
            
            for (Map.Entry<String, Double> entry : fieldMap.entrySet()) {
                MetricsField newField = new MetricsField();
                newField.setFieldName(entry.getKey());
                newField.setFieldValue(entry.getValue());
                newField.setFieldType(MetricsFieldTypeEnum.number);
                targetFields.add(newField);
            }
        } catch (Exception e) {
            logError("Failed to convert mapNumber field. Field: {}, Value: {}", 
                    field.getFieldName(), field.getFieldValue(), e);
            // Add original field to preserve data
            targetFields.add(field);
        }
    }
    
    /**
     * Convert an object to a Map with specified value type
     */
    @SuppressWarnings("unchecked")
    private <T> Map<String, T> convertToMap(Object value) {
        if (value instanceof Map) {
            return (Map<String, T>) value;
        } else {
            return JsonUtils.toObject(value.toString(), Map.class);
        }
    }
    
    /**
     * Process regular metrics fields by converting values to correct types
     */
    private void processRegularMetricsFields(Metrics metrics) {
        for (MetricsField field : metrics.getFields()) {
            if (field.getFieldValue() == null) {
                continue;
            }
            
            if (MetricsFieldTypeEnum.number.equals(field.getFieldType()) && !(field.getFieldValue() instanceof Double)) {
                field.setFieldValue(NumberUtils.toDouble(field.getFieldValue().toString(), 0.0D));
            } else if (MetricsFieldTypeEnum.string.equals(field.getFieldType()) && !(field.getFieldValue() instanceof String)) {
                field.setFieldValue(String.valueOf(field.getFieldValue()));
            } else if (MetricsFieldTypeEnum.histogram.equals(field.getFieldType())) {
                String jsonString = JSON.toJSONString(field.getFieldValue());
                List<Double> fieldValue = JSON.parseArray(jsonString, Double.class);
                field.setFieldValue(fieldValue);
            }
        }
    }

    /**
     * Adjust metrics by checking if number type fields from metricsCfg exist in metrics.fields
     * If not, add them with default value 0
     * 
     * @param metrics the metrics to adjust
     * @param metricsCfg the metrics configuration
     */
    private void adjustMetrics(Metrics metrics, MetricsCfg metricsCfg) {
        try {
            if (metrics == null || metricsCfg == null || CollectionUtils.isEmpty(metricsCfg.getFields())) {
                return;
            }

            // Get existing field names from metrics
            Set<String> existingFieldNames = metrics.getFields().stream()
                    .map(MetricsField::getFieldName)
                    .collect(Collectors.toSet());

            // Check each field in metricsCfg
            for (MetricsFieldCfg fieldCfg : metricsCfg.getFields()) {
                // Only process number type fields
                if (fieldCfg.getFieldType() != MetricsFieldTypeEnum.number) {
                    continue;
                }
                String fieldName = fieldCfg.getFieldName();
                // If field doesn't exist in metrics, add it with default value 0
                if (!existingFieldNames.contains(fieldName)) {
                    MetricsField newField = new MetricsField();
                    newField.setFieldName(fieldName);
                    newField.setFieldType(MetricsFieldTypeEnum.number);
                    metrics.getFields().add(newField);
                }
            }
        } catch (Exception e) {
            log.error("adjustMetrics error, metrics:{}, cfg:{}", metrics, metricsCfg, e);
        }

    }

    private Map<String, String> adjustTags(Map<String, String> tags, MetricsCfg metricsCfg) {
        if (MapUtils.isEmpty(tags)) {
            return Collections.emptyMap();
        }
        Map<String, String> result = new HashMap<>(tags.size());
        if (!org.springframework.util.CollectionUtils.isEmpty(metricsCfg.getTagNameList())) {
            metricsCfg.getTagNameList().forEach(v -> {
                String tagValue = tags.get(v);
                if (StringUtils.isNotEmpty(tagValue)) {
                    result.put(v, tagValue);
                }
            });
        }
        return result;
    }

    private List<AlarmCfg> calAlarmCfgForMetrics(Metrics metrics) {
        List<AlarmCfg> alarmCfgList = configCache.getAlarmsByMetricsId(metrics.getMetricsId());
        Boolean isTurnON = sysParaService.getAlarmSwitchBooleanValue(TURN_ON_TAG_MATCH_ALARM);
        if (!Boolean.TRUE.equals(isTurnON)) {
            return alarmCfgList;
        }
        List<AlarmCfg> res = new ArrayList<>();
        for (AlarmCfg alarmCfg : alarmCfgList) {
            if (AlarmMatchModeEnum.traversal.equals(alarmCfg.getAlarmMatchMode())) {
                res.add(alarmCfg);
            } else if (tag_match.equals(alarmCfg.getAlarmMatchMode())) {
                if (isAlarmMatch(metrics, alarmCfg)) {
                    res.add(alarmCfg);
                }
            }
        }
        return res;
    }

    private boolean isInvalidAlarm(AlarmCfg alarmCfg) {
        if (alarmCfg == null || CollectionUtils.isEmpty(alarmCfg.getNotifications())) {
            return false;
        }

        // Check if notifications size is 1
        if (alarmCfg.getNotifications().size() != 1) {
            return true;
        }

        Notification notification = alarmCfg.getNotifications().get(0);
        if (notification == null || notification.getChannel() == null) {
            return false;
        }

        // Check if channel engine name is "Zoom Chat"
        if (!"Zoom Chat".equals(notification.getChannel().getEngineName())) {
            return false;
        }

        // Check if parameters contain Endpoint with specific value
        List<ChannelParameter> parameters = notification.getChannel().getParameters();
        if (CollectionUtils.isEmpty(parameters)) {
            return false;
        }

        return parameters.stream()
                .filter(param -> "Endpoint".equals(param.getName()))
                .map(ChannelParameter::getValue)
                .anyMatch("xxx"::equals);
    }

    private boolean isAlarmMatch(Metrics metrics, AlarmCfg alarmCfg) {
        if (alarmCfg.getAlarmMatchMode() == null || traversal.equals(alarmCfg.getAlarmMatchMode())) {
            return false;
        } else if (tag_match.equals(alarmCfg.getAlarmMatchMode())) {
            Map<String, String> tagValueMap = metrics.getTags();
            boolean alarmMatched = false;
            for (AlarmRuleCfg ruleCfg : alarmCfg.getAlarmRuleCfgs()) {
                boolean ruleMatched = true;
                List<RuleCondition> tagConditionList = ruleCfg.getAlarmRule().getConditions().stream().filter(e -> ConditionType.TAG.equals(e.getConditionType())).collect(Collectors.toList());
                for (RuleCondition tagCondition : tagConditionList) {
                    String tagName = tagCondition.getName();
                    if (IN.getValue().equals(tagCondition.getOperator())) {
                        boolean valueMatched = Arrays.stream(tagCondition.getThreshold().split(",")).anyMatch(t -> t.equals(tagValueMap.get(tagName)));
                        if (!valueMatched) {
                            ruleMatched = false;
                            break;
                        }
                    } else if (NOT_IN.getValue().equals(tagCondition.getOperator())) {
                        boolean valueMatched = Arrays.stream(tagCondition.getThreshold().split(",")).anyMatch(t -> t.equals(tagValueMap.get(tagName)));
                        if (valueMatched) {
                            ruleMatched = false;
                            break;
                        }
                    }
                }
                if (ruleMatched) {
                    alarmMatched = true;
                    break;
                }
            }
            return alarmMatched;
        }
        return true;
    }

    private void updateIsNoticed(List<AlarmMatchRecord> realActionRecordList) {
        for (AlarmMatchRecord record : realActionRecordList) {
            if (record.getIsNoticed()) {
                updateTriggered(record);
                storeToCache(record);
            }
        }
    }

    private void updateTriggered(AlarmMatchRecord currentAlarmMatchRecord) {
        currentAlarmMatchRecord.setStatus(AlarmRecordStatusEnum.new_triggered);
        currentAlarmMatchRecord.setAlarmEventType(AlarmEventTypeEnum.sys_triggered);
        storageService.addAlarmRecordV2(currentAlarmMatchRecord);
        storageService.addAlarmRecordEvent(currentAlarmMatchRecord);
    }

    private void storeToCache(AlarmMatchRecord currentAlarmMatchRecord) {
        long startTime = System.currentTimeMillis();
        //add to local alarmNoticeCache
        NotifyImgKey notifyImgKey = new NotifyImgKey(currentAlarmMatchRecord.getAlarmId(), TagUtils.createKey(currentAlarmMatchRecord.getMetricsTags()));
        NotifyImgList<NotifyImg> notifyImgList = localCacheHolder.getAlarmNoticeCache().getIfPresent(notifyImgKey);
        if (null == notifyImgList) {
            notifyImgList = new NotifyImgList<>();
            localCacheHolder.getAlarmNoticeCache().put(notifyImgKey, notifyImgList);
        }
        notifyImgList.addImg(new NotifyImg(currentAlarmMatchRecord.getId(), System.currentTimeMillis(), currentAlarmMatchRecord.getStatus().getStatus(), currentAlarmMatchRecord.getAlarmLevel()));


        //add to redis cache
        boolean needGroupByTags = alarmParaService.getParamBooleanValue(ALARM_NOTIFY_GROUP_BY_TAGS, false);
        if (needGroupByTags && StringUtils.isNoneBlank(currentAlarmMatchRecord.getGroupTagKey())) {
            NotifyImgKey groupNotifyImgKey = new NotifyImgKey(currentAlarmMatchRecord.getAlarmId(), currentAlarmMatchRecord.getGroupTagKey());
            String redisKey = groupNotifyImgKey.generateKey();
            NotifyImgList<NotifyImg> groupNotifyImgList = Optional.ofNullable(redisService.getCastValue(redisKey, NotifyImgList.class, alarmParaService.isNewRedisSwitchForGroupByTag())).orElse(new NotifyImgList());
            groupNotifyImgList.addImg(new NotifyImg(currentAlarmMatchRecord.getId(), System.currentTimeMillis(), currentAlarmMatchRecord.getStatus().getStatus(), currentAlarmMatchRecord.getAlarmLevel()));
            redisService.set(redisKey, JsonUtils.toJsonString(groupNotifyImgList), 1, TimeUnit.DAYS, alarmParaService.isNewRedisSwitchForGroupByTag());
        }

        //need send incident, set cache
        String alarmId = currentAlarmMatchRecord.getAlarmId();
        List<String> levelsSendIncident = Optional.ofNullable(configCache.getAlarmCache().get(alarmId)).map(AlarmCfg::getLevelsSendIncident).orElse(new ArrayList<>());

        /*
           1. check alarm level
           2. check env
           3. check block list
         */
        AlarmNewStatContext alarmNewStatContext = alarmNewStatContextThreadLocal.get();
        // 1
        boolean isLevelSend = levelsSendIncident.contains(currentAlarmMatchRecord.getAlarmLevel().name())
                || (incidentInterval > 0 && currentAlarmMatchRecord.getAlarmLevel().getValue() <= levelThreshold);
        if (!isLevelSend) {
            return;
        }

        // 2
        boolean isEnvSend = alarmNoticeHelperService.isSendAlarmUnderThisEnv(currentAlarmMatchRecord.getTenantId());
        if (!isEnvSend) {
            if (alarmNewStatContext != null) {
                staticsDetailService.statics(AlarmMonitorTypeEnum.setIncidentCacheEnvNotMatch, alarmNewStatContext.getTaskEntity(), currentAlarmMatchRecord, alarmNewStatContext.getMetrics(), 1, startTime);
            }
            return;
        }

        // 3
        boolean isBlock = alarmParaService.needToBlockIncident(currentAlarmMatchRecord.getTenantName(), currentAlarmMatchRecord.getAlarmName());
        if (isBlock) {
            if (alarmNewStatContext != null) {
                staticsDetailService.statics(AlarmMonitorTypeEnum.setIncidentCacheBlock, alarmNewStatContext.getTaskEntity(), currentAlarmMatchRecord, alarmNewStatContext.getMetrics(), 1, startTime);
            }
            return;
        }
        if (alarmNewStatContext != null) {
            staticsDetailService.statics(AlarmMonitorTypeEnum.setIncidentCache, alarmNewStatContext.getTaskEntity(), currentAlarmMatchRecord, alarmNewStatContext.getMetrics(), 1, startTime);
        }
        AlarmImgKey alarmImgKey = new AlarmImgKey(currentAlarmMatchRecord.getAlarmRuleId(), TagUtils.createKey(currentAlarmMatchRecord.fetchTags()));

        String incidentCache = localCacheHolder.getIncidentSendCache().getIfPresent(alarmImgKey);
        if (null == incidentCache) {
            localCacheHolder.getIncidentSendCache().put(alarmImgKey, currentAlarmMatchRecord.getAlarmRuleId());

            localCacheHolder.getSendIncidentQueue().offer(transIncidentItem(alarmImgKey, currentAlarmMatchRecord));
        }
    }

    public IncidentItem transIncidentItem(AlarmImgKey alarmKey, AlarmMatchRecord alarmRecord) {
        boolean isMaskVar = alarmServiceParaService.isMaskVar(alarmRecord.getTenantName());
        IncidentItem.IncidentItemBuilder builder = IncidentItem.builder()
                .service(alarmRecord.getTenantName())
                .description(alarmRecord.getAlarmName() + "-" + alarmKey.getTagKey())
                .check(alarmKey.getAlarmRuleId() + "_" + alarmKey.getTagKey())
                .status(String.valueOf((alarmRecord.getAlarmLevel().getValue() - 1)))
                .time(DateUtils.changeUTC2Date(alarmRecord.getTime()))
                .alertUrl(notificationHelper.buildAlarmDetailUrl(alarmNoticeAction.getEndpoint(), alarmRecord));

        if(!isMaskVar) {
            builder.tags(alarmRecord.getMetricsTags())
                    .fields(alarmRecord.getMetricFields());
        }
        return builder.build();
    }

    public static void logError(String format, Object... obj) {
        if (COMMON_ERROR_ALARM_LOG_INDEX.incrementAndGet() % LogSampling.getErrorSamplingCount() == 0) {
            if (needMaskLog) {
                log.error(format, LogConstant.MASK_LOG);
            } else {
                log.error(format, obj);
            }
        }
        COMMON_ERROR_ALARM_LOG_INDEX.compareAndSet(Long.MAX_VALUE, 0);
    }

    @Data
    @AllArgsConstructor
    public static class AlarmNewStatContext {
        private TaskEntity<byte[]> taskEntity;
        private Metrics metrics;
        private MetricsCfg metricsCfg;
        private String channelName;
        private AlarmAction.AlarmSilenceRes alarmSilenceRes;
        private List<AlarmCfg> alarmCfgList;
        private long inputTime;

        public boolean hasValidAlarmSilence() {
            return alarmSilenceRes != null
                    && alarmSilenceRes.getIsSilenced()
                    && StringUtils.isNotEmpty(alarmSilenceRes.getAlarmSilenceId())
                    && StringUtils.isNotEmpty(alarmSilenceRes.getAlarmSilenceName());
        }
    }

}
