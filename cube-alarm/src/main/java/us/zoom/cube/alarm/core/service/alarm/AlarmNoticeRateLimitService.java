package us.zoom.cube.alarm.core.service.alarm;

import com.google.common.cache.Cache;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import us.zoom.cube.alarm.core.service.cache.LocalCacheHolder;
import us.zoom.cube.alarm.core.service.syspara.AlarmParaService;
import us.zoom.cube.alarm.infra.redis.MultiRedisService;
import us.zoom.cube.alarm.core.config.AlarmRateLimitConfig;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * Alarm notification rate limiting service
 * Implements sliding window rate limiting based on Redis ZSet with local cache optimization
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
@Slf4j
@Service
public class AlarmNoticeRateLimitService {

    @Autowired
    private MultiRedisService redisService;

    @Autowired
    private AlarmParaService alarmParaService;

    @Autowired
    private LocalCacheHolder localCacheHolder;

    /**
     * Redis key prefix
     */
    private static final String RATE_LIMIT_KEY_PREFIX = "alarm:notice:rate_limit:";

    private static final String ALL = "all";

    /**
     * Check if notification is allowed to be sent
     *
     * @param ruleId alarm rule ID
     * @param serviceName service name
     * @param rateLimitRule rate limiting rule, if null then use configured rule
     * @return true means allowed to send, false means rate limited
     */
    public boolean isAllowedToNotify(String ruleId, String serviceName, RateLimitRule rateLimitRule) {
        // If rate limiting is not enabled, allow sending directly
        if (!alarmParaService.getAlarmRateLimitConfig().isEnabled()) {
            return true;
        }

        // Check service name whitelist
        if (!isServiceInWhitelist(serviceName)) {
            log.debug("Service {} not in rate limit whitelist, allow notification", serviceName);
            return true;
        }

        if (ruleId == null || ruleId.trim().isEmpty()) {
            log.warn("Rule ID is null or empty, allow notification");
            return true;
        }

        // Check local cache first
        Cache<String, RateLimitCacheEntry> rateLimitCache = localCacheHolder.getRateLimitCache();
        RateLimitCacheEntry cacheEntry = rateLimitCache.getIfPresent(ruleId);

        if (cacheEntry != null && cacheEntry.isRateLimited()) {
            long currentTime = System.currentTimeMillis();
            // If still within cache validity period, return rate limited
            if (currentTime - cacheEntry.getRateLimitTime() < cacheEntry.getCacheValidityPeriod()) {
                log.debug("Rate limit hit from local cache for rule: {}", ruleId);
                return false;
            } else {
                // Cache expired, remove it and check Redis
                rateLimitCache.invalidate(ruleId);
            }
        }

        // Get service-specific rate limiting parameters
        AlarmRateLimitConfig.RateLimitParams serviceParams = alarmParaService.getAlarmRateLimitConfig().getRateLimitParams(serviceName);
        long cacheExpireTime = 60 * 1000L;
        // If rate limiting is disabled for this service, allow sending directly
        if (!serviceParams.isEnabled()) {
            return true;
        }

        // Priority: use passed rule first, then rule-specific config, finally service config
        RateLimitRule rule = rateLimitRule;
        if (rule == null) {
            // Use service-specific configuration
            rule = RateLimitRule.builder()
                    .rule5Min(serviceParams.getRule5Min())
                    .rule30Min(serviceParams.getRule30Min())
                    .rule1Hour(serviceParams.getRule1Hour())
                    .build();
        }
        if (Objects.isNull(rule)) {
            return true;
        }

        String redisKey = RATE_LIMIT_KEY_PREFIX + ruleId;
        long currentTime = System.currentTimeMillis();

        try {
            // Check 5-minute rate limit
            if (rule.getRule5Min() > 0) {
                long count5Min = getNotificationCountInTimeWindow(redisKey, currentTime - 5 * 60 * 1000, currentTime);
                if (count5Min >= rule.getRule5Min()) {
                    log.info("Rate limit exceeded for rule {}: 5min limit {} reached, current count: {}",
                            ruleId, rule.getRule5Min(), count5Min);
                    // Cache rate limit status in local memory
                    cacheRateLimitStatus(ruleId, currentTime, 5 * 60 * 1000L);
                    return false;
                }
            }

            // Check 30-minute rate limit
            if (rule.getRule30Min() > 0) {
                long count30Min = getNotificationCountInTimeWindow(redisKey, currentTime - 30 * 60 * 1000, currentTime);
                if (count30Min >= rule.getRule30Min()) {
                    log.info("Rate limit exceeded for rule {}: 30min limit {} reached, current count: {}",
                            ruleId, rule.getRule30Min(), count30Min);
                    // Cache rate limit status in local memory
                    cacheRateLimitStatus(ruleId, currentTime, 30 * 60 * 1000L);
                    return false;
                }
            }

            // Check 1-hour rate limit
            if (rule.getRule1Hour() > 0) {
                long count1Hour = getNotificationCountInTimeWindow(redisKey, currentTime - 60 * 60 * 1000, currentTime);
                if (count1Hour >= rule.getRule1Hour()) {
                    log.info("Rate limit exceeded for rule {}: 1hour limit {} reached, current count: {}",
                            ruleId, rule.getRule1Hour(), count1Hour);
                    // Cache rate limit status in local memory
                    cacheRateLimitStatus(ruleId, currentTime, 60 * 60 * 1000L);
                    return false;
                }
            }

            // Record this notification timestamp
            recordNotification(redisKey, currentTime, serviceName);

            return true;

        } catch (Exception e) {
            log.error("Error checking rate limit for rule: {}", ruleId, e);
            // Allow sending notification when exception occurs to avoid affecting normal business
            return true;
        }
    }

    /**
     * Cache rate limit status in local memory
     *
     * @param ruleId alarm rule ID
     * @param rateLimitTime time when rate limit was hit
     * @param windowSize time window size in milliseconds
     */
    private void cacheRateLimitStatus(String ruleId, long rateLimitTime, long windowSize) {
        // Cache validity period is 80% of the window size to ensure we don't miss the recovery
        long cacheValidityPeriod = (long) (windowSize * 0.8);
        RateLimitCacheEntry cacheEntry = new RateLimitCacheEntry(rateLimitTime, cacheValidityPeriod);

        Cache<String, RateLimitCacheEntry> rateLimitCache = localCacheHolder.getRateLimitCache();
        rateLimitCache.put(ruleId, cacheEntry);

        log.debug("Cached rate limit status for rule: {}, validity period: {}ms, cache size: {}",
                ruleId, cacheValidityPeriod, rateLimitCache.size());
    }

    /**
     * Get notification count within specified time window
     *
     * @param redisKey Redis key
     * @param startTime start timestamp
     * @param endTime end timestamp
     * @return notification count
     */
    private long getNotificationCountInTimeWindow(String redisKey, long startTime, long endTime) {
        return redisService.zcount(redisKey, startTime, endTime, true);
    }

    /**
     * Record notification timestamp
     *
     * @param redisKey Redis key
     * @param timestamp timestamp
     * @param serviceName service name
     */
    private void recordNotification(String redisKey, long timestamp, String serviceName) {
        try {
            // Use timestamp as score, notification ID as value
            String notificationId = String.valueOf(timestamp) + "_" + System.nanoTime();
            redisService.zadd(redisKey, notificationId, timestamp, true);

            // Get service-specific rate limiting parameters
            AlarmRateLimitConfig.RateLimitParams serviceParams = alarmParaService.getAlarmRateLimitConfig().getRateLimitParams(serviceName);

            // Clean up data older than configured time to avoid unlimited Redis memory growth
            long cleanupTimeAgo = timestamp - serviceParams.getDataCleanupHours() * 60 * 60 * 1000L;
            redisService.zremrangeByScore(redisKey, 0, cleanupTimeAgo, true);

            // Set key expiration time to ensure data will be cleaned up eventually
            redisService.set(redisKey + ":expire", "1", serviceParams.getRedisKeyExpireHours(), TimeUnit.HOURS, true);

        } catch (Exception e) {
            log.error("Error recording notification timestamp for key: {}", redisKey, e);
        }
    }

    /**
     * Get notification statistics for specified rule
     *
     * @param ruleId alarm rule ID
     * @return statistics information
     */
    public NotificationStats getNotificationStats(String ruleId) {
        if (ruleId == null || ruleId.trim().isEmpty()) {
            return new NotificationStats();
        }

        String redisKey = RATE_LIMIT_KEY_PREFIX + ruleId;
        long currentTime = System.currentTimeMillis();

        try {
            long count5Min = getNotificationCountInTimeWindow(redisKey, currentTime - 5 * 60 * 1000, currentTime);
            long count30Min = getNotificationCountInTimeWindow(redisKey, currentTime - 30 * 60 * 1000, currentTime);
            long count1Hour = getNotificationCountInTimeWindow(redisKey, currentTime - 60 * 60 * 1000, currentTime);

            return NotificationStats.builder()
                    .ruleId(ruleId)
                    .count5Min(count5Min)
                    .count30Min(count30Min)
                    .count1Hour(count1Hour)
                    .build();

        } catch (Exception e) {
            log.error("Error getting notification stats for rule: {}", ruleId, e);
            return new NotificationStats();
        }
    }

    /**
     * Check if service name is in whitelist
     *
     * @param serviceName service name
     * @return true means in whitelist, false means not in whitelist
     */
    private boolean isServiceInWhitelist(String serviceName) {
        if (serviceName == null || serviceName.trim().isEmpty()) {
            log.warn("Service name is null or empty, skip rate limit whitelist check");
            return false;
        }

        List<String> whitelist = alarmParaService.getAlarmRateLimitConfig().getServiceWhitelist();

        // If whitelist is empty, enable rate limiting for all services
        if (whitelist == null || whitelist.isEmpty()) {
            return true;
        }

        // Check if service name is in whitelist
        boolean inWhitelist = whitelist.contains(serviceName) || whitelist.contains(ALL);
        if (!inWhitelist) {
            log.debug("Service {} not in rate limit whitelist: {}", serviceName, whitelist);
        }

        return inWhitelist;
    }

    /**
     * Rate limiting rule configuration
     */
    @Getter
    public static class RateLimitRule {
        private final int rule5Min;   // Maximum notifications in 5 minutes
        private final int rule30Min;  // Maximum notifications in 30 minutes
        private final int rule1Hour;  // Maximum notifications in 1 hour

        private RateLimitRule(Builder builder) {
            this.rule5Min = builder.rule5Min;
            this.rule30Min = builder.rule30Min;
            this.rule1Hour = builder.rule1Hour;
        }

        public static Builder builder() {
            return new Builder();
        }

        public static class Builder {
            private int rule5Min = 10;
            private int rule30Min = 50;
            private int rule1Hour = 100;

            public Builder rule5Min(int rule5Min) {
                this.rule5Min = rule5Min;
                return this;
            }

            public Builder rule30Min(int rule30Min) {
                this.rule30Min = rule30Min;
                return this;
            }

            public Builder rule1Hour(int rule1Hour) {
                this.rule1Hour = rule1Hour;
                return this;
            }

            public RateLimitRule build() {
                return new RateLimitRule(this);
            }
        }
    }

    /**
     * Notification statistics information
     */
    @Getter
    public static class NotificationStats {
        private String ruleId;
        private long count5Min;
        private long count30Min;
        private long count1Hour;

        public NotificationStats() {}

        private NotificationStats(Builder builder) {
            this.ruleId = builder.ruleId;
            this.count5Min = builder.count5Min;
            this.count30Min = builder.count30Min;
            this.count1Hour = builder.count1Hour;
        }

        public static Builder builder() {
            return new Builder();
        }

        public static class Builder {
            private String ruleId;
            private long count5Min;
            private long count30Min;
            private long count1Hour;

            public Builder ruleId(String ruleId) {
                this.ruleId = ruleId;
                return this;
            }

            public Builder count5Min(long count5Min) {
                this.count5Min = count5Min;
                return this;
            }

            public Builder count30Min(long count30Min) {
                this.count30Min = count30Min;
                return this;
            }

            public Builder count1Hour(long count1Hour) {
                this.count1Hour = count1Hour;
                return this;
            }

            public NotificationStats build() {
                return new NotificationStats(this);
            }
        }
    }

    /**
     * Local cache entry for rate limit status
     */
    @Getter
    public static class RateLimitCacheEntry {
        private final long rateLimitTime;        // Time when rate limit was hit
        private final long cacheValidityPeriod;  // Cache validity period in milliseconds

        public RateLimitCacheEntry(long rateLimitTime, long cacheValidityPeriod) {
            this.rateLimitTime = rateLimitTime;
            this.cacheValidityPeriod = cacheValidityPeriod;
        }

        public boolean isRateLimited() {
            return true; // This entry only exists when rate limited
        }
    }

    /**
     * Cache statistics for monitoring
     */
    @Getter
    public static class CacheStats {
        private final int cacheSize;
        private final long lastCleanupTime;
        private final int maxCacheSize;

        public CacheStats(int cacheSize, long lastCleanupTime, int maxCacheSize) {
            this.cacheSize = cacheSize;
            this.lastCleanupTime = lastCleanupTime;
            this.maxCacheSize = maxCacheSize;
        }

        /**
         * Get cache usage percentage
         *
         * @return cache usage percentage (0-100)
         */
        public double getCacheUsagePercentage() {
            return maxCacheSize > 0 ? (double) cacheSize / maxCacheSize * 100 : 0;
        }
    }
}
