package us.zoom.cube.alarm.core.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import us.zoom.infra.model.alarm.AlarmLevel;

import java.util.*;

/**
 * RCA (Root Cause Analysis) automatic analysis configuration
 * Supports default global configuration and service-based configuration
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
@Slf4j
@Data
@Component
public class RcaAnalysisConfig {

    /**
     * Keyword for matching all items
     */
    public static final String ALL_KEYWORD = "all";

    /**
     * Whether to enable RCA automatic analysis functionality globally
     */
    private boolean enabled = true;

    /**
     * Whether to allow all services to be analyzed
     */
    private boolean allAllowed = false;

    /**
     * Service name whitelist for automatic RCA analysis
     * If contains "all" or is empty, RCA analysis will be enabled for all services based on their individual configuration
     * Otherwise, only services in this list will be considered for RCA analysis
     */
    private List<String> serviceWhitelist = new ArrayList<>();

    /**
     * Default global RCA analysis parameters
     */
    private RcaAnalysisParams defaultParams = new RcaAnalysisParams();

    /**
     * Service-based RCA analysis parameter configuration
     * key: service name, value: RCA analysis parameters for that service
     */
    private Map<String, RcaAnalysisParams> serviceParams = new HashMap<>();

    /**
     * Get RCA analysis parameters for the specified service
     * If the service has specific configuration, return the specific configuration; otherwise return default configuration
     * 
     * @param serviceName service name
     * @return RCA analysis parameters
     */
    public RcaAnalysisParams getRcaAnalysisParams(String serviceName) {
        if (serviceName == null || serviceName.trim().isEmpty()) {
            return defaultParams;
        }
        
        RcaAnalysisParams serviceSpecificParams = serviceParams.get(serviceName);
        if (serviceSpecificParams != null) {
            return serviceSpecificParams;
        }
        
        return defaultParams;
    }

    /**
     * Check if RCA analysis should be performed for a specific alarm
     * 
     * @param serviceName service name
     * @param alarmName alarm name
     * @param alarmLevel alarm level
     * @param tags alarm tags
     * @return true if RCA analysis should be performed
     */
    public boolean shouldAnalyze(String serviceName, String alarmName, AlarmLevel alarmLevel, Map<String, String> tags) {
        if (!enabled) {
            return false;
        }

        if (allAllowed) {
            return true;
        }

        // Check service whitelist if configured
        // Is whitelist contains "all" or is empty, allow all services
        if (!serviceWhitelist.isEmpty() && !serviceWhitelist.contains(ALL_KEYWORD) && !serviceWhitelist.contains(serviceName)) {
            return false;
        }

        RcaAnalysisParams params = getRcaAnalysisParams(serviceName);
        return params.shouldAnalyze(alarmName, alarmLevel, tags);
    }

    /**
     * RCA analysis parameter configuration class
     */
    @Data
    public static class RcaAnalysisParams {
        /**
         * Whether to enable RCA analysis for this service
         */
        private boolean enabled = true;

        /**
         * Alarm name whitelist
         * If contains "all", all alarms will be analyzed (except those in blacklist)
         * Otherwise, only alarms with names in this list will be analyzed
         * Default: contains "all" to analyze all alarms
         */
        private Set<String> alarmNameWhitelist = new HashSet<>(List.of(ALL_KEYWORD));

        /**
         * Alarm name blacklist
         * Alarms with names in this list will not be analyzed (higher priority than whitelist)
         */
        private Set<String> alarmNameBlacklist = new HashSet<>();

        /**
         * Minimum alarm level required for analysis
         * Only alarms with level >= this value will be analyzed
         * AlarmLevel: INFO(4), WARN(3), ERROR(2), FATAL(1)
         */
        private AlarmLevel minimumAlarmLevel = AlarmLevel.ERROR;

        /**
         * Required tags for analysis
         * Only alarms containing all these tags with matching values will be analyzed
         * key: tag name, value: tag value (support wildcard * for any value, comma-separated for multiple values)
         * Example: {"region": "us-west,us-east", "env": "prod"}
         */
        private Map<String, String> requiredTags = new HashMap<>();

        /**
         * Excluded tags from analysis
         * Alarms containing any of these tags with matching values will not be analyzed
         * key: tag name, value: tag value (support wildcard * for any value, comma-separated for multiple values)
         * Example: {"env": "test,dev", "debug": "true"}
         */
        private Map<String, String> excludedTags = new HashMap<>();

        /**
         * Maximum number of RCA analyses per hour for this service
         * -1 means unlimited
         */
        private int maxAnalysesPerHour = 100;

        /**
         * RCA analysis timeout in seconds
         */
        private int analysisTimeoutSeconds = 30;

        /**
         * Check if an alarm should be analyzed based on configured parameters
         * 
         * @param alarmName alarm name
         * @param alarmLevel alarm level
         * @param tags alarm tags
         * @return true if the alarm should be analyzed
         */
        public boolean shouldAnalyze(String alarmName, AlarmLevel alarmLevel, Map<String, String> tags) {
            if (!enabled) {
                return false;
            }

            // Check alarm level
            if (alarmLevel == null || alarmLevel.getValue() > minimumAlarmLevel.getValue()) {
                return false;
            }

            // Check alarm name blacklist (highest priority)
            if (alarmNameBlacklist.contains(alarmName)) {
                return false;
            }

            // Check alarm name whitelist
            // If whitelist contains "all", analyze all alarms (except blacklisted)
            // Otherwise, only analyze alarms in the whitelist
            if (!alarmNameWhitelist.contains(RcaAnalysisConfig.ALL_KEYWORD) && !alarmNameWhitelist.contains(alarmName)) {
                return false;
            }

            // Check excluded tags
            if (!excludedTags.isEmpty() && matchesTags(tags, excludedTags)) {
                return false;
            }

            // Check required tags
            if (!requiredTags.isEmpty() && !matchesTags(tags, requiredTags)) {
                return false;
            }

            return true;
        }

        /**
         * Check if alarm tags match the specified tag patterns
         * 
         * @param alarmTags alarm tags
         * @param patterns tag patterns to match
         * @return true if all patterns match
         */
        private boolean matchesTags(Map<String, String> alarmTags, Map<String, String> patterns) {
            if (alarmTags == null || alarmTags.isEmpty()) {
                return false;
            }

            for (Map.Entry<String, String> pattern : patterns.entrySet()) {
                String tagName = pattern.getKey();
                String tagPattern = pattern.getValue();

                if (!alarmTags.containsKey(tagName)) {
                    return false;
                }

                String tagValue = alarmTags.get(tagName);
                
                // Support wildcard * for any value
                if ("*".equals(tagPattern)) {
                    continue;
                }
                
                // Support comma-separated multiple values
                if (tagPattern.contains(",")) {
                    String[] allowedValues = tagPattern.split(",");
                    boolean matched = false;
                    for (String allowedValue : allowedValues) {
                        if (allowedValue.trim().equals(tagValue)) {
                            matched = true;
                            break;
                        }
                    }
                    if (!matched) {
                        return false;
                    }
                } else if (!tagPattern.equals(tagValue)) {
                    return false;
                }
            }

            return true;
        }

        /**
         * Create default RCA analysis parameters
         */
        public static RcaAnalysisParams createDefault() {
            return new RcaAnalysisParams();
        }

        /**
         * Create RCA analysis parameters for analyzing all alarms
         */
        public static RcaAnalysisParams createAnalyzeAll(AlarmLevel minimumLevel) {
            RcaAnalysisParams params = new RcaAnalysisParams();
            params.getAlarmNameWhitelist().add(RcaAnalysisConfig.ALL_KEYWORD);
            params.setMinimumAlarmLevel(minimumLevel);
            return params;
        }

        /**
         * Create RCA analysis parameters for selective analysis
         */
        public static RcaAnalysisParams createSelective(Set<String> alarmNames, AlarmLevel minimumLevel) {
            RcaAnalysisParams params = new RcaAnalysisParams();
            params.setAlarmNameWhitelist(new HashSet<>(alarmNames));
            params.setMinimumAlarmLevel(minimumLevel);
            return params;
        }
    }
} 