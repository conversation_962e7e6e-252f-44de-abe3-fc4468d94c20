package us.zoom.cube.alarm.core.service.syspara;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.infra.dao.model.SysParaDO;
import us.zoom.infra.enums.SysParaEnums;
import us.zoom.infra.syspara.SysParaEventHandlerIntf;
import us.zoom.infra.syspara.SysParaEventService;
import us.zoom.infra.model.alarm.AlarmLevel;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import us.zoom.cube.alarm.core.config.AlarmRateLimitConfig;
import us.zoom.cube.alarm.core.config.RcaAnalysisConfig;

/**
 * @author: Roy.sun
 * @date: 2023/05/29
 **/
@Component
@Slf4j
public class AlarmParaService implements SysParaEventHandlerIntf {

    @Autowired
    private ObjectMapper mapper = new ObjectMapper();

    public static final String INCIDENT_CONFIG = "incidentConfig";

    private static final String ALARM_RATE_LIMIT_CONFIG = "alarmRateLimitConfig";

    private static final String RCA_ANALYSIS_CONFIG = "rcaAnalysisConfig";

    public static final String NOTIFY_CONTAINS_PREFIX = "notifyContainsPrefix";

    public static final String TEST_ALARM = "testAlarm";

    public static final String LAST_COUNT_CACHE_SINGLE_KEY_MAX_CAPACITY = "lastCountCacheSingleKeyMaxCapacity";

    public static final String ALARM_NOTIFY_GROUP_BY_TAGS = "alarmNotifyGroupByTags";

    public static final String AI_ORIGIN_RESULT_CLICKHOUSE_ENABLED = "aiOriginResultClickhouseEnabled";

    public static final String AI_METRICS_CLICKHOUSE_ENABLED = "aiMetricsClickhouseEnabled";

    private static final String REDIS_NEW_SWITCH = "redisNewSwitch";

    private static final String ALARM_SWITCH = "alarmSwitch";

    public static final String ALL = "all";


    private final AtomicReference<Map<String, String>> paramMap = new AtomicReference<>(new HashMap<>());

    private final AtomicReference<IncidentConfigValue> incidentConfigValueRef = new AtomicReference<>(null);

    private static final AtomicReference<List<String>> TEST_ALARM_LIST = new AtomicReference<>();

    private final AtomicReference<AlarmRateLimitConfig> alarmRateLimitConfigRef = new AtomicReference<>(null);

    private final AtomicReference<RcaAnalysisConfig> rcaAnalysisConfigRef = new AtomicReference<>(null);

    private final AtomicReference<AlarmSwitch> alarmSwitchRef = new AtomicReference<>(null);

    private final AtomicReference<NewRedisSwitch> newRedisSwitchRef = new AtomicReference<>(null);

    public AlarmParaService() {
        SysParaEventService.registeSysparaListener(this, Collections.singletonList(SysParaEnums.cubeAlarm.name()));
    }

    public void setIncidentConfigValue(IncidentConfigValue incidentConfigValue){
        this.incidentConfigValueRef.set(incidentConfigValue);
    }

    public boolean isTestAlarm(String alarmId) {
        try {
            return Optional.of(TEST_ALARM_LIST)
                    .map(AtomicReference::get)
                    .map(list -> list.contains(alarmId) || list.contains(ALL))
                    .orElse(false);
        } catch (Exception ignored) {
        }
        return false;
    }

    public boolean isNewRedisSwitch() {
        try {
            return newRedisSwitchRef.get().isNewRedis;
        } catch (Exception ignored){
        }
        return false;
    }

    public boolean isNewRedisSwitchForGroupByTag() {
        try {
            return newRedisSwitchRef.get().isNewRedisForGroupByTag;
        } catch (Exception ignored){
        }
        return false;

    }

    public boolean isNewRedisSwitchForRateLimit() {
        try {
            return newRedisSwitchRef.get().isNewRedisForRateLimit;
        } catch (Exception ignored){
        }
        return false;

    }

    public AlarmRateLimitConfig getAlarmRateLimitConfig() {
        return alarmRateLimitConfigRef.get();
    }

    public RcaAnalysisConfig getRcaAnalysisConfig() {
        return rcaAnalysisConfigRef.get();
    }

    /**
     * Check if RCA analysis should be performed for a specific alarm
     * Returns false if config is not loaded or any error occurs
     * 
     * @param serviceName service name
     * @param alarmName alarm name
     * @param alarmLevel alarm level
     * @param tags alarm tags
     * @return true if RCA analysis should be performed, false otherwise
     */
    public boolean shouldAnalyzeRca(String serviceName, String alarmName, AlarmLevel alarmLevel, Map<String, String> tags) {
        try {
            RcaAnalysisConfig rcaConfig = rcaAnalysisConfigRef.get();
            if (rcaConfig == null) {
                return false;
            }
            return rcaConfig.shouldAnalyze(serviceName, alarmName, alarmLevel, tags);
        } catch (Exception e) {
            log.error("Error checking RCA analysis for service: {}, alarm: {}", serviceName, alarmName, e);
            return false;
        }
    }

    public AlarmSwitch getAlarmSwitch() {
        return alarmSwitchRef.get();
    }

    public boolean needToBlockIncident(String serviceName, String alarmName) {
        try {
            if(StringUtils.isEmpty(serviceName) || StringUtils.isEmpty(alarmName)){
                return false;
            }
            IncidentConfigValue incidentConfigValue = incidentConfigValueRef.get();
            if(incidentConfigValue == null){
                return false;
            }

            if(!incidentConfigValue.isValid()){
                return false;
            }
            for(IncidentConfigBlackValue incidentConfigBlackValue: incidentConfigValue.getIncidentConfigBlackValueList()){
                if(incidentConfigBlackValue == null){
                    continue;
                }
                if(!incidentConfigBlackValue.isValid()){
                    continue;
                }
                if(!StringUtils.equals(incidentConfigBlackValue.getServiceName(), serviceName)){
                    continue;
                }
                for(String alarmNameKeyword: incidentConfigBlackValue.getAlarmNameKeywordList()){
                    if(alarmName.contains(alarmNameKeyword)){
                        return true;
                    }
                }

            }
            return false;
        } catch (Exception e) {
            log.error("needToBlockIncident serviceName:{}, alarmName:{}", serviceName, alarmName, e);
            return false;
        }
    }

    public boolean getParamBooleanValue(String key, boolean defaultValue) {
        try {
            String paramValue = paramMap.get().get(key);
            if (StringUtils.isEmpty(paramValue)) {
                return defaultValue;
            }
            return Boolean.parseBoolean(paramValue.trim());
        } catch (Exception e) {
            log.error("AlarmParaService convert system param value to boolean failed for key:{}", key);
        }
        return defaultValue;
    }

    public Long getParamLongValue(String key, Long defaultValue) {
        try {
            String paramValue = paramMap.get().get(key);
            if (StringUtils.isEmpty(paramValue)) {
                return defaultValue;
            }
            return Long.parseLong(paramValue.trim());
        } catch (Exception e) {
            log.error("AlarmParaService convert system param value to long failed for key:{}", key);
        }
        return defaultValue;
    }

    public int getParamIntValue(String key, int defaultValue) {
        try {
            String paramValue = paramMap.get().get(key);
            if (StringUtils.isEmpty(paramValue)) {
                return defaultValue;
            }
            return Integer.parseInt(paramValue.trim());
        } catch (Exception e) {
            log.error("AlarmParaService convert system param value to integer failed for key:{}", key);
        }
        return defaultValue;
    }

    @Override
    public boolean onEventWhenHasData(List<SysParaDO> sysParas) {

        for (SysParaDO s : sysParas) {
            if (StringUtils.equals(s.getParaKey(), TEST_ALARM)) {
                try {
                    List<String> list = mapper.readValue(s.getValue(), List.class);
                    TEST_ALARM_LIST.set(list);
                } catch (Exception e) {
                    log.error("Error when parse alarm sysPara TEST_ALARM, SysParaDO:{}", s, e);
                }
            }
        }

        try{
            log.info("refresh cubeAlarm system param");
            Map<String, String> temp = Optional.of(sysParas).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(SysParaDO::getParaKey, SysParaDO::getValue, (a, b) -> a));
            paramMap.set(temp);
        } catch (Exception e){
            log.error("init cubeAlarm system param exception, e",e);
        }

        try {
            String paramValue = paramMap.get().get(INCIDENT_CONFIG);
            if (StringUtils.isEmpty(paramValue)) {
                return true;
            }
            IncidentConfigValue incidentConfigValue = JsonUtils.toObject(paramValue, IncidentConfigValue.class);
            if (incidentConfigValue != null) {
                incidentConfigValueRef.set(incidentConfigValue);
            }
        } catch (Exception e) {
            log.error("init incidentConfig param exception, e", e);
        }

        try {
            String paramValue = paramMap.get().get(ALARM_RATE_LIMIT_CONFIG);
            if (StringUtils.isEmpty(paramValue)) {
                return true;
            }
            AlarmRateLimitConfig alarmRateLimitConfig = JsonUtils.toObject(paramValue, AlarmRateLimitConfig.class);
            if (alarmRateLimitConfig != null) {
                alarmRateLimitConfigRef.set(alarmRateLimitConfig);
            }
        } catch (Exception e) {
            log.error("init alarmRateLimitConfig param exception, e", e);
        }

        try {
            String paramValue = paramMap.get().get(RCA_ANALYSIS_CONFIG);
            if (StringUtils.isEmpty(paramValue)) {
                return true;
            }
            RcaAnalysisConfig rcaAnalysisConfig = JsonUtils.toObject(paramValue, RcaAnalysisConfig.class);
            if (rcaAnalysisConfig != null) {
                rcaAnalysisConfigRef.set(rcaAnalysisConfig);
                log.info("init rcaAnalysisConfig, value:{}", rcaAnalysisConfig);
            }
        } catch (Exception e) {
            log.error("init rcaAnalysisConfig param exception, e", e);
        }

        try {
            String paramValue = paramMap.get().get(REDIS_NEW_SWITCH);
            if (StringUtils.isEmpty(paramValue)) {
                return true;
            }
            NewRedisSwitch newRedisSwitch = JsonUtils.toObject(paramValue, NewRedisSwitch.class);
            if (newRedisSwitch != null) {
                newRedisSwitchRef.set(newRedisSwitch);
            }
        } catch (Exception e) {
            log.error("init newRedisSwitch param exception, e", e);
        }

        try {
            String paramValue = paramMap.get().get(ALARM_SWITCH);
            if (StringUtils.isEmpty(paramValue)) {
                return true;
            }
            AlarmSwitch alarmSwitch = JsonUtils.toObject(paramValue, AlarmSwitch.class);
            if (alarmSwitch != null) {
                alarmSwitchRef.set(alarmSwitch);
                log.info("init alarmSwitchRef, value:{}", alarmSwitch);
            }
        } catch (Exception e) {
            log.error("init alarmSwitchRef param exception, e", e);
        }
        return true;
    }

    @Override
    public boolean onEventWhenNoData() {
        return false;
    }

    @Data
    public static class AlarmSwitch {
        private List<String> groupByTagCheckPermissionServiceList = new ArrayList<>();
        private boolean groupByTagNeedPermission = true;
    }

    @Data
    public static class NewRedisSwitch {

        private boolean isNewRedis;
        private boolean isNewRedisForGroupByTag = false;
        private boolean isNewRedisForRateLimit = true;
    }

    @Data
    public static class IncidentConfigValue{
        private List<IncidentConfigBlackValue> incidentConfigBlackValueList;
        public boolean isValid(){
            return !CollectionUtils.isEmpty(incidentConfigBlackValueList);
        }
    }

    @Data
    public static class IncidentConfigBlackValue{
        private String serviceName;
        private List<String> alarmNameKeywordList;
        public boolean isValid(){
            return (!StringUtils.isEmpty(serviceName)) && (!CollectionUtils.isEmpty(alarmNameKeywordList));
        }
    }
}
