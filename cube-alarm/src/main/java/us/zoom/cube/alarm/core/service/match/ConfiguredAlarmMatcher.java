package us.zoom.cube.alarm.core.service.match;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zoom.op.monitor.domain.alarm.AlarmExtensionRelation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.alarm.biz.service.AlarmHandler;
import us.zoom.cube.alarm.core.config.ConfigCache;
import us.zoom.cube.alarm.core.model.alarm.config.AlarmCfg;
import us.zoom.cube.alarm.core.model.alarm.config.AlarmRuleCfg;
import us.zoom.cube.alarm.core.model.alarm.config.Tenant;
import us.zoom.cube.alarm.core.model.alarm.match.AlarmMatchRequest;
import us.zoom.cube.alarm.core.model.alarm.match.AlarmRuleMatchRequest;
import us.zoom.cube.alarm.core.model.alarm.match.AlarmRuleMatchResult;
import us.zoom.cube.alarm.core.model.cache.AlarmImg;
import us.zoom.cube.alarm.core.model.cache.AlarmImgKey;
import us.zoom.cube.alarm.core.model.cache.AlarmImgList;
import us.zoom.cube.alarm.core.model.metrics.MetricsCfg;
import us.zoom.cube.alarm.core.service.cache.LocalCacheHolder;
import us.zoom.cube.alarm.core.service.syspara.AlarmParaService;
import us.zoom.cube.alarm.infra.LogSampling;
import us.zoom.cube.alarm.util.LogUtils;
import us.zoom.cube.lib.agent.AlarmSelfMonitorTypeEnum;
import us.zoom.cube.lib.integrations.Metrics;
import us.zoom.cube.lib.integrations.MetricsField;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.sdk.util.MonitorLogReporter;
import us.zoom.infra.enums.AlarmSourceTypeEnum;
import us.zoom.infra.enums.MetricsTypeEnum;
import us.zoom.infra.influx.model.alarm.AlarmMatchRecord;
import us.zoom.infra.influx.model.alarm.BaseAlarmMatch;
import us.zoom.infra.influx.util.TagUtils;
import us.zoom.infra.loader.PiiTableLoader;
import us.zoom.infra.model.aggregation.MetricsTenant;
import us.zoom.infra.model.alarm.AlarmLevel;
import us.zoom.infra.monitor.AlarmSelfMonitor;
import us.zoom.infra.utils.Instance;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Junjian
 * @create 2019/12/4 8:56
 */
@Component
@Slf4j
@SuppressWarnings("rawtypes")
public class ConfiguredAlarmMatcher {

    private static final AtomicLong LOG_INDEX = new AtomicLong();
    private static final AtomicLong ERROR_LOG_INDEX = new AtomicLong();

    @Autowired
    private ConfigCache configCache;

    @Autowired
    private AlarmRuleMatcher alarmRuleMatcher;

    @Autowired
    private LocalCacheHolder localCacheHolder;

    @Autowired
    private PiiTableLoader piiTableLoader;

    @Autowired
    private AlarmParaService alarmParaService;

    private final Logger monitorLog = LoggerFactory.getLogger("Monitor");

    public static final String ALARM_NEED_HITS= "alarmNeedHits";

    public static final String ALARM_TIMES_IN_PERIOD = "alarmTimesInPeriod";

    private final int DEFAULT_LAST_COUNT_CACHE_SINGLE_KEY_MAX_CAPACITY = 1000;
    /**
     *
     * @param request request
     * @return result map: alarmId -> List<AlarmMatchRecord>
     */
    public Map<String, List<AlarmMatchRecord>> calculate(AlarmMatchRequest request) {
        Map<String, List<AlarmMatchRecord>> alarmMatchRecordsMap = Maps.newHashMap();

        //One metrics may have multiple alarms.
        List<AlarmCfg> alarms = Optional.ofNullable(AlarmHandler.alarmNewStatContextThreadLocal.get())
                .map(AlarmHandler.AlarmNewStatContext::getAlarmCfgList)
                .orElse(null);
        if (CollectionUtils.isEmpty(alarms)) {
            return alarmMatchRecordsMap;
        }

        Objects.requireNonNull(alarms).forEach(alarm -> {
            List<AlarmMatchRecord> alarmMatchRecord = calculateSingleAlarm(request, alarm);
            if (alarmMatchRecord != null) {
                alarmMatchRecordsMap.put(alarm.getId(), alarmMatchRecord);
            }
            Metrics metrics = Optional.of(request).map(AlarmMatchRequest::getMetricsList).filter(list -> !list.isEmpty()).map(list -> list.get(list.size() - 1)).orElse(null);
            clearLastCountCache(metrics, alarm, alarmMatchRecord);
        });
        return alarmMatchRecordsMap;
    }

    private void clearLastCountCache(Metrics metrics, AlarmCfg alarm, List<AlarmMatchRecord> alarmMatchRecords) {

        if (Objects.isNull(metrics) || CollectionUtils.isEmpty(alarm.getAlarmRuleCfgs())) {
            return;
        }
        String serviceName = Optional.ofNullable(configCache.getMetricsTenantByMetricsId(metrics.getMetricsId())).map(MetricsTenant::getTenantName).orElse(null);
        Set<String> matchRules = Optional.ofNullable(alarmMatchRecords).stream()
                .flatMap(List::stream)
                .map(AlarmMatchRecord::getAlarmRuleId)
                .collect(Collectors.toSet());
        //clearMap: K=alarmImgKey V=timestamp
        Map<AlarmImgKey, Long> clearMap = new HashMap<>(alarm.getAlarmRuleCfgs().size());
        for (AlarmRuleCfg alarmRuleCfg : alarm.getAlarmRuleCfgs()) {
            AlarmImgKey alarmImgKey = generateAlarmImgKey(alarmRuleCfg, metrics, alarm);
            //default clear exceed time window records(round down).
            long ts = metrics.getTs() - alarmRuleCfg.getTimeWindow() * 1000;
            //need continuous and not hit alarm rule, clear records by current metric time.
            if(alarmRuleCfg.isContinuous() && !matchRules.contains(alarmRuleCfg.getId())) {
                ts = Math.max(metrics.getTs(), ts);
            }
            clearMap.put(alarmImgKey, ts);
        }
        clearMap.forEach((key, value) -> {
            AlarmImgList alarmImgList = localCacheHolder.getLastCountCache().getIfPresent(key);
            if (Objects.isNull(alarmImgList)) {
                return;
            }
            alarmImgList.deleteBeforeTime(value);
            //Check current capacity, clean up and monitor if it exceeds the limit
            if (alarmImgList.getSize() > alarmParaService.getParamIntValue(AlarmParaService.LAST_COUNT_CACHE_SINGLE_KEY_MAX_CAPACITY, DEFAULT_LAST_COUNT_CACHE_SINGLE_KEY_MAX_CAPACITY)) {
                alarmImgList.deleteBeforeTime(System.currentTimeMillis());
                String monitorInfo = String.format("Clean lastCountCache service = %s, alarm = %s, alarmRuleId = %s, currentImgSize = %s", serviceName, alarm.getAlarmName(), key.getAlarmRuleId(), alarmImgList.getSize());
                MonitorLogReporter.report(monitorLog, new AlarmSelfMonitor(AlarmSelfMonitorTypeEnum.clearLastCountCache.name(), monitorInfo));
            }
        });
    }

    private AlarmImgKey generateAlarmImgKey(AlarmRuleCfg alarmRuleCfg, Metrics metrics, AlarmCfg alarm) {
        Map<String, String> tags = BaseAlarmMatch.createFetchTags(
                Optional.ofNullable(alarmRuleCfg).map(AlarmRuleCfg::getAlarmLevel).map(AlarmLevel::getLevel).orElse(null),
                alarm.getId(), Objects.requireNonNull(alarmRuleCfg).getId(), metrics.getTags());
        return new AlarmImgKey(alarmRuleCfg.getId(), TagUtils.createKey(tags));
    }

    private List<AlarmMatchRecord> calculateSingleAlarm(AlarmMatchRequest request, AlarmCfg alarm) {

        List<Metrics> metricsList = request.getMetricsList();
        int listSize = metricsList.size();
        Metrics metrics = metricsList.get(listSize - 1);
        Map<String, String> tags = metrics.getTags();
        List<String> groupTags = alarm.getGroupTags();

        //AlarmCfgAggregationCfg
        String metricsId = request.getMetricsId();
        MetricsCfg metricsCfg = configCache.getMetricsCfgCache().get(metricsId);
        if (metricsCfg == null) {
            return null;
        }

        //AlarmCfgAlarmRuleCfg
        List<AlarmRuleCfg> alarmRuleCfgs = alarm.getAlarmRuleCfgs();
        if (CollectionUtils.isEmpty(alarmRuleCfgs)) {
            return null;
        }

        List<AlarmMatchRecord> alarmMatchRecordList = Lists.newArrayList();
        //AlarmRule，ConcurrentModificationException
        for (AlarmRuleCfg alarmRuleCfg : alarmRuleCfgs) {
            try {
                AlarmRuleMatchRequest alarmRuleMatchRequest = new AlarmRuleMatchRequest();
                alarmRuleMatchRequest.setAlarmRuleCfg(alarmRuleCfg);
                alarmRuleMatchRequest.setMetricsId(metricsId);
                alarmRuleMatchRequest.setMetricsList(request.getMetricsList());
                alarmRuleMatchRequest.setIgnoreAiCondition(false);
                AlarmRuleMatchResult alarmRuleMatchResult = alarmRuleMatcher.isMatch(alarmRuleMatchRequest);
                boolean isAlarmRuleMatched = alarmRuleMatchResult.isMatched();
                if (!isAlarmRuleMatched) {
                    continue;
                }
                //alarm matched
                AlarmMatchRecord alarmMatchRecord = new AlarmMatchRecord();
                alarmMatchRecord.setTime(request.getTimestamp());
                alarmMatchRecord.setNoData(metrics.isNoData());
                alarmMatchRecord.setAlarmSourceType(metrics.isDerived() ? AlarmSourceTypeEnum.SQL :AlarmSourceTypeEnum.CONFIGURED);
                alarmMatchRecord.setMetricsId(request.getMetricsId());
                alarmMatchRecord.setMetricsType(Optional.of(metricsCfg).map(MetricsCfg::getType).map(MetricsTypeEnum::getValue).orElse(null));
                alarmMatchRecord.setAggPeriod(null != metricsCfg.getMetricsAggregationCfg() ? metricsCfg.getMetricsAggregationCfg().getAggPeriod() : null);
                alarmMatchRecord.setSubPeriod(null != metricsCfg.getMetricsAggregationCfg() ? metricsCfg.getMetricsAggregationCfg().getSubPeriod() : null);
                alarmMatchRecord.setAggTimeZone(null != metricsCfg.getMetricsAggregationCfg() ? metricsCfg.getMetricsAggregationCfg().getAggTimeZone() : null);
                alarmMatchRecord.setAlarmName(alarm.getAlarmName());
                alarmMatchRecord.setInhibitInterval(alarm.getInhibitInterval());
                alarmMatchRecord.setInhibitThreshold(alarm.getInhibitThreshold());
                alarmMatchRecord.setMetricsName(metrics.getMetricsName());
                alarmMatchRecord.setMetricFields(convertMetricFields(metrics.getFields(),alarmRuleMatchResult));
                alarmMatchRecord.setTitle(alarm.getTitle());
                alarmMatchRecord.setAlarmId(alarm.getId());
                alarmMatchRecord.setIsNoticed(false);
                alarmMatchRecord.setAlarmLevel(alarmRuleMatchResult.getAlarmLevel());
                alarmMatchRecord.setAlarmRuleId(alarmRuleCfg.getId());
                alarmMatchRecord.setHittedRule(alarmRuleCfg.getAlarmRule());
                alarmMatchRecord.setMetricsName(request.getName());
                alarmMatchRecord.setMetricsTags(tags);
                //add tagKey, groupTagKey and notifyGroupKey
                alarmMatchRecord.setKeys(tags, groupTags);
                MetricsTenant metricsTenant = configCache.getMetricsTenantByMetricsId(metricsId);
                alarmMatchRecord.setTenantName(metricsTenant.getTenantName());
                List<AlarmExtensionRelation> alarmExtensionRelations = alarm.getAlarmExtensionRelations().stream().map(AlarmExtensionRelation::copy).toList();
                alarmMatchRecord.setAlarmExtensionRelations(alarmExtensionRelations);
                alarmMatchRecord.setLastCount(alarmRuleCfg.getLastCount());
                alarmMatchRecord.setTenantId(alarm.getTenantId());
                AlarmMatchRecord.RecordExtendInfo extendInfo = new AlarmMatchRecord.RecordExtendInfo();
                AlarmHandler.AlarmNewStatContext alarmNewStatContext = AlarmHandler.alarmNewStatContextThreadLocal.get();

                // Extend info.
                Long inputTime = Optional.ofNullable(AlarmHandler.alarmNewStatContextThreadLocal.get())
                        .map(AlarmHandler.AlarmNewStatContext::getInputTime)
                        .orElse(null);
                extendInfo.setInputTime(inputTime);

                //validate pii
                Tenant tenant = configCache.getTenantMap().get(metricsCfg.getTenantId());
                if(null != tenant && piiTableLoader.existPii(tenant.getName(), metricsCfg.getMetricsName())) {
                    alarmMatchRecord.setContainsPii(true);
                }
                //Modify the id generation rules
                alarmMatchRecord.generateId();
                //add histogram result->AlarmAction.getVarMapFromRecord
                alarmMatchRecord.setHistogramResult(alarmRuleMatchResult.getHistogramResult());
                alarmMatchRecordList.add(alarmMatchRecord);
            } catch (Exception e) {
                LogUtils.logCalcError("Alarm single rule match calc error, alarm rule:{}", JsonUtils.toJsonStringIgnoreExp(alarmRuleCfg), e);
            }
        }

        if (CollectionUtils.isEmpty(alarmMatchRecordList)) {
            return null;
        }
        try {
            batchAddToLastCountCache(alarmMatchRecordList);
        } catch (Exception e) {
            LogUtils.logUpdateLastCountError("updateLastCount error! AlarmMatchRecord: {}. ", JsonUtils.toJsonStringIgnoreExp(alarmMatchRecordList), e);
        }
        return alarmMatchRecordList;
    }

    private Map<String, Object> convertMetricFields(List<MetricsField> fields, AlarmRuleMatchResult alarmRuleMatchResult) {
        if(CollectionUtils.isEmpty(fields) && CollectionUtils.isEmpty(alarmRuleMatchResult.getContext())){
            return MapUtils.EMPTY_MAP;
        }
        Map<String,Object> result=new HashMap<>();

        if(!CollectionUtils.isEmpty(fields)){
            for(MetricsField metricsField:fields){
                result.put(metricsField.getFieldName(),metricsField.getFieldValue());
            }
        }

        if(!CollectionUtils.isEmpty(alarmRuleMatchResult.getContext())){
            result.putAll(alarmRuleMatchResult.getContext());
        }

        return result;
    }

    private void batchAddToLastCountCache(List<AlarmMatchRecord> alarmMatchRecordList) {
        try {
            Instance.ofNullable(alarmMatchRecordList).forEach(record -> {
                AlarmImgKey alarmImgKey = new AlarmImgKey(record.getAlarmRuleId(), TagUtils.createKey(record.fetchTags()));
                AlarmImgList alarmImgList = localCacheHolder.getLastCountCache().getIfPresent(alarmImgKey);
                if (null != alarmImgList) {
                    alarmImgList.add(new AlarmImg(record.getTime()));
                } else {
                    alarmImgList = new AlarmImgList();
                    alarmImgList.add(new AlarmImg(record.getTime()));
                    localCacheHolder.getLastCountCache().put(alarmImgKey, alarmImgList);
                }
            });
        } catch (Exception e) {
            logError("batchAddToLastCountCache error! AlarmMatchRecord: ", alarmMatchRecordList, e);

        }
    }

    private void printAbnormalLog(AlarmMatchRequest request, AlarmCfg alarm) {
        if (LOG_INDEX.incrementAndGet() % LogSampling.getInfoSamplingCount() == 0) {
            log.info("[ConfiguredAlarmMatcher#calculateSingleAlarm] is paused! request is: {}, alarm is: {}", request, alarm);
        }
        LOG_INDEX.compareAndSet(Long.MAX_VALUE, 0);
    }

    private void printCycleLog(AlarmMatchRequest request, AlarmCfg alarm) {
        if (LOG_INDEX.get() % LogSampling.getInfoSamplingCount() == 0) {
            log.info("[ConfiguredAlarmMatcher#calculateSingleAlarm] not in alarm cycle! request is: {}, alarm is: {}", request, alarm);
        }
        LOG_INDEX.compareAndSet(Long.MAX_VALUE, 0);
    }

    private void printTimePeriodLog(AlarmMatchRequest request, AlarmCfg alarm) {
        if (LOG_INDEX.incrementAndGet() % LogSampling.getInfoSamplingCount() == 0) {
            log.info("[ConfiguredAlarmMatcher#calculateSingleAlarm] not in time period! request is: {}, alarm is: {}", request, alarm);
        }
        LOG_INDEX.compareAndSet(Long.MAX_VALUE, 0);
    }


    private void logError(String prefix, Object obj, Exception e) {
        if(ERROR_LOG_INDEX.incrementAndGet() % LogSampling.getErrorSamplingCount() == 0){
            log.error("{}{}", prefix, JsonUtils.toJsonStringIgnoreExp(obj), e);
        }
        ERROR_LOG_INDEX.compareAndSet(Long.MAX_VALUE,0);
    }
}
