package us.zoom.cube.alarm.core.config;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zoom.op.monitor.domain.alarm.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.alarm.core.model.alarm.config.AdCfg;
import us.zoom.cube.alarm.core.model.alarm.config.AlarmCfg;
import us.zoom.cube.alarm.core.model.alarm.config.AlarmRuleCfg;
import us.zoom.cube.config.client.api.ConfigApi;
import us.zoom.cube.lib.integrations.MetricsFieldTypeEnum;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.infra.dao.model.CollectorFieldDO;
import us.zoom.infra.dao.model.CollectorMetricsDO;
import us.zoom.infra.dao.model.MetricsFieldDO;
import us.zoom.infra.enums.FieldTypeEnum;
import us.zoom.infra.model.alarm.AlarmLevel;
import us.zoom.infra.model.alarm.AlarmRuleSingleContent;
import us.zoom.infra.model.alarm.CompareType;
import us.zoom.infra.utils.Instance;
import us.zoom.cube.alarm.core.model.alarm.config.Tenant;
import us.zoom.infra.enums.AlarmOperatorEnum;
import java.util.*;
import java.util.stream.Collectors;

import static us.zoom.cube.alarm.core.config.ConfigCache.DEFAULT_PERIOD_UNIT;
import static us.zoom.infra.utils.AlarmConstants.CHAINED_RATIO_DECREASE_EXPR;
import static us.zoom.infra.utils.AlarmConstants.CHAINED_RATIO_INCREASE_EXPR;

/**
 * <AUTHOR> Junjian
 * @create 2019/10/21 10:51 
 */
@Component
@Slf4j
public class AlarmCacheLoader {

    private static final Logger LOG = LoggerFactory.getLogger(AlarmCacheLoader.class);

    @Autowired
    private ConfigCache configCache;

    @Autowired
    private ConfigApi configApi;

    void loadCache() {
        long begin = System.currentTimeMillis();

        LOG.info("Begin loading alarms to configCache!");

        List<AlarmDefinition> alarmDefinitions = configApi.getAllAlarmDefinitions();
        //This api cube-config has restrictions on returning data of pi type and histogram type
        List<CollectorFieldDO> fieldDOList = configApi.getAllCollectorFields().stream()
                .filter(f -> Objects.equals(f.getFieldType(), FieldTypeEnum.histogram.name())).collect(Collectors.toList());
        List<CollectorMetricsDO> collectorMetricsDOList = configApi.getAllCollectorMetrics();
        log.info("alarmDefinitions size = {}", alarmDefinitions.size());
        if (CollectionUtils.isEmpty(alarmDefinitions)) {
            return;
        }
        List<MetricsFieldDO> metricsFieldDOList = configApi.getAllMetricFields();
        Map<String, AlarmCfg> alarmCfgMap = createAlarmCfg(alarmDefinitions, metricsFieldDOList, fieldDOList, collectorMetricsDOList);
        configCache.setAlarmCache(alarmCfgMap);

        Map<String, AlarmRuleCfg> alarmRuleCfgMap = createAlarmRuleMap(alarmCfgMap);

        configCache.setAlarmRuleCache(alarmRuleCfgMap);

        LOG.info("Loading alarms to configCache finished, time cost = {}", (System.currentTimeMillis() - begin));
    }

    private Map<String, AlarmCfg> createAlarmCfg(List<AlarmDefinition> alarmDefinitions, List<MetricsFieldDO> metricsFieldDOList, List<CollectorFieldDO> fieldDOList, List<CollectorMetricsDO> collectorMetricsDOList) {

        Map<String, AlarmCfg> alarmCfgs = Maps.newHashMap();

        Map<String, Map<String, MetricsFieldDO>> metricsFiledMap = new HashMap<>();

        Instance.ofNullable(metricsFieldDOList).forEach(metricsFieldDO -> {
            if (!metricsFiledMap.containsKey(metricsFieldDO.getMetricsId())) {
                metricsFiledMap.put(metricsFieldDO.getMetricsId(), new HashMap<>());
            }

            metricsFiledMap.get(metricsFieldDO.getMetricsId()).put(metricsFieldDO.getFieldName(), metricsFieldDO);
        });

        Map<String, List<CollectorFieldDO>> metricsCollectorFieldMap = new HashMap<>();
        try {
            Map<String, List<CollectorMetricsDO>> collectorMetricsMap = Instance.ofNullable(collectorMetricsDOList).stream()
                    .collect(Collectors.groupingBy(CollectorMetricsDO::getMetricsId));
            if (!CollectionUtils.isEmpty(collectorMetricsMap)) {
                collectorMetricsMap.forEach((k, v) -> {
                    List<String> collectIdList = v.stream().map(CollectorMetricsDO::getCollectorId).distinct().toList();
                    List<CollectorFieldDO> collectorFieldDOList = Instance.ofNullable(fieldDOList).stream().filter(f -> collectIdList.contains(f.getCollectorId())).collect(Collectors.toList());
                    metricsCollectorFieldMap.put(k, collectorFieldDOList);
                });

            }
        } catch (Exception e) {
            log.error(" collectorField error:{}", e.getMessage(), e);
        }


        alarmDefinitions.forEach(alarmDefinition -> {
            try {
                if (!BooleanUtils.isTrue(alarmDefinition.getEnabled()) ||
                        CollectionUtils.isEmpty(alarmDefinition.getRules()) ||
                        !AlarmConfigStatusEnum.APPROVED.equals(alarmDefinition.getStatus())) {
                    return;
                }
                Map<String, MetricsFieldDO> metricsFields = metricsFiledMap.get(alarmDefinition.getMetricId());
                if (metricsFields == null) {
                    return;
                }
                Integer period = configCache.getMetricsPeriod(alarmDefinition.getMetricId());
                List<AlarmRuleCfg> alarmRuleCfgs = Lists.newArrayList();
                List<CollectorFieldDO> collectorFieldDOList = metricsCollectorFieldMap.get(alarmDefinition.getMetricId());
                alarmDefinition.getRules().forEach(alarmRule -> {
                    if (alarmRule == null) {
                        return;
                    }
                    if (alarmRule.getNeedHits() == null) {
                        alarmRule.setNeedHits(1);
                    }
                    AlarmRuleCfg alarmRuleCfg = transformRuleCfg(alarmRule, period, metricsFields, collectorFieldDOList);
                    alarmRuleCfg.setAlarmId(alarmDefinition.getId());
                    alarmRuleCfgs.add(alarmRuleCfg);
                });

                AlarmCfg alarmCfg = transformAlarmCfg(alarmDefinition, alarmRuleCfgs);
                alarmRuleCfgs.sort(Comparator.comparingInt(o -> o.getAlarmLevel().getValue()));

                alarmCfgs.put(alarmDefinition.getId(), alarmCfg);
            } catch (Exception e) {
                try {
                    log.error("transform Alarm cfg error, alarm id = {}, alarm name = {}, service = {}",
                            alarmDefinition.getId(),
                            alarmDefinition.getName(),
                            Optional.ofNullable(configCache.getTenantMap()).map(tenantMap -> tenantMap.get(alarmDefinition.getTenantId())).map(Tenant::getName).orElse("null"), e);
                } catch (Exception ex) {
                    log.error("transform Alarm cfg error, alarm id = {}, alarm name = {}", alarmDefinition.getId(), alarmDefinition.getName(), e);
                }
            }
        });

        return alarmCfgs;
    }

    private AlarmRuleCfg transformRuleCfg(AlarmRule alarmRule, Integer metricsPeriod, Map<String, MetricsFieldDO> metricsFiledMap, List<CollectorFieldDO> collectorFieldDOList) {
        if (metricsPeriod == null || metricsPeriod <= 0) {
            metricsPeriod = DEFAULT_PERIOD_UNIT;
        }
        AlarmRuleCfg alarmRuleCfg = new AlarmRuleCfg();
        alarmRuleCfg.setId(alarmRule.getId());
        alarmRuleCfg.setLastCount(alarmRule.getNeedHits());
        alarmRuleCfg.setHitCount(alarmRule.getHitCount() == null ? alarmRule.getNeedHits() : alarmRule.getHitCount());
        alarmRuleCfg.setAlarmLevel(getAlarmLevel(alarmRule));
        alarmRuleCfg.setAlarmRule(alarmRule);
        alarmRuleCfg.setTimeWindow(Optional.ofNullable(alarmRule.getTimeWindow()).orElse(metricsPeriod * alarmRule.getNeedHits()));
        alarmRuleCfg.setContinuous(alarmRule.getIsContinuous());

        List<AlarmRuleSingleContent> alarmRuleSingleContents = new ArrayList<>();

        int finalMetricsPeriod = metricsPeriod;

        List<RuleCondition> extendRuleConditions = new ArrayList<>();
        //new config -> time window & isContinuous
        Instance.ofNullable(alarmRule.getConditions()).forEach(ruleCondition -> {
            AlarmRuleSingleContent singleContent = new AlarmRuleSingleContent();
            CompareType compareType = getCompareType(ruleCondition);
            singleContent.setType(compareType);
            String fieldName = ruleCondition.getName();
            if (CompareType.TAG_COMPARE.equals(compareType)) {
                singleContent.setTag(fieldName);
                singleContent.setFieldType(FieldTypeEnum.string);
            } else if (CompareType.FUNC_COMPARE.equals(compareType)) {
                singleContent.setExpression(ruleCondition.getExpression());
            } else if (CompareType.CHAIN_RATIO.equals(compareType)) {
                singleContent.setExpression(ruleCondition.getName() + " " + ruleCondition.getOperator() + "  " + ruleCondition.getThreshold());
                singleContent.setField(ruleCondition.getName());
                singleContent.setType(compareType);
                singleContent.setFieldType(FieldTypeEnum.number);
            } else if (CompareType.AI_COMPARE.equals(compareType)) {
                if (StringUtils.isEmpty(ruleCondition.getExtension())) {

                    AlarmRuleSingleContent.AlarmRuleConditionExtension extension = new AlarmRuleSingleContent.AlarmRuleConditionExtension();
                    AdCfg adCfg = configCache.getAdCfgMap().get(ruleCondition.getExpression());
                    AlarmRuleSingleContent.DetectModeEnum detectModeEnum = AlarmRuleSingleContent.fromAnomalyDirectionType(adCfg.getAnomalyDirectionType());
                    extension.setDetectMode(detectModeEnum);
                    singleContent.setExtension(extension);
                    ruleCondition.setExtension(JsonUtils.toJsonStringIgnoreExp(extension));
                    if (StringUtils.isNoneBlank(adCfg.getRollingAlertSensitivityNum())) {
                        alarmRuleCfg.setHitCount(Integer.parseInt(adCfg.getRollingAlertSensitivityNum()));
                        alarmRule.setHitCount(Integer.parseInt(adCfg.getRollingAlertSensitivityNum()));
                    }
                    if (StringUtils.isNoneBlank(adCfg.getRollingAlertSensitivityAll())) {
                        alarmRuleCfg.setLastCount(Integer.parseInt(adCfg.getRollingAlertSensitivityAll()));
                        alarmRule.setNeedHits(Integer.parseInt(adCfg.getRollingAlertSensitivityAll()));
                    }
                    alarmRuleCfg.setContinuous(false);

                    if (StringUtils.isNoneBlank(adCfg.getThresValueFilterUp()) || StringUtils.isNoneBlank(adCfg.getThresValueFilterDown())) {
                        //add new condition
                        AlarmRuleSingleContent appendSingleContent = new AlarmRuleSingleContent();
                        appendSingleContent.setType(CompareType.FUNC_COMPARE);
                        String expression = AlarmRuleSingleContent.buildUserDefinedThresholdExpression(adCfg.getFieldName(), adCfg.getThresValueFilterUp(), adCfg.getThresValueFilterDown());
                        appendSingleContent.setExpression(expression);
                        alarmRuleSingleContents.add(appendSingleContent);
                        //add new condition in alarmRule
                        RuleCondition rule = new RuleCondition();
                        rule.setConditionType(ConditionType.EXPRESSION);
                        rule.setExpression(expression);
                        extendRuleConditions.add(rule);
                    }
                    alarmRuleCfg.setTimeWindow(finalMetricsPeriod * alarmRuleCfg.getLastCount());
                    alarmRule.setTimeWindow(finalMetricsPeriod * alarmRuleCfg.getLastCount());
                } else {
                    //add extension
                    AlarmRuleSingleContent.AlarmRuleConditionExtension extension = JsonUtils.toObject(ruleCondition.getExtension(), AlarmRuleSingleContent.AlarmRuleConditionExtension.class);
                    singleContent.setExtension(extension);
                }
                singleContent.setExpression(ruleCondition.getExpression());
                singleContent.setField(fieldName);
                singleContent.setFieldType(getFieldTypeEnum(metricsFiledMap.get(fieldName)));
            } else {
                FieldTypeEnum fieldTypeEnum =  determineFieldType(ruleCondition, metricsFiledMap);
                if (fieldTypeEnum == null) {
                    return;
                }
                singleContent.setField(fieldName);
                singleContent.setFieldType(fieldTypeEnum);
                singleContent.setExpression(ruleCondition.getExpression());
            }
            singleContent.setOperator(ruleCondition.getOperator());
            singleContent.setCmpValue(ruleCondition.getThreshold());

            //add histogram
            MetricsFieldDO metricsField = metricsFiledMap.get(fieldName);
            if (!Objects.isNull(metricsField)) {
                MetricsFieldTypeEnum metricsFieldTypeEnum = MetricsFieldTypeEnum.fromValue(metricsField.getFieldType());
                if (Objects.equals(metricsFieldTypeEnum, MetricsFieldTypeEnum.histogram) && !CollectionUtils.isEmpty(collectorFieldDOList)) {
                    singleContent.setHistogramQuantile(ruleCondition.getHistogramQuantile());
                    singleContent.setFieldType(FieldTypeEnum.histogram);
                    //find histogram
                    Optional<CollectorFieldDO> collectorFieldDO = collectorFieldDOList.stream()
                            .filter(f -> StringUtils.isNotBlank(f.getFieldSchema()) && Objects.equals(f.getSourceField(), ruleCondition.getName()))
                            .findFirst();
                    collectorFieldDO.ifPresent((collectorField) -> singleContent.setHistogramBucket(collectorField.getFieldSchema()));
                // add mapString and mapNumber support
                } else if (Objects.equals(metricsFieldTypeEnum, MetricsFieldTypeEnum.mapString)) {
                    singleContent.setFieldType(FieldTypeEnum.mapString);
                    if (StringUtils.isNotBlank(ruleCondition.getMapKey())) {
                        singleContent.setMapKey(ruleCondition.getMapKey());
                    }
                } else if (Objects.equals(metricsFieldTypeEnum, MetricsFieldTypeEnum.mapNumber)) {
                    singleContent.setFieldType(FieldTypeEnum.mapNumber);
                    if (StringUtils.isNotBlank(ruleCondition.getMapKey())) {
                        singleContent.setMapKey(ruleCondition.getMapKey());
                    }
                }
            }

            alarmRuleSingleContents.add(singleContent);
        });
        if (!extendRuleConditions.isEmpty()) {
            alarmRule.getConditions().addAll(extendRuleConditions);
        }
        alarmRuleCfg.setAlarmRuleSingleContents(alarmRuleSingleContents);
        return alarmRuleCfg;
    }

    private static final String DERIVED_FIELD_NUMBER_MAP = "derivedFieldNumberMap";
    private static final String DERIVED_FIELD_STRING_MAP = "derivedFieldStringMap";

    /**
     * Judge field type based on rule conditions and metrics field information (string/number)
     */
    private FieldTypeEnum determineFieldType(RuleCondition ruleCondition, Map<String, MetricsFieldDO> metricsFieldMap) {
        String fieldName = ruleCondition.getName();
        String operator = ruleCondition.getOperator();

        // derived field
        if (metricsFieldMap.containsKey(DERIVED_FIELD_NUMBER_MAP) || metricsFieldMap.containsKey(DERIVED_FIELD_STRING_MAP)) {
            if (AlarmOperatorEnum.IN.getOperation().equals(operator) || AlarmOperatorEnum.NOT_IN.getOperation().equals(operator)) {
                return FieldTypeEnum.string;
            }
            return FieldTypeEnum.number;
        }

        if (!metricsFieldMap.containsKey(fieldName)) {
            return null;
        }

        return getFieldTypeEnum(metricsFieldMap.get(fieldName));
    }



    private FieldTypeEnum getFieldTypeEnum(MetricsFieldDO metricsField) {

        return MetricsFieldTypeEnum.string.equals(MetricsFieldTypeEnum.fromValue(metricsField.getFieldType())) ?
                FieldTypeEnum.string :
                FieldTypeEnum.number;

    }

    private AlarmLevel getAlarmLevel(AlarmRule alarmRule) {
        return switch (alarmRule.getLevel()) {
            case FATAL -> AlarmLevel.FATAL;
            case ERROR -> AlarmLevel.ERROR;
            case WARN -> AlarmLevel.WARN;
            default -> AlarmLevel.INFO;
        };
    }

    /**
     * Determines the appropriate CompareType based on the RuleCondition's type and operator.
     *
     * @param condition The rule condition to analyze
     * @return The corresponding CompareType for the given condition
     */
    private CompareType getCompareType(RuleCondition condition) {
        // First check the condition type
        return switch (condition.getConditionType()) {
            case EXPRESSION -> CompareType.FUNC_COMPARE;
            case TAG -> CompareType.TAG_COMPARE;
            case AI -> CompareType.AI_COMPARE;
            case NODATA -> CompareType.NODATA_COMPARE;
            default -> {
                // For FIELD type, check if it's a chain ratio based on operator
                String operator = condition.getOperator();
                if (StringUtils.equalsIgnoreCase(CHAINED_RATIO_INCREASE_EXPR, operator) ||
                    StringUtils.equalsIgnoreCase(CHAINED_RATIO_DECREASE_EXPR, operator)) {
                    yield CompareType.CHAIN_RATIO;
                } else {
                    yield CompareType.THRESHOLD;
                }
            }
        };
    }

    private AlarmCfg transformAlarmCfg(AlarmDefinition alarmDefinition, List<AlarmRuleCfg> alarmRules) {
        AlarmCfg alarmCfg = new AlarmCfg();
        alarmCfg.setId(String.valueOf(alarmDefinition.getId()));
        alarmCfg.setAlarmName(alarmDefinition.getName());
        alarmCfg.setTitle(alarmDefinition.getName());
        alarmCfg.setInhibitInterval(alarmDefinition.getPeriodInMinutes() * 60L);
        alarmCfg.setInhibitThreshold(alarmDefinition.getTimesInPeriod());
        alarmCfg.setAlarmRuleCfgs(alarmRules);
        alarmCfg.setTenantId(alarmDefinition.getTenantId());
        alarmCfg.setGroupTags(Optional.ofNullable(alarmDefinition.getGroupTags()).map(s -> s.split(",")).map(Arrays::asList).orElse(Collections.emptyList()));
        alarmCfg.setNotifications(alarmDefinition.getNotifications());
        alarmCfg.setAlarmExtensionRelations(CollectionUtils.isEmpty(alarmDefinition.getAlarmExtensionRelations()) ? Lists.newArrayList() : alarmDefinition.getAlarmExtensionRelations());
        List<String> sendIncidentLevelList = Optional.ofNullable(alarmDefinition.getLevelsSendIncident()).map(s -> s.split(",")).map(Arrays::asList).orElse(new ArrayList<>());
        alarmCfg.setLevelsSendIncident(sendIncidentLevelList);
        alarmCfg.setAlarmMatchMode(alarmDefinition.getAlarmMatchMode());
        return alarmCfg;
    }

    private Map<String, AlarmRuleCfg> createAlarmRuleMap(Map<String, AlarmCfg> alarmCfgMap) {
        Map<String, AlarmRuleCfg> alarmRuleCfgMap = Maps.newHashMap();
        for (Map.Entry<String, AlarmCfg> entry : alarmCfgMap.entrySet()) {
            if (null == entry.getValue() || CollectionUtils.isEmpty(entry.getValue().getAlarmRuleCfgs())) {
                continue;
            }
            for (AlarmRuleCfg alarmRuleCfg : entry.getValue().getAlarmRuleCfgs()) {
                alarmRuleCfgMap.put(alarmRuleCfg.getId(), alarmRuleCfg);
            }
        }
        return alarmRuleCfgMap;
    }

}
