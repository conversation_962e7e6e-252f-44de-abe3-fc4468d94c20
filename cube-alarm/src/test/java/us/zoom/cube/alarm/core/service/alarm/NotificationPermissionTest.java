package us.zoom.cube.alarm.core.service.alarm;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.alarm.CubeAlarmApplicationTest;
import us.zoom.cube.alarm.infra.redis.MultiRedisService;
import us.zoom.cube.alarm.core.service.syspara.AlarmParaService;
import us.zoom.infra.influx.model.alarm.AlarmMatchRecord;
import us.zoom.infra.model.alarm.AlarmLevel;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Test notification permission acquisition mechanism
 * <AUTHOR>
 */
public class NotificationPermissionTest extends CubeAlarmApplicationTest {

    @Autowired
    private MultiRedisService redisService;

    @Autowired
    private AlarmParaService alarmParaService;

    @Test
    public void testNotificationPermissionAcquisition() throws InterruptedException {
        // Simulate multiple threads trying to acquire notification permission simultaneously
        int threadCount = 10;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);

        // Create mock alarm record
        AlarmMatchRecord mockRecord = createMockAlarmRecord();

        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            executor.submit(() -> {
                try {
                    boolean acquired = acquireNotificationPermission(mockRecord);
                    if (acquired) {
                        successCount.incrementAndGet();
                        System.out.println("Thread " + threadId + " acquired permission");
                    } else {
                        failCount.incrementAndGet();
                        System.out.println("Thread " + threadId + " failed to acquire permission");
                    }
                } catch (Exception e) {
                    System.err.println("Thread " + threadId + " error: " + e.getMessage());
                } finally {
                    latch.countDown();
                }
            });
        }

        latch.await();
        executor.shutdown();

        System.out.println("Test completed:");
        System.out.println("Success count: " + successCount.get());
        System.out.println("Fail count: " + failCount.get());
        
        // Verify that only one thread acquired permission
        assert successCount.get() == 1 : "Expected exactly 1 success, but got " + successCount.get();
        assert failCount.get() == threadCount - 1 : "Expected " + (threadCount - 1) + " failures, but got " + failCount.get();
    }

    private AlarmMatchRecord createMockAlarmRecord() {
        AlarmMatchRecord record = new AlarmMatchRecord();
        record.setAlarmId("test-alarm-id");
        record.setTagKey("test-tag-key");
        record.setAlarmLevel(AlarmLevel.ERROR);
        return record;
    }

    private boolean acquireNotificationPermission(AlarmMatchRecord alarmMatchRecord) {
        String permissionKey = generateNotificationPermissionKey(alarmMatchRecord);
        String machineId = getMachineId();
        int expireSeconds = 30;
        
        try {
            Boolean result = redisService.setIfAbsent(permissionKey, machineId, expireSeconds, alarmParaService.isNewRedisSwitchForGroupByTag());
            return result != null && result;
        } catch (Exception e) {
            System.err.println("Error acquiring permission: " + e.getMessage());
            return false;
        }
    }

    private String generateNotificationPermissionKey(AlarmMatchRecord alarmMatchRecord) {
        long timeWindow = System.currentTimeMillis() / 60000;
        return String.format("notify:permission:%s:%s:%s:%d", 
            alarmMatchRecord.getAlarmId(), 
            alarmMatchRecord.getTagKey(),
            alarmMatchRecord.getAlarmLevel().name(),
            timeWindow);
    }

    private String getMachineId() {
        try {
            String hostname = java.net.InetAddress.getLocalHost().getHostName();
            String processId = String.valueOf(ProcessHandle.current().pid());
            return hostname + "_" + processId + "_" + Thread.currentThread().getId();
        } catch (Exception e) {
            return "unknown_" + Thread.currentThread().getId();
        }
    }
} 