//package us.zoom.cube.alarm.core.service.alarm;
//
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import us.zoom.cube.alarm.CubeAlarmApplicationTest;
//import us.zoom.cube.alarm.core.service.syspara.AlarmParaService;
//import us.zoom.cube.alarm.infra.redis.MultiRedisService;
//import us.zoom.infra.redis.RedisService;
//
//import java.util.*;
//
//import static org.junit.jupiter.api.Assertions.assertFalse;
//import static org.junit.jupiter.api.Assertions.assertTrue;
//import static org.mockito.ArgumentMatchers.*;
//import static org.mockito.Mockito.*;
//
///**
// * Alarm notification rate limiting service test class
// */
//class AlarmNoticeRateLimitServiceTest extends CubeAlarmApplicationTest  {
//
//    @Autowired
//    private MultiRedisService redisService;
//
//    @Autowired
//    private AlarmParaService alarmParaService;
//
//    @Autowired
//    private AlarmNoticeRateLimitService rateLimitService;
//
//    private static final String TEST_RULE_ID = "test-rule-123";
//    private static final String TEST_SERVICE_NAME = "test-service";
//
//    @Test
//    void testIsAllowedToNotify_WhenDisabled_ShouldReturnTrue() {
//        when(alarmParaService.getAlarmRateLimitConfig()).thenReturn(createConfig(false, null, null));
//
//        boolean result = rateLimitService.isAllowedToNotify(TEST_RULE_ID, TEST_SERVICE_NAME, null);
//
//        assertTrue(result);
//        verify(redisService, never()).zcount(anyString(), anyLong(), anyLong(), MultiRedisService.RedisInstance.PRIMARY);
//    }
//
//    @Test
//    void testIsAllowedToNotify_WhenServiceNotInWhitelist_ShouldReturnTrue() {
//        List<String> whitelist = Arrays.asList("service1", "service2");
//        when(alarmParaService.getAlarmRateLimitConfig()).thenReturn(createConfig(true, whitelist, null));
//
//        boolean result = rateLimitService.isAllowedToNotify(TEST_RULE_ID, "service3", null);
//
//        assertTrue(result);
//        verify(redisService, never()).zcount(anyString(), anyLong(), anyLong(),MultiRedisService.RedisInstance.PRIMARY);
//    }
//
//    @Test
//    void testIsAllowedToNotify_WhenServiceInWhitelist_ShouldCheckRateLimit() {
//        List<String> whitelist = Arrays.asList("service1", "test-service");
//        when(alarmParaService.getAlarmRateLimitConfig()).thenReturn(createConfig(true, whitelist, null));
//        when(redisService.zcount(anyString(), anyLong(), anyLong(), MultiRedisService.RedisInstance.PRIMARY)).thenReturn(5L, 20L, 40L);
//        when(redisService.zadd(anyString(), anyString(), anyDouble(),MultiRedisService.RedisInstance.PRIMARY)).thenReturn(true);
//
//        boolean result = rateLimitService.isAllowedToNotify(TEST_RULE_ID, TEST_SERVICE_NAME, null);
//
//        assertTrue(result);
//        verify(redisService, times(3)).zcount(anyString(), anyLong(), anyLong(),MultiRedisService.RedisInstance.PRIMARY);
//    }
//
//    @Test
//    void testIsAllowedToNotify_WhenWhitelistEmpty_ShouldCheckRateLimit() {
//        when(alarmParaService.getAlarmRateLimitConfig()).thenReturn(createConfig(true, Collections.emptyList(), null));
//        when(redisService.zcount(anyString(), anyLong(), anyLong())).thenReturn(5L, 20L, 40L);
//        when(redisService.zadd(anyString(), anyString(), anyDouble())).thenReturn(true);
//
//        boolean result = rateLimitService.isAllowedToNotify(TEST_RULE_ID, TEST_SERVICE_NAME, null);
//
//        assertTrue(result);
//        verify(redisService, times(3)).zcount(anyString(), anyLong(), anyLong());
//    }
//
//    @Test
//    void testIsAllowedToNotify_WhenWhitelistNull_ShouldCheckRateLimit() {
//        when(alarmParaService.getAlarmRateLimitConfig()).thenReturn(createConfig(true, null, null));
//        when(redisService.zcount(anyString(), anyLong(), anyLong())).thenReturn(5L, 20L, 40L);
//        when(redisService.zadd(anyString(), anyString(), anyDouble())).thenReturn(true);
//
//        boolean result = rateLimitService.isAllowedToNotify(TEST_RULE_ID, TEST_SERVICE_NAME, null);
//
//        assertTrue(result);
//        verify(redisService, times(3)).zcount(anyString(), anyLong(), anyLong());
//    }
//
//    @Test
//    void testIsAllowedToNotify_WhenServiceNameNull_ShouldReturnTrue() {
//        when(alarmParaService.getAlarmRateLimitConfig()).thenReturn(createConfig(true, Arrays.asList("service1"), null));
//
//        boolean result = rateLimitService.isAllowedToNotify(TEST_RULE_ID, null, null);
//
//        assertTrue(result);
//        verify(redisService, never()).zcount(anyString(), anyLong(), anyLong());
//    }
//
//    @Test
//    void testIsAllowedToNotify_WhenServiceNameEmpty_ShouldReturnTrue() {
//        when(alarmParaService.getAlarmRateLimitConfig()).thenReturn(createConfig(true, Arrays.asList("service1"), null));
//
//        boolean result = rateLimitService.isAllowedToNotify(TEST_RULE_ID, "", null);
//
//        assertTrue(result);
//        verify(redisService, never()).zcount(anyString(), anyLong(), anyLong());
//    }
//
//    @Test
//    void testIsAllowedToNotify_WhenServiceDisabled_ShouldReturnTrue() {
//        Map<String, us.zoom.cube.alarm.core.config.AlarmRateLimitConfig.RateLimitParams> serviceParams = new HashMap<>();
//        serviceParams.put(TEST_SERVICE_NAME, createServiceParams(false));
//        when(alarmParaService.getAlarmRateLimitConfig()).thenReturn(createConfig(true, null, serviceParams));
//
//        boolean result = rateLimitService.isAllowedToNotify(TEST_RULE_ID, TEST_SERVICE_NAME, null);
//
//        assertTrue(result);
//        verify(redisService, never()).zcount(anyString(), anyLong(), anyLong());
//    }
//
//    @Test
//    void testIsAllowedToNotify_WhenServiceHasCustomParams_ShouldUseCustomParams() {
//        Map<String, us.zoom.cube.alarm.core.config.AlarmRateLimitConfig.RateLimitParams> serviceParams = new HashMap<>();
//        serviceParams.put(TEST_SERVICE_NAME, createServiceParams(5, 20, 50));
//        when(alarmParaService.getAlarmRateLimitConfig()).thenReturn(createConfig(true, null, serviceParams));
//        when(redisService.zcount(anyString(), anyLong(), anyLong())).thenReturn(4L, 19L, 49L);
//        when(redisService.zadd(anyString(), anyString(), anyDouble())).thenReturn(true);
//
//        boolean result = rateLimitService.isAllowedToNotify(TEST_RULE_ID, TEST_SERVICE_NAME, null);
//
//        assertTrue(result);
//        verify(redisService, times(3)).zcount(anyString(), anyLong(), anyLong());
//    }
//
//    @Test
//    void testIsAllowedToNotify_WhenServiceOverCustomLimit_ShouldReturnFalse() {
//        Map<String, us.zoom.cube.alarm.core.config.AlarmRateLimitConfig.RateLimitParams> serviceParams = new HashMap<>();
//        serviceParams.put(TEST_SERVICE_NAME, createServiceParams(5, 20, 50));
//        when(alarmParaService.getAlarmRateLimitConfig()).thenReturn(createConfig(true, null, serviceParams));
//        when(redisService.zcount(anyString(), anyLong(), anyLong())).thenReturn(5L);
//
//        boolean result = rateLimitService.isAllowedToNotify(TEST_RULE_ID, TEST_SERVICE_NAME, null);
//
//        assertFalse(result);
//        verify(redisService, times(1)).zcount(anyString(), anyLong(), anyLong());
//    }
//
//    @Test
//    void testIsAllowedToNotify_WhenUnderLimit_ShouldReturnTrue() {
//        when(alarmParaService.getAlarmRateLimitConfig()).thenReturn(createConfig(true, null, null));
//        when(redisService.zcount(anyString(), anyLong(), anyLong())).thenReturn(5L, 20L, 40L);
//        when(redisService.zadd(anyString(), anyString(), anyDouble())).thenReturn(true);
//
//        boolean result = rateLimitService.isAllowedToNotify(TEST_RULE_ID, TEST_SERVICE_NAME, null);
//
//        assertTrue(result);
//        verify(redisService, times(3)).zcount(anyString(), anyLong(), anyLong());
//    }
//
//    @Test
//    void testIsAllowedToNotify_WhenOverLimit_ShouldReturnFalse() {
//        when(alarmParaService.getAlarmRateLimitConfig()).thenReturn(createConfig(true, null, null));
//        when(redisService.zcount(anyString(), anyLong(), anyLong())).thenReturn(10L);
//
//        boolean result = rateLimitService.isAllowedToNotify(TEST_RULE_ID, TEST_SERVICE_NAME, null);
//
//        assertFalse(result);
//        verify(redisService, times(1)).zcount(anyString(), anyLong(), anyLong());
//    }
//
//    @Test
//    void testIsAllowedToNotify_WhenRuleIdNull_ShouldReturnTrue() {
//        when(alarmParaService.getAlarmRateLimitConfig()).thenReturn(createConfig(true, null, null));
//
//        boolean result = rateLimitService.isAllowedToNotify(null, TEST_SERVICE_NAME, null);
//
//        assertTrue(result);
//        verify(redisService, never()).zcount(anyString(), anyLong(), anyLong());
//    }
//
//    @Test
//    void testIsAllowedToNotify_WhenRuleIdEmpty_ShouldReturnTrue() {
//        when(alarmParaService.getAlarmRateLimitConfig()).thenReturn(createConfig(true, null, null));
//
//        boolean result = rateLimitService.isAllowedToNotify("", TEST_SERVICE_NAME, null);
//
//        assertTrue(result);
//        verify(redisService, never()).zcount(anyString(), anyLong(), anyLong());
//    }
//
//    @Test
//    void testIsAllowedToNotify_WhenRedisException_ShouldReturnTrue() {
//        when(alarmParaService.getAlarmRateLimitConfig()).thenReturn(createConfig(true, null, null));
//        when(redisService.zcount(anyString(), anyLong(), anyLong())).thenThrow(new RuntimeException("Redis error"));
//
//        boolean result = rateLimitService.isAllowedToNotify(TEST_RULE_ID, TEST_SERVICE_NAME, null);
//
//        assertTrue(result);
//    }
//
//    private us.zoom.cube.alarm.core.config.AlarmRateLimitConfig createConfig(boolean enabled, List<String> whitelist,
//                                                                             Map<String, us.zoom.cube.alarm.core.config.AlarmRateLimitConfig.RateLimitParams> serviceParams) {
//        us.zoom.cube.alarm.core.config.AlarmRateLimitConfig config = new us.zoom.cube.alarm.core.config.AlarmRateLimitConfig();
//        config.setEnabled(enabled);
//        config.setServiceWhitelist(whitelist);
//        config.setDefaultParams(createServiceParams(10, 50, 100));
//        if (serviceParams != null) {
//            config.setServiceParams(serviceParams);
//        }
//        return config;
//    }
//
//    private us.zoom.cube.alarm.core.config.AlarmRateLimitConfig.RateLimitParams createServiceParams(int rule5Min, int rule30Min, int rule1Hour) {
//        us.zoom.cube.alarm.core.config.AlarmRateLimitConfig.RateLimitParams params = new us.zoom.cube.alarm.core.config.AlarmRateLimitConfig.RateLimitParams();
//        params.setRule5Min(rule5Min);
//        params.setRule30Min(rule30Min);
//        params.setRule1Hour(rule1Hour);
//        params.setRedisKeyExpireHours(2);
//        params.setDataCleanupHours(1);
//        params.setEnabled(true);
//        return params;
//    }
//
//    private us.zoom.cube.alarm.core.config.AlarmRateLimitConfig.RateLimitParams createServiceParams(boolean enabled) {
//        us.zoom.cube.alarm.core.config.AlarmRateLimitConfig.RateLimitParams params = new us.zoom.cube.alarm.core.config.AlarmRateLimitConfig.RateLimitParams();
//        params.setEnabled(enabled);
//        return params;
//    }
//}