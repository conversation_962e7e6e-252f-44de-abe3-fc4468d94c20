#MySql data source
dataSource.url=************************************************************************************************************************************************************************************************************************************
# To set in CSMS
#cube.dataSource.username=
# To set in CSMS
#cube.dataSource.password=
dataSourceStandby.url=************************************************************************************************************************************************************************************************************************************
# To set in CSMS
#cube.dataSourceStandby.username=
# To set in CSMS
#cube.dataSourceStandby.password=

#spring.data.redis.ssl.enabled=true
spring.data.redis.cluster.nodes=localhost:7001,localhost:7002,localhost:7003

cube.config.client.endpoint=https://cubeconfig-perf-new.zoomdev.us

cube-duty.server.env=local

#cube-duty.server.local-auth=true

csms.jwt.public.key.path.list=ds01/cube


incident-detail-basic-link=https://cube-perfdebug.zoomdev.us/incident-workspace/detail?id={workspaceId}&incidentId={incidentId}
incident-smart_rca-basic-link=https://cube-perfdebug.zoomdev.us/incident-rca?incidentId={incidentId}&incidentName={title}