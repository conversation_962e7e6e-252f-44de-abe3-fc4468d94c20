#choose environment
server.port=8080
server.tomcat.uri-encoding=UTF-8
server.ssl.enabled=false
server.tomcat.min-spare-threads=100
server.tomcat.accept-count=100
server.tomcat.max-threads=15
server.tomcat.max-connections=100


#DruidDataSource setting
dataSource.filters=stat
dataSource.maxActive=10
dataSource.initialSize=3
dataSource.maxWait=60000
dataSource.minIdle=1
dataSource.timeBetweenEvictionRunsMillis=60000
dataSource.minEvictableIdleTimeMillis=300000
dataSource.testWhileIdle=true
dataSource.testOnBorrow=false
dataSource.testOnReturn=false
dataSource.poolPreparedStatements=true
dataSource.maxOpenPreparedStatements=20
dataSource.asyncInit=true
dataSource.validationQuery= "SELECT 1"

dataSource.csms_rotate_username_key= cube.dataSource.username
dataSource.csms_rotate_password_key= cube.dataSource.password
dataSourceStandby.csms_rotate_username_key= cube.dataSourceStandby.username
dataSourceStandby.csms_rotate_password_key= cube.dataSourceStandby.password

spring.application.name=cube-duty
management.health.defaults.enabled=false
management.health.ping.enabled=true
management.endpoint.health.show-components=always
management.endpoint.health.show-details=always
management.endpoints.web.exposure.include=info,prometheus,up,health

#apm
apm.log.home=cube-duty/logs
monitor.executorsReplace.enabled=false

cube.config.dao.enable=false
mysql.datasource.enabled=true
spring.jackson.deserialization.fail-on-unknown-properties=false
spring.jackson.default-property-inclusion=non_null

#spring.redis.password=
spring.data.redis.timeout=1000

async.mq.endpoint=https://asyncmq.zoomdev.us
alarm.async.mq.username=app_cube

