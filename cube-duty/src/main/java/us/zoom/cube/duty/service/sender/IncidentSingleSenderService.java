package us.zoom.cube.duty.service.sender;

import us.zoom.cube.duty.service.sender.entity.IncidentOpenEntity;
import us.zoom.cube.duty.service.sender.entity.IncidentSmartRCAEntity;
import us.zoom.cube.duty.service.sender.entity.IncidentStatusChangeEntity;
import us.zoom.cube.duty.service.sender.entity.IncidentStormEntity;
import us.zoom.cube.duty.service.sender.entity.SendTo;

/**
 * <AUTHOR> song
 */
public interface IncidentSingleSenderService {

    boolean sendOpen(SendTo sendTo, IncidentOpenEntity incident);

    boolean sendStorm(SendTo sendTo, IncidentStormEntity incident);

    boolean sendStatusChange(SendTo sendTo, IncidentStatusChangeEntity entity);

    boolean sendSmartRCALink(SendTo sendTo, IncidentSmartRCAEntity incident);
}
