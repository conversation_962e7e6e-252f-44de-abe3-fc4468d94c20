package us.zoom.cube.duty.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.clickhouse.data.value.UnsignedLong;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.oxm.ValidationFailureException;
import org.springframework.stereotype.Service;
import us.zoom.common.RandomUtil;
import us.zoom.cube.duty.config.ClickhouseHandlerFactory;
import us.zoom.cube.duty.constat.DutyConstat;
import us.zoom.cube.duty.constat.IncidentStatus;
import us.zoom.cube.duty.dto.IncidentCountDTO;
import us.zoom.cube.duty.dto.IncidentDTO;
import us.zoom.cube.duty.dto.OriginalAlarmDTO;
import us.zoom.cube.duty.dto.WorkspaceDTO;
import us.zoom.cube.duty.dto.WorkspaceDetailDTO;
import us.zoom.cube.duty.dao.entity.DutyAlarmIncident;
import us.zoom.cube.duty.dao.entity.DutyIncident;
import us.zoom.cube.duty.dao.service.IDutyAlarmIncidentService;
import us.zoom.cube.duty.dao.service.IDutyIncidentService;
import us.zoom.cube.duty.dto.IncidentListItemDTO;
import us.zoom.cube.duty.dto.DispatchingStrategyDetailDTO;
import us.zoom.cube.duty.filter.auth.UserContext;
import us.zoom.cube.duty.constat.IncidentOperateAction;
import us.zoom.cube.duty.service.sender.entity.IncidentOpenEntity;
import us.zoom.cube.duty.dto.IncidentOperationDTO;
import us.zoom.cube.duty.service.sender.entity.IncidentStormEntity;
import us.zoom.cube.duty.service.sender.IncidentSingleSenderService;
import us.zoom.cube.duty.service.sender.entity.SendTo;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.duty.dto.PageResult;
import us.zoom.infra.clickhouse.ClickhouseData;
import us.zoom.infra.clickhouse.ClickhouseKey;
import us.zoom.infra.clickhouse.ClickhouseSqlUtil;
import us.zoom.infra.utils.IdUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> song
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class IncidentService implements IncidentCacheService {
    public static final String SYSTEM = "System";
    private final IDutyIncidentService dutyIncidentService;
    private final IDutyAlarmIncidentService dutyAlarmIncidentService;
    private final WorkspaceService workspaceService;
    @Qualifier("zoomWebHookSenderService")
    private final IncidentSingleSenderService incidentSingleSenderService;
    private final ClickhouseHandlerFactory clickhouseHandlerFactory;
    private final RedisTemplate<String, Object> redisTemplate;
    private final RedisTemplate<String, DutyIncident> incidentRedisTemplate;
    @Autowired
    @Lazy
    private IncidentCacheService self;

    public void executeDispatchingStrategyForOpenIncident(DutyIncident incident, OriginalAlarmDTO request, WorkspaceDetailDTO workspaceInfo,
                                                          DispatchingStrategyDetailDTO strategy) {
        saveTimeLine(IncidentOperateAction.Triggered.getActionName() + "_01_DISPATCHING", incident.getId(), SYSTEM, buildMap("dispatching_strategy_id",
                strategy.getId(), "dispatching_strategy_name", strategy.getName()));
        sendIncidentOpenNotification(request.getId(), strategy, new IncidentOpenEntity(incident, request, workspaceInfo));
    }

    public void executeDispatchingStrategyForStormIncident(DutyIncident incident, WorkspaceDetailDTO workspaceInfo, DispatchingStrategyDetailDTO dispatchingStrategy) {
        int count = (int) getGroupingAlarmCount(incident.getId());
        sendStormNotification(incident, count, dispatchingStrategy, workspaceInfo);
        saveTimeLine("storm", incident.getId(), SYSTEM, buildMap("count", String.valueOf(count)));
    }

    public Map<String, Boolean> ack(IncidentOperationDTO request) {
        return doChangeStatusAction(IncidentOperateAction.ACK, request);
    }

    public Map<String, Boolean> unAck(IncidentOperationDTO request) {
        return doChangeStatusAction(IncidentOperateAction.UnAck, request);
    }

    public Map<String, Boolean> close(IncidentOperationDTO request) {
        return doChangeStatusAction(IncidentOperateAction.CLOSE, request);
    }

    public Map<String, Boolean> reopen(IncidentOperationDTO request) {
        return doChangeStatusAction(IncidentOperateAction.REOPEN, request);
    }

    public Boolean smartRCAGenerate(String incidentId, Map<String,Object> data) {
        if(StringUtils.isEmpty(incidentId)){
            log.error("smartRCAGenerate: incidentId is empty");
            return false;
        }
        log.info("receive smartRCAGenerate, incidentId : {}", incidentId);
        return doIncidentAdjustment(IncidentOperateAction.SMART_RCA_GENERATION, incidentId, SYSTEM, data);
    }

    private Map<String, Boolean> doChangeStatusAction(IncidentOperateAction status, IncidentOperationDTO request) {
        Map<String, Boolean> res = new HashMap<>();
        for (String id : request.getIncidentIds()) {
            res.put(id, doIncidentAdjustment(status, id, UserContext.getUserName()));
        }
        return res;
    }

    private boolean doIncidentAdjustment(IncidentOperateAction status, String id, String operatorUserName){
        return this.doIncidentAdjustment(status, id, operatorUserName, null);
    }

    private boolean doIncidentAdjustment(IncidentOperateAction status, String id, String operatorUserName, Map<String,Object> extraData) {
        try {
            DutyIncident incident = getIncidentById(id);
            if (incident == null) {
                log.error("change incident failed, id :{}, unknown incident id", id);
                return false;
            } else {
                WorkspaceDetailDTO workspaceInfo = workspaceService.getWorkspaceById(incident.getWorkspaceId());
                updateIncidentStatus(id, incident, operatorUserName, status);
                saveTimeLine(status.getActionName(), id, operatorUserName, buildMap("operatorId", UserContext.getUserId(), "operatorName",operatorUserName));
                sendIncidentChangeMessage(status, incident, operatorUserName, workspaceInfo, extraData);
                return true;
            }
        } catch (Exception e) {
            log.error("change incident failed, id :{}, exception:{}", id, e);
            return false;
        }
    }

    private boolean updateIncidentStatus(String id, DutyIncident incident, String operatorUserName, IncidentOperateAction status) {
        LambdaUpdateWrapper<DutyIncident> updateWrapper = status.buildUpdateWrapper(incident, operatorUserName);
        // Don't need to update the incident record
        if(updateWrapper == null){
            return true;
        }
        if(status == IncidentOperateAction.CLOSE){
            self.clearIncidentCache(id);
        }
        return dutyIncidentService.update(updateWrapper);
    }


    public IncidentDTO getIncident(String id) {
        DutyIncident incident = getIncidentById(id);
        if (incident == null) {
            throw new ValidationFailureException("incident not found, id:" + id);
        }
        Map<String,Object> extInfo = getDutyAlarmIncidentInfo(List.of(id)).get(id);
        return new IncidentDTO(incident,extInfo);
    }

    private Map<String,Map<String,Object>> getDutyAlarmIncidentInfo(List<String> incidentIds){
        List<Map<String, Object>> extDataList = dutyAlarmIncidentService.listMaps(new QueryWrapper<DutyAlarmIncident>().select("duty_incident_id", "count(1) as related_alarm_count", "max(create_time) as last_received_time").groupBy("duty_incident_id").in("duty_incident_id", incidentIds));
        return extDataList.stream().collect(Collectors.toMap(map-> map.remove("duty_incident_id").toString(), map -> map));
    }

    public PageResult<IncidentListItemDTO> listIncidents(String dutyWorkspaceId, Integer[] status, Long begin, Long end, String[] keywords, int pageIndex, int pageSize) {
        LambdaQueryWrapper<DutyIncident> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(dutyWorkspaceId)) {
            queryWrapper.eq(DutyIncident::getWorkspaceId, dutyWorkspaceId);
        }
        if (status != null && status.length > 0) {
            queryWrapper.in(DutyIncident::getStatus, status);
        }
        if (begin != null) {
            queryWrapper.ge(DutyIncident::getCreateTime, new Date(begin));
        }
        if (end != null) {
            queryWrapper.le(DutyIncident::getCreateTime, new Date(end));
        }
        if (keywords != null && keywords.length > 0) {
            queryWrapper.and(keywordsFilter -> {
                Arrays.stream(keywords).forEach(keyword -> {
                    keywordsFilter.or().like(DutyIncident::getTitle, keyword).or().eq(DutyIncident::getId, keyword);
                });
            });
        }
        queryWrapper.orderByDesc(DutyIncident::getCreateTime);
        queryWrapper.select(DutyIncident::getId, DutyIncident::getWorkspaceId,
                DutyIncident::getDispatchingStrategyId, DutyIncident::getTitle,
                DutyIncident::getStatus,
                DutyIncident::getLevel,
                DutyIncident::getAckUser, DutyIncident::getCloseUser,
                DutyIncident::getCreator,
                DutyIncident::getCreateTime, DutyIncident::getEditor, DutyIncident::getModifyTime);

        IPage<DutyIncident> page = dutyIncidentService.page(new Page<>(pageIndex, pageSize), queryWrapper);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return new PageResult<>(page.getTotal(), new ArrayList<>());
        }
        List<String> incidentIds = page.getRecords().stream().map(DutyIncident::getId).collect(Collectors.toList());
        List<String> workspaceIds = page.getRecords().stream().map(DutyIncident::getWorkspaceId).collect(Collectors.toList());
        Map<String, Map<String, Object>> extInfoMap = getDutyAlarmIncidentInfo(incidentIds);
        Map<String, String> workspaceId2Name = workspaceService.listWorkspaceByIds(workspaceIds).stream().collect(Collectors.toMap(WorkspaceDTO::getId, WorkspaceDTO::getName));
        List<IncidentListItemDTO> result = page.getRecords().stream().map(item -> {
            IncidentListItemDTO listItem = new IncidentListItemDTO(item, extInfoMap.get(item.getId()));
            listItem.setWorkspaceName(workspaceId2Name.get(item.getWorkspaceId()));
            return listItem;
        }).collect(Collectors.toList());
        return new PageResult<>(page.getTotal(), result);
    }

    private DutyIncident getIncidentById(String id) {
        return dutyIncidentService.getById(id);
    }

    public DutyIncident saveIncident(OriginalAlarmDTO request, DispatchingStrategyDetailDTO strategy, String incidentId, String workspaceId) {
        // insert incident
        DutyIncident incident = new DutyIncident();
        incident.setId(incidentId);
        incident.setWorkspaceId(workspaceId);
        incident.setDispatchingStrategyId(strategy == null ? null : strategy.getId());
        incident.setService(request.getService());
        incident.setLevel(request.getLevel());
        incident.setTitle(request.getTitle());
        incident.setDescription(request.getDescription());
        incident.setStatus(0);
        incident.setTags(JsonUtils.toJsonString(request.getTags()));
        incident.setCreator(SYSTEM);
        incident.setEditor("");
        incident.setCreateTime(new Date());
        incident.setModifyTime(new Date());
        dutyIncidentService.save(incident);
        return incident;
    }

    public long saveAlarmIncidentRelationship(String alarmId, DutyIncident incident, Integer timeWindow, boolean newIncident) {
        // insert the relationship
        DutyAlarmIncident alarmIncident = new DutyAlarmIncident();
        alarmIncident.setId(IdUtils.generateId());
        alarmIncident.setDutyAlarmId(alarmId);
        alarmIncident.setDutyIncidentId(incident.getId());
        alarmIncident.setCreateTime(new Date());
        dutyAlarmIncidentService.save(alarmIncident, incident.getCreateTime());
        timeWindow = timeWindow == null || timeWindow <= 0 ? 3 * 24 * 60 : timeWindow;
        long count = redisTemplate.opsForValue().increment("GroupedAlarmCount::" + incident.getId());
        if (count == 1) {
            if (!newIncident) {
                count = dutyAlarmIncidentService.count(new LambdaQueryWrapper<DutyAlarmIncident>().eq(DutyAlarmIncident::getDutyIncidentId, incident.getId()));
                redisTemplate.opsForValue().set("GroupedAlarmCount::" + incident.getId(), count);
            }
            redisTemplate.expire("GroupedAlarmCount::" + incident.getId(), timeWindow, TimeUnit.MINUTES);
        }
        return count;
    }

    private void sendIncidentOpenNotification(String alarmId, DispatchingStrategyDetailDTO strategy, IncidentOpenEntity openEntity) {
        if (strategy.getSettings() != null) {
            for (DispatchingStrategyDetailDTO.Setting setting : strategy.getSettings()) {
                if (setting.getGroupNotificationSettings() != null) {
                    for (DispatchingStrategyDetailDTO.Setting.GroupNotificationSetting notification : setting.getGroupNotificationSettings()) {
                        switch (notification.getType()) {
                            case 0: {
                                SendTo sendTo = JsonUtils.toObject(notification.getInfo(), SendTo.class);
                                boolean res;
                                if ((res = incidentSingleSenderService.sendOpen(sendTo, openEntity))) {
                                    log.info("send open succeed, sendTo={}, incident:{}, alarmId:{}", sendTo.getAlias(), openEntity.getId(), alarmId);
                                } else {
                                    log.error("send open failed, sendTo={}, incident:{}, alarmId:{}", sendTo.getAlias(), openEntity.getId(), alarmId);
                                }
                                saveTimeLine(IncidentOperateAction.Triggered + "_10_SEND_MESSAGE", openEntity.getId(), SYSTEM, buildMap("sendTo", sendTo.getAlias(), "result", String.valueOf(res), "type", String.valueOf(notification.getType())));
                                break;
                            }
                        }
                    }
                }
            }
        }
    }


    private void sendStormNotification(DutyIncident incident, long count, DispatchingStrategyDetailDTO strategy, WorkspaceDetailDTO workspaceInfo) {
        if (strategy == null) {
            log.info("matched dispatching strategy is null, ignoring notification for incident id {}, workspace_id:{}", incident.getId(), incident.getWorkspaceId());
            return;
        }
        if (strategy != null && strategy.getSettings() != null) {
            for (DispatchingStrategyDetailDTO.Setting setting : strategy.getSettings()) {
                if (setting.getGroupNotificationSettings() != null) {
                    for (DispatchingStrategyDetailDTO.Setting.GroupNotificationSetting notification : setting.getGroupNotificationSettings()) {
                        switch (notification.getType()) {
                            case 0: {
                                SendTo sendTo = JsonUtils.toObject(notification.getInfo(), SendTo.class);
                                if (incidentSingleSenderService.sendStorm(sendTo, new IncidentStormEntity(incident, count, workspaceInfo))) {
                                    log.info("send storm succeed, sendTo={}, incident:{}", sendTo.getAlias(), incident.getId());
                                } else {
                                    log.error("send storm failed, sendTo={}, incident:{}", sendTo.getAlias(), incident.getId());
                                }
                                break;
                            }
                        }
                    }
                }
            }
        }
    }

    private void sendIncidentChangeMessage(IncidentOperateAction action, DutyIncident incident, String operator, WorkspaceDTO workspaceInfo, Map<String,Object> extraData) {
        if (StringUtils.isNotBlank(incident.getDispatchingStrategyId())) {
            DispatchingStrategyDetailDTO strategy = workspaceService.getDispatchingStrategyDTOById(incident.getDispatchingStrategyId());
            if (strategy != null && strategy.getSettings() != null) {
                for (DispatchingStrategyDetailDTO.Setting setting : strategy.getSettings()) {
                    if (setting.getGroupNotificationSettings() != null) {
                        for (DispatchingStrategyDetailDTO.Setting.GroupNotificationSetting notification : setting.getGroupNotificationSettings()) {
                            switch (notification.getType()) {
                                case 0: {
                                    SendTo sendTo = JsonUtils.toObject(notification.getInfo(), SendTo.class);
                                    if (action.sendNotification(incidentSingleSenderService, incident, operator, workspaceInfo, sendTo, extraData)) {
                                        log.info("send incident change message succeed, sendTo={}, incident:{}", sendTo.getAlias(), incident.getId());
                                    } else {
                                        log.error("send incident change message failed, sendTo={}, incident:{}", sendTo.getAlias(), incident.getId());
                                    }
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        }
    }


    public void saveTimeLine(String action, String incidentId, String creator, Map<String, String> params) {
        Map<String, Object> columns = new HashMap<>(16);
        columns.put("id", incidentId);
        columns.put("create_time", new Timestamp(System.currentTimeMillis()));
        columns.put("creator", creator);
        columns.put("action", action);
        columns.put("params", params);
        long recordMemorySize = ClickhouseSqlUtil.approximateSize(columns);
        String dbName = ClickhouseSqlUtil.toClickhouseName(DutyConstat.CLICKHOUSE_DATABASE);
        clickhouseHandlerFactory.getClickhouseWriter().appendData(new ClickhouseData(new ClickhouseKey(dbName, "duty_incident_time_line"), columns, recordMemorySize, false), false);
    }

    private static Map<String, String> buildMap(String... kvs) {
        Map<String, String> map = new HashMap<>(8);
        for (int i = 0; i < kvs.length; i = i + 2) {
            if (StringUtils.isNotBlank(kvs[i + 1])) {
                map.put(kvs[i], kvs[i + 1]);
            }
        }
        return map;
    }

    public List<Map<String, Object>> listIncidentTimeline(String incidentId) {
        return clickhouseHandlerFactory.get().query(DutyConstat.CLICKHOUSE_DATABASE, String.format("select * from %s.%s where id='%s' order by create_time asc",
                ClickhouseSqlUtil.encodeClickhouseName(DutyConstat.CLICKHOUSE_DATABASE), "duty_incident_time_line", incidentId));
    }

    public long getGroupingAlarmCount(String incidentId) {
        return dutyAlarmIncidentService.count(new LambdaQueryWrapper<DutyAlarmIncident>().eq(DutyAlarmIncident::getDutyIncidentId, incidentId));
    }


    public PageResult<Map<String, Object>> listAlarmByQuery(List<String> workspaceIds, List<String> integrationIds, List<String> incidentIds, int pageIndex, int pageSize) {
        String sql = " from %s.%s ".formatted(ClickhouseSqlUtil.encodeClickhouseName(DutyConstat.CLICKHOUSE_DATABASE), "duty_original_alarm");
        List<String> whereList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(integrationIds)) {
            String where = integrationIds.stream().map(id -> "'%s'".formatted(id)).collect(Collectors.joining(","));
            where = "integration_id in (%s)".formatted(where);
            whereList.add(where);
        }
        if (CollectionUtils.isNotEmpty(workspaceIds)) {
            String where = workspaceIds.stream().map(id -> "'%s'".formatted(id)).collect(Collectors.joining(","));
            where = "workspace_id in (%s)".formatted(where);
            whereList.add(where);
        }
        if (CollectionUtils.isNotEmpty(incidentIds)) {
            LambdaQueryWrapper<DutyAlarmIncident> query = new LambdaQueryWrapper<>();
            query.in(DutyAlarmIncident::getDutyIncidentId, incidentIds);
            Set<String> alarmIds =
                    dutyAlarmIncidentService.list(query).stream().map((DutyAlarmIncident::getDutyAlarmId)).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(alarmIds)) {
                return new PageResult<>(0, Lists.newArrayList());
            }
            String where = alarmIds.stream().map(id -> "'%s'".formatted(id)).collect(Collectors.joining(","));
            where = "id in (%s)".formatted(where);
            whereList.add(where);
        }
        if(CollectionUtils.isNotEmpty(whereList)){
            String whereSql = whereList.stream().collect(Collectors.joining(" and "));
            sql = sql + " where " + whereSql;
        }
        String countSql = "select count(1) as cnt " + sql;
        long total = getCount(countSql);
        pageIndex = pageIndex - 1;
        if (total <= pageIndex * pageSize) {
            return new PageResult<>(0, Lists.newArrayList());
        }
        String listSql = "select * %s order by create_time desc limit %d, %d".formatted(sql, pageIndex * pageSize, pageSize);
        return new PageResult<>(total, clickhouseHandlerFactory.get().query(DutyConstat.CLICKHOUSE_DATABASE, listSql));
    }

    private long getCount(String sql) {
        List<Map<String, Object>> query = clickhouseHandlerFactory.get().query(DutyConstat.CLICKHOUSE_DATABASE, sql);
        if (CollectionUtils.isEmpty(query)) {
            return 0;
        }
        return ((UnsignedLong) query.get(0).get("cnt")).longValue();
    }

    @Override
    public void clearIncidentCache(String incidentId) {
        incidentRedisTemplate.delete("IncidentInfo::%s".formatted(incidentId));
    }


    @Override
    public void setIncidentByAlertGroupingParams(String workspaceId, Map<String, Object> params, DutyIncident incident) {
        int ttlMinutes = 2*60 + RandomUtil.getRandomInt(60);
        redisTemplate.opsForValue().set("AGParam2IncidentId::%s:%s".formatted(workspaceId, DigestUtils.md5Hex(params.toString())), incident.getId(), ttlMinutes, TimeUnit.MINUTES);
        incidentRedisTemplate.opsForValue().set("IncidentInfo::%s".formatted(incident.getId()), incident, ttlMinutes, TimeUnit.MINUTES);
    }

    @Override
    public DutyIncident getIncidentByAlertGroupingParams(String workspaceId, Map<String, Object> params, Integer timeWindow) {
        String incidentId = (String) redisTemplate.opsForValue().get("AGParam2IncidentId::%s:%s".formatted(workspaceId, DigestUtils.md5Hex(params.toString())));
        DutyIncident incident = StringUtils.isBlank(incidentId) ? null : incidentRedisTemplate.opsForValue().get("IncidentInfo::%s".formatted(incidentId));
        if(incident == null || timeWindow == null || incident.getCreateTime().after(new Date(System.currentTimeMillis() - timeWindow * 60L * 1000))){
            return incident;
        }
        return null;
    }

    public DutyIncident getIncidentFromDBByParams(String workspaceId, Map<String, Object> params, Integer timeWindow) {
        QueryWrapper<DutyIncident> query = new QueryWrapper<>();
        query.lambda().eq(DutyIncident::getWorkspaceId, workspaceId);
        query.lambda().in(DutyIncident::getStatus, IncidentStatus.getOpenStatus());
        if(timeWindow != null){
            query.lambda().ge(DutyIncident::getCreateTime, new Date(System.currentTimeMillis() - timeWindow * 60L * 1000L));
        }
        if(params != null){
            params.entrySet().stream().forEach(entry -> {
                if(StringUtils.equalsIgnoreCase("title", entry.getKey())){
                    query.lambda().eq(DutyIncident::getTitle, entry.getValue());
                } else if(StringUtils.equalsIgnoreCase("level", entry.getKey())){
                    query.lambda().eq(DutyIncident::getLevel, entry.getValue());
                } else {
                    String tag = entry.getKey().substring(entry.getKey().indexOf(".")+1);
                    query.eq("tags->>'$.\"%s\"'".formatted(tag), entry.getValue());
                }
            });
        }
        query.lambda().orderByDesc(DutyIncident::getCreateTime).last("limit 1");
        return dutyIncidentService.getOne(query);
    }

    public List<IncidentCountDTO> getIncidentCount(List<String> dutyWorkspaceIds) {
        if(CollectionUtils.isEmpty(dutyWorkspaceIds)) {
            return Lists.newArrayList();
        }
        QueryWrapper<DutyIncident> query = new QueryWrapper<>();
        query.lambda().in(DutyIncident::getWorkspaceId, dutyWorkspaceIds).in(DutyIncident::getStatus, IncidentStatus.triggered.getValue(), IncidentStatus.processing.getValue());
        query.lambda().groupBy(DutyIncident::getWorkspaceId, DutyIncident::getStatus);
        query.select("workspace_id", "status", "count(*) as cnt");
        List<Map<String, Object>> dataList = dutyIncidentService.getBaseMapper().selectMaps(query);
        Map<String, Map<Integer, Long>> results = dataList.stream()
                .collect(Collectors.groupingBy(
                        m -> (String) m.get("workspace_id"),
                        Collectors.toMap(
                                m -> ((Number) m.get("status")).intValue(),
                                m -> ((Number) m.get("cnt")).longValue()
                        )
                ));

        return dutyWorkspaceIds.stream().map(id-> new IncidentCountDTO(id, results.get(id))).collect(Collectors.toList());
    }

    public boolean saveOriginalAlarm(OriginalAlarmDTO originalAlarm) {
        ObjectMapper mapper = new ObjectMapper();
        Map<String, Object> columns = mapper.convertValue(originalAlarm, new TypeReference<>() {
        });
        long recordMemorySize = ClickhouseSqlUtil.approximateSize(columns);
        String dbName = ClickhouseSqlUtil.toClickhouseName(DutyConstat.CLICKHOUSE_DATABASE);
        return clickhouseHandlerFactory.getClickhouseWriter().appendData(new ClickhouseData(new ClickhouseKey(dbName, "duty_original_alarm"), columns, recordMemorySize, false), false);
    }
}
