package us.zoom.cube.duty.util;

import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.AbstractResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR> song
 */
public class HttpUtil {

	private static final PoolingHttpClientConnectionManager CONNECTION_MANAGER;


	private static final CloseableHttpClient HTTP_CLIENT;

	static {

		CONNECTION_MANAGER = new PoolingHttpClientConnectionManager();
		CONNECTION_MANAGER.setMaxTotal(100);
		CONNECTION_MANAGER.setDefaultMaxPerRoute(20);


		RequestConfig requestConfig = RequestConfig.custom()
				.setSocketTimeout(5000)
				.setConnectTimeout(5000)
				.setConnectionRequestTimeout(5000)
				.build();


		HTTP_CLIENT = HttpClients.custom()
				.setConnectionManager(CONNECTION_MANAGER)
				.setDefaultRequestConfig(requestConfig)
				.build();
	}


	public static <T> T doPost(String url, String json, Header[] headers, ResponseHandler<T> responseHandler) throws IOException {
		HttpPost httpPost = new HttpPost(url);
		httpPost.setHeaders(headers);
		httpPost.setHeader("Content-Type", "application/json");
		httpPost.setEntity(new StringEntity(json, "UTF-8"));
		return HTTP_CLIENT.execute(httpPost, responseHandler);
	}


	public static void close() throws IOException {
		HTTP_CLIENT.close();
		CONNECTION_MANAGER.close();
	}
}
