package us.zoom.cube.duty.handler.smart_rca;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import us.zoom.cube.duty.service.IncidentService;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.mq.client.clients.consumer.handle.SingleHandler;
import us.zoom.mq.common.ConsumeResult;
import us.zoom.mq.common.entity.TaskEntity;

import java.util.Set;

/**
 * <AUTHOR> song
 */
@Component
@RequiredArgsConstructor
public class SmartRCACommandHandler implements SingleHandler<String> {

    private TypeReference<String> typeReference = new TypeReference<>() {};

    private final IncidentService incidentService;

    private static final Set<String> TASK_TYPES = Sets.newHashSet("cubeduty_task_type");

    @Override
    public ConsumeResult onMessage(TaskEntity<String> taskEntity) {
        String message = taskEntity.getPayload();
        SmartRCACommand payload = JsonUtils.toObject(message, SmartRCACommand.class);
        switch (payload.getCommand()) {
            case rca_generate -> incidentService.smartRCAGenerate((String) payload.getData().get("incidentId"),  payload.getData());
        }
        return ConsumeResult.SUCCESS;
    }

    @Override
    public Set<String> taskTypes() {
        return TASK_TYPES;
    }

    @Override
    public TypeReference<String> type() {
        return typeReference;
    }
}
