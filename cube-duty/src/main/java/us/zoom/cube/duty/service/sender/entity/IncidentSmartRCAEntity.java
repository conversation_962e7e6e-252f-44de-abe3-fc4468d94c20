package us.zoom.cube.duty.service.sender.entity;

import com.google.common.net.UrlEscapers;
import lombok.Getter;
import lombok.Setter;
import us.zoom.cube.duty.config.SpringContextHelper;
import us.zoom.cube.duty.dao.entity.DutyIncident;
import us.zoom.cube.duty.dto.WorkspaceDTO;

import java.util.Map;

public class IncidentSmartRCAEntity extends IncidentExt {
    @Getter
    private String smartRCALink;

    @Setter
    @Getter
    private Map<String, Object> extraData;
    public IncidentSmartRCAEntity(DutyIncident source, WorkspaceDTO workspaceInfo, Map<String, Object> extraData){
        super(source, workspaceInfo);
        this.smartRCALink = SpringContextHelper.getProperty("incident-smart_rca-basic-link").replace("{title}", UrlEscapers.urlFragmentEscaper().escape(this.getTitle())).replace("{incidentId}", this.getId());
        this.extraData = extraData;
    }
}
