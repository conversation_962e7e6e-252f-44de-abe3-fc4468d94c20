package us.zoom.cube.duty.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;
import us.zoom.cube.duty.dao.entity.DutyIncident;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.Map;

@Getter
@Setter
public class IncidentDTO extends DutyIncident {

    @JsonIgnore
    private Map<String,Object> extInfo;

    public IncidentDTO(DutyIncident dutyIncident, Map<String,Object> extInfo) {
        BeanUtils.copyProperties(dutyIncident, this);
        this.extInfo = extInfo;
    }

    @JsonProperty(value = "related_alarm_count", access = JsonProperty.Access.READ_ONLY)
    public long getRelatedAlarmCount() {
        return extInfo == null? null : (long) extInfo.get("related_alarm_count");
    }

    @JsonProperty(value = "last_received_time", access = JsonProperty.Access.READ_ONLY)
    public Date getLastReceivedTime() {
        return extInfo == null? null : Date.from(((LocalDateTime) extInfo.get("last_received_time")).atZone(ZoneId.systemDefault()).toInstant());
    }
}