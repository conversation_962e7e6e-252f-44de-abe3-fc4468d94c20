package us.zoom.cube.duty.constat;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.Getter;
import us.zoom.cube.duty.dao.entity.DutyIncident;
import us.zoom.cube.duty.dto.WorkspaceDTO;
import us.zoom.cube.duty.service.sender.IncidentSingleSenderService;
import us.zoom.cube.duty.service.sender.entity.IncidentSmartRCAEntity;
import us.zoom.cube.duty.service.sender.entity.IncidentStatusChangeEntity;
import us.zoom.cube.duty.service.sender.entity.SendTo;

import java.util.Map;

public enum IncidentOperateAction {
    Triggered(IncidentStatus.triggered.getValue(), "triggered"){
        @Override
        public LambdaUpdateWrapper<DutyIncident> buildUpdateWrapper(DutyIncident incident, String operatorUser) {
            return null;
        }

        @Override
        public boolean sendNotification(IncidentSingleSenderService senderService, DutyIncident incident, String operator, WorkspaceDTO workspaceInfo, SendTo sendTo, Map<String,Object> extraData) {
            return true;
        }
    },
    ACK(IncidentStatus.processing.getValue(), "acknowledged"){
        @Override
        public LambdaUpdateWrapper<DutyIncident> buildUpdateWrapper(DutyIncident incident, String operatorUser) {
            return IncidentOperateAction.buildUpdateWrapper(incident, operatorUser, null, this);
        }

        @Override
        public boolean sendNotification(IncidentSingleSenderService senderService, DutyIncident incident, String operator, WorkspaceDTO workspaceInfo, SendTo sendTo, Map<String,Object> extraData) {
            return senderService.sendStatusChange(sendTo, new IncidentStatusChangeEntity(incident, operator, getActionName(), workspaceInfo));
        }
    },
    UnAck(IncidentStatus.triggered.getValue(), "unacknowledged"){
        @Override
        public LambdaUpdateWrapper<DutyIncident> buildUpdateWrapper(DutyIncident incident, String operatorUser) {
            return IncidentOperateAction.buildUpdateWrapper(incident, null, null, this);
        }

        @Override
        public boolean sendNotification(IncidentSingleSenderService senderService, DutyIncident incident, String operator, WorkspaceDTO workspaceInfo, SendTo sendTo, Map<String,Object> extraData) {
            return senderService.sendStatusChange(sendTo, new IncidentStatusChangeEntity(incident, operator, getActionName(), workspaceInfo));
        }
    },
    CLOSE(IncidentStatus.closed.getValue(), "closed"){
        @Override
        public LambdaUpdateWrapper<DutyIncident> buildUpdateWrapper(DutyIncident incident, String operatorUser) {
            return  IncidentOperateAction.buildUpdateWrapper(incident, incident.getAckUser(), operatorUser, this);
        }

        @Override
        public boolean sendNotification(IncidentSingleSenderService senderService, DutyIncident incident, String operator, WorkspaceDTO workspaceInfo, SendTo sendTo, Map<String,Object> extraData) {
            return senderService.sendStatusChange(sendTo, new IncidentStatusChangeEntity(incident, operator, getActionName(), workspaceInfo));
        }
    },
    REOPEN(IncidentStatus.triggered.getValue(), "reopen") {
        @Override
        public LambdaUpdateWrapper<DutyIncident> buildUpdateWrapper(DutyIncident incident, String operatorUser) {
            return IncidentOperateAction.buildUpdateWrapper(incident, null, null, this);
        }

        @Override
        public boolean sendNotification(IncidentSingleSenderService senderService, DutyIncident incident, String operator, WorkspaceDTO workspaceInfo, SendTo sendTo, Map<String,Object> extraData) {
            return senderService.sendStatusChange(sendTo, new IncidentStatusChangeEntity(incident, operator, getActionName(), workspaceInfo));
        }
    },
    SMART_RCA_GENERATION(-1, "smart_rca_report_generated") {
        @Override
        public LambdaUpdateWrapper<DutyIncident> buildUpdateWrapper(DutyIncident incident, String operatorUser) {
            return null;
        }

        @Override
        public boolean sendNotification(IncidentSingleSenderService senderService, DutyIncident incident, String operator, WorkspaceDTO workspaceInfo, SendTo sendTo, Map<String,Object> extraData) {
            return senderService.sendSmartRCALink(sendTo, new IncidentSmartRCAEntity(incident, workspaceInfo, extraData));
        }
    };


    @Getter
    private final int status;
    @Getter
    private final String actionName;

    IncidentOperateAction(int status, String actionName) {
        this.status = status;
        this.actionName = actionName;
    }

    public static boolean isClosed(int status) {
        return status == 2;
    }

    public abstract LambdaUpdateWrapper<DutyIncident> buildUpdateWrapper(DutyIncident incident, String operatorUser);

    public abstract boolean sendNotification(IncidentSingleSenderService senderService, DutyIncident incident, String operator, WorkspaceDTO workspaceInfo, SendTo sendTo, Map<String,Object> extraData);

    private static LambdaUpdateWrapper<DutyIncident> buildUpdateWrapper(DutyIncident incident, String ackUser, String closeUser, IncidentOperateAction action) {
        LambdaUpdateWrapper<DutyIncident> updateWrapper = new UpdateWrapper<DutyIncident>().lambda();
        updateWrapper.eq(DutyIncident::getId, incident.getId());
        updateWrapper.set(DutyIncident::getStatus, action.getStatus());
        updateWrapper.set(DutyIncident::getAckUser, ackUser);
        updateWrapper.set(DutyIncident::getCloseUser, closeUser);
        return updateWrapper;
    }
}
