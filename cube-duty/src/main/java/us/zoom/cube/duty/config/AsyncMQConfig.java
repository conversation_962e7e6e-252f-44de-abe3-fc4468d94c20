package us.zoom.cube.duty.config;

import jakarta.annotation.PreDestroy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import us.zoom.cube.duty.handler.smart_rca.SmartRCACommand;
import us.zoom.infra.asyncmq.AsyncMQRotateHandler;
import us.zoom.mq.client.AsyncMQ;
import us.zoom.mq.client.DefaultAsyncMQ;
import us.zoom.mq.client.clients.consumer.Consumer;
import us.zoom.mq.client.clients.consumer.handle.SingleHandler;
import us.zoom.mq.client.pojo.Subscriber;
import jakarta.annotation.PostConstruct;

@Service
@Slf4j
@RequiredArgsConstructor
public class AsyncMQConfig {

    private volatile Consumer consumer;

    private volatile AsyncMQ asyncMQ;

    @Value("${async.mq.endpoint}")
    private String alarmAsyncMqEndpoint;

    @Value("${alarm.async.mq.username}")
    private String alarmAsyncMqUsername;

    //support hot reload in listener already
    @Value("${alarm.async.mq.password}")
    private String alarmAsyncMqPassword;

    //support hot reload in listener already
    @Value("${duty.async.mq.smart-rca.topic:us_smart_rca}")
    private String smartRcaTopic;

    private final SingleHandler<String> smartRCACommandHandler;

    @PostConstruct
    public void initAsyncMQ() {
        init(alarmAsyncMqEndpoint, alarmAsyncMqUsername, alarmAsyncMqPassword);
        registerSmartRCACommandHandlerConsumer(smartRcaTopic, "cube-duty", smartRCACommandHandler);
    }

    private void init(String endpoint, String username, String password) {
        log.info("init AsyncMQInstance.");
        if (StringUtils.isBlank(endpoint) || StringUtils.isBlank(username) || StringUtils.isBlank(password)) {
            throw new RuntimeException("endpoint, username and password cannot be empty");
        }

        asyncMQ = new DefaultAsyncMQ(endpoint, username, password);
        consumer = asyncMQ.consumer();

        AsyncMQRotateHandler.addMQListener(asyncMQ, "alarm.async.mq.password", username);

        consumer.start();
        log.info("init AsyncMQInstance success.");
    }

    public void registerSmartRCACommandHandlerConsumer(String topic, String groupId, SingleHandler<String> handler) {
        Subscriber subscriber = consumer.registerSubscriber(topic, groupId);
        consumer.registerSingleHandler(subscriber, handler);
        consumer.start(subscriber, 1, 1);
        log.info("start consumer, topic:{}, group id:{}", topic, groupId);
    }

    @PreDestroy
    public void shutdown() {
        try {
            log.info("shutdown AsyncMQInstance.");
            asyncMQ.shutdown();
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage(), e);
        }
    }
}
