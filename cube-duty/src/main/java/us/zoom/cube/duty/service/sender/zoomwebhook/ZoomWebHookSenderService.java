package us.zoom.cube.duty.service.sender.zoomwebhook;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import freemarker.ext.beans.BeansWrapper;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Header;
import org.apache.http.message.BasicHeader;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Service;
import us.zoom.cube.config.client.api.ConfigApi;
import us.zoom.cube.duty.service.sender.entity.IncidentOpenEntity;
import us.zoom.cube.duty.service.sender.entity.IncidentSmartRCAEntity;
import us.zoom.cube.duty.service.sender.entity.IncidentStatusChangeEntity;
import us.zoom.cube.duty.service.sender.entity.IncidentStormEntity;
import us.zoom.cube.duty.service.sender.IncidentSingleSenderService;
import us.zoom.cube.duty.service.sender.entity.SendTo;
import us.zoom.cube.duty.util.HttpUtil;
import us.zoom.infra.dao.model.SysParaDO;

import java.io.IOException;
import java.io.StringReader;
import java.io.StringWriter;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
@RequiredArgsConstructor
public class ZoomWebHookSenderService implements IncidentSingleSenderService {

    private static Configuration cfg = new Configuration(Configuration.VERSION_2_3_31);

    static {
        cfg.setDefaultEncoding("UTF-8");
        cfg.setSharedVariable("statics",
                ((BeansWrapper) cfg.getObjectWrapper()).getStaticModels());
    }

    private final ConfigApi configApi;

    private LoadingCache<String, Template> cache = CacheBuilder.newBuilder()
            .refreshAfterWrite(1, TimeUnit.MINUTES)
            .maximumSize(10)
            .build(new CacheLoader<>() {
                @SneakyThrows
                @Override
                public Template load(String key) {
                    SysParaDO res = configApi.getSysParaByTypeAndParaKey("cube-duty-zoom-webhook-template", key);
                    String value = res.getValue();
                    return buildTemplate(key, value);
                }
            });

    private static Template buildTemplate(String name, String template) throws IOException {
        return new Template(name, new StringReader(template), cfg);
    }

    /**
     * @param sendTo
     * @param incident
     * @return
     */
    @Override
    public boolean sendOpen(SendTo sendTo, IncidentOpenEntity incident) {
        try {
            return send("open", sendTo, incident, incident.getId());
        } catch (Exception e) {
            log.error("send open incident message via zoom webhook channel failed", e);
        }
        return false;
    }

    @Override
    public boolean sendStorm(SendTo sendTo, IncidentStormEntity storm) {
        try {
            return send("storm", sendTo, storm, storm.getId());
        } catch (Exception e) {
            log.error("send storm message via zoom webhook channel failed", e);
        }
        return false;
    }

    @Override
    public boolean sendStatusChange(SendTo sendTo, IncidentStatusChangeEntity entity) {
        try {
            return send("status_change", sendTo, entity, entity.getId());
        } catch (Exception e) {
            log.error("send status change message via zoom webhook channel failed", e);
        }
        return false;
    }

    @Override
    public boolean sendSmartRCALink(SendTo sendTo, IncidentSmartRCAEntity entity) {
        try {
            return send("smart_rca_generate", sendTo, entity, entity.getId());
        } catch (Exception e) {
            log.error("send smart-rca generate message via zoom webhook channel failed", e);
        }
        return false;
    }

    private boolean send(String templateName, SendTo sendTo, Object entity, String groupKey) throws IOException, TemplateException, ExecutionException {
        StringWriter out = new StringWriter();
        this.cache.get(templateName).process(entity, out);
        String req = out.toString();
        Header[] headers = {new BasicHeader("Authorization", sendTo.getToken()), new BasicHeader("groupKey", groupKey)};
        return HttpUtil.doPost(sendTo.getEndpoint() + "?format=full", req, headers, httpResponse -> {
            if (httpResponse.getStatusLine().getStatusCode() == 200) {
                return true;
            } else {
                log.error("send status change message via zoom webhook channel failed, status code {}, request:{}, response:{}", httpResponse.getStatusLine().getStatusCode(), req, EntityUtils.toString(httpResponse.getEntity()));
                return false;
            }
        });
    }
}
