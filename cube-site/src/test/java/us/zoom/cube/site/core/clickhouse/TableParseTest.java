package us.zoom.cube.site.core.clickhouse;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import us.zoom.infra.clickhouse.ClickhouseHandler;
import us.zoom.infra.clickhouse.ClickhouseSchema;

/**
 * <AUTHOR>
 * @date 2023/01/03 9:32 AM
 */
@ExtendWith(MockitoExtension.class)
public class TableParseTest {


    @BeforeEach
    public void initMocks() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testNormalDistributedTable(){
        String db="Meeting_Web_asyncmq_application_cube_ch_env1_132d";
        String table="asyncmq_client_consumer_stats";
        String sql="CREATE TABLE Meeting_Web_asyncmq_application_cube_ch_env1_132d.asyncmq_client_consumer_stats\n" +
                "(\n" +
                "    `app` String DEFAULT 'no_available',\n" +
                "    `hostName` String DEFAULT 'no_available',\n" +
                "    `failover_count` Float64\n" +
                ")\n" +
                "ENGINE = Distributed('{cluster}', 'Meeting_Web_asyncmq_application_cube_ch_env1_132d', 'asyncmq_client_consumer_stats_click_local', rand())";

        ClickhouseSchema clickhouseSchema = ClickhouseHandler.parseCreateSql(db, table, sql);
        Assertions.assertEquals(clickhouseSchema.getLocalTable(), "asyncmq_client_consumer_stats_click_local");
        Assertions.assertEquals(4,clickhouseSchema.getColDef().size());
    }

    @Test
    public void testNormalLocalTable(){
        String db="Meeting_Web_asyncmq_application_cube_ch_env1_132d";
        String table="asyncmq_client_consumer_stats";
        String sql="CREATE TABLE Meeting_Web_asyncmq_application_cube_ch_env1_132d.asyncmq_client_consumer_stats_click_local\n" +
                "(\n" +
                "    `time` DateTime,\n" +
                "    `app` String,\n" +
                "    `hostName` String,\n" +
                "    `failover_count` Float64\n" +
                ")\n" +
                "ENGINE = ReplicatedMergeTree('/clickhouse/tables/{shard}/Meeting_Web_asyncmq_application_cube_ch_env1_132d.asyncmq_client_consumer_stats_click_local.3d7db90b3d9345f7ae2c01c6205222eb', '{replica}')\n" +
                "PARTITION BY toMonday(time + toIntervalDay(3))\n" +
                "ORDER BY time\n" +
                "TTL time + toIntervalDay(5)\n" +
                "SETTINGS index_granularity = 8192";

        ClickhouseSchema clickhouseSchema = ClickhouseHandler.parseCreateSql(db, table, sql);
        Assertions.assertEquals(clickhouseSchema.getZkPath(), "/clickhouse/tables/{shard}/Meeting_Web_asyncmq_application_cube_ch_env1_132d.asyncmq_client_consumer_stats_click_local.3d7db90b3d9345f7ae2c01c6205222eb");
        Assertions.assertEquals(4,clickhouseSchema.getColDef().size());
    }

    @Test
    public void testWithCodec(){
        String db="Meeting_Web_asyncmq_application_cube_ch_env1_132d";
        String table="asyncmq_client_consumer_stats";
        String sql="CREATE TABLE Meeting_Web_asyncmq_application_cube_ch_env1_132d.nginx_access_ct_click_local\n" +
                "(\n" +
                "    `time` DateTime CODEC(Delta(4), LZ4),\n" +
                "    `requestTime` Float64 CODEC(Gorilla),\n" +
                "    `hostName` LowCardinality(String),\n" +
                "    `request` LowCardinality(String),\n" +
                "    `count_4xx` Float64 CODEC(ZSTD(1)),\n" +
                "    `instanceId` String,\n" +
                "    `count_5xx` Float64 CODEC(Delta(8), ZSTD(1)),\n" +
                "    `count_2xx` Float64 CODEC(DoubleDelta, ZSTD(1)),\n" +
                "    `count_3xx` Float64 CODEC(Gorilla, LZ4),\n" +
                ")\n" +
                "ENGINE = ReplicatedMergeTree('/clickhouse/tables/{shard}/Meeting_Web_asyncmq_application_cube_ch_env1_132d.nginx_access_ct_click_local.2250d301df4943f4b7e1104102b41680', '{replica}')\n" +
                "PARTITION BY toMonday(time + toIntervalDay(3))\n" +
                "ORDER BY time\n" +
                "TTL time + toIntervalDay(40)\n" +
                "SETTINGS index_granularity = 8192";

        ClickhouseSchema clickhouseSchema = ClickhouseHandler.parseCreateSql(db, table, sql);
        Assertions.assertEquals(clickhouseSchema.getZkPath(), "/clickhouse/tables/{shard}/Meeting_Web_asyncmq_application_cube_ch_env1_132d.nginx_access_ct_click_local.2250d301df4943f4b7e1104102b41680");
        Assertions.assertEquals(9,clickhouseSchema.getColDef().size());
    }
}
