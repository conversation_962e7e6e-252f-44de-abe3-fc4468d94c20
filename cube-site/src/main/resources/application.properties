activatedProperties#choose environment
spring.profiles.active=perf_for_local
spring.kafka.listener.missing-topics-fatal=false
spring.lifecycle.timeout-per-shutdown-phase=20s

server.port=8080
server.shutdown=graceful
server.forward-headers-strategy=framework
server.tomcat.uri-encoding=UTF-8
#DruidDataSource setting
dataSource.filters=stat
dataSource.maxActive=20
dataSource.initialSize=1
dataSource.maxWait=60000
dataSource.minIdle=1
dataSource.timeBetweenEvictionRunsMillis=60000
dataSource.minEvictableIdleTimeMillis=300000
dataSource.testWhileIdle=true
dataSource.testOnBorrow=false
dataSource.testOnReturn=false
dataSource.poolPreparedStatements=true
dataSource.maxOpenPreparedStatements=20
dataSource.asyncInit=true

spring.main.allow-bean-definition-overriding=true

#calculate cfg
calculate.entry.class=us.zoom.cube.calc.CalculatorApplication

#login info
login.timeout=7200

spring.thymeleaf.mode=HTML5
spring.thymeleaf.encoding=UTF-8
spring.thymeleaf.content-type=text/html
spring.thymeleaf.prefix=classpath:/static

spring.mvc.static-path-pattern=/**
server.compression.enabled=true
server.compression.mime-types=application/json,application/xml,application/javascript,text/html,text/xml,text/plain,text/css,text/javascript
server.compression.min-response-size=1024
spring.web.resources.cache.cachecontrol.cache-public=true
spring.web.resources.cache.cachecontrol.no-cache=true
spring.web.resources.cache.cachecontrol.no-store=false
spring.web.resources.cache.cachecontrol.must-revalidate=false
spring.web.resources.cache.cachecontrol.max-age=86400
spring.main.allow-circular-references=true
spring.jpa.open-in-view=false

# kms config
kms.plain.key=dummy-key

tpl.manager.tenant=Cube_Template_Manager

tpl.manager.channel.im=cube_template_manager_im

alarm.channel.email.smtp.server=smtp.sendgrid.net

gwatch.endpoint=https://gwatch-agent-zcp.zoomdev.us

atlas.endpoint=https://atlas-console-ui.zoomdev.us
cube-duty.endpoint=https://cube-duty.corp.zoomdev.us
management.health.defaults.enabled=false
management.health.ping.enabled=true
management.endpoint.health.show-components=always
management.endpoint.health.show-details=always
management.endpoints.web.exposure.include=info,prometheus,up,health

fileServer.endpoint=file.zoomdev.us
outage-image.channelId=440
outage-image.channelName=outage-image
cube.outage.url.alert=https://cube-site.zoomdev.us:8443/

cube.outage.url.agent=https://new-dayone.zoomdev.us/api/v1/edo/service/ai/invoke
cube.outage.agent.id=addf0050-84de-4f86-8783-c51abc80f31b

outage-ucs.accountId=A481Jn0CQsS9ATG5Wq3amQ
outage-ucs.other.accountId=R2I-btf7T4emXV6LNyQleQ
outage-ucs.endPoint=https://qa01contactservice.zoomdev.us/rcs/api/v1/batch/contacts/search/emails
outage-ucsOrg.endPoint=https://qa01contactservice.zoomdev.us/api/v1/ucs/server/org/charts


server.max-http-request-header-size=100KB
cache-loader.startup.common-config.enable=false
cache-loader.startup.dash-config.enable=false

alarm.pull-query.topic=cube_pull_query_alarm
globaltracing.endpoint=https://linktrace.zoomdev.us
okta.csrf.disabled=true

server.servlet.session.cookie.same-site=lax
server.servlet.session.cookie.secure=false
server.servlet.session.cookie.http-only=true

# Session timeout (24 hours)
server.servlet.session.timeout=24h

# Enable session creation on demand
server.servlet.session.tracking-modes=cookie
hub.config.load.enable=false
agent.heart.beat.enabled=false
trace.autoBindTemplate.enable=false