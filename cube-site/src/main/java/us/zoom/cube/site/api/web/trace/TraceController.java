package us.zoom.cube.site.api.web.trace;

import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import jakarta.validation.constraints.NotBlank;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;
import us.zoom.cube.site.biz.trace.TraceQueryService;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.dto.trace.*;
import us.zoom.cube.site.lib.output.PageResult;
import us.zoom.cube.site.lib.output.trace.TraceDependencyResponse;
import us.zoom.cube.site.lib.output.trace.TraceHistogram;
import us.zoom.cube.site.lib.output.trace.TraceItem;
import us.zoom.cube.site.lib.output.trace.queryrange.TraceFeature;

import java.beans.PropertyEditorSupport;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @authoer: eason.jia
 * @createDate: 2022/6/29
 * @description:
 */
@RestController
@RequestMapping("/api/v1")
public class TraceController {

    private static final Logger logger = LoggerFactory.getLogger(TraceController.class);

    private static final long THREE_DAYS_AGO_MILLIS = 3 * 24 * 60 * 60 * 1000;

    @Autowired
    private TraceQueryService traceQueryService;

    @Value("${span_render_limit:2500}")
    private int spanRenderLimit;
    @Value("${max_spans_in_trace:250000}")
    private int maxSpansInTrace;

    @InitBinder
    protected void initBinder(WebDataBinder binder) {
        binder.registerCustomEditor(Integer.class, new PropertyEditorSupport() {
            public void setAsText(String value) {
                if (StringUtils.equalsAnyIgnoreCase(value, "null")) {
                    setValue(null);
                } else {
                    setValue(Integer.parseInt(value));
                }
            }
        });
    }

    @RequestMapping(value = "/traces/{traceId}", method = RequestMethod.GET)
    @ResponseBody
    public List<SearchSpansResult> searchTraces(@NotBlank @PathVariable("traceId") String traceId,
                                                @RequestParam(value = "spanId", required = false) String spanId,
                                                @RequestParam(value = "levelUp", defaultValue = "0") Integer levelUp,
                                                @RequestParam(value = "levelDown", defaultValue = "0") Integer levelDown,
                                                @RequestParam(value = "spanRenderLimit", required = false) Integer spanRenderLimit,
                                                @RequestParam(value = "maxSpansInTrace", required = false) Integer maxSpansInTrace) {
        if (StringUtils.equalsAnyIgnoreCase(spanId, "null")) {
            spanId = null;
        }
        if (spanRenderLimit == null || spanRenderLimit.longValue() == 0) {
            spanRenderLimit = this.spanRenderLimit;
        }
        if (maxSpansInTrace == null || maxSpansInTrace.longValue() == 0) {
            maxSpansInTrace = this.maxSpansInTrace;
        }
        List<SearchSpansResult> searchSpansResults = traceQueryService.searchTraces(traceId, spanId, levelUp, levelDown, spanRenderLimit, maxSpansInTrace);
        return searchSpansResults;
    }

    @RequestMapping(value = "/featureFlags", method = RequestMethod.GET)
    @ResponseBody
    public ResponseObject<List<TraceFeature>> featureFlags() {
        return ResponseObject.success(TraceFeature.DEFAULT_FEATURE_SET);
    }

    @RequestMapping(value = "/services", method = RequestMethod.POST)
    @ResponseBody
    public List<ServiceItem> searchServices(@RequestBody BasicTraceQueryParam getServicesParams) {
        getServicesParams.check();
        List<ServiceItem> serviceItems = traceQueryService.searchServices(getServicesParams);
        return serviceItems;
    }

    @RequestMapping(value = "/service/top_operations", method = RequestMethod.POST)
    @ResponseBody
    public List<TopOperationsItem> searchServiceTopOperations(@RequestBody BasicTraceQueryParam getServicesParams) {
        getServicesParams.check();
        List<TopOperationsItem> topOperationsItems = traceQueryService.queryServiceTopOperations(getServicesParams);
        return topOperationsItems;
    }

    @RequestMapping(value = "/service/top_level_operations", method = RequestMethod.POST)
    @ResponseBody
    public Map<String, List<String>> searchServiceTopLevelOperations(@RequestBody(required = false) BasicTraceQueryParam queryParam) {
        TopLevelOperationsQueryParam topLevelOperationsQueryParam = new TopLevelOperationsQueryParam();
        if (queryParam != null) {
            queryParam.check();
            if (StringUtils.isNotEmpty(queryParam.getServiceName())) {
                topLevelOperationsQueryParam.setServiceNames(Lists.newArrayList(queryParam.getServiceName()));
            }
            topLevelOperationsQueryParam.setStart(queryParam.getStart());
            topLevelOperationsQueryParam.setEnd(queryParam.getEnd());
        } else {
            long time = TimeUnit.MILLISECONDS.toNanos(System.currentTimeMillis() - THREE_DAYS_AGO_MILLIS);
            topLevelOperationsQueryParam.setStart(time);
            topLevelOperationsQueryParam.setEnd(time);
        }
        Map<String, List<String>> resp = traceQueryService.getTopLevelOperations(topLevelOperationsQueryParam);
        return resp;
    }

    @RequestMapping(value = "/listErrors", method = RequestMethod.POST)
    @ResponseBody
    public List<TraceError> listTraceErrors(@RequestBody ListErrorsParams errorsParams) {
        errorsParams.check();
        return traceQueryService.listTraceErrors(errorsParams);
    }

    @RequestMapping(value = "/countErrors", method = RequestMethod.POST)
    @ResponseBody
    public Long countTraceErrors(@RequestBody ListErrorsParams errorsParams) {
        errorsParams.check();
        return traceQueryService.countErrors(errorsParams);
    }

    @RequestMapping(value = {"/errorFromGroupID", "/errorFromErrorID"}, method = RequestMethod.GET)
    @ResponseBody
    public ErrorWithSpan getErrorFromGroupID(@RequestParam(value = "groupID", required = false) String groupId,
                                                   @RequestParam("timestamp") Long timestamp,
                                                   @RequestParam(value = "errorID", required = false) String errorId) {
        GetErrorParams getErrorParams = GetErrorParams.builder()
                .errorID(errorId)
                .groupID(groupId)
                .timestamp(timestamp)
                .build();
        return traceQueryService.getErrorWithSpanList(getErrorParams);
    }

    @RequestMapping(value = "/nextPrevErrorIDs", method = RequestMethod.GET)
    @ResponseBody
    public NextPrevErrorIDs getErrorFromErrorID(@RequestParam(value = "groupID", required = false) String groupId,
                                                @RequestParam("timestamp") Long timestamp,
                                                @RequestParam(value = "errorID", required = false) String errorId) {
        GetErrorParams getErrorParams = GetErrorParams.builder()
                .errorID(errorId)
                .groupID(groupId)
                .timestamp(timestamp)
                .build();
        return traceQueryService.getNextPrevErrorID(getErrorParams);
    }

    @RequestMapping(value = "/dependency_graph", method = RequestMethod.POST)
    @ResponseBody
    public List<ServiceMapDependencyResponseItem> getDependencyGraph(@RequestBody BasicTraceQueryParam queryParam) {
        return traceQueryService.getDependencyGraph(queryParam);
    }

    @RequestMapping(value = "/settings/apdex", method = RequestMethod.GET)
    @ResponseBody
    public List<Map<String, Object>> apdex(@RequestParam("services") String services) {
        return Splitter.on(",").splitToList(services)
                .stream()
                .map(service -> ImmutableMap.<String, Object>builder()
                        .put("serviceName", service)
                        .put("threshold", 0.5)
                        .put("excludeStatusCodes", "")
                        .build())
                .collect(Collectors.toList());
    }


    @RequestMapping(value = "/trace/meta", method = RequestMethod.GET)
    @ResponseBody
    public ResponseObject<List<ClusterAndRegion>> getTraceMetaData() {
        return ResponseObject.success(traceQueryService.getTraceServiceMetaData());
    }

    @RequestMapping(value = "/trace/traces", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject<PageResult<TraceItem>> queryTraces(@RequestBody TraceQueryRequest request) {
        request.check();
        PageResult<TraceItem> response = traceQueryService.queryTraces(request);
        return ResponseObject.success(response);
    }

    @RequestMapping(value = "/trace/histograms", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject<List<TraceHistogram>> histograms(@RequestBody TraceQueryRequest request) {
        List<TraceHistogram> response = traceQueryService.queryTraceHistogram(request);
        return ResponseObject.success(response);
    }


    @RequestMapping(value = "/trace/dependency", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject<TraceDependencyResponse> getDependency(@RequestBody TraceDependencyRequest request) {
        request.check();
        TraceDependencyResponse response = traceQueryService.queryTraceDependency(request);
        return ResponseObject.success(response);
    }
}
