package us.zoom.cube.site.biz;

import com.alibaba.fastjson.JSON;
import com.zoom.op.monitor.dao.alarm.AlarmDefinitionDao;
import com.zoom.op.monitor.domain.TagInputDo;
import com.zoom.op.monitor.domain.alarm.AlarmConfigStatusEnum;
import com.zoom.op.monitor.domain.alarm.AlarmDefinition;
import com.zoom.op.monitor.domain.alarm.AlarmSourceTypeEnum;
import com.zoom.op.monitor.domain.alarm.Channel;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import us.zoom.cube.lib.integrations.MetricsFieldTypeEnum;
import us.zoom.cube.site.biz.ai.AiCheckService;
import us.zoom.cube.site.biz.alarm.AlarmDefinitionCheckServiceImpl;
import us.zoom.cube.site.biz.alarm.AlarmDefinitionServiceImpl;
import us.zoom.cube.site.biz.alarm.rule.ValidationResult;
import us.zoom.cube.site.biz.alarm.rule.baseimpl.alarm.AlarmCheckDTO;
import us.zoom.cube.site.biz.alarm.rule.impl.AlarmDefinitionCheck;
import us.zoom.cube.site.core.ChannelHandler;
import us.zoom.cube.site.core.DashHandler;
import us.zoom.cube.site.core.UserHandler;
import us.zoom.cube.site.core.tag.ResourceTypeConstant;
import us.zoom.cube.site.core.tag.TagConvertUtil;
import us.zoom.cube.site.core.tag.service.ResourceTagService;
import us.zoom.cube.site.core.tag.service.TagService;
import us.zoom.cube.site.lib.input.FullSearchDashInput;
import us.zoom.cube.site.lib.input.alarm.batch.AlarmBatchInput;
import us.zoom.cube.site.lib.input.alarm.batch.AlarmDefinitionInput;
import us.zoom.cube.site.lib.input.tag.input.ResourceTagInfoInput;
import us.zoom.cube.site.lib.output.config.metrics.FieldOrTag;
import us.zoom.cube.site.lib.output.tag.TagInfoOut;
import us.zoom.infra.dao.model.DashHasUserRelaDO;
import us.zoom.infra.dao.model.MetricsDO;
import us.zoom.infra.dao.service.MetricsDAO;
import us.zoom.infra.dao.service.MetricsFieldDAO;
import us.zoom.infra.thread.NamedThreadFactory;
import us.zoom.infra.thread.ThreadLocalStore;
import us.zoom.infra.utils.CommonSplitConstants;

import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: <EMAIL>
 * @date: 2024-07-23 14:24
 **/

@Service
@RequiredArgsConstructor
@Slf4j
public class DataParserBatchImportService {

    private final AlarmDefinitionCheck alarmDefinitionCheck;
    private final MetricsService metricsService;
    private final AlarmDefinitionDao alarmDefinitionDao;
    private final UserHandler userHandler;
    private final MetricsDAO metricsDAO;
    private final MetricsFieldDAO metricsFieldDAO;
    private final ChannelHandler channelHandler;
    private final DashHandler dashHandler;
    private final DataParserBatchExportService dataParserBatchExportService;
    private final AiCheckService aiCheckService;
    private final AlarmDefinitionServiceImpl alarmDefinitionService;
    private final AlarmDefinitionCheckServiceImpl alarmDefinitionCheckService;
    private final DerivedMetricService derivedMetricService;
    private final TagService tagService;

    /**
     * Batch operation is a low-frequency operation. When the number of simultaneous operations is greater than 5, multi-threaded operation is enabled.
     * In most cases, threads are idle, so set core=0 to avoid the maintenance costs brought by idle threads, such as thread context, CPU registers, etc.
     */
     private static ExecutorService alarmCacheThreadPool = new ThreadPoolExecutor(0, 20,
            60L, TimeUnit.SECONDS,
            new LinkedBlockingQueue(200),
            new NamedThreadFactory("Alarm Bbatch TheadPool "), new ThreadPoolExecutor.CallerRunsPolicy());
    private final ResourceTagService resourceTagService;

    /**
     * Check the data fields
     *
     * @param alarmBatchImportInput
     * @return
     */
    public List<ValidationResult> check(AlarmBatchInput alarmBatchImportInput) {
        List<AlarmDefinitionInput> alarmList = alarmBatchImportInput.getAlarmList();
        Assert.isTrue(CollectionUtils.isNotEmpty(alarmList), "The imported data cannot be empty");
        //The default maximum limit for batch operations is 30, which can be configured through the site interface
        Long limit = dataParserBatchExportService.getBatchLimit();
        Assert.isTrue(alarmList.size() <= limit, "A maximum of " + limit + " records can be batch operated");
        log.info("{} execute batch check, the data is: {}", alarmBatchImportInput.getUserId(), JSON.toJSONString(alarmBatchImportInput));
        //The currently imported data should not have two records with the same alarm name.
        checkRepeatName(alarmList);
        List<AlarmDefinition> existAlarmList = getExistedAlarmDefinitions(alarmBatchImportInput.getTenantId(), alarmList);
        existAlarmList=aiCheckService.checkAndReplaceAiAlarm(existAlarmList);
        Map<String, String> existedAlarmMap = existAlarmList.stream().collect(Collectors.toMap(AlarmDefinition::getName, AlarmDefinition::getId));
        //Backfilling id is mainly to pave the way for batchImport. Import needs to execute check() again
        alarmList.forEach(alarm -> {
            if (existedAlarmMap.containsKey(alarm.getName())) {
                alarm.setId(existedAlarmMap.get(alarm.getName()));
            }
            alarm.setTenantId(alarmBatchImportInput.getTenantId());
            alarm.setUserId(alarmBatchImportInput.getUserId());
            if (AlarmSourceTypeEnum.SQL.equals(alarm.getSourceType())) {
                alarm.setStatus(AlarmConfigStatusEnum.PENDING);
            }
        });
        List<ValidationResult> validationResults = getValidationResults(alarmList, existAlarmList, alarmBatchImportInput.getTenantId());
        log.info("The check  result is: {}", JSON.toJSONString(validationResults));
        return validationResults;
    }

    private List<AlarmDefinition> getExistedAlarmDefinitions(String tenantId, List<AlarmDefinitionInput> alarmList) {
        List<String> alarmNameList = alarmList.stream().map(e -> e.getName()).collect(Collectors.toList());
        List<AlarmDefinition> alarmDefinitionList = alarmDefinitionDao.findAllByAlarmNamesAndTenantId(alarmNameList, tenantId);
        if(!CollectionUtils.isEmpty(alarmDefinitionList)){
            alarmDefinitionList.stream().forEach(e -> {
                List<TagInputDo> tagInputDos = resourceTagService.listResourceTagOutDoV2(ThreadLocalStore.getTenantInfoLocal(), e.getId(),
                        ResourceTypeConstant.RESOURCE_AlARM_TYPE);
                e.setLabelInfoList(tagInputDos);
            });
        }
        return alarmDefinitionList;
    }


    /**
     * Batch import interface
     *
     * @param alarmBatchInput
     * @return
     */
    public boolean batchImport(AlarmBatchInput alarmBatchInput) {
        //Execute check again
        List<ValidationResult> check = check(alarmBatchInput);
        boolean allMatch = check.stream().allMatch(ValidationResult::checkPass);
        Assert.isTrue(allMatch, "Data verification failed");
        //Data verification object AlarmBatchInput is converted into AlarmDefinition
        List<AlarmDefinition> alarmDefinitionList = JSON.parseArray(JSON.toJSONString(alarmBatchInput.getAlarmList()), AlarmDefinition.class);
        //Transaction splitting reduces the time for opening and closing transactions to the time for executing the actual import operation.
        return ((DataParserBatchImportService) AopContext.currentProxy()).doBatchImport(alarmDefinitionList, alarmBatchInput.getUserId(), alarmBatchInput.getTenantId());
    }

    /**
     * In the check phase, the data ID and other information will be supplemented, so in the import phase, simple judgment logic is performed.
     */
    @Transactional(rollbackFor = Exception.class)
    protected boolean doBatchImport(List<AlarmDefinition> alarmList, String userId, String tenantId) {
        String userName = userHandler.getNameById(userId);
        if (CollectionUtils.isNotEmpty(alarmList)) {
            alarmList.forEach(e -> {
                Date date = new Date();
                e.setTenantId(tenantId);
                e.setUserId(userId);
                e.setModifyTime(date);
                e.setEditor(userName);
                if (StringUtils.isBlank(e.getId())) {
                    e.setCreator(userName);
                    e.setCreateTime(date);
                    if (!CollectionUtils.isEmpty(e.getAlarmExtensionRelations())) {
                        e.getAlarmExtensionRelations().forEach(d -> {
                            d.setId(null);
                        });
                    }
                    if (!CollectionUtils.isEmpty(e.getNotifications())) {
                        e.getNotifications().forEach(d -> {
                            d.setId(null);
                        });
                    }
                    if (!CollectionUtils.isEmpty(e.getRules())) {
                        e.getRules().forEach(d -> {
                            d.setId(null);
                        });
                    }
                }

                if(StringUtils.isBlank(e.getId())) {
                    alarmDefinitionService.save(e);
                } else {
                    alarmDefinitionService.update(e);
                }

                if(!CollectionUtils.isEmpty(e.getLabelInfoList())){
                    ResourceTagInfoInput resourceTagInfoInput = new ResourceTagInfoInput();
                    resourceTagService.deleteResourceTagByName(e.getId(), ResourceTypeConstant.RESOURCE_AlARM_TYPE);
                    e.getLabelInfoList().forEach(labInfo->{
                        TagInfoOut tagInfoOut = TagConvertUtil.toTagInfoOut(labInfo);
                        String tagId = tagService.createOrUpdateTagInfo(tagInfoOut, ThreadLocalStore.getTenantInfoLocal(), ThreadLocalStore.getUserNameLocal());
                        if(tagId != null){
                            resourceTagInfoInput.setTagId(tagId);
                            resourceTagInfoInput.setResource(e.getId());
                            resourceTagInfoInput.setResourceType(ResourceTypeConstant.RESOURCE_AlARM_TYPE);
                            resourceTagService.addResourceTag(resourceTagInfoInput);
                        }
                    });
                }else{
                    resourceTagService.deleteResourceTagByName(e.getId(), ResourceTypeConstant.RESOURCE_AlARM_TYPE);
                }
            });

            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }


    private void checkRepeatName(List<AlarmDefinitionInput> alarmList) {
        List<String> duplicates = alarmList.stream()
                .collect(Collectors.groupingBy(e -> e.getName(), Collectors.counting()))
                .entrySet().stream()
                .filter(e -> e.getValue() > 1)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        Assert.isTrue(CollectionUtils.isEmpty(duplicates), String.format("The same alarm name exists in the same batch [%s], please check it", StringUtils.join(duplicates, CommonSplitConstants.COMMA_SPLIT)));
    }

    @SneakyThrows
    private List<ValidationResult> getValidationResults(List<AlarmDefinitionInput> alarmList, List<AlarmDefinition> existAlarmNameList, String tenantId) {
        //Verification parameter common object
        List<MetricsDO> metricsList = metricsDAO.getMetricsByTenant(tenantId);
        Map<String, List<String>> existedDashNameAndIdMap = dashHandler.fullSearchByName(new FullSearchDashInput(tenantId)).stream().collect(Collectors.groupingBy(DashHasUserRelaDO::getName, Collectors.mapping(DashHasUserRelaDO::getId, Collectors.toList())));
        List<Channel> existedChannelList = channelHandler.findByTenantId(tenantId);
        //Batch operation is a low-frequency operation. When the number of simultaneous operations is > 5, multi-threaded operation is enabled to avoid the overhead of invalid empty threads.
        if (alarmList.size() > 5) {
            List<ValidationResult> resultList = new ArrayList<>();
            List<CompletableFuture> futureList = new ArrayList<>();
            for (int i = 0; i < alarmList.size(); i++) {
                AlarmDefinitionInput alarm = alarmList.get(i);
                final Integer sort = i;
                futureList.add(
                        CompletableFuture.supplyAsync(() -> getValidationResult(existedDashNameAndIdMap, metricsList, existedChannelList, existAlarmNameList, alarm, sort), alarmCacheThreadPool)
                                .thenAcceptAsync(result -> resultList.add(result), alarmCacheThreadPool));
            }
            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[]{})).join();
            resultList.sort(Comparator.comparing(ValidationResult::getSort));
            log.info("The result of multithreaded execution");
            return resultList;
        } else {
            log.info("The result of single-threaded execution");
            return alarmList.stream().map(alarm -> getValidationResult(existedDashNameAndIdMap, metricsList, existedChannelList, existAlarmNameList, alarm, 0)).collect(Collectors.toList());
        }

    }

    private ValidationResult getValidationResult(Map<String, List<String>> existedDashNameAndIdMap, List<MetricsDO> metricsList, List<Channel> channelIdList, List<AlarmDefinition> existAlarmNameList, AlarmDefinitionInput alarm, Integer sort) {
        //begin Encapsulation of validation parameters
        AlarmCheckDTO alarmCheckDTO = new AlarmCheckDTO();
        alarmCheckDTO.setSource(alarm.getSource());
        alarmCheckDTO.setSourceType(alarm.getSourceType());
        alarmCheckDTO.setDerivedMetric(alarm.getDerivedMetric());
        alarmCheckDTO.setChannelIdList(channelIdList);
        alarmCheckDTO.setMetricsDOList(metricsList);
        alarmCheckDTO.setExistedDashNameAndIdMap(existedDashNameAndIdMap);
        List<MetricsDO> metricsDOList = alarmCheckDTO.getMetricsDOList();

        if (AlarmSourceTypeEnum.SQL.equals(alarm.getSourceType())) {
            AlarmDefinitionCheckServiceImpl.FieldAndTagResult result = alarmDefinitionCheckService.getFieldOrTagMapFromDerivedMetric(alarm.getDerivedMetric());
            Map<String, MetricsFieldTypeEnum> fieldTypeMap = result.getFieldTypeMap();
            List<String> tagKeys = result.getTagKeys();
            alarmCheckDTO.setTagNames(tagKeys);

            // for rule condition check and notification check
            List<FieldOrTag> fieldOrTagList = fieldTypeMap.entrySet().stream()
                    .map(entry -> {
                        String fieldOrTagName = entry.getKey();
                        FieldOrTag fieldOrTag = tagKeys.contains(fieldOrTagName) ? FieldOrTag.newTag() : FieldOrTag.newField();
                        fieldOrTag.setFieldOrTagName(fieldOrTagName);
                        fieldOrTag.setValueType(entry.getValue().name());
                        return fieldOrTag;
                    })
                    .collect(Collectors.toList());


            alarmCheckDTO.setFieldList(fieldOrTagList);

        }else {
            Optional<MetricsDO> metricsDOOptional = metricsDOList.stream().filter(f -> Objects.equals(alarm.getMetricsName(), f.getMetricsName())).findFirst();
            metricsDOOptional.ifPresent((metricsDO) -> {
                List<FieldOrTag> fieldList = alarmDefinitionService.getMetricTagAndFieldForAlarm(metricsDO.getId());
                alarmCheckDTO.setFieldList(fieldList);
                alarmCheckDTO.setMetricsDO(metricsDO);
                alarmCheckDTO.setTagNames(Arrays.asList(metricsDO.getTagNames().split(CommonSplitConstants.COMMA_SPLIT)));
            });
        }

        //end  Encapsulation of validation parameters

        //The core verification interface configures the verification rules for each attribute of the verified class, performs attribute traversal verification, and encapsulates the result of each attribute verification into json and returns it
        ValidationResult verification = alarmDefinitionCheck.verification(alarm, alarmCheckDTO, alarmDefinitionCheck.getRule());
        verification.putErrorSize(verification.size());
        Optional<AlarmDefinition> existAlarmDefinition = existAlarmNameList.stream().filter(f -> StringUtils.equals(alarm.getName(), f.getName())).findFirst();
        verification.putIsUpdate(existAlarmDefinition.isPresent());
        //Returns the existing alarm to facilitate the front-end plug-in to perform diff operations
        existAlarmDefinition.ifPresent((alarmDefinition) -> {
            String metricName = Optional.ofNullable(metricsDAO.getMetricsById(alarmDefinition.getMetricId()))
                    .map(MetricsDO::getMetricsName)
                    .orElse("");
            //Export includes this metrics field to optimize diff display effect
            verification.putExistAlarm(alarmDefinition, metricName);
        });
        verification.setSort(sort);
        return verification;
    }


}
