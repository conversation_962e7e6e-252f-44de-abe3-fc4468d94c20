package us.zoom.cube.site.api.web.out;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;
import us.zoom.cube.site.biz.MetricsService;
import us.zoom.cube.site.biz.ai.AiCkService;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.smartcube.CkQueryInput;
import us.zoom.cube.site.lib.query.ClickhouseQuery;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/out/server/smartCube")
@RequiredArgsConstructor
public class OutSmartCubeController {
    private final AiCkService aiCkService;
    private final MetricsService metricsService;
    @PostMapping("ck")
    public ResponseObject<List<Map<String, Object>>> executeQuery(@Valid @RequestBody CkQueryInput ckQueryInput) {
        return ResponseObject.success(aiCkService.executeQuery(ckQueryInput.getServiceName(), ckQueryInput.getSql()));
    }
    @PostMapping(value = "/queryClickHouse")
    public ResponseObject queryClickHouse(@Valid @RequestBody ClickhouseQuery metricQuery)  {
        return metricsService.queryClickHouse(metricQuery);
    }
    @RequestMapping("forward")
    public ModelAndView forward(String target, HttpServletRequest request) {
        return aiCkService.forward(target,request);
    }
}
