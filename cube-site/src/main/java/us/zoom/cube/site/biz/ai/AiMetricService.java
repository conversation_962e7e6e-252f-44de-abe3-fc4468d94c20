package us.zoom.cube.site.biz.ai;

import com.zoom.op.monitor.domain.alarm.v2.AlarmChannelDO;
import com.zoom.op.monitor.domain.alarm.v2.AlarmNotificationDO;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import us.zoom.cube.sdk.util.MonitorLogReporter;
import us.zoom.cube.site.core.AdAlarmHandler;
import us.zoom.cube.site.core.AdCfgHandler;
import us.zoom.cube.site.core.AdTrainHandler;
import us.zoom.cube.site.core.model.ad.*;
import us.zoom.infra.dao.model.AdCfgDO;
import us.zoom.infra.dao.model.AdTagHolidayCfgDO;
import us.zoom.infra.dao.model.AdTrainDO;
import us.zoom.infra.dao.model.MetricsDO;
import us.zoom.infra.dao.service.AdAlarmConditionDAO;
import us.zoom.infra.dao.service.AdTagHolidayCfgDAO;
import us.zoom.infra.dao.service.AlarmDAO;
import us.zoom.infra.enums.AdTrainStatusEnum;
import us.zoom.infra.enums.AlarmSwitchStatusEnum;
import us.zoom.infra.thread.ApiMonitorScheduler;
import us.zoom.infra.utils.CommonSplitConstants;
import us.zoom.infra.utils.Instance;
import us.zoom.infra.utils.IpUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-04-09 15:36
 */
@Service
@Slf4j
public class AiMetricService {
    private final Logger monitorLog = LoggerFactory.getLogger("Monitor");

    @Autowired
    private AdTrainHandler adTrainHandler;

    @Autowired
    private AdCfgHandler adCfgHandler;

    @Autowired
    private AdAlarmHandler adAlarmHandler;

    @Autowired
    private AdAlarmConditionDAO adAlarmConditionDAO;

    @Autowired
    private AdTagHolidayCfgDAO adTagHolidayCfgDAO;

    @Autowired
    private AlarmDAO alarmDAO;


    @PostConstruct
    public void printAdMetric() {
        ApiMonitorScheduler.getInstance().getScheduler().scheduleAtFixedRate(this::reportAdMetric, 0, 3, TimeUnit.HOURS);
    }

    public void reportAdMetric() {
        long begin = System.currentTimeMillis();
        log.info("[AiMetricService] Begin reportAdMetric, beginTime ={}", begin);
        List<AdCfgDO> adCfgDOList = getAdCfg();
        Map<String, List<AdCfgDO>> adCfgIdMap = getAdCfgMap(adCfgDOList);
        Map<String, String> metricNameMap = getMetricNameMap(adCfgDOList);
        List<String> adIds = Instance.ofNullable(adCfgDOList).stream().map(AdCfgDO::getId).collect(Collectors.toList());
        List<AiAlarmCfg> alarmCfgList = getAlarmCfg(adIds);
        Map<String, List<AdCfgDO>> adCfgMap = getAdCfgTenant(adCfgDOList);
        Map<String, List<AiAlarmCfg>> alarmCfgMap = getAlarmCfgTenant(alarmCfgList);
        Map<String, String> tenantMap = getTenantMap(adCfgMap);
        List<String> tenantIdList = new ArrayList<>(adCfgMap.keySet());
        List<AdTrainDO> adTrainDOList = adTrainHandler.getAdTrainByAdId(adIds);
        Map<String, List<MetricsDO>> metricMap = getMetricCfg(tenantIdList);
        Map<String, Map<Integer, List<AdTrainDO>>> trainStatusMap = getAdTrainCfgStatus(adTrainDOList);
        Map<String, List<AdTrainDO>> trainTenantMap = getAdTrainCfgTenant(adTrainDOList);
        reportCoverMetric(adCfgMap, alarmCfgMap, metricMap, trainTenantMap, trainStatusMap, tenantMap);
        reportDetailMetric(adCfgDOList, tenantMap);
        reportAiAlarmMetric(alarmCfgList, tenantMap, adCfgIdMap, metricNameMap);
        reportChannelMetric(alarmCfgList, tenantMap);
        long end = System.currentTimeMillis();
        log.info("[AiMetricService] End reportAdMetric, endTime = {}, cost = {}", end, end - begin);
    }

    public void reportCoverMetric(Map<String, List<AdCfgDO>> adCfgMap, Map<String, List<AiAlarmCfg>> alarmCfgMap,
                                  Map<String, List<MetricsDO>> metricMap, Map<String, List<AdTrainDO>> trainTenantMap,
                                  Map<String, Map<Integer, List<AdTrainDO>>> trainMap, Map<String, String> tenantMap) {
        for (String tenantId : adCfgMap.keySet()) {
            List<String> adNameList = Instance.ofNullable(adCfgMap.get(tenantId)).stream().map(AdCfgDO::getAdName)
                    .collect(Collectors.toList());
            List<String> alarmNameList = Instance.ofNullable(alarmCfgMap.get(tenantId)).stream().map(AiAlarmCfg::getName)
                    .collect(Collectors.toList());
            AdCfgCoverMetric adCfgCoverMetric = new AdCfgCoverMetric(IpUtils.getHost(), IpUtils.getLocalIP(),
                    tenantMap.get(tenantId), metricMap.get(tenantId).size(), getCount(adCfgMap.get(tenantId)),
                    getCount(alarmCfgMap.get(tenantId)),
                    getCount(getAlarmStatusMap(alarmCfgMap.get(tenantId)).get(AlarmSwitchStatusEnum.enabled.getStatus())),
                    getCount(getAlarmStatusMap(alarmCfgMap.get(tenantId)).get(AlarmSwitchStatusEnum.disabled.getStatus())),
                    getCount(trainTenantMap.get(tenantId)),
                    getCount(trainMap.get(tenantId).get(AdTrainStatusEnum.initialising.getStatus())),
                    getCount(trainMap.get(tenantId).get(AdTrainStatusEnum.training.getStatus())),
                    getCount(trainMap.get(tenantId).get(AdTrainStatusEnum.complete.getStatus())),
                    getCount(trainMap.get(tenantId).get(AdTrainStatusEnum.fail.getStatus())),
                    StringUtils.join(adNameList, CommonSplitConstants.COMMA_SPLIT), StringUtils.join(alarmNameList,
                    CommonSplitConstants.COMMA_SPLIT));
            MonitorLogReporter.report(monitorLog, adCfgCoverMetric);
        }
    }

    public void reportDetailMetric(List<AdCfgDO> adCfgDOList, Map<String, String> tenantMap) {
        for (AdCfgDO adCfgDO : adCfgDOList) {
            AdCfgDetailMetric adCfgDetailMetric = new AdCfgDetailMetric(IpUtils.getHost(), IpUtils.getLocalIP(),
                    tenantMap.get(adCfgDO.getTenantId()), adCfgDO.getAdName(), adCfgDO.getMetricsName(),
                    adCfgDO.getMetricsType(), adCfgDO.getAggPeriod(), adCfgDO.getFieldName(), adCfgDO.getAdStatus(),
                    adCfgDO.getAnomalyDirectionType(), adCfgDO.getThresValueFilterUp(), adCfgDO.getThresValueFilterDown(),
                    adCfgDO.getFillEmptyAsZero(),getHolidayStatus(adCfgDO.getId()));
            MonitorLogReporter.report(monitorLog, adCfgDetailMetric);
        }
    }

    public void reportAiAlarmMetric(List<AiAlarmCfg> alarmCfgList, Map<String, String> tenantMap, Map<String,
            List<AdCfgDO>> adCfgIdMap, Map<String, String> metricMap) {
        for (AiAlarmCfg aiAlarmCfg : alarmCfgList) {
            processAiAlarmCfg(aiAlarmCfg, tenantMap.get(aiAlarmCfg.getTenantId()), adCfgIdMap, metricMap.get(aiAlarmCfg.getMetricId())
                    , aiAlarmCfg.getEnabled(), aiAlarmCfg.getName(), aiAlarmCfg.getPeriodInMinutes(), aiAlarmCfg.getTimesInPeriod());
        }
    }

    public void reportChannelMetric(List<AiAlarmCfg> alarmCfgList, Map<String, String> tenantMap) {
        Map<String, List<AiAlarmCfg>> aiAlarmMap = Instance.ofNullable(alarmCfgList).stream().
                collect(Collectors.groupingBy(AiAlarmCfg::getId));
        List<String> alarmIdList = Instance.ofNullable(alarmCfgList).stream().map(AiAlarmCfg::getId)
                .collect(Collectors.toList());
        Map<String, List<AlarmChannelDO>> notificationMap = getAlarmChannel(alarmIdList);
        for (String alarmDefinitionId : notificationMap.keySet()) {
            processChannel(notificationMap.get(alarmDefinitionId), tenantMap, aiAlarmMap.get(alarmDefinitionId).get(0));
        }
    }

    public void processChannel(List<AlarmChannelDO> alarmChannelDOList, Map<String, String> tenantMap, AiAlarmCfg aiAlarmCfg) {
        for (AlarmChannelDO alarmChannelDO : alarmChannelDOList) {
            AiAlarmChannelMetric aiAlarmChannelMetric = new AiAlarmChannelMetric(IpUtils.getHost(), IpUtils.getLocalIP(),
                    tenantMap.get(aiAlarmCfg.getTenantId()), aiAlarmCfg.getName(), alarmChannelDO.getName(), alarmChannelDO.getEngineName());
            MonitorLogReporter.report(monitorLog, aiAlarmChannelMetric);
        }
    }

    public void processAiAlarmCfg(AiAlarmCfg aiAlarmCfg, String tenantName,
                                  Map<String, List<AdCfgDO>> modelMap, String metricsName, Integer enabled, String alarmName,
                                  Integer periodInMinutes, Integer timesInPeriod) {
        for (AiAlarmRuleCfg aiAlarmRuleCfg : aiAlarmCfg.getAiAlarmRuleCfgList()) {
            processAiAlarmRuleCfg(aiAlarmRuleCfg, tenantName, modelMap, metricsName, enabled, alarmName, aiAlarmRuleCfg.getId()
                    , periodInMinutes, timesInPeriod, aiAlarmRuleCfg.getLevel());
        }
    }

    public void processAiAlarmRuleCfg(AiAlarmRuleCfg aiAlarmRuleCfg, String tenantName, Map<String, List<AdCfgDO>> modelMap,
                                      String metricsName, Integer enabled, String alarmName, String alarmRuleId,
                                      Integer periodInMinutes, Integer timesInPeriod, Integer level) {
        for (AiAlarmConditionCfg aiAlarmConditionCfg : aiAlarmRuleCfg.getAiAlarmConditionCfgList()) {
            AiAlarmDetailMetric aiAlarmDetailMetric = new AiAlarmDetailMetric(IpUtils.getHost(), IpUtils.getLocalIP(),
                    tenantName, modelMap.get(aiAlarmConditionCfg.getExpression()).get(0).getAdName(), metricsName, enabled,
                    alarmName, alarmRuleId, level, aiAlarmConditionCfg.getThreshold(), aiAlarmConditionCfg.getName(),
                    periodInMinutes, timesInPeriod);
            MonitorLogReporter.report(monitorLog, aiAlarmDetailMetric);
        }
    }

    public List<AdCfgDO> getAdCfg() {
        return adCfgHandler.getAllAdCfg();
    }

    public List<AiAlarmCfg> getAlarmCfg(List<String> idList) {
        return adAlarmHandler.buildAiAlarmCfg(adAlarmConditionDAO.listAdAlarmConditionByAdIdList(idList));
    }

    public Map<String, List<AdCfgDO>> getAdCfgTenant(List<AdCfgDO> adCfgDOList) {
        return Instance.ofNullable(adCfgDOList).stream().collect(Collectors.groupingBy(AdCfgDO::getTenantId));
    }

    public Map<String, List<AiAlarmCfg>> getAlarmCfgTenant(List<AiAlarmCfg> alarmCfgList) {
        return Instance.ofNullable(alarmCfgList).stream().
                collect(Collectors.groupingBy(AiAlarmCfg::getTenantId));
    }

    public Map<String, List<MetricsDO>> getMetricCfg(List<String> tenantIds) {
        List<MetricsDO> metricsDOList = adCfgHandler.getMetricByTenant(tenantIds);
        return Instance.ofNullable(metricsDOList).stream().collect(Collectors.groupingBy(MetricsDO::getTenantId));
    }

    public Map<String, Map<Integer, List<AdTrainDO>>> getAdTrainCfgStatus(List<AdTrainDO> adTrainDOList) {
        Map<String, List<AdTrainDO>> adTrainMap = getAdTrainCfgTenant(adTrainDOList);
        Map<String, Map<Integer, List<AdTrainDO>>> AdTrainStatusMap = new HashMap<>(adTrainMap.size());
        for (String tenantId : adTrainMap.keySet()) {
            AdTrainStatusMap.put(tenantId, getAdStatusMap(adTrainMap.get(tenantId)));
        }
        return AdTrainStatusMap;
    }

    public Map<String, List<AdTrainDO>> getAdTrainCfgTenant(List<AdTrainDO> adTrainDOList) {
        return Instance.ofNullable(adTrainDOList).stream().collect(
                Collectors.groupingBy(AdTrainDO::getTenantId));
    }

    public Map<Integer, List<AdTrainDO>> getAdStatusMap(List<AdTrainDO> adTrainDOList) {
        return Instance.ofNullable(adTrainDOList).stream().collect(Collectors.groupingBy(AdTrainDO::getAdTrainStatus));
    }

    public Map<Integer, List<AiAlarmCfg>> getAlarmStatusMap(List<AiAlarmCfg> alarmCfgList) {
        return Instance.ofNullable(alarmCfgList).stream().collect(Collectors.groupingBy(AiAlarmCfg::getEnabled));
    }

    public Map<String, String> getTenantMap(Map<String, List<AdCfgDO>> adCfgMap) {
        Map<String, String> tenantMap = new HashMap<>();
        for (String tenantId : adCfgMap.keySet()) {
            tenantMap.put(tenantId, adCfgMap.get(tenantId).get(0).getTenantName());
        }
        return tenantMap;
    }

    public Map<String, List<AdCfgDO>> getAdCfgMap(List<AdCfgDO> adCfgDOList) {
        return Instance.ofNullable(adCfgDOList).stream().collect(Collectors.groupingBy(AdCfgDO::getId));
    }

    public Map<String, String> getMetricNameMap(List<AdCfgDO> adCfgDOList) {
        Map<String, String> metricMap = new HashMap<>();
        for (AdCfgDO adCfgDO : adCfgDOList) {
            metricMap.put(adCfgDO.getMetricsId(), adCfgDO.getMetricsName());
        }
        return metricMap;
    }

    public Map<String, List<AlarmChannelDO>> getAlarmChannel(List<String> alarmIds) {
        Map<String, List<AlarmChannelDO>> alarmNotificationDOMap = new HashMap<>();
        List<AlarmNotificationDO> alarmNotificationDOList = alarmDAO.listChannelByAlarmIds(alarmIds);
        List<String> channelIdList = Instance.ofNullable(alarmNotificationDOList).stream()
                .map(AlarmNotificationDO::getChannelId).collect(Collectors.toList());
        List<AlarmChannelDO> alarmChannelDOList = alarmDAO.listChannelByIds(channelIdList);
        Map<String, List<AlarmChannelDO>> alarmChannelDOMap = Instance.ofNullable(alarmChannelDOList).stream().
                collect(Collectors.groupingBy(AlarmChannelDO::getId));
        Map<String, List<AlarmNotificationDO>> alarmNotificationMap = Instance.ofNullable(alarmNotificationDOList)
                .stream().collect(Collectors.groupingBy(AlarmNotificationDO::getAlarmDefinitionId));
        for (String id : alarmNotificationMap.keySet()) {
            alarmNotificationDOMap.put(id, processAlarmChannel(alarmNotificationMap.get(id), alarmChannelDOMap));
        }
        return alarmNotificationDOMap;
    }

    public List<AlarmChannelDO> processAlarmChannel(List<AlarmNotificationDO> alarmNotificationDOList,
                                                    Map<String, List<AlarmChannelDO>> alarmChannelDOMap) {
        List<AlarmChannelDO> alarmChannelDOList = new ArrayList<>();
        for (AlarmNotificationDO alarmNotificationDO : alarmNotificationDOList) {
            alarmChannelDOList.add(alarmChannelDOMap.get(alarmNotificationDO.getChannelId()).get(0));
        }
        return alarmChannelDOList;
    }

    public Integer getCount(List<?> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        return list.size();
    }

    public Boolean getHolidayStatus(String adId) {
        List<AdTagHolidayCfgDO> adTagHolidayCfgDOList = adTagHolidayCfgDAO.batchTagHolidayByAdIdList(Collections.singletonList(adId));
        if (!CollectionUtils.isEmpty(adTagHolidayCfgDOList)){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
}

