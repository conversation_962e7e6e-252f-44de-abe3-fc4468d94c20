package us.zoom.cube.site.api.web;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import us.zoom.cube.site.biz.ServiceMonitorChannelService;
import us.zoom.cube.site.biz.ServiceMonitorExportService;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.migration.ServiceMigrationInput;
import us.zoom.cube.site.lib.input.migration.ServiceMonitorMigrationInput;
import us.zoom.cube.site.lib.output.migration.ChannelMigrationOutput;

@Slf4j
@RestController
@RequestMapping("/api/migration")
public class ServiceMonitorMigrateController {

    @Autowired
    private ServiceMonitorExportService serviceMonitorExportService;

    @Autowired
    private ServiceMonitorChannelService serviceMonitorChannelService;

    @RequestMapping(value = "/serviceMonitor", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject<String> getAgentCfg(@RequestBody ServiceMonitorMigrationInput input){
        String serviceName = input.getService();
        try {
            String json = serviceMonitorExportService.exportDataParserWithAll(serviceName);
            return ResponseObject.success(json);
        } catch (Exception e) {
            String errorMessage = String.format("Error occurs when exporting data parser by service:%s, more details:%s", serviceName, e.getMessage());
            log.error(errorMessage, e);
            return ResponseObject.fail(errorMessage);
        }
    }

    @PostMapping("/service-monitor-channel")
    public ResponseObject migrateServiceMonitorChannel(@RequestBody ServiceMigrationInput input) {
        try {
            Assert.isTrue(StringUtils.isNotBlank(input.getServiceMonitorService()), "serviceMonitorService can not be empty");
            Assert.isTrue(StringUtils.isNotBlank(input.getTargetService()), "targetService can not be empty");
            Assert.isTrue(StringUtils.isNotBlank(input.getSourceService()), "sourceService can not be empty");

            ChannelMigrationOutput output = serviceMonitorChannelService.shareChannel(input);
            return ResponseObject.success(output);
        } catch (Exception e) {
            return ResponseObject.fail(e.getMessage());
        }
    }

}
