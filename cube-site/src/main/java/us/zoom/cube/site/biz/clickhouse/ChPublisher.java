package us.zoom.cube.site.biz.clickhouse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * Publisher for ClickHouse table events
 * <AUTHOR>
 * @date 2025/4/7 10:02
 */
@Component
public class ChPublisher {
    @Autowired
    private ApplicationEventPublisher publisher;

    /**
     * Publish a generic ClickHouse table event
     * @param chTableEvent the event to publish
     * @param <T> the type of source data
     */
    public <T> void publish(ChTableEvent<T> chTableEvent) {
        publisher.publishEvent(chTableEvent);
    }
}
