package us.zoom.cube.site.lib.input.trace;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import us.zoom.cube.site.infra.enums.trace.PanelType;
import us.zoom.cube.site.infra.enums.trace.QueryType;

import java.util.Iterator;
import java.util.Map;

import static us.zoom.cube.site.infra.enums.trace.QueryType.BUILDER;


/**
 * @author: eason.jia
 * @date: 2024/8/18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CompositeQuery {

    private static final Logger logger = LoggerFactory.getLogger(CompositeQuery.class);

    private Map<String, BuilderQuery> builderQueries;
    private Map<String, ClickHouseQuery> chQueries;
    private Map<String, PromQuery> promQueries;
    private String queryType;
    private String panelType;
    private String unit;
    private boolean fillGaps;

    public QueryValidateResult validate() {
        QueryValidateResult validateResult = new QueryValidateResult();
        if (MapUtils.isEmpty(builderQueries)) {
            validateResult.setErrorMessage("query is empty");
            return validateResult;
        }

        if (!StringUtils.equals(queryType, BUILDER.getType())) {
            validateResult.setErrorMessage("illegal query type: " + queryType);
            return validateResult;
        }

        if (!PanelType.validate(panelType)) {
            validateResult.setErrorMessage("panel type is invalid: " + panelType);
            return validateResult;
        }

        if (!QueryType.validate(queryType)) {
            validateResult.setErrorMessage("query type is invalid: " + queryType);
            return validateResult;
        }

        Map<String, String> validation = validateResult.getValidation();
        Iterator<Map.Entry<String, BuilderQuery>> iterator = builderQueries.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, BuilderQuery> entry = iterator.next();
            String result = entry.getValue().validate();
            if (StringUtils.isNotBlank(result)) {
                validation.put(entry.getValue().getQueryName(), "builder query " + entry.getKey() + " is invalid, error msg: " + result);
                iterator.remove();
                logger.warn("validate failure: {}", validation.get(entry.getValue().getQueryName()));
            }
        }

        if (MapUtils.isEmpty(builderQueries) && MapUtils.isNotEmpty(validation)) {
            validateResult.setSuccess(false);
        } else {
            validateResult.setSuccess(true);
        }

        return validateResult;
    }

    public int enabledBuilderQueries() {
        return (int) builderQueries.values()
                .stream()
                .filter(builderQuery -> !builderQuery.isDisabled())
                .count();
    }
}
