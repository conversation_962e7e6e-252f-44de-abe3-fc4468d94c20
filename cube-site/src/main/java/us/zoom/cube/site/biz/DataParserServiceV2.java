package us.zoom.cube.site.biz;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import com.zoom.op.monitor.domain.IdAndName;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.math3.util.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import us.zoom.async.mq.openapi.model.base.Result;
import us.zoom.async.mq.openapi.model.result.topic.TopicResult;
import us.zoom.cube.lib.common.*;
import us.zoom.cube.lib.hub.ForwardProcessorTypeEnum;
import us.zoom.cube.lib.integrations.MetricsFieldTypeEnum;
import us.zoom.cube.lib.utils.ExceptionStackUtils;
import us.zoom.cube.sdk.util.MonitorLogReporter;
import us.zoom.cube.site.api.web.StatusEnum;
import us.zoom.cube.site.biz.model.AutoDiscoverBlackListParam;
import us.zoom.cube.site.biz.syspara.AutoDiscoverParaService;
import us.zoom.cube.site.biz.syspara.DiscoverBlackListParaService;
import us.zoom.cube.site.biz.syspara.ServiceMonitorParaService;
import us.zoom.cube.site.core.*;
import us.zoom.cube.site.core.auth.AuthHandler;
import us.zoom.cube.site.infra.AsyncMqOpenApiClientWrapper;
import us.zoom.cube.site.infra.enums.WebCodeEnum;
import us.zoom.cube.site.infra.utils.AsyncMqUtils;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.BasePara;
import us.zoom.cube.site.lib.IdPara;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.SiteException;
import us.zoom.cube.site.lib.common.dataparser.BaseDataParserPipeline;
import us.zoom.cube.site.lib.input.*;
import us.zoom.cube.site.lib.input.dataparser.*;
import us.zoom.cube.site.lib.monitor.AutoDiscoverMetric;
import us.zoom.cube.site.lib.output.MetricsAndAlarmNames;
import us.zoom.cube.site.lib.output.PageResult;
import us.zoom.cube.site.lib.output.dataparser.*;
import us.zoom.cube.site.lib.query.*;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.enums.*;
import us.zoom.infra.thread.CubeThreadPool;
import us.zoom.infra.utils.AsyncUtils;
import us.zoom.infra.utils.Instance;
import us.zoom.cube.lib.utils.JsonUtils;

import jakarta.validation.Valid;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static us.zoom.infra.utils.CommonSplitConstants.COMMA_SPLIT;

/**
 * <AUTHOR> zheng
 */
@Service
@Slf4j
public class DataParserServiceV2 {
    private Logger monitorLog = LoggerFactory.getLogger("Monitor");

    private AuthService authService;

    private DataParserHandler dataParserHandler;

    private DataParserService dataParserService;

    private AsyncMqQueueHandler asyncMqQueueHandler;

    private KafkaQueueHandler kafkaQueueHandler;

    private DataParserDeleteService dataParserDeleteService;

    private DataParserPipelineHandler dataParserPipelineHandler;

    private TenantHandler tenantHandler;

    private DataForwardHandler dataForwardHandler;

    private Environment environment;

    private UserHandler userHandler;

    private AutoDiscoverParaService autoDiscoverParaService;

    private CollectorHandler collectorHandler;

    private AlarmDefinitionHandler alarmDefinitionHandler;

    private MetricsHandler metricsHandler;

    private SysParaHandler sysParaHandler;

    @Autowired
    private TemplateItemHandler templateItemHandler;

    @Value("${dataparser.must.admin}")
    private String dataParserMustAdmin;

    private AutoDiscoverPipeLineHandler autoDiscoverPipeLineHandler;

    private AsyncmqClusterHandler asyncmqClusterHandler;

    private HubGroovyCheckService hubGroovyCheckService;

    @Autowired
    private DiscoverBlackListParaService discoverParaService;

    @Autowired
    private  AuthHandler authHandler;

    @Autowired
    public DataParserServiceV2(AuthService authService, KafkaQueueHandler kafkaQueueHandler, DataParserHandler dataParserHandler, AutoDiscoverPipeLineHandler autoDiscoverPipeLineHandler,
                               DataParserService dataParserService, AsyncMqQueueHandler asyncMqQueueHandler, DataParserPipelineHandler dataParserPipelineHandler,
                               Environment environment, DataForwardHandler dataForwardHandler, TenantHandler tenantHandler, UserHandler userHandler,
                               AutoDiscoverParaService autoDiscoverParaService, CollectorHandler collectorHandler, AlarmDefinitionHandler alarmDefinitionHandler,
                               MetricsHandler metricsHandler, SysParaHandler sysParaHandler, DataParserDeleteService dataParserDeleteService, AsyncmqClusterHandler asyncmqClusterHandler,
                               HubGroovyCheckService hubGroovyCheckService) {
        this.authService = authService;
        this.kafkaQueueHandler = kafkaQueueHandler;
        this.dataParserHandler = dataParserHandler;
        this.autoDiscoverPipeLineHandler = autoDiscoverPipeLineHandler;
        this.dataParserService = dataParserService;
        this.asyncMqQueueHandler = asyncMqQueueHandler;
        this.dataParserPipelineHandler = dataParserPipelineHandler;
        this.dataForwardHandler = dataForwardHandler;
        this.environment = environment;
        this.tenantHandler = tenantHandler;
        this.userHandler = userHandler;
        this.autoDiscoverParaService = autoDiscoverParaService;
        this.collectorHandler = collectorHandler;
        this.alarmDefinitionHandler = alarmDefinitionHandler;
        this.metricsHandler = metricsHandler;
        this.sysParaHandler = sysParaHandler;
        this.dataParserDeleteService = dataParserDeleteService;
        this.asyncmqClusterHandler = asyncmqClusterHandler;
        this.hubGroovyCheckService = hubGroovyCheckService;
    }

    public ResponseObject<List<AsyncMqTopicOut>> batchGetAsyncMqTopicByName(AsyncmqBatchQuery asyncmqBatchQuery) {
        authCheck(asyncmqBatchQuery);
        Assert.isTrue(StringUtils.isNotBlank(asyncmqBatchQuery.getAsyncmqClusterId()), "asyncmqClusterId is null!");
        Assert.isTrue(CollectionUtils.isNotEmpty(asyncmqBatchQuery.getTopicList()), "topicList is empty!");

        AsyncMqOpenApiClientWrapper clientWrapper = asyncmqClusterHandler.getClientByClusterId(asyncmqBatchQuery.getAsyncmqClusterId());

        List<List<String>> topicBatchList = AsyncMqUtils.splitIntoMultipleBatches(asyncmqBatchQuery.getTopicList());

        List<AsyncMqTopicOut> asyncMqTopicOutList = AsyncUtils.parallelFutureJoinWithResult(topicBatchList, topicList -> {
            Result<List<TopicResult>> restResult = clientWrapper.getOpenApi().listTopicsByNames(topicList);
            if (!restResult.isSuccess()) {
                log.error("batchGetAsyncMqTopicByName from async mq failed, reason: {}", restResult.getMessage());
                return null;
            }
            return restResult.getData();
        }, (e, topicList) -> {
            log.error("batchGetAsyncMqTopicByName from async mq error", e);
            return null;
        }, CubeThreadPool.getThreadPool()).stream().filter(Objects::nonNull).flatMap(List::stream).map(topicResult -> {
            return new AsyncMqTopicOut(topicResult.getName(),
                    topicResult.getDcName(),
                    topicResult.getClusterName(),
                    topicResult.getPartitions(),
                    topicResult.getId());
        }).toList();
        return ResponseObject.success(asyncMqTopicOutList);
    }

    public ResponseObject<PageResult<AsyncMqTopicOut>> searchAsyncMQ(PageQuery<AsyncmqQuery> pageQuery) {
        authCheck(pageQuery);
        List<AsyncMqTopicOut> list = new ArrayList<>();
        AsyncmqQuery asyncmqQuery = pageQuery.getQueryPara();
        Assert.notNull(asyncmqQuery, "asyncmqQuery is null!");
        AsyncMqOpenApiClientWrapper clientWrapper;
        if (StringUtils.isNotEmpty(asyncmqQuery.getAsyncmqClusterName())) {
            clientWrapper = asyncmqClusterHandler.getOpenapiClientByClusterName(asyncmqQuery.getAsyncmqClusterName());
        } else {
            Assert.notNull(asyncmqQuery.getAsyncmqClusterId(), "asyncmqClusterId is null!");
            clientWrapper = asyncmqClusterHandler.getClientByClusterId(asyncmqQuery.getAsyncmqClusterId());
        }
        Map<String, String> queryMap = Maps.newHashMap();
        queryMap.put("page", String.valueOf(pageQuery.getPageIndex()));
        queryMap.put("size", String.valueOf(pageQuery.getPageSize()));
        queryMap.put("name", asyncmqQuery.getTopic());
        Result<us.zoom.async.mq.openapi.model.page.PageResult<TopicResult>> result = clientWrapper.getOpenApi().pageQueryTopics(queryMap);
        if (result.isSuccess()) {
            result.getData().getContent().forEach(topic -> {
                list.add(new AsyncMqTopicOut(topic.getName(), topic.getDcName(),
                        topic.getClusterName(), topic.getPartitions(), topic.getId()));
            });
        } else {
            log.error("searchTopicByNameLike from async mq failed, reason:" + result.getMessage());
            return ResponseObject.fail("query async mq failed");
        }
        return ResponseObject.success(pageQuery.getOperId(), new PageResult((int) result.getData().getTotal(), list));
    }

    public ResponseObject<PageResult> searchDataParser(PageQuery<DataParserV2Query> pageQuery) throws Exception {
        authCheck(pageQuery);
        DataParserV2Query query = pageQuery.getQueryPara() == null ? new DataParserV2Query() : pageQuery.getQueryPara();
        String service = pageQuery.getTenantId();
        if (StringUtils.isNotBlank(query.getService())) {
            service = query.getService().equals("all") ? null : query.getService();
        }

        Set<String> dataParserIdList = new HashSet<>();
        if (StringUtils.isNotBlank(query.getTopic())) {
            dataParserIdList.addAll(asyncMqQueueHandler.findSourceIdsBySourceTypeTopicTenantId(
                    AsyncMqQueueSourceTypeEnum.DataParser.getType(), query.getTopic(), service));
        }

        if (StringUtils.isNotBlank(query.getTopic()) && dataParserIdList.size() == 0) {
            return ResponseObject.success(pageQuery.getOperId(), new PageResult(dataParserIdList.size(), new ArrayList<>()));
        }

        List<DataParserDO> dataParserDOS = dataParserHandler.findByParam(
                service,
                query.getName(),
                query.getRawDataType(),
                query.getUseStatus(),
                dataParserIdList,
                DataParserPurposeEnum.handle.getValue(),
                pageQuery.getPageIndex(), pageQuery.getPageSize());
        int total = dataParserHandler.getCountByParam(
                service,
                query.getName(),
                query.getRawDataType(),
                query.getUseStatus(),
                dataParserIdList,
                DataParserPurposeEnum.handle.getValue());
        // userIds
        Set<String> userIds = new HashSet<>();
        Instance.ofNullable(dataParserDOS).forEach(dataParserDO -> {
            if (StringUtils.isNotBlank(dataParserDO.getCreateUserId())) {
                userIds.add(dataParserDO.getCreateUserId());
            }
            if (StringUtils.isNotBlank(dataParserDO.getEditUserId())) {
                userIds.add(dataParserDO.getEditUserId());
            }
        });
        List<UserDO> userDOList = userHandler.getByIds(userIds);
        Map<String, String> userIdNameMap = new HashMap<>();
        Instance.ofNullable(userDOList).forEach(userDO -> {
            userIdNameMap.put(userDO.getId(), userDO.getName());
        });

        List<DataParserV2SearchOut> resultList = new ArrayList<>();
        Instance.ofNullable(dataParserDOS).forEach(dataParserDO -> {
            String createUserName = StringUtils.isBlank(dataParserDO.getCreateUserId()) ? null : userIdNameMap.get(dataParserDO.getCreateUserId());
            String editUserName = StringUtils.isBlank(dataParserDO.getEditUserId()) ? null : userIdNameMap.get(dataParserDO.getEditUserId());
            resultList.add(new DataParserV2SearchOut(dataParserDO.getId(),
                    dataParserDO.getName(),
                    dataParserDO.getRawDataType(),
                    dataParserDO.getTenantId(),
                    StringUtils.isBlank(dataParserDO.getTenantId()) ? null : tenantHandler.getTenantById(dataParserDO.getTenantId()).getName(),
                    dataParserDO.getUseStatus(),
                    dataParserDO.getCreateUserId(),
                    createUserName,
                    dataParserDO.getEditUserId(),
                    editUserName,
                    dataParserDO.getTopicTemplate(),
                    dataParserDO.getGmtModify(),
                    dataParserDO.getGmtCreate()));
        });
        return ResponseObject.success(pageQuery.getOperId(), new PageResult(total, resultList));
    }

    @Transactional
    public ResponseObject<String> addDataParser(@Valid DataParserV2Input dataParserV2Input) throws Exception {
        authCheck(dataParserV2Input);
        DataParserDO dataParserDO = addDataParserBase(dataParserV2Input);
        return ResponseObject.success(dataParserDO.getId());
    }

    protected DataParserDO addDataParserBase(DataParserV2Input dataParserV2Input) throws Exception {
        dataParserV2Input.check();
        Assert.isTrue(dataParserService.getDataParserByTenantIdAndName(dataParserV2Input.getTenantId(), dataParserV2Input.getName()) == null, "name is exist");
        if (hubGroovyCheckService.hasDataParserPermission(dataParserV2Input) == false) {
            throw new RuntimeException("No permission to modify groovy scripts");
        }
        DataParserInput dpi = transferDataParserInput(dataParserV2Input);
        dpi.setId(IdUtils.generateId());
        DataParserDO dataParserDO = transferDataParserDO(dpi, dataParserV2Input);

        dataParserHandler.addDataParser(dataParserDO, new ArrayList<>(), new ArrayList<>(), new HashMap<>());
        return dataParserDO;
    }


    @Transactional
    public ResponseObject editDataParser(@Valid DataParserV2Input dataParserV2Input) {
        authCheck(dataParserV2Input);

        Assert.notNull(dataParserV2Input.getId(), "id is null!");
        dataParserV2Input.check();

        Assert.isTrue(dataParserService.getDataParserByTenantIdAndNameNotId(dataParserV2Input.getTenantId(), dataParserV2Input.getName(), dataParserV2Input.getId()) == null, "name is exist");

        if (!authService.hasSuchDataParser(dataParserV2Input, dataParserV2Input.getId())) {
            return ResponseObject.hasNoSuchDataParser();
        }
        if (hubGroovyCheckService.hasDataParserPermission(dataParserV2Input, dataParserHandler.getById(dataParserV2Input.getId())) == false) {
            return ResponseObject.fail("No permission to modify groovy scripts");
        }
        DataParserInput dpi = transferDataParserInput(dataParserV2Input);
        dpi.setId(dataParserV2Input.getId());

        DataParserDO dataParserDO = transferDataParserDO(dpi, dataParserV2Input);

        dataParserHandler.editDataParser(dataParserDO);
        return ResponseObject.success(true);
    }

    public ResponseObject<DataParserV2Out> getDataParserById(IdPara idPara) {
        Assert.notNull(idPara.getId(), "id is null!");
        authCheck(idPara);

        if (!authService.hasSuchDataParser(idPara, idPara.getId())) {
            return ResponseObject.hasNoSuchDataParser();
        }

        DataParserDO dataParserDO = dataParserHandler.getById(idPara.getId());
        DataParserV2Out out = new DataParserV2Out();
        out.transfer(dataParserDO);
        return ResponseObject.success(out);
    }

    protected void delDataParser(String dataParserId) {
        List<DataParserPipelineDO> dataParsers = dataParserPipelineHandler.listPipelinesByDataParserId(dataParserId);
        Assert.isTrue(CollectionUtils.isEmpty(dataParsers), "The data parser has pipeline,you can not del it");
        dataParserHandler.delDataParser(dataParserId);
        asyncMqQueueHandler.deleteBySourceId(dataParserId);
        autoDiscoverPipeLineHandler.deleteByDataParserId(dataParserId);
    }

    public ResponseObject<PageResult<String>> searchDataParserName(PageQuery<DataParserV2Query> pageQuery) throws Exception {
        authCheck(pageQuery);
        List<String> results = dataParserHandler.findNameByTenantId(pageQuery);
        DataParserV2Query query = pageQuery.getQueryPara() == null ? new DataParserV2Query() : pageQuery.getQueryPara();
        String service = pageQuery.getTenantId();
        int count = dataParserHandler.getCountByTenantId(service, query.getName());
        return ResponseObject.success(new PageResult<>(count, results));
    }

    public ResponseObject<List<DataParserPipelineOut>> getPipelinesByDataParserId(IdPara idPara) {
        authCheck(idPara);
        return ResponseObject.success(getPipelinesByDataParserIds(idPara));
    }

    public ResponseObject<PageResult<DataParserPipelineOut>> searchPipeline(PageQuery<PipelineQuery> pageQuery) {
        authCheck(pageQuery);
        PipelineQuery queryPara = pageQuery.getQueryPara();
        List<DataParserPipelineDO> results = dataParserPipelineHandler.findByParam(pageQuery);
        int count = dataParserPipelineHandler.getCountByParam(queryPara);
        List<String> plNameList = results.stream()
                .map(DataParserPipelineDO::getName)
                .collect(Collectors.toList());
        List<AutoDiscoverPipelineDO> plList = autoDiscoverPipeLineHandler.queryByPipelineNameList(plNameList, pageQuery.getQueryPara().getTenantId(), pageQuery.getQueryPara().getDataParserId(), AutoDiscoverPipelineStatusEnum.update.getCode());
        Set<String> autoDiscoverPipelineSet = plList.stream()
                .map(AutoDiscoverPipelineDO::getPipelineName)
                .collect(Collectors.toSet());

        List<DataParserPipelineOut> pipelineOutList = results.stream().map(pipeline -> {
            DataParserPipelineOut pipelineOut = new DataParserPipelineOut();
            BeanUtils.copyProperties(pipeline, pipelineOut);
            if(autoDiscoverPipelineSet.contains(pipeline.getName())) {
                pipelineOut.setNewDiscovery(true);
            } else {
                pipelineOut.setNewDiscovery(false);
            }
            return pipelineOut;
        }).toList();
        PageResult<DataParserPipelineOut> pageResult = new PageResult<>(pipelineOutList);
        pageResult.setTotal(count);

        return ResponseObject.success(pageResult);
    }


    public ResponseObject<PageResult<String>> searchPipelineName(PageQuery<PipelineQuery> pageQuery) {
        authCheck(pageQuery);
        List<String> results = dataParserPipelineHandler.findNameByParam(pageQuery);
        int count = dataParserPipelineHandler.getCountByParam(pageQuery.getQueryPara());
        return ResponseObject.success(new PageResult<>(count, results));
    }

    protected List<DataParserPipelineOut> getPipelinesByDataParserIds(IdPara idPara) {
        DataParserOut out = new DataParserOut();
        Assert.notNull(idPara.getId(), " id is null!");
        dataParserService.setPipeline(idPara, out);
        return out.getPipelines();
    }


    public ResponseObject<List<CollectorFieldDO>> batchAddField(BatchCollectorFieldInput batchCollectorFieldInput) {
        authCheck(batchCollectorFieldInput);
        if (CollectionUtils.isEmpty(batchCollectorFieldInput.getCollectorFieldInputList())) {
            return ResponseObject.success(new ArrayList<>());
        }
        batchCollectorFieldInput.checkCollectorFieldInput();
        return ResponseObject.success(dataParserService.batchAddFieldBase(batchCollectorFieldInput));
    }

    protected DataParserDO transferDataParserDO(DataParserInput dpi, DataParserV2Input dataParserV2Input) {
        DataParserDO dataParserDO = new DataParserDO();
        BeanUtils.copyProperties(dpi, dataParserDO);

        dataParserDO.setFilePath(null);
        dataParserDO.setDataType(null);
        dataParserDO.setLanguageType(null);
        dataParserDO.setIncludeRule(null);
        dataParserDO.setExcludeRule(null);
        dataParserDO.setInfraType(null);

        dataParserDO.setUnitTagIds("");

        dataParserDO.setGmtCreate(new Date());
        dataParserDO.setGmtModify(new Date());
        dataParserDO.setCreateUserId(dataParserV2Input.getUserId());
        dataParserDO.setEditUserId(dataParserV2Input.getUserId());
        dataParserDO.setTopicTemplate(dataParserV2Input.getTopicTemplate());
        return dataParserDO;
    }

    protected DataParserInput transferDataParserInput(DataParserV2Input dataParserInput) {
        DataParserInput dpi = new DataParserInput();
        //set new version
        dpi.setVersion(DataParserVersionEnum.VERSION_1.getVersion());
        dpi.setQueueType(QueueTypeEnum.async_mq.name());
        //new version DataParserCollectTypeEnum.aq
        dpi.setCollectType(DataParserCollectTypeEnum.aq.getCode());
        dpi.setAqGroupId("");
        dpi.setOutputAqId("");
        dpi.setChannelType(HubChannelType.normal.name());
        dpi.setIsForwardSameTopic(ForwardProcessorTypeEnum.DEFAULT.name());

        dpi.setRawDataType(dataParserInput.getRawDataType());
        dpi.setRawDataParseType(dataParserInput.getRawDataParseType());
        dpi.setInvokeFunction(dataParserInput.getInvokeFunction());
        dpi.setRawDataParseRule(dataParserInput.getRawDataParseRule());

        dpi.setName(dataParserInput.getName());
        dpi.setRemark(dataParserInput.getRemark());
        dpi.setPurpose(DataParserPurposeEnum.handle.getValue());
        dpi.setForwardProcessorType(ForwardProcessorTypeEnum.DEFAULT.name());
        dpi.setUseStatus(dataParserInput.getUseStatus());
        dpi.setTenantId(dataParserInput.getTenantId());
        dpi.check();
        return dpi;
    }

    private void authCheck(BasePara basePara) {
        authService.checkAuth(basePara);
    }

    public ResponseObject<PageResult> getDiscoverPipelineByDataParserId(PageQuery<DiscoverPipelineQuery> discoverPipelineQueryPageQuery) {
        authCheck(discoverPipelineQueryPageQuery);
        DiscoverPipelineQuery query = discoverPipelineQueryPageQuery.getQueryPara();
        Assert.isTrue(query != null, "query param is null");
        List<AutoDiscoverPipelineDO> autoDiscoverPipelineUnHandleList = dataParserHandler.selectDiscoverPipelineByNameAndParserId(query.getDataParserId(), query.getPipelineName(), discoverPipelineQueryPageQuery.getPageIndex(), discoverPipelineQueryPageQuery.getPageSize());
        int totalCount = dataParserHandler.selectDiscoverPipelineCountByNameAndParserId(query.getDataParserId(), query.getPipelineName());
        Map<String, List<Map<String, Object>>> pipelineName2DataParser = getExistsPipelineDataParsers(discoverPipelineQueryPageQuery, autoDiscoverPipelineUnHandleList);
        List<DiscoverPipelineOut> result = autoDiscoverPipelineUnHandleList.stream().map(item -> makeDiscoverPipelineOut(item, pipelineName2DataParser)).collect(Collectors.toList());
        return ResponseObject.success(new PageResult(totalCount, result));
    }

    private Map<String, List<Map<String, Object>>> getExistsPipelineDataParsers(PageQuery<DiscoverPipelineQuery> query, List<AutoDiscoverPipelineDO> pipelineList) {
        try {
            List<String> pipelineNameList = pipelineList.stream().map(AutoDiscoverPipelineDO::getPipelineName).collect(Collectors.toList());
            List<DataParserPipelineDO> existPipeline = CollectionUtils.isEmpty(pipelineNameList) ? null : dataParserPipelineHandler.getExistInOtherDataParsers(query.getTenantId(), pipelineNameList, query.getQueryPara().getDataParserId());
            if(CollectionUtils.isEmpty(existPipeline)){
                return null;
            }
            List<Map<String, Object>> existDataParser = dataParserHandler.listDataparserNameByIds(existPipeline.stream().map(DataParserPipelineDO::getDataParserId).collect(Collectors.toSet()));
            Map<String, List<Map<String, Object>>> pipelineName2DataParser = existPipeline.stream()
                    .collect(Collectors.toMap(
                            DataParserPipelineDO::getName,
                            pipeline -> existDataParser.stream()
                                    .filter(parser -> parser.get("id").equals(pipeline.getDataParserId()))
                                    .collect(Collectors.toList()),
                            (existing, replacement) -> existing
                    ));
            return pipelineName2DataParser;
        } catch (Exception e) {
            log.error("An error occurred while getting information that the pipeline name already exists in another dataparser. , record is {}", JsonUtils.toJsonString(query), e);
        }
        return null;
    }

    public ResponseObject<String> deleteDiscoverPipelineById(IdPara idPara) {
        authCheck(idPara);

        AutoDiscoverPipelineDO discoverPipelineDO = dataParserHandler.getDiscoverPipelineById(idPara.getId());
        if (discoverPipelineDO == null) {
            return ResponseObject.success("success");
        }
        checkEditAuth(discoverPipelineDO.getDataParserId(), discoverParaService.getDiscoverParams(),idPara.getUserId());

        String key = discoverPipelineDO.getTenantId() + "_" + discoverPipelineDO.getDataParserId() + "_" + discoverPipelineDO.getPipelineName();
        String uniqueId = key + "_" + IdUtils.generateId();
        // 
        dataParserHandler.deleteByDiscoverPipelineId(idPara.getId(), uniqueId);
        return ResponseObject.success("success");
    }

    public ResponseObject<Map<String, Object>> batchSubmitDiscoverPipelineToDataParser(BatchDiscoverPipelineInput discoverPipelineInputList) {
        authCheck(discoverPipelineInputList);
        if (CollectionUtils.isEmpty(discoverPipelineInputList.getDiscoverPipelineInputList())) {
            return ResponseObject.success(null);
        }
        Map<String, ResponseObject> failedDetails = new LinkedHashMap<>();
        for (DiscoverPipelineInput discoverPipelineInput : discoverPipelineInputList.getDiscoverPipelineInputList()) {
            ResponseObject result = null;
            try {
                if (discoverPipelineInput.getStatus().equals(AutoDiscoverPipelineStatusEnum.add.getCode())) {
                    result = addDiscoverPipelineToDataParser(discoverPipelineInput);
                } else if (discoverPipelineInput.getStatus().equals(AutoDiscoverPipelineStatusEnum.update.getCode())) {
                    result = updateDiscoverPipelineToDataParser(discoverPipelineInput);
                }
            } catch (Exception e) {
                String msg = "Pipeline:" + discoverPipelineInput.getPipelineName() + "submit error, error is:" + e.getMessage();
                result = ResponseObject.fail(null, msg, null);
            }
            if (result != null && StatusEnum.FAIL.getStatus().equals(result.getStatus())) {
                failedDetails.put(discoverPipelineInput.getId(), result);
            }
        }
        if (failedDetails.size() > 0) {
            return ResponseObject.success(Map.of("failedDetails", failedDetails));
        }
        return ResponseObject.success(null);
    }

    @Transactional(rollbackFor = Exception.class)
    public ResponseObject<Object> addDiscoverPipelineToDataParser(DiscoverPipelineInput discoverPipelineInput) {
        authCheck(discoverPipelineInput);


        checkEditAuth(discoverPipelineInput.getDataParserId(), discoverParaService.getDiscoverParams(),discoverPipelineInput.getUserId());

        discoverPipelineInput.addRemapperProcessorIfNeeded();
        discoverPipelineInput.checkAdd();
        dataParserHandler.checkTenantId(discoverPipelineInput.getTenantId(), discoverPipelineInput.getDataParserId());
        DataParserPipelineDO dataParserPipelineDO = dataParserPipelineHandler.getByTenantIdAndName(discoverPipelineInput.getTenantId(), discoverPipelineInput.getPipelineName());
        if (dataParserPipelineDO != null) {
            ResponseObject<Object> result = ResponseObject.failWithCode(null, String.format("Pipeline: [%s] already exists.", discoverPipelineInput.getPipelineName()), WebCodeEnum.DuplicatedPipelineName.getCode());
            result.setData(dataParserPipelineDO);
            return result;
        }
        // add pipeline
        DataParserPipelineInput dataParserPipelineInput = discoverPipelineInput.makeDataParserPipelineInput(discoverPipelineInput);
        ResponseObject<String> result = dataParserService.addPipeline(dataParserPipelineInput);
        discoverPipelineInput.setPipelineId(result.getData());
        // batch add collect fields
        BatchCollectorFieldInput batchCollectorFieldInput = discoverPipelineInput.makeBatchCollectorFiledInput(discoverPipelineInput);
        batchAddField(batchCollectorFieldInput);
        addOrUpdateRemapperProcessorIfNeeded(discoverPipelineInput);
        // update auto discover pipeline
        String key = discoverPipelineInput.getTenantId() + "_" + discoverPipelineInput.getDataParserId() + "_" + discoverPipelineInput.getPipelineName();
        String uniqueId = key + "_" + IdUtils.generateId();
        dataParserHandler.addDiscoverPipeline(discoverPipelineInput.getId(), uniqueId);
        // add origin metric
        if (!autoDiscoverParaService.getAutoDiscoverAddMetricsBlacklist().contains(discoverPipelineInput.getDataParserId()) && !CollectionUtils.isEmpty(discoverPipelineInput.getTagItems())
                && !CollectionUtils.isEmpty(discoverPipelineInput.getFieldItems())) {
            MetricsInput metricsInput = makeMetricsInput(discoverPipelineInput, discoverPipelineInput.getTagItems(), discoverPipelineInput.getFieldItems());
            MetricsDO metricsDO = dataParserService.addMetrics(metricsInput);
        }
        return ResponseObject.success(discoverPipelineInput.getId());
    }

    private void checkEditAuth( String dataParserId, AutoDiscoverBlackListParam discoverParams,String userId) {

        boolean isSystemMaintainer = authHandler.hasSuchRole(userId, RoleTypeEnum.systemMaintainer.name());

        if(discoverParams.getIdList().contains(dataParserId) && !isSystemMaintainer){
            throw new SiteException(WebCodeEnum.AUTO_DISCOVER_FORBID_EDIT);
        }
    }

    private void addOrUpdateRemapperProcessorIfNeeded(DiscoverPipelineInput discoverPipelineInput) {
        DataParserRemapperProcessorInput remapperProcessorInput;
        if ((remapperProcessorInput = discoverPipelineInput.getRemapperProcessorInput()) != null) {
            List<DataParserRemapperProcessorDO> remapperProcessors = dataParserService.getRemapperProcessorByPipelineId(discoverPipelineInput.getPipelineId());
            DataParserRemapperProcessorDO autoRemapperProcessor = CollectionUtils.isEmpty(remapperProcessors) ? null : remapperProcessors.stream()
                    .filter(item -> StringUtils.equalsIgnoreCase(remapperProcessorInput.getName(), item.getName())).findFirst().orElse(null);
            // Auto remapper processor didn't exist , need to add a new one for pipeline.
            if (autoRemapperProcessor == null) {
                remapperProcessorInput.setDataParserPipelineId(discoverPipelineInput.getPipelineId());
                dataParserService.addRemapperProcessor(remapperProcessorInput);
            } else {
                String[] newSourceFields = ArrayUtils.addAll(StringUtils.splitByWholeSeparatorPreserveAllTokens(autoRemapperProcessor.getSourceFileds(), ","), StringUtils.splitByWholeSeparatorPreserveAllTokens(remapperProcessorInput.getSourceFileds(), ","));
                String[] newTargetFields = ArrayUtils.addAll(StringUtils.splitByWholeSeparatorPreserveAllTokens(autoRemapperProcessor.getTargetFields(), ","), StringUtils.splitByWholeSeparatorPreserveAllTokens(remapperProcessorInput.getTargetFields(), ","));
                BeanUtils.copyProperties(autoRemapperProcessor, remapperProcessorInput);
                Set<Pair> pairs = IntStream.range(0, newSourceFields.length).mapToObj(i -> new Pair(newSourceFields[i], newTargetFields[i])).collect(Collectors.toSet());
                remapperProcessorInput.setSourceFileds(String.join(",", pairs.stream().map(pair -> pair.getKey()).toArray(String[]::new)));
                remapperProcessorInput.setTargetFields(String.join(",", pairs.stream().map(pair -> pair.getValue()).toArray(String[]::new)));
                dataParserService.editRemapperProcessor(remapperProcessorInput);
            }
        }
    }

    private MetricsInput makeMetricsInput(DiscoverPipelineInput discoverPipelineInput, List<DiscoverItem> tags, List<DiscoverItem> fields) {
        DataParserPipelineDO dataParserPipelineDO = dataParserPipelineHandler.getPipelineById(discoverPipelineInput.getPipelineId());
        MetricsInput metricsInput = new MetricsInput();
        metricsInput.setCollectorId(dataParserPipelineDO.getCollectorId());
        metricsInput.setMetricsName(replaceSpecialCharactor(dataParserPipelineDO.getName()));
        metricsInput.setType(MetricsTypeEnum.ORIGINAL.getValue());
        List<String> tagNames = tags.stream().map(item -> item.getKey()).collect(Collectors.toList());
        List<MetricsFieldInput> metricsFieldList = new ArrayList<>();
        for (DiscoverItem discoverItem : fields) {
            MetricsFieldInput metricsFieldInput = new MetricsFieldInput();
            metricsFieldInput.setFieldName(discoverItem.getKey());
            metricsFieldInput.setFieldType(MetricsFieldTypeEnum.fromName(discoverItem.getType().toLowerCase()).getValue());
            metricsFieldList.add(metricsFieldInput);
        }
        metricsInput.setTagNames(tagNames);
        metricsInput.setMetricsFieldList(metricsFieldList);
        return metricsInput;
    }

    private String replaceSpecialCharactor(String source) {
        if (StringUtils.isBlank(source)) {
            return source;
        }
        return source.replaceAll("[^a-zA-Z0-9_]", "_");
    }

    public ResponseObject<DiscoverPipelineOut> getAutoDiscoverPipeline(DiscoverPipelineGet discoverPipelineGet) {
        AutoDiscoverPipelineDO autoDiscoverPipelineDO = autoDiscoverPipeLineHandler.getAutoDiscoverPipeline(discoverPipelineGet);
        if(autoDiscoverPipelineDO == null) {
            ResponseObject.success("Pipeline has been updated.");
        }
        DiscoverPipelineOut discoverPipelineOut = getDiscoverPipelineOut(autoDiscoverPipelineDO);
        return ResponseObject.success(discoverPipelineOut);
    }

    @Transactional(rollbackFor = Exception.class)
    public ResponseObject<String> updateDiscoverPipelineToDataParser(DiscoverPipelineInput discoverPipelineInput) {
        authCheck(discoverPipelineInput);
        checkEditAuth(discoverPipelineInput.getDataParserId(), discoverParaService.getDiscoverParams(),discoverPipelineInput.getUserId());
        discoverPipelineInput.addRemapperProcessorIfNeeded();
        discoverPipelineInput.checkUpdate();
        dataParserHandler.checkTenantId(discoverPipelineInput.getTenantId(), discoverPipelineInput.getDataParserId());
        DataParserPipelineDO dataParserPipelineDO = dataParserPipelineHandler.getByTenantIdAndName(discoverPipelineInput.getTenantId(), discoverPipelineInput.getPipelineName());
        Assert.isTrue(dataParserPipelineDO != null, "don't have this pipeline in db");
        discoverPipelineInput.setPipelineId(dataParserPipelineDO.getId());

        List<DiscoverItem> tagItems = discoverPipelineInput.getTagItems();
        List<DiscoverItem> fieldItems = discoverPipelineInput.getFieldItems();
        List<CollectorFieldDO> collectorFieldUpdateList = new ArrayList<>();
        List<DiscoverItem> updateSchemaFields = new ArrayList<>();
        // Filter out existing fields
        if (StringUtils.isNotBlank(dataParserPipelineDO.getCollectorId())) {
            List<CollectorFieldDO> collectorFieldDOSExist = collectorHandler.getFiledByCollectorId(dataParserPipelineDO.getCollectorId());
            Map<String, CollectorFieldDO> collectorFieldDOMap = Instance.ofNullable(collectorFieldDOSExist).stream().collect(Collectors.toMap(CollectorFieldDO::getSourceField, collectorFieldDO -> collectorFieldDO, (a, b) -> a));
            discoverPipelineInput.setFields(discoverPipelineInput.getFields().stream().filter(e -> collectorFieldDOMap.get(e.getFieldName()) == null).collect(Collectors.toList()));
            updateSchemaFields = fieldItems.stream().filter(item -> collectorFieldDOMap.get(item.getKey()) != null && (collectorFieldDOMap.get(item.getKey()).getFieldType().equals(MetricsFieldTypeEnum.histogram.name()) || collectorFieldDOMap.get(item.getKey()).getFieldType().equals(MetricsFieldTypeEnum.summary.name())) && !collectorFieldDOMap.get(item.getKey()).getFieldSchema().equals(item.getSchema())).collect(Collectors.toList());
            for (DiscoverItem discoverItem : updateSchemaFields) {
                CollectorFieldDO collectorFieldDO = collectorFieldDOMap.get(discoverItem.getKey());
                collectorFieldDO.setFieldSchema(discoverItem.getSchema());
                collectorFieldUpdateList.add(collectorFieldDO);
            }
            tagItems = tagItems.stream().filter(item -> collectorFieldDOMap.get(item.getKey()) == null).collect(Collectors.toList());
            fieldItems = fieldItems.stream().filter(item -> collectorFieldDOMap.get(item.getKey()) == null).collect(Collectors.toList());
        }
        String key = discoverPipelineInput.getTenantId() + "_" + discoverPipelineInput.getDataParserId() + "_" + discoverPipelineInput.getPipelineName();
        String uniqueId = key + "_" + IdUtils.generateId();
        if (discoverPipelineInput.getFields().size() == 0 && updateSchemaFields.size() == 0) {
            dataParserHandler.updateDiscoverPipeline(discoverPipelineInput.getId(), uniqueId);
            addOrUpdateRemapperProcessorIfNeeded(discoverPipelineInput);
            return ResponseObject.success(dataParserPipelineDO.getId());
        }
        // batch add collect fields
        BatchCollectorFieldInput batchCollectorFieldInput = discoverPipelineInput.makeBatchCollectorFiledInput(discoverPipelineInput);
        batchAddField(batchCollectorFieldInput);
        addOrUpdateRemapperProcessorIfNeeded(discoverPipelineInput);
        dataParserHandler.updateDiscoverPipeline(discoverPipelineInput.getId(), uniqueId);
        // update field schema
        collectorHandler.batchUpdateSchema(collectorFieldUpdateList);
        // batch update metrics
        if (StringUtils.isNotBlank(dataParserPipelineDO.getCollectorId())) {
            List<MetricsDO> metricsDOList = metricsHandler.listMetricsByCollectorId(dataParserPipelineDO.getCollectorId());
            metricsDOList = metricsDOList.stream().filter(item -> item.getType() == MetricsTypeEnum.ORIGINAL.getValue()).collect(Collectors.toList());
            List<String> metricsIds = Instance.ofNullable(metricsDOList).stream().map(item -> item.getId()).collect(Collectors.toList());
            List<MetricsFieldDO> metricsFieldDOList = metricsHandler.listFieldsByMetricsIds(metricsIds);
            Map<String, List<MetricsFieldDO>> metricsFieldMap = new HashMap<>();
            for (MetricsFieldDO metricsFieldDO : metricsFieldDOList) {
                List<MetricsFieldDO> fieldDOS = metricsFieldMap.computeIfAbsent(metricsFieldDO.getMetricsId(), t -> new ArrayList<>());
                fieldDOS.add(metricsFieldDO);
            }
            for (MetricsDO metricsDO : metricsDOList) {
                List<MetricsFieldDO> fieldDOList = metricsFieldMap.get(metricsDO.getId());
                MetricsInput editMetricsInput = makeEditMetricsInput(metricsDO, fieldDOList, dataParserPipelineDO.getCollectorId(), tagItems, fieldItems);
                dataParserService.editMetrics(editMetricsInput);
            }
        }
        return ResponseObject.success(dataParserPipelineDO.getId());
    }

    private MetricsInput makeEditMetricsInput(MetricsDO metricsDO, List<MetricsFieldDO> fieldDOList, String collectorId, List<DiscoverItem> tags, List<DiscoverItem> fields) {
        MetricsInput metricsInput = new MetricsInput();
        metricsInput.setId(metricsDO.getId());
        metricsInput.setCollectorId(collectorId);
        metricsInput.setMetricsName(metricsDO.getMetricsName());
        metricsInput.setType(MetricsTypeEnum.ORIGINAL.getValue());
        List<String> metricsTags = new ArrayList<>(Arrays.asList(metricsDO.getTagNames().split(COMMA_SPLIT)));
        metricsTags.addAll(tags.stream().map(item -> item.getKey()).collect(Collectors.toList()));
        List<MetricsFieldInput> metricsFieldList = new ArrayList<>();
        for (DiscoverItem item : fields) {
            MetricsFieldInput metricsFieldInput = new MetricsFieldInput();
            metricsFieldInput.setFieldName(item.getKey());
            metricsFieldInput.setFieldType(MetricsFieldTypeEnum.fromName(item.getType().toLowerCase()).getValue());
            metricsFieldList.add(metricsFieldInput);
        }
        for (MetricsFieldDO fieldDO : fieldDOList) {
            MetricsFieldInput metricsFieldInput = new MetricsFieldInput();
            metricsFieldInput.setId(fieldDO.getId());
            metricsFieldInput.setMetricsId(fieldDO.getMetricsId());
            metricsFieldInput.setFieldName(fieldDO.getFieldName());
            metricsFieldInput.setFieldType(MetricsFieldTypeEnum.number.getValue());
            metricsFieldList.add(metricsFieldInput);
        }
        metricsInput.setTagNames(metricsTags);
        metricsInput.setMetricsFieldList(metricsFieldList);
        return metricsInput;
    }

    public ResponseObject<PipelineInfoOut> getPipelineInfoByPipelineId(IdPara idPara) {
        authCheck(idPara);
        PipelineInfoOut pipelineInfoOut = getPipelineInfoByPipelineId(idPara.getId());
        return ResponseObject.success(pipelineInfoOut);
    }

    protected PipelineInfoOut getPipelineInfoByPipelineId(String pipelineId) {
        Assert.isTrue(org.apache.commons.lang3.StringUtils.isNotBlank(pipelineId), "pipeline id can not be null");
        PipelineInfoOut pipelineInfoOut = dataParserService.getPipelineInfoByPipelineId(pipelineId);
        return pipelineInfoOut;
    }

    public ResponseObject saveDiscoverPipeline(List<OutDiscoverPipelineInput> outDiscoverPipelineInputs) {
        AutoDiscoverMetric.AutoDiscoverMetricBuilder builder = AutoDiscoverMetric.builder();
        try {
            builder.switchStatus(String.valueOf(autoDiscoverParaService.getAutoDiscoverSwitch()));
            builder.totalSize(Instance.ofNullable(outDiscoverPipelineInputs).size());

            if (autoDiscoverParaService.getAutoDiscoverSwitch() == false) {
                MonitorLogReporter.report(monitorLog, builder.build());
                return ResponseObject.success("Auto discover closed");
            }

            if (CollectionUtils.isEmpty(outDiscoverPipelineInputs)) {
                MonitorLogReporter.report(monitorLog, builder.build());
                return ResponseObject.success("success");
            }

            long preStart = System.currentTimeMillis();
            // pipelineMap: dataParserId Map<pipelineName,DataParserPipelineDO>
            List<String> dataParserIds = Instance.ofNullable(outDiscoverPipelineInputs).stream().map(item -> item.getDataParserId()).collect(Collectors.toList());
            List<DataParserPipelineDO> pipelineDOList = dataParserPipelineHandler.listByDataParserIds(dataParserIds);
            Map<String, Map<String, DataParserPipelineDO>> pipelineMap = new HashMap<>();
            for (DataParserPipelineDO pipelineDO : pipelineDOList) {
                Map<String, DataParserPipelineDO> map = pipelineMap.computeIfAbsent(pipelineDO.getDataParserId(), t -> new HashMap<>());
                map.put(pipelineDO.getName(), pipelineDO);
            }
            // collectorMap: collectorId List<CollectorFieldDO>
            List<String> collectorList = Instance.ofNullable(pipelineDOList).stream().map(item -> item.getCollectorId()).collect(Collectors.toList());
            List<CollectorFieldDO> collectorFieldDOList = dataParserHandler.listFieldByCollectorIds(collectorList);
            Map<String, List<CollectorFieldDO>> collectorMap = new HashMap<>();
            for (CollectorFieldDO fieldDO : collectorFieldDOList) {
                List<CollectorFieldDO> fieldDOList = collectorMap.computeIfAbsent(fieldDO.getCollectorId(), t -> new ArrayList<>());
                fieldDOList.add(fieldDO);
            }
            // data Map(key : AutoDiscoverPipelineDO)
            Map<String, AutoDiscoverPipelineDO> discoverPipelineMap = new HashMap<>();
            List<AutoDiscoverPipelineDO> autoDiscoverPipelineDOList = dataParserHandler.listPipelineUnhandled(dataParserIds);
            for (AutoDiscoverPipelineDO autoDiscoverPipelineDO : autoDiscoverPipelineDOList) {
                discoverPipelineMap.put(autoDiscoverPipelineDO.getTenantId() + "_" + autoDiscoverPipelineDO.getDataParserId() + "_" + autoDiscoverPipelineDO.getPipelineName(), autoDiscoverPipelineDO);
            }
            List<AutoDiscoverPipelineDO> autoDiscoverPipelineDOListToAdd = new ArrayList<>();
            List<AutoDiscoverPipelineDO> autoDiscoverPipelineDOListToUpdate = new ArrayList<>();
            for (OutDiscoverPipelineInput outDiscoverPipelineInput : outDiscoverPipelineInputs) {
                try {
                    if (outDiscoverPipelineInput.hasDuplicateKeys()) {
                        log.warn("auto discover pipeline has duplicate keys , record is {}", JsonUtils.toJsonStringIgnoreExp(outDiscoverPipelineInput));
                        continue;
                    }
                    Date now = new Date();
                    DataParserPipelineDO pipelineDOExist = null;
                    if (pipelineMap.get(outDiscoverPipelineInput.getDataParserId()) != null) {
                        pipelineDOExist = pipelineMap.get(outDiscoverPipelineInput.getDataParserId()).get(outDiscoverPipelineInput.getPipelineName());
                    }
                    if (!autoDiscoverPipeLineHandler.checkCacheIsReady()) {
                        continue;
                    }
                    dataParserHandler.setTypeByTenantIdAndDataParserId(outDiscoverPipelineInput);
                    //  if the status is correct
                    if ((outDiscoverPipelineInput.getStatus().equals(AutoDiscoverPipelineActionEnum.create.name()) && pipelineDOExist != null)
                            || (outDiscoverPipelineInput.getStatus().equals(AutoDiscoverPipelineActionEnum.update.name()) && pipelineDOExist == null)) {
                        continue;
                    }
                    outDiscoverPipelineInput.getHisFields().forEach(item -> item.setType(MetricsFieldTypeEnum.histogram.name()));
                    outDiscoverPipelineInput.getSumFields().forEach(item -> item.setType(MetricsFieldTypeEnum.summary.name()));
                    // Correct the data automatically discovered by the hub
                    if (pipelineDOExist != null && StringUtils.isNotBlank(pipelineDOExist.getCollectorId())) {
                        List<CollectorFieldDO> collectorFieldDOS = collectorMap.get(pipelineDOExist.getCollectorId());
                        Map<String, CollectorFieldDO> collectorFieldDOMap = Instance.ofNullable(collectorFieldDOS).stream().collect(Collectors.toMap(CollectorFieldDO::getSourceField, collectorFieldDO -> collectorFieldDO, (a, b) -> a));
                        outDiscoverPipelineInput.setTags(outDiscoverPipelineInput.getTags().stream().filter(e -> collectorFieldDOMap.get(e.getKey()) == null).collect(Collectors.toList()));
                        outDiscoverPipelineInput.setFields(outDiscoverPipelineInput.getFields().stream().filter(e -> collectorFieldDOMap.get(e.getKey()) == null).collect(Collectors.toList()));
                        // judge if his or sum is a schema change
                        outDiscoverPipelineInput.setHisFields(outDiscoverPipelineInput.getHisFields().stream()
                                .filter(e -> (collectorFieldDOMap.get(e.getKey()) == null)
                                        || (collectorFieldDOMap.get(e.getKey()) != null && collectorFieldDOMap.get(e.getKey()).getFieldType().equals(MetricsFieldTypeEnum.histogram.name()) && !collectorFieldDOMap.get(e.getKey()).getFieldSchema().equals(e.getSchema())))
                                .collect(Collectors.toList()));
                        outDiscoverPipelineInput.setSumFields(outDiscoverPipelineInput.getSumFields().stream()
                                .filter(e -> (collectorFieldDOMap.get(e.getKey()) == null)
                                        || (collectorFieldDOMap.get(e.getKey()) != null && collectorFieldDOMap.get(e.getKey()).getFieldType().equals(MetricsFieldTypeEnum.summary.name()) && !collectorFieldDOMap.get(e.getKey()).getFieldSchema().equals(e.getSchema())))
                                .collect(Collectors.toList()));
                        outDiscoverPipelineInput.setPiiFields(outDiscoverPipelineInput.getPiiFields().stream().filter(e -> collectorFieldDOMap.get(e) == null).collect(Collectors.toList()));
                    }
                    // Correct if there are any deleted records
                    dataParserHandler.correctAutoDiscoverPipelineDeletedFields(outDiscoverPipelineInput);
                    // if there are any newly discovered fields, if not, return directly
                    if (CollectionUtils.isEmpty(outDiscoverPipelineInput.getTags()) && CollectionUtils.isEmpty(outDiscoverPipelineInput.getFields()) && CollectionUtils.isEmpty(outDiscoverPipelineInput.getHisFields()) && CollectionUtils.isEmpty(outDiscoverPipelineInput.getSumFields())) {
                        log.info("dont have newly discovered fields, record is {}", JsonUtils.toJsonString(outDiscoverPipelineInput));
                        continue;
                    }
                    // if there are any records to be added or updated in the database
                    String key = outDiscoverPipelineInput.getTenantId() + "_" + outDiscoverPipelineInput.getDataParserId() + "_" + outDiscoverPipelineInput.getPipelineName();
                    String uniqueId = key + "_" + outDiscoverPipelineInput.getStatus();
                    AutoDiscoverPipelineDO autoDiscoverPipelineDO = discoverPipelineMap.get(key);
                    if (autoDiscoverPipelineDO == null) {
                        autoDiscoverPipelineDO = new AutoDiscoverPipelineDO(IdUtils.generateId(), outDiscoverPipelineInput.getDataParserId(), outDiscoverPipelineInput.getTenantId(),
                                outDiscoverPipelineInput.getType(), pipelineDOExist == null ? 0 : 1, outDiscoverPipelineInput.getPipelineName(), outDiscoverPipelineInput.getFilterRule(), JsonUtils.toJsonString(outDiscoverPipelineInput.getTags()),
                                JsonUtils.toJsonString(outDiscoverPipelineInput.getFields()), JsonUtils.toJsonString(outDiscoverPipelineInput.getHisFields()), JsonUtils.toJsonString(outDiscoverPipelineInput.getSumFields()), JsonUtils.toJsonString(outDiscoverPipelineInput.getPiiFields()), AutoDiscoverPipelineActionEnum.valueOf(outDiscoverPipelineInput.getStatus()).getCode(),
                                uniqueId, now, now);
                        autoDiscoverPipelineDOListToAdd.add(autoDiscoverPipelineDO);
                    } else {
                        outDiscoverPipelineInput.MergeAndRemoveDuplicates(autoDiscoverPipelineDO);
                        autoDiscoverPipelineDO.setTags(JsonUtils.toJsonString(outDiscoverPipelineInput.getTags()));
                        autoDiscoverPipelineDO.setFields(JsonUtils.toJsonString(outDiscoverPipelineInput.getFields()));
                        autoDiscoverPipelineDO.setHisFields(JsonUtils.toJsonString(outDiscoverPipelineInput.getHisFields()));
                        autoDiscoverPipelineDO.setSumFields(JsonUtils.toJsonString(outDiscoverPipelineInput.getSumFields()));
                        autoDiscoverPipelineDO.setPiiFields(JsonUtils.toJsonString(outDiscoverPipelineInput.getPiiFields()));
                        autoDiscoverPipelineDO.setIsExist(pipelineDOExist == null ? 0 : 1);
                        autoDiscoverPipelineDO.setStatus(AutoDiscoverPipelineActionEnum.valueOf(outDiscoverPipelineInput.getStatus()).getCode());
                        autoDiscoverPipelineDO.setUniqueId(uniqueId);
                        autoDiscoverPipelineDO.setGmtUpdate(now);
                        autoDiscoverPipelineDOListToUpdate.add(autoDiscoverPipelineDO);
                    }
                } catch (Exception e) {
                    log.error("handler discover pipeline error , record is {}", JsonUtils.toJsonString(outDiscoverPipelineInput), e);
                }
            }
            builder.preCost(System.currentTimeMillis() - preStart);

            long dbStart = System.currentTimeMillis();
            dataParserHandler.saveDiscoverPipeline(autoDiscoverPipelineDOListToAdd, autoDiscoverPipelineDOListToUpdate);
            builder.addSize(autoDiscoverPipelineDOListToAdd.size());

            builder.updateSize(autoDiscoverPipelineDOListToUpdate.size());
            builder.dbCost(System.currentTimeMillis() - dbStart);
        } catch (Exception e) {
            builder.error(ExceptionStackUtils.parseExceptionStackToString(e));
        }
        MonitorLogReporter.report(monitorLog, builder.build());
        return ResponseObject.success("success");
    }

    @Transactional(rollbackFor = Exception.class)
    public ResponseObject<String> addPipelineByJson(PipelineInput pipelineInput) {
        authCheck(pipelineInput);
        pipelineInput.checkAdd();
        DataParserPipelineDO dataParserPipelineDO = dataParserPipelineHandler.getByTenantIdAndName(pipelineInput.getTenantId(), pipelineInput.getName());
        Assert.isTrue(dataParserPipelineDO == null, "has this pipeline name in this service");
        // add pipeline
        DataParserPipelineInput dataParserPipelineInput = pipelineInput.makeDataParserPipelineInput(pipelineInput);
        ResponseObject<String> result = dataParserService.addPipeline(dataParserPipelineInput);
        pipelineInput.setId(result.getData());
        // add timestamp processor
        DataParserTimestampProcessorInput dataParserTimestampProcessorInput = pipelineInput.makeTimestampProcessorInput(pipelineInput);
        if (dataParserTimestampProcessorInput != null) {
            dataParserService.addTimestampProcessor(dataParserTimestampProcessorInput);
        }
        // imestamp，ts，remapper
        pipelineInput.checkTimeStampField(pipelineInput);
        // add remapper processor
        DataParserRemapperProcessorInput remapperProcessorInput = pipelineInput.makeDataParserRemapperProcessorInput(pipelineInput);
        if (remapperProcessorInput != null) {
            dataParserService.addRemapperProcessor(remapperProcessorInput);
        }
        // batch add collect fields
        BatchCollectorFieldInput batchCollectorFieldInput = pipelineInput.makeBatchCollectorFiledInput(pipelineInput);
        List<String> sourceFieldList = batchCollectorFieldInput.getCollectorFieldInputList().stream().map(item -> item.getSourceField()).collect(Collectors.toList());
        Set<String> distinctSourceFields = new HashSet<>(sourceFieldList);
        Assert.isTrue(sourceFieldList.size() == distinctSourceFields.size(), "have duplicate field name, please check");
        batchAddField(batchCollectorFieldInput);
        return ResponseObject.success(pipelineInput.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    public ResponseObject<String> batchAddPipelineField(BatchPipelineCollectorFieldInput batchPipelineCollectorFieldInput) {
        authCheck(batchPipelineCollectorFieldInput);
        if (batchPipelineCollectorFieldInput.getPipelineIdList().size() == 0 || batchPipelineCollectorFieldInput.getCollectorFieldInputList().size() == 0) {
            return ResponseObject.success("success");
        }
        List<DataParserPipelineDO> dataParserPipelineDOList = dataParserPipelineHandler.listByPipelineIds(batchPipelineCollectorFieldInput.getPipelineIdList());
        // ipelinefields
        checkDuplicatedFields(batchPipelineCollectorFieldInput, dataParserPipelineDOList);
        List<CollectorDO> collectorDOToAdd = new ArrayList<>();
        List<DataParserPipelineDO> parserPipelineDOSToUpdate = new ArrayList<>();
        List<CollectorFieldDO> fieldListToAdd = new ArrayList<>();
        for (DataParserPipelineDO dataParserPipelineDO : dataParserPipelineDOList) {
            if (StringUtils.isBlank(dataParserPipelineDO.getCollectorId())) {
                CollectorDO collectorDO = new CollectorDO();
                collectorDO.setId(IdUtils.generateId());
                collectorDO.setCollectorName(dataParserPipelineDO.getName());
                collectorDO.setParseType(CollectorParseTypeEnum.json.name());
                collectorDO.setCollectorType(DataSourceTypeEnum.AGENT.getCode());
                collectorDO.setCollectorKey(IdUtils.generateId());
                collectorDO.setTenantId(batchPipelineCollectorFieldInput.getTenantId());
                collectorDO.setCreateTime(new Date());
                collectorDO.setModifyTime(new Date());
                collectorDO.setTopic(StringUtils.isEmpty(dataParserPipelineDO.getTopic()) ? "" : dataParserPipelineDO.getTopic());
                dataParserPipelineDO.setCollectorId(collectorDO.getId());
                collectorDOToAdd.add(collectorDO);
                parserPipelineDOSToUpdate.add(dataParserPipelineDO);
            }
            List<CollectorFieldDO> fieldList = batchPipelineCollectorFieldInput.makeBatchCollectorFieldInput(dataParserPipelineDO, batchPipelineCollectorFieldInput);
            fieldListToAdd.addAll(fieldList);
        }
        dataParserHandler.batchAddPipelineField(parserPipelineDOSToUpdate, collectorDOToAdd, fieldListToAdd);
        return ResponseObject.success("success");
    }

    private void checkDuplicatedFields(BatchPipelineCollectorFieldInput batchPipelineCollectorFieldInput, List<DataParserPipelineDO> dataParserPipelineDOList) {
        Map<String, String> pipelineCollectorMap = new HashMap<>();
        for (DataParserPipelineDO dataParserPipelineDO : dataParserPipelineDOList) {
            if (StringUtils.isNotBlank(dataParserPipelineDO.getCollectorId())) {
                pipelineCollectorMap.put(dataParserPipelineDO.getId(), dataParserPipelineDO.getCollectorId());
            }
        }
        List<CollectorFieldDO> collectorFieldDOS = collectorHandler.listFieldByCollectorIds(pipelineCollectorMap.values().stream().collect(Collectors.toList()));
        Map<String, List<String>> collectorFieldMap = new HashMap<>();
        for (CollectorFieldDO collectorFieldDO : collectorFieldDOS) {
            List<String> collectorFieldList = collectorFieldMap.computeIfAbsent(collectorFieldDO.getCollectorId(), k -> new ArrayList<>());
            collectorFieldList.add(collectorFieldDO.getSourceField());
        }
        for (CollectorFieldInput collectorFieldInput : batchPipelineCollectorFieldInput.getCollectorFieldInputList()) {
            List<String> pipelineNames = new ArrayList<>();
            for (DataParserPipelineDO dataParserPipelineDO : dataParserPipelineDOList) {
                if (pipelineCollectorMap.get(dataParserPipelineDO.getId()) != null) {
                    List<String> fieldExistList = collectorFieldMap.get(pipelineCollectorMap.get(dataParserPipelineDO.getId()));
                    if (fieldExistList != null && fieldExistList.contains(collectorFieldInput.getSourceField())) {
                        pipelineNames.add(dataParserPipelineDO.getName());
                    }
                }
            }
            if (pipelineNames.size() > 0) {
                Assert.isTrue(false, "has field named " + collectorFieldInput.getSourceField() + " in pipeline " + JsonUtils.toJsonString(pipelineNames));
            }
        }
    }


    @Transactional
    public ResponseObject<Boolean> revokeDataParser(@Valid IdPara idPara) {
        authService.checkAuth(idPara);
        Assert.notNull(idPara.getId(), "id is null!");

        SysParaQuery sysParaQuery = new SysParaQuery();
        sysParaQuery.setType("templateService");
        List<SysParaDO> sysParaDOList = sysParaHandler.listByType(sysParaQuery);
        Set<String> tenantNameList = Sets.newHashSet();
        Instance.ofNullable(sysParaDOList).forEach(sysParaDO -> {
            tenantNameList.add(sysParaDO.getValue());
        });
        TenantDO tenantDO = tenantHandler.getTenantById(idPara.getTenantId());
        if (tenantNameList.contains(tenantDO.getName())) {
            return ResponseObject.fail("service:" + tenantDO.getName() + " contain template, cannot delete");
        }

        TemplateItemDO templateItemDO = templateItemHandler.listByServiceIdAndItemId(idPara.getTenantId(), idPara.getId());
        if (Objects.nonNull(templateItemDO)) {
            return ResponseObject.fail("serviceName:" + tenantDO.getName() + ", dataparserId: " + templateItemDO.getItemId() + ", set as a template, cannot delete");
        }
        dataParserDeleteService.deleteDataParser(idPara.getTenantId(), idPara.getId(), false);

        return ResponseObject.success(true);
    }

    private DiscoverPipelineOut getDiscoverPipelineOut(AutoDiscoverPipelineDO autoDiscoverPipelineDO) {
        DiscoverPipelineOut out = new DiscoverPipelineOut();
        BeanUtils.copyProperties(autoDiscoverPipelineDO, out);
        out.setCreateTime(autoDiscoverPipelineDO.getGmtCreate());
        out.setUpdateTime(autoDiscoverPipelineDO.getGmtUpdate());
        List<DiscoverItem> discoverItemList = new ArrayList<>();
        List<DataField> dataFields = new ArrayList<>();
        List<DiscoverItem> tags = StringUtils.isNotBlank(autoDiscoverPipelineDO.getTags()) ? JsonUtils.toObjectByTypeRef(autoDiscoverPipelineDO.getTags(), new TypeReference<List<DiscoverItem>>() {
        }) : new ArrayList<>();
        discoverItemList.addAll(tags);
        List<DiscoverItem> fields = StringUtils.isNotBlank(autoDiscoverPipelineDO.getFields()) ? JsonUtils.toObjectByTypeRef(autoDiscoverPipelineDO.getFields(), new TypeReference<List<DiscoverItem>>() {
        }) : new ArrayList<>();
        discoverItemList.addAll(fields);
        List<DiscoverItem> hisFields = StringUtils.isNotBlank(autoDiscoverPipelineDO.getHisFields()) ? JsonUtils.toObjectByTypeRef(autoDiscoverPipelineDO.getHisFields(), new TypeReference<List<DiscoverItem>>() {
        }) : new ArrayList<>();
        hisFields.forEach(item -> item.setType(MetricsFieldTypeEnum.histogram.name()));
        discoverItemList.addAll(hisFields);
        List<DiscoverItem> sumFields = StringUtils.isNotBlank(autoDiscoverPipelineDO.getSumFields()) ? JsonUtils.toObjectByTypeRef(autoDiscoverPipelineDO.getSumFields(), new TypeReference<List<DiscoverItem>>() {
        }) : new ArrayList<>();
        sumFields.forEach(item -> item.setType(MetricsFieldTypeEnum.summary.name()));
        discoverItemList.addAll(sumFields);
        List<String> piiFields = StringUtils.isNotBlank(autoDiscoverPipelineDO.getPiiFields()) ? JsonUtils.toObjectByTypeRef(autoDiscoverPipelineDO.getPiiFields(), new TypeReference<List<String>>() {
        }) : new ArrayList<>();
        for (DiscoverItem discoverItem : discoverItemList) {
            DataField dataField = new DataField();
            dataField.setFieldName(discoverItem.getKey());
            dataField.setSourceField(discoverItem.getKey());
            dataField.setFieldType(discoverItem.getType());
            dataField.setHasPii(piiFields.contains(discoverItem.getKey()) ? 1 : 0);
            dataField.setFieldSchema(discoverItem.getSchema());
            dataFields.add(dataField);
        }
        out.setFields(dataFields);
        out.getTagItems().addAll(tags);
        out.getFieldItems().addAll(fields);
        out.getFieldItems().addAll(hisFields);
        out.getFieldItems().addAll(sumFields);
        if (AutoDiscoverPipelineStatusEnum.update.getCode() == autoDiscoverPipelineDO.getStatus() || AutoDiscoverPipelineStatusEnum.add.getCode() == autoDiscoverPipelineDO.getStatus()) {
            out.setAction(autoDiscoverPipelineDO.getIsExist() == 0 ? AutoDiscoverPipelineStatusEnum.add.name() : AutoDiscoverPipelineStatusEnum.update.name());
        }
        return out;
    }

    private DiscoverPipelineOut makeDiscoverPipelineOut(AutoDiscoverPipelineDO autoDiscoverPipelineDO, Map<String, List<Map<String, Object>>> pipelineName2DataParser) {
        DiscoverPipelineOut out = getDiscoverPipelineOut(autoDiscoverPipelineDO);
        out.setExistIn(makeExistDataParser(autoDiscoverPipelineDO.getPipelineName(), pipelineName2DataParser));
        return out;
    }

    private List<Map<String, String>> makeExistDataParser(String pipelineName, Map<String, List<Map<String, Object>>> pipelineName2DataParser) {
        List<Map<String, Object>> dataParserDOs = MapUtils.getObject(pipelineName2DataParser, pipelineName);
        return CollectionUtils.isEmpty(dataParserDOs) ? null : dataParserDOs.stream().map(parser -> Map.of("dataParserId", (String) parser.get("id"), "dataParserName", (String) parser.get("name"), "dataParserVersion", (String) parser.get("version"))).collect(Collectors.toList());
    }

    public ResponseObject<String> editStatus(EditDataParserStatusInput editDataParserStatusInput) {
        authCheck(editDataParserStatusInput);
        editDataParserStatusInput.check();
        DataParserDO dataParserDO = dataParserHandler.getById(editDataParserStatusInput.getId());
        Assert.isTrue(dataParserDO != null, "can not find this data parser");
        dataParserDO.setUseStatus(editDataParserStatusInput.getEnabled() ? DataParserUseStatusEnum.use.getValue() : DataParserUseStatusEnum.stop.getValue());
        dataParserHandler.editDataParser(dataParserDO);
        return ResponseObject.success("success");
    }


    public MetricsAndAlarmNames fetchRelatedMetricsAndAlarmByPipeline(BaseDataParserPipeline pipelineInfo) {
        authCheck(pipelineInfo);
        MetricsAndAlarmNames metricsAndAlarmNames = new MetricsAndAlarmNames();
        final String pipelineId = pipelineInfo.getId();
        Assert.notNull(pipelineId, "PipelineId can't be null");
        String collectorId = dataParserPipelineHandler.getCollectorIdById(pipelineId);
        if (StringUtils.isBlank(collectorId)) {
            return metricsAndAlarmNames;
        }
        List<String> metricsIdList = collectorHandler.listMetricsCollectorByCollectorIds(Collections.singletonList(collectorId))
                .stream().map(CollectorMetricsDO::getMetricsId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(metricsIdList)) {
            return metricsAndAlarmNames;
        }

        List<MetricsDO> metricsDOList = collectorHandler.listMetricsByMetricsIds(metricsIdList);
        //metrics name
        metricsAndAlarmNames.getMetrics().addAll(metricsDOList.stream().map(MetricsDO::getMetricsName).collect(Collectors.toList()));
        //alarm name
        metricsAndAlarmNames.getAlarms().addAll(alarmDefinitionHandler.getAlarmNameByMetricsIdList(metricsIdList).stream().map(IdAndName::getName).collect(Collectors.toList()));

        return metricsAndAlarmNames;

    }

    @Transactional
    public ResponseObject migrateForward(MigrateInput migrateInput) {
        dataForwardHandler.deleteByUnitTagId(migrateInput.getUnitTagId());

        List<AsyncMqQueueDO> asyncMqQueueDOS = asyncMqQueueHandler.getByUnitTagIdAndSourceType(migrateInput.getUnitTagId(),
                AsyncMqQueueSourceTypeEnum.DataParser.getType());
        Set<String> dataParserIds = new HashSet<>();
        asyncMqQueueDOS.forEach(asyncMqQueueDO -> {
            dataParserIds.add(asyncMqQueueDO.getSourceId());
        });
        List<DataParserDO> dataParserDOS = dataParserHandler.getByIds(dataParserIds);

        Map<String, DataParserDO> idToObject = new HashMap<>();
        dataParserDOS.forEach(dataParserDO -> {
            if (dataParserDO.getUseStatus() == DataParserUseStatusEnum.use.getValue()
                    && dataParserDO.getPurpose() == DataParserPurposeEnum.forward.getValue()) {
                idToObject.put(dataParserDO.getId(), dataParserDO);
            }
        });

        int success = 0;
        int fail = 0;
        List<DataForwardDO> dataForwards = Lists.newArrayList();
        List<AsyncMqQueueDO> failAsyncMqQueues = Lists.newArrayList();
        for (AsyncMqQueueDO asyncMqQueueDO : asyncMqQueueDOS) {
            try {
                DataParserDO dataParserDO = idToObject.get(asyncMqQueueDO.getSourceId());
                if (dataParserDO == null) {
                    continue;
                }
                DataForwardDO dataForwardDO = new DataForwardDO();
                dataForwardDO.setId(IdUtils.generateId());
                dataForwardDO.setUnitTagId(asyncMqQueueDO.getUnitTagId());
                dataForwardDO.setRule(ForwardProcessorTypeEnum.tranForwardRuleEnum(dataParserDO.getForwardProcessorType()).getCode());
                dataForwardDO.setInputTopic(asyncMqQueueDO.getTopic());
                dataForwardDO.setInputClusterId(migrateInput.getAsyncmqClusterId());
                dataForwardDO.setGroupId(asyncMqQueueDO.getGroupId());
                dataForwardDO.setThreadCount(asyncMqQueueDO.getThreadCount());
                OutputQueueCfg outputQueueCfg = new Gson().fromJson(asyncMqQueueDO.getOutputQueueCfg(), OutputQueueCfg.class);
                dataForwardDO.setOutputTopic(outputQueueCfg.getOutputTopic());
                dataForwardDO.setOutputClusterId(migrateInput.getAsyncmqClusterId());
                dataForwardDO.setEditor("");
                dataForwardDO.setCreator("");
                dataForwardDO.setStatus(DataForwardStatusEnum.enable.getValue());
                dataForwardDO.setScriptFunction(dataParserDO.getInvokeFunction());
                dataForwardDO.setScriptContent(dataParserDO.getRawDataParseRule());
                dataForwards.add(dataForwardDO);
                success++;
            } catch (Exception e) {
                fail++;
                failAsyncMqQueues.add(asyncMqQueueDO);
                log.error("migrateForward update data forward fail", e);
            }
        }
        if (CollectionUtils.isEmpty(dataForwards)) {
            return ResponseObject.success("not exist forward queue");
        }
        List<List<DataForwardDO>> listList = Lists.partition(dataForwards, 50);
        listList.forEach(list -> {
            try {
                dataForwardHandler.batchAdd(list);
            } catch (Exception e) {
                log.error("migrateForward batch add fail", e);
                throw new RuntimeException("migrateForward batch add fail", e);
            }
        });

        return ResponseObject.success("success:" + success + ",fail:" + fail + ", failAsyncMqQueues: " + JsonUtils.toJsonStringIgnoreExp(failAsyncMqQueues));
    }

    @Transactional
    public ResponseObject migrateDataParser(MigrateInput migrateInput) {
        List<AsyncMqQueueDO> asyncMqQueueDOS = asyncMqQueueHandler.getByUnitTagIdAndSourceType(migrateInput.getUnitTagId(),
                AsyncMqQueueSourceTypeEnum.DataParser.getType());
        if (CollectionUtils.isEmpty(asyncMqQueueDOS)) {
            return ResponseObject.success("asyncMqQueues not exist");
        }
        Map<String, List<AsyncMqQueueDO>> dataParserIDToAsyncMqQueues = new HashMap<>();
        asyncMqQueueDOS.forEach(asyncMqQueueDO -> {
            List<AsyncMqQueueDO> asyncMqQueues = dataParserIDToAsyncMqQueues.computeIfAbsent(asyncMqQueueDO.getSourceId(), k -> Lists.newArrayList());
            asyncMqQueues.add(asyncMqQueueDO);
        });

        List<KafkaQueueDO> kafkaQueues = kafkaQueueHandler.getCalcAndAlarmQueueByDataParserIds(dataParserIDToAsyncMqQueues.keySet());
        Map<String, List<KafkaQueueDO>> dataParserIDToKafkaQueues = new HashMap<>();
        kafkaQueues.forEach(kafkaQueueDO -> {
            List<KafkaQueueDO> tmpKafkaQueues = dataParserIDToKafkaQueues.computeIfAbsent(kafkaQueueDO.getDataParserId(), k -> Lists.newArrayList());
            tmpKafkaQueues.add(kafkaQueueDO);
        });

        List<DataParserDO> dataParserDOS = dataParserHandler.getByIds(dataParserIDToAsyncMqQueues.keySet());

        if (CollectionUtils.isEmpty(dataParserDOS)) {
            return ResponseObject.success("dataParsers not exist");
        }

        Map<String, DataParserDO> idToDataParser = new HashMap<>();
        dataParserDOS.forEach(dataParserDO -> {
            if (dataParserDO.getPurpose() == DataParserPurposeEnum.handle.getValue()) {
                idToDataParser.put(dataParserDO.getId(), dataParserDO);
            }
        });

        List<AsyncMqQueueDO> result = new ArrayList<>();
        AtomicInteger dataParserNotExist = new AtomicInteger();
        StringBuilder notExistDataParserIds = new StringBuilder();
        dataParserIDToAsyncMqQueues.forEach((dataParserId, asyncMqQueues) -> {
            DataParserDO dataParserDO = idToDataParser.get(dataParserId);
            if (CollectionUtils.isEmpty(asyncMqQueues) || dataParserDO == null) {
                dataParserNotExist.incrementAndGet();
                notExistDataParserIds.append(dataParserId).append(",");
                return;
            }
            asyncMqQueues.forEach(asyncMqQueue -> {
                List<KafkaQueueDO> kafkaQueueDOS = dataParserIDToKafkaQueues.get(asyncMqQueue.getSourceId());
                asyncMqQueue.setFlinkQueueId("");
                asyncMqQueue.setAlarmQueueId("");
                if (!CollectionUtils.isEmpty(kafkaQueueDOS)) {
                    kafkaQueueDOS.forEach(kafkaQueueDO -> {
                        if (DataParserQueueType.calc_output.name().equals(kafkaQueueDO.getType())) {
                            asyncMqQueue.setFlinkQueueId(kafkaQueueDO.getOuterId());
                        }
                        if (DataParserQueueType.alarm_output.name().equals(kafkaQueueDO.getType())) {
                            asyncMqQueue.setAlarmQueueId(kafkaQueueDO.getOuterId());
                        }
                    });
                }
                asyncMqQueue.setAsyncmqClusterId(migrateInput.getAsyncmqClusterId());
                asyncMqQueue.setTenantId(dataParserDO.getTenantId());
                asyncMqQueue.setStatus(AsyncMqQueueStatusEnum.Approved.getStatus());
                asyncMqQueue.setSourceType(AsyncMqQueueSourceTypeEnum.DataParser.getType());
                result.add(asyncMqQueue);
            });
        });
        List<List<AsyncMqQueueDO>> listList = Lists.partition(result, 50);
        listList.forEach(list -> {
            asyncMqQueueHandler.batchUpdateConfig(list);
        });

        return ResponseObject.success("total:" + asyncMqQueueDOS.size() + ",update size:" + result.size() + ",dataParserNotExist:" + dataParserNotExist + ", notExistDataParserIds:" + notExistDataParserIds);
    }
}
