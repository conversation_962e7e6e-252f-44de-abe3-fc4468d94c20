package us.zoom.cube.site.api.web.alarm;

import com.zoom.op.monitor.domain.alarm.Channel;
import com.zoom.op.monitor.domain.alarm.ChannelParameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;
import us.zoom.cube.site.api.intercept.AuthInterceptor;
import us.zoom.cube.site.biz.alarm.AlarmChannelCheckService;
import us.zoom.cube.site.biz.alarm.ChannelService;
import us.zoom.cube.site.biz.alarm.MentionGroupsService;
import us.zoom.cube.site.lib.IdPara;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.alarm.channel.ChannelPagePara;
import us.zoom.cube.site.lib.input.alarm.channel.MentionGroupInput;
import us.zoom.cube.site.lib.input.alarm.channel.ShareChannelInput;
import us.zoom.cube.site.lib.output.alarm.channel.MentionGroupOutput;
import us.zoom.infra.dao.model.TenantDO;
import us.zoom.cube.site.infra.enums.AlarmEmailTypeEnum;
import us.zoom.infra.notification.channel.ChannelEngine;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

import static us.zoom.infra.notification.channel.zoomchat.IMChannelEngine.IS_DEAULT;
import static us.zoom.infra.notification.channel.zoomchat.IMChannelEngine.ZOOM_CHAT;

@RestController
@RequestMapping("/api/alarm/channel")
public class ChannelController {

    private ChannelService channelService;

    @Autowired
    private AlarmChannelCheckService alarmChannelCheckService;

    @Autowired
    private MentionGroupsService mentionGroupsService;

    @Autowired
    public void setChannelService(ChannelService channelService) {
        this.channelService = channelService;
    }

    //@GetMapping(params = {"page", "size"})
    @RequestMapping(value = "/getChannelPage", method = RequestMethod.POST)
    public ResponseObject<Page<Channel>> page(
            @ModelAttribute Channel channel,
            @RequestBody ChannelPagePara channelPagePara) {
        Sort sort = Sort.by(Sort.Direction.DESC,"id");
        int page = channelPagePara.getPage();
        int size = channelPagePara.getSize();
        Pageable pageable = PageRequest.of(page, size, sort);
        channel.setTenantId(AuthInterceptor.getTenantId());
        channel.setEngineName(channelPagePara.getEngineName());
        channel.setName(channelPagePara.getName());
        ResponseObject<Page<Channel>> pageResponseObject = channelService.pageBy(channel, pageable);
        for (Channel c : pageResponseObject.getData()) {
            if (ZOOM_CHAT.equals(c.getEngineName())) {
                ChannelParameter isDefault = new ChannelParameter();
                isDefault.setName(IS_DEAULT);
                String defaultValue = c.getIsDefault() != null && 1 == c.getIsDefault() ? "true" : "false";
                isDefault.setValue(defaultValue);
                if (c.getParameters().stream().noneMatch(u -> IS_DEAULT.equals(u.getName()))) {
                    c.getParameters().add(isDefault);
                } else {
                    c.getParameters().stream().filter(u -> IS_DEAULT.equals(u.getName())).findFirst().get().setValue(defaultValue);
                }
            }
        }
        return pageResponseObject;
    }

    //@GetMapping
    @RequestMapping(value = "/listChannel", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject<List<Channel>> list() {

        return channelService.listBy(AuthInterceptor.getTenantId());
    }

    //@GetMapping("/{id}")
    @RequestMapping(value = "/getChannelById", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject<Channel> get(@RequestBody IdPara idPara) {

        return channelService.getById(idPara.getId());
    }

    //@GetMapping("/engine")
    @RequestMapping(value = "/listEngine", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject<List<ChannelEngine>> listEngines() {

        return channelService.listEngines();
    }

    //@PostMapping
    @RequestMapping(value = "/addChannel", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject<Channel> save(@Valid @RequestBody Channel channel) {
        alarmChannelCheckService.preProcess(channel);
        return channelService.save(channel);
    }

    //@PutMapping({"", "/{id}"})
    @RequestMapping(value = "/editChannel", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject<Channel> update(@Valid @RequestBody Channel channel) {
        alarmChannelCheckService.preProcess(channel);
        return channelService.update(channel);
    }

    //@DeleteMapping("/{id}")
    @RequestMapping(value = "/deleteChannel", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject<Channel> delete(@RequestBody IdPara idPara) {
        return channelService.delete(idPara.getId());
    }

    @RequestMapping(value = "/check", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject<Object> check(@Valid @RequestBody Channel channel) {
        return channelService.checkHasLimit(channel, AlarmEmailTypeEnum.TEST.getType());
    }

    @RequestMapping(value = "/getCanOperationTenant", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject<List<TenantDO>> getCanOperationTenant(@RequestBody IdPara idPara) {
        return channelService.CanOperationTenant(idPara);
    }

    @RequestMapping(value = "/shareOthersService", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject<Object> shareOthersService(@Valid @RequestBody ShareChannelInput shareChannelInput) {
        return channelService.shareOthersService(shareChannelInput);
    }

    @RequestMapping(value = "/getShareServiceList", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject<Map<String, List<String>>> getShareServiceList(@RequestBody IdPara idPara) {
        return channelService.getShareServiceList(idPara);
    }

    @RequestMapping(value = "/getMentionGroups", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject<List<MentionGroupOutput>> getMentionGroups(@Valid @RequestBody MentionGroupInput input) {
        return mentionGroupsService.getMentionGroupsByChannel(input.getId(), input.getCreator(), input.getOwner());
    }

}
