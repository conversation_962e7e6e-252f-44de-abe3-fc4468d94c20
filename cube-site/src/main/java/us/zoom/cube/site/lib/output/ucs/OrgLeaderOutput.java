package us.zoom.cube.site.lib.output.ucs;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class OrgLeaderOutput {
    @JsonProperty("userId")
    private String userId;

    @JsonProperty("accountId")
    private String accountId;

    @JsonProperty("email")
    private String email;

    @JsonProperty("name")
    private String name;

    @JsonProperty("firstName")
    private String firstName;

    @JsonProperty("lastName")
    private String lastName;

    @JsonProperty("dept")
    private String dept;

    @JsonProperty("userType")
    private int userType;

    @JsonProperty("jid")
    private String jid;

    @JsonProperty("options")
    private String options;
}
