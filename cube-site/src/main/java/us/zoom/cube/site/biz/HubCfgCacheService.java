package us.zoom.cube.site.biz;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.common.AsyncMqQueueSourceTypeEnum;
import us.zoom.cube.lib.common.AsyncMqQueueStatusEnum;
import us.zoom.cube.lib.common.AsyncmqClusterTypeEnum;
import us.zoom.cube.lib.common.AuthEncrTypeEnum;
import us.zoom.cube.lib.common.CubeConstants;
import us.zoom.cube.lib.common.DataParserQueueType;
import us.zoom.cube.lib.common.DataParserUseStatusEnum;
import us.zoom.cube.lib.common.KafkaZipTypeEnum;
import us.zoom.cube.lib.common.QueueTypeEnum;
import us.zoom.cube.lib.common.SaslMechanismEnum;
import us.zoom.cube.lib.hub.AqCfg;
import us.zoom.cube.lib.hub.AqGroupCfg;
import us.zoom.cube.lib.hub.AsyncMqQueueCfg;
import us.zoom.cube.lib.hub.AsyncmqClusterCfg;
import us.zoom.cube.lib.hub.CollectorCfg;
import us.zoom.cube.lib.hub.ConsumeTopicCfg;
import us.zoom.cube.lib.hub.CustomerLabel;
import us.zoom.cube.lib.hub.CustomerLabelInfoSource;
import us.zoom.cube.lib.hub.DataFlowCfg;
import us.zoom.cube.lib.hub.DataForwardCfg;
import us.zoom.cube.lib.hub.DataParserConfig;
import us.zoom.cube.lib.hub.DataParserPipelineCfg;
import us.zoom.cube.lib.hub.DataParserSourceCfg;
import us.zoom.cube.lib.hub.InputQueueCfg;
import us.zoom.cube.lib.hub.KafkaQueueCfg;
import us.zoom.cube.lib.hub.KinesisStreamCfg;
import us.zoom.cube.lib.hub.MetricFieldCfg;
import us.zoom.cube.lib.hub.OutputAqCfg;
import us.zoom.cube.lib.utils.ExceptionStackUtils;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.site.api.web.StatusEnum;
import us.zoom.cube.site.biz.client.GwatchService;
import us.zoom.cube.site.biz.syspara.KinesisParaService;
import us.zoom.cube.site.core.AsyncMqQueueHandler;
import us.zoom.cube.site.core.AsyncQueueGroupHandler;
import us.zoom.cube.site.core.AsyncQueueHandler;
import us.zoom.cube.site.core.AsyncmqClusterHandler;
import us.zoom.cube.site.core.AsyncmqLoginTokenHandle;
import us.zoom.cube.site.core.CollectorHandler;
import us.zoom.cube.site.core.DataFlowDataParserRelationHandler;
import us.zoom.cube.site.core.DataFlowHandler;
import us.zoom.cube.site.core.DataForwardHandler;
import us.zoom.cube.site.core.DataParserHandler;
import us.zoom.cube.site.core.DataParserPipelineHandler;
import us.zoom.cube.site.core.DataParserSourceHandler;
import us.zoom.cube.site.core.KafkaClusterHandler;
import us.zoom.cube.site.core.TaskQueueHandler;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.core.UnitTagHandler;
import us.zoom.cube.site.lib.hub.converter.MetricsFieldDO2CfgConverter;
import us.zoom.cube.site.lib.hub.converter.PipelineDO2CfgConverter;
import us.zoom.cube.site.lib.input.InputTopicProfileAreaCfgs;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.service.*;
import us.zoom.infra.enums.DataForwardStatusEnum;
import us.zoom.infra.enums.DataParserCollectTypeEnum;
import us.zoom.infra.enums.FieldTypeEnum;
import us.zoom.infra.enums.MonitorTypeEnum;
import us.zoom.infra.enums.PipelineUseStatusEnum;
import us.zoom.infra.thread.CacheLoaderScheduler;
import us.zoom.infra.utils.CommonSplitConstants;
import us.zoom.infra.utils.Instance;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: luis.zheng
 * @Date: 2021/3/29 3:43
 */
@Slf4j
@Component
public class HubCfgCacheService {
    private static final Logger LOG = LoggerFactory.getLogger(HubCfgCacheService.class.getName());

    private Logger monitorLog = LoggerFactory.getLogger("Monitor");

    private static final String ALL_MODULE = "all";
    private static final String DATAFLOW_MODULE = "dataflow";

    private static final long GET_GWATCH_DATA_INTERVAL = 3 * 60 * 60 * 1000;
    private static final long GET_GWATCH_DATA_INITIAL_DELAY = 5 * 60 * 1000;
    @Autowired
    private HubService hubService;

    @Autowired
    private DataParserHandler dataParserHandler;

    @Autowired
    private DataFlowDataParserRelationHandler dataFlowDataParserRelationHandler;

    @Autowired
    private DataParserPipelineHandler dataParserPipelineHandler;

    @Autowired
    private AsyncQueueHandler asyncQueueHandler;

    @Autowired
    private GwatchService gwatchService;

    @Autowired
    private AsyncMqQueueHandler asyncMqQueueHandler;

    @Autowired
    private KafkaClusterHandler kafkaClusterHandler;

    @Autowired
    private UnitTagHandler unitTagHandler;

    @Autowired
    private AsyncQueueGroupHandler asyncQueueGroupHandler;

    @Autowired
    private CollectorHandler collectorHandler;

    @Autowired
    private TenantHandler tenantHandler;

    @Autowired
    private DataParserFilterProcessorDAO dataParserFilterProcessorDAO;

    @Autowired
    private DataParserRemapperProcessorDAO dataParserRemapperProcessorDAO;

    @Autowired
    private DataParserTimestampProcessorDAO dataParserTimestampProcessorDAO;

    @Autowired
    private DataParserGroovyProcessorDAO dataParserGroovyProcessorDAO;

    @Autowired
    private DataParserSplitProcessorDAO dataParserSplitProcessorDAO;

    @Autowired
    private DataParserExpressionProcessorDAO dataParserExpressionProcessorDAO;

    @Autowired
    private DataParserIpProcessorDAO dataParserIpProcessorDAO;

    @Autowired
    private DataParserCustomerLabelProcessorDAO dataParserCustomerLabelProcessorDAO;

    @Autowired
    private DataFlowHandler dataFlowHandler;

    @Autowired
    private DataParserSourceHandler dataParserSourceHandler;

    @Autowired
    private TaskQueueHandler taskQueueHandler;

    @Autowired
    private AsyncmqLoginTokenHandle asyncmqLoginTokenHandle;

    @Autowired
    private EnvironmentTaskRelaDAO environmentTaskRelaDAO;

    @Autowired
    private DataForwardHandler dataForwardHandler;

    @Autowired
    private AsyncmqClusterHandler asyncmqClusterHandler;
    @Autowired
    private KinesisParaService kinesisParaService;

    @Value("${hub.config.load.enable:true}")
    private boolean enableCacheLoader;

    private HubCache cache;

    private final static TypeReference<List<InputTopicProfileAreaCfgs>> profileTypeReference = new TypeReference<List<InputTopicProfileAreaCfgs>>() {
    };

    private Gson gson = new Gson();

    @PostConstruct
    public void init() {
        if (!enableCacheLoader) {
            return;
        }
        CacheLoaderScheduler.getInstance().getScheduler().scheduleWithFixedDelay(new Runnable() {
            @Override
            public void run() {
                cacheCustomerLabelInfoFromGwatch();
            }
        }, GET_GWATCH_DATA_INITIAL_DELAY, GET_GWATCH_DATA_INTERVAL, TimeUnit.MILLISECONDS);

        CacheLoaderScheduler.getInstance().getScheduler().scheduleWithFixedDelay(new Runnable() {
            @Override
            public void run() {
                cacheDataParserInfo();
                cacheAsyncmqAccount();
            }
        }, 1, 60, TimeUnit.SECONDS);
    }


    public void cacheDataParserInfo() {
        LOG.info("timing cache cacheDataParserInfo start");
        long startTime = System.currentTimeMillis();
        boolean isSuccess = true;
        try {
            //Map:key->OutputId
            Map<String, OutputAqCfg> outputAqCfgMap = hubService.getAllOutputAqMap();

            List<AsyncQueueDO> asyncQueues = asyncQueueHandler.listAllNotDecrypt();
            //Map:key->AsyncQueueGroupId
            Map<String, List<AqCfg>> aqCfgMap = Maps.newHashMap();
            Instance.ofNullable(asyncQueues).forEach(asyncQueueDO -> {
                List<AqCfg> aqCfgList = aqCfgMap.computeIfAbsent(asyncQueueDO.getAsyncQueueGroupId(), k -> Lists.newArrayList());
                AqCfg aqCfg = new AqCfg();
                BeanUtils.copyProperties(asyncQueueDO, aqCfg);
                aqCfgList.add(aqCfg);
            });

            List<AsyncQueueGroupDO> asyncQueueGroupDOList = asyncQueueGroupHandler.listAll();
            //Map:key->AsyncQueueGroupId
            Map<String, AqGroupCfg> aqGroupCfgMap = Instance.ofNullable(asyncQueueGroupDOList).stream().collect(Collectors.toMap(AsyncQueueGroupDO::getId, e -> {
                AqGroupCfg aqGroupCfg = new AqGroupCfg();
                BeanUtils.copyProperties(e, aqGroupCfg);
                return aqGroupCfg;
            }));

            List<DataParserPipelineDO> pipelines = dataParserPipelineHandler.listAllPipeline(PipelineUseStatusEnum.use.getValue());
            List<String> collectorIds = Instance.ofNullable(pipelines).stream().map(pipeline -> pipeline.getCollectorId()).collect(Collectors.toList());
            List<List<String>> collectorBatchIds = ListUtils.partition(collectorIds, CubeConstants.CUBE_SQL_BATCH_QUERY_SIZE);
            List<CollectorFieldDO> fieldDOs = new ArrayList<>();
            List<CollectorMetricsDO> collectorMetricsDOs = new ArrayList<>();
            for (List<String> collectorBatchId : collectorBatchIds) {
                fieldDOs.addAll(collectorHandler.listFieldByCollectorIds(collectorBatchId));
                collectorMetricsDOs.addAll(collectorHandler.listMetricsCollectorByCollectorIds(collectorBatchId));
            }
            // metric and field
            // List<CollectorFieldDO> fieldDOs = collectorHandler.listFieldByCollectorIds(Instance.ofNullable(pipelines).stream().map(pipeline -> pipeline.getCollectorId()).collect(Collectors.toList()));
            // List<CollectorMetricsDO> collectorMetricsDOs = collectorHandler.listMetricsCollectorByCollectorIds(Instance.ofNullable(pipelines).stream().map(pipeline -> pipeline.getCollectorId()).collect(Collectors.toList()));
            List<String> metrics = hubService.collectorToMetricsId(collectorMetricsDOs);
            List<List<String>> metricBatchIds = ListUtils.partition(metrics, CubeConstants.CUBE_SQL_BATCH_QUERY_SIZE);
            List<MetricsDO> metricsDOs = new ArrayList<>();
            List<MetricsFieldDO> metricsFieldDOs = new ArrayList<>();
            for (List<String> metricBatchId : metricBatchIds) {
                metricsDOs.addAll(collectorHandler.listEnableMetricsByMetricsIds(metricBatchId));
                metricsFieldDOs.addAll(collectorHandler.listMetricsFieldByMetricsIds(metricBatchId));
            }
            // List<MetricsDO> metricsDOs = collectorHandler.listMetricsByMetricsIds(metrics);
            // List<MetricsFieldDO> metricsFieldDOs = collectorHandler.listMetricsFieldByMetricsIds(metrics);
            Map<String, List<CollectorFieldDO>> fieldsMap = Instance.ofNullable(fieldDOs).stream().collect(Collectors.groupingBy(CollectorFieldDO::getCollectorId));

            //Processor
            List<DataParserFilterProcessorDO> filterProcessors = dataParserFilterProcessorDAO.listAllProcessor();
            Map<String, List<DataParserFilterProcessorDO>> filterProcessorMap = Instance.ofNullable(filterProcessors).stream().collect(Collectors.groupingBy(DataParserFilterProcessorDO::getDataParserPipelineId));

            List<DataParserRemapperProcessorDO> reMapperProcessors = dataParserRemapperProcessorDAO.listAllProcessor();
            Map<String, List<DataParserRemapperProcessorDO>> reMapperMap = Instance.ofNullable(reMapperProcessors).stream().collect(Collectors.groupingBy(DataParserRemapperProcessorDO::getDataParserPipelineId));

            List<DataParserTimestampProcessorDO> timestampProcessors = dataParserTimestampProcessorDAO.listAllProcessors();
            Map<String, List<DataParserTimestampProcessorDO>> timestampMap = Instance.ofNullable(timestampProcessors).stream().collect(Collectors.groupingBy(DataParserTimestampProcessorDO::getDataParserPipelineId));

            List<DataParserIpProcessorDO> ipProcessors = dataParserIpProcessorDAO.listAllProcessors();
            Map<String, List<DataParserIpProcessorDO>> ipMap = Instance.ofNullable(ipProcessors).stream().collect(Collectors.groupingBy(DataParserIpProcessorDO::getDataParserPipelineId));

            List<DataParserCustomerLabelProcessorDO> customerLabelProcessors = dataParserCustomerLabelProcessorDAO.listAllProcessors();
            Map<String, List<DataParserCustomerLabelProcessorDO>> customerLabelProcessorMap = Instance.ofNullable(customerLabelProcessors).stream().collect(Collectors.groupingBy(DataParserCustomerLabelProcessorDO::getDataParserPipelineId));

            List<DataParserGroovyProcessorDO> groovyProcessors = dataParserGroovyProcessorDAO.listAllProcessors();
            Map<String, List<DataParserGroovyProcessorDO>> groovyMap = Instance.ofNullable(groovyProcessors).stream().collect(Collectors.groupingBy(DataParserGroovyProcessorDO::getDataParserPipelineId));

            List<DataParserSplitProcessorDO> splitProcessors = dataParserSplitProcessorDAO.listAllProcessor();
            Map<String, List<DataParserSplitProcessorDO>> splitMap = Instance.ofNullable(splitProcessors).stream().collect(Collectors.groupingBy(DataParserSplitProcessorDO::getDataParserPipelineId));

            List<DataParserExpressionProcessorDO> expressionProcessors = dataParserExpressionProcessorDAO.listAllProcessor();
            Map<String, List<DataParserExpressionProcessorDO>> expressionMap = Instance.ofNullable(expressionProcessors).stream().collect(Collectors.groupingBy(DataParserExpressionProcessorDO::getDataParserPipelineId));

            if (GwatchService.CustomerLabelCache.getCustomerAttributes() == null) {
                cacheCustomerLabelInfoFromGwatch();
            }
            // dataParserId -> cacheList
            Map<String, Set<CustomerLabelInfoSource>> customerProcessorCacheDataMap = Maps.newHashMap();

            //Map:key->parserId
            Map<String, List<DataParserPipelineCfg>> parserAndPipelineMap = new HashMap<>();
            Map<String, MetricsDO> metricsMap = metricsDOs.stream()
                    .collect(Collectors.toMap(
                            MetricsDO::getId,
                            Function.identity(),
                            (existing, replacement) -> replacement
                    ));

            Map<String, List<MetricFieldCfg>> metricsFieldMap = metricsFieldDOs.stream()
                    .collect(Collectors.groupingBy(
                            MetricsFieldDO::getMetricsId,
                            Collectors.mapping(MetricsFieldDO2CfgConverter.getInstance()::convert, Collectors.toList())
                    ));

            Instance.ofNullable(pipelines).forEach(pipeline -> {
                DataParserPipelineCfg pipelineCfg;
                pipelineCfg = PipelineDO2CfgConverter.getInstance().convert(pipeline);
                List<DataParserPipelineCfg> pipelinesOfParser = parserAndPipelineMap.computeIfAbsent(pipeline.getDataParserId(), f -> new ArrayList<>());
                pipelinesOfParser.add(pipelineCfg);
                hubService.setCollectorInfo(pipelineCfg, pipeline, fieldsMap, metricsMap, collectorMetricsDOs, metricsFieldMap);
                hubService.setProcessor(pipelineCfg, customerProcessorCacheDataMap, filterProcessorMap, reMapperMap, groovyMap, timestampMap, ipMap, customerLabelProcessorMap, splitMap, expressionMap);
            });

            Map<String, CollectorDO> collectorMap = Instance.ofNullable(collectorHandler.listAll()).stream().collect(Collectors.toMap(CollectorDO::getId, f -> f));
            Map<String, TenantDO> allTenantsMap = new HashMap<>();
            Map<String, List<TenantDO>> dataFlowIdToTenants = new HashMap<>();
            Instance.ofNullable(tenantHandler.listAll()).forEach(tenantDO -> {
                allTenantsMap.put(tenantDO.getId(), tenantDO);
                if (StringUtils.isNotBlank(tenantDO.getDataFlowId())) {
                    List<TenantDO> tenantList = dataFlowIdToTenants.computeIfAbsent(tenantDO.getDataFlowId(), k -> Lists.newArrayList());
                    tenantList.add(tenantDO);
                }
            });

            List<TaskQueueDO> taskQueueList = taskQueueHandler.listAll();

            // taskQueueId -> envName -> EnvironmentTaskRelaDO
            Map<String, Map<String, EnvironmentTaskRelaDO>> taskQueueIdToEnvMap = new HashMap<>();
            List<EnvironmentTaskRelaDO> taskQueueEnvRelaList = environmentTaskRelaDAO.listAll();
            Instance.ofNullable(taskQueueEnvRelaList).forEach(environmentTaskRelaDO -> {
                Map<String, EnvironmentTaskRelaDO> environmentTaskRelaDOS = taskQueueIdToEnvMap.computeIfAbsent(environmentTaskRelaDO.getTaskQueueId(), k -> Maps.newHashMap());
                environmentTaskRelaDOS.put(environmentTaskRelaDO.getEnvironmentName(), environmentTaskRelaDO);
            });

            Map<String, TaskQueueDO> taskQueueMap = new HashMap<>();
            Instance.ofNullable(taskQueueList).forEach(taskQueueDO -> {
                if (StringUtils.isNotBlank(taskQueueDO.getId())) {
                    taskQueueMap.put(taskQueueDO.getId(), taskQueueDO);
                }
            });

            //new data parser config
            List<DataParserDO> dataParsers = dataParserHandler.listByStatus(DataParserUseStatusEnum.use);
            Map<String, DataParserDO> idToDataParserMap = new HashMap<>();
            Map<String, DataParserConfig> idToDataParserConfigMap = new HashMap<>();
            Instance.ofNullable(dataParsers).forEach(dataParserDO -> {
                idToDataParserMap.put(dataParserDO.getId(), dataParserDO);

                DataParserConfig dataParserConfig = new DataParserConfig();
                BeanUtils.copyProperties(dataParserDO, dataParserConfig);

                List<DataParserPipelineCfg> pipelineCfgs = parserAndPipelineMap.get(dataParserConfig.getId());
                dataParserConfig.setPipelines(Lists.newArrayList());
                if (pipelineCfgs != null) {
                    dataParserConfig.setPipelines(pipelineCfgs);
                }
                dataParserConfig.setTenantName(allTenantsMap.get(dataParserConfig.getTenantId()) == null ? "" : allTenantsMap.get(dataParserConfig.getTenantId()).getName());
                idToDataParserConfigMap.put(dataParserConfig.getId(), dataParserConfig);
            });

            List<KafkaClusterDO> kafkaClusterList = kafkaClusterHandler.listAll();
            Map<String, KafkaClusterDO> allKafkaClusterMap = Maps.newHashMap();
            Instance.ofNullable(kafkaClusterList).forEach(kafkaClusterDO -> {
                allKafkaClusterMap.put(kafkaClusterDO.getId(), kafkaClusterDO);
            });

            List<UnitTagDO> unitTagList = unitTagHandler.listAll();
            Map<String, String> unitTagIdToNameMap = Maps.newHashMap();
            Instance.ofNullable(unitTagList).forEach(unitTagDO -> {
                unitTagIdToNameMap.put(unitTagDO.getId(), unitTagDO.getName());
            });

//            List<KafkaQueueDO> kafkaQueueList = kafkaQueueHandler.listAll();
            //Key : dataParserId
            Map<String, List<KafkaQueueCfg>> allKafkaQueueMap = new HashMap<>();
            //2023-06-30 async mq cluster
            Map<String, AsyncmqClusterDO> tmpAsyncMqClusterIdToDetailMap = new HashMap<>();
            List<AsyncmqClusterDO> asyncMqClusterDOS = asyncmqClusterHandler.listAll();
            Instance.ofNullable(asyncMqClusterDOS).forEach(asyncMqClusterDO -> {
                tmpAsyncMqClusterIdToDetailMap.put(asyncMqClusterDO.getId(), asyncMqClusterDO);
            });

            //Key : dataParserId / dataFlowId
            Map<String, List<AsyncMqQueueCfg>> allAsyncMqQueueMap = new HashMap<>();

            //key : hubUnitTagName
            Map<String, List<InputQueueCfg>> hubUnitTagNameToDataParserQueuesMap = new HashMap<>();
            Map<String, List<InputQueueCfg>> hubUnitTagNameToDataFlowQueuesMap = new HashMap<>();

            List<AsyncMqQueueDO> asyncMqQueueList = asyncMqQueueHandler.listAll();
            Instance.ofNullable(asyncMqQueueList).forEach(asyncMqQueueDO -> {
                AsyncMqQueueCfg asyncMqQueueCfg = new AsyncMqQueueCfg();
                BeanUtils.copyProperties(asyncMqQueueDO, asyncMqQueueCfg);
                asyncMqQueueCfg.setDataParserId(asyncMqQueueDO.getSourceId());
                asyncMqQueueCfg.setUnitTagName(unitTagIdToNameMap.get(asyncMqQueueDO.getUnitTagId()));
                if (StringUtils.isNotBlank(asyncMqQueueDO.getOutputQueueCfg())) {
                    try {
                        AsyncMqQueueCfg.OutputQueue outputQueue = gson.fromJson(asyncMqQueueDO.getOutputQueueCfg(), AsyncMqQueueCfg.OutputQueue.class);
                        asyncMqQueueCfg.setOutputQueue(outputQueue);
                        if (outputQueue.getOutputQueueType() == QueueTypeEnum.kafka) {
                            loadOutputAqCfgMap(outputQueue.getOuterId(), DataParserQueueType.forward_output.name(),
                                    outputQueue.getOutputKafkaClusterId(), outputQueue.getOutputKafkaProtocol(), outputQueue.getOutputCompressType(), allKafkaClusterMap, outputAqCfgMap, taskQueueMap, taskQueueIdToEnvMap);
                        }
                    } catch (Exception e) {
                        LOG.error("OutputQueueCfg form json error, message:{}", asyncMqQueueDO.getOutputQueueCfg(), e);
                    }
                }
                List<AsyncMqQueueCfg> asyncMqQueueCfgs = allAsyncMqQueueMap.computeIfAbsent(asyncMqQueueDO.getSourceId(),
                        k -> Lists.newArrayList());
                asyncMqQueueCfgs.add(asyncMqQueueCfg);

                //2023-06-30 data parser input queue
                if (DataParserQueueType.input.name().equals(asyncMqQueueDO.getType())
                        && AsyncMqQueueStatusEnum.Approved.getStatus().equals(asyncMqQueueDO.getStatus())) {
                    InputQueueCfg inputQueueCfg = new InputQueueCfg();
                    BeanUtils.copyProperties(asyncMqQueueDO, inputQueueCfg);

                    AsyncmqClusterDO asyncmqClusterDO = getInnerAsyncMqCluster(tmpAsyncMqClusterIdToDetailMap, asyncMqQueueDO.getAsyncmqClusterId());

                    if (asyncmqClusterDO == null) {
                        inputQueueCfg.setInputClusterId("");
                        inputQueueCfg.setInputClusterName("");
                    } else {
                        inputQueueCfg.setInputClusterId(asyncmqClusterDO.getId());
                        inputQueueCfg.setInputClusterName(asyncmqClusterDO.getName());
                    }
                    String hubUnitTagName = unitTagIdToNameMap.get(inputQueueCfg.getUnitTagId());
                    if (StringUtils.isNotBlank(hubUnitTagName)) {
                        inputQueueCfg.setUnitTagName(hubUnitTagName);
                        AsyncMqQueueSourceTypeEnum sourceType = AsyncMqQueueSourceTypeEnum.fromType(asyncMqQueueDO.getSourceType());
                        switch (sourceType) {
                            case DataParser -> {
                                inputQueueCfg.setDataParserId(inputQueueCfg.getSourceId());
                                List<InputQueueCfg> dataParserInputQueueCfgs = hubUnitTagNameToDataParserQueuesMap.computeIfAbsent(hubUnitTagName, k -> Lists.newArrayList());
                                dataParserInputQueueCfgs.add(inputQueueCfg);
                            }
                            case DataFlow -> {
                                List<InputQueueCfg> dataFlowInputQueueCfgs = hubUnitTagNameToDataFlowQueuesMap.computeIfAbsent(hubUnitTagName, k -> Lists.newArrayList());
                                dataFlowInputQueueCfgs.add(inputQueueCfg);
                            }
                            default -> {
                            }
                        }

                    }
                    loadOutputAqCfgMap(inputQueueCfg.getAlarmQueueId(), DataParserQueueType.alarm_output.name(), null, null, null, allKafkaClusterMap, outputAqCfgMap, taskQueueMap, taskQueueIdToEnvMap);
                    loadOutputAqCfgMap(inputQueueCfg.getFlinkQueueId(), DataParserQueueType.calc_output.name(), null, null, null, allKafkaClusterMap, outputAqCfgMap, taskQueueMap, taskQueueIdToEnvMap);
                }
            });

            //load data flow and data parser source(new data parser)
            List<DataFlowDO> allDataFlow = dataFlowHandler.listAll();

            List<DataParserSourceDO> allDataParserSource = dataParserSourceHandler.listAll();
            Map<String, DataParserSourceDO> idToDataParserSourceMap = new HashMap<>();
            Instance.ofNullable(allDataParserSource).forEach(dataParserSourceDO -> {
                idToDataParserSourceMap.put(dataParserSourceDO.getId(), dataParserSourceDO);
            });
            //load data parser source
            Map<String, List<DataParserSourceDO>> dataFlowIdToDataParserSourceIdsMap = loadDataParserSource(idToDataParserSourceMap);

            Map<String, DataFlowCfg> idToDataFlowCfgMap = Maps.newHashMap();
            Instance.ofNullable(allDataFlow).forEach(dataFlowDO -> {
                try {
                    DataFlowCfg dataFlowCfg = new DataFlowCfg();
                    dataFlowCfg.setGroup(dataFlowDO.getGroup());
                    dataFlowCfg.setName(dataFlowDO.getName());
                    dataFlowCfg.setId(dataFlowDO.getId());
                    dataFlowCfg.setScriptContent(dataFlowDO.getScriptContent());
                    dataFlowCfg.setScriptMethod(dataFlowDO.getScriptMethod());
                    dataFlowCfg.setAlarmQueueId(dataFlowDO.getAlarmQueueId());
                    dataFlowCfg.setCalculationQueueId(dataFlowDO.getCalculationQueueId());
                    dataFlowCfg.setHubUnitTagName(unitTagIdToNameMap.get(dataFlowDO.getHubUnitTagId()));
                    List<InputTopicProfileAreaCfgs> inputTopicProfileAreaCfgsList;
                    try {
                        if (StringUtils.isEmpty(dataFlowDO.getInputTopicProfileArea())) {
                            inputTopicProfileAreaCfgsList = Lists.newArrayList();
                        } else {
                            inputTopicProfileAreaCfgsList = JsonUtils.toObjectByTypeRef(dataFlowDO.getInputTopicProfileArea(), profileTypeReference);
                        }
                    } catch (Exception e) {
                        log.error("inputTopicProfileArea: {}, data flow id: {}", dataFlowDO.getInputTopicProfileArea(), dataFlowDO.getId());
                        return;
                    }
                    List<ConsumeTopicCfg> topics = new ArrayList<>();
                    Instance.ofNullable(inputTopicProfileAreaCfgsList).forEach(inputTopicProfileAreaCfgs -> {
                        String inputTopicProfile = inputTopicProfileAreaCfgs.getInputTopicProfile();
                        Instance.ofNullable(inputTopicProfileAreaCfgs.getAreaCfgs()).forEach(areaCfg -> {
                            topics.add(new ConsumeTopicCfg(areaCfg.getArea() + "_" + inputTopicProfile, areaCfg.getGroupId(), areaCfg.getThreadCount()));
                        });
                    });

                    dataFlowCfg.setInputQueues(topics);

                    if (StringUtils.isNotBlank(dataFlowDO.getAlarmQueueId())) {
                        TaskQueueDO alarmTaskQueue = taskQueueMap.get(dataFlowDO.getAlarmQueueId());
                        if (alarmTaskQueue != null) {
                            loadOutputAqCfgMap(dataFlowCfg.getAlarmQueueId(), DataParserQueueType.alarm_output.name(), alarmTaskQueue.getKafkaClusterId(), alarmTaskQueue.getProtocol(), alarmTaskQueue.getCompressionType(), allKafkaClusterMap, outputAqCfgMap, taskQueueMap, taskQueueIdToEnvMap);
                            // Compatible with versions before aa, and can be deleted later
                            dataFlowCfg.setAlarmOutputQueueKey(generateOutPutQueueId(alarmTaskQueue.getKafkaClusterId(), alarmTaskQueue.getProtocol(), alarmTaskQueue.getCompressionType()));
                            dataFlowCfg.setAlarmTopicName(alarmTaskQueue.getTopic());
                        } else {
                            log.error("HubCfgCacheService get alarm task is null, data flow:{}", JsonUtils.toJsonStringIgnoreExp(dataFlowDO));
                        }
                    }
                    if (StringUtils.isNotBlank(dataFlowDO.getCalculationQueueId())) {
                        TaskQueueDO calculationTaskQueue = taskQueueMap.get(dataFlowDO.getCalculationQueueId());
                        if (calculationTaskQueue != null) {
                            loadOutputAqCfgMap(dataFlowCfg.getCalculationQueueId(), DataParserQueueType.calc_output.name(), calculationTaskQueue.getKafkaClusterId(), calculationTaskQueue.getProtocol(), calculationTaskQueue.getCompressionType(), allKafkaClusterMap, outputAqCfgMap, taskQueueMap, taskQueueIdToEnvMap);
                            // Compatible with versions before aa, and can be deleted later
                            dataFlowCfg.setCalculationOutputQueueKey(generateOutPutQueueId(calculationTaskQueue.getKafkaClusterId(), calculationTaskQueue.getProtocol(), calculationTaskQueue.getCompressionType()));
                            dataFlowCfg.setCalculationTopicName(calculationTaskQueue.getTopic());
                        } else {
                            log.error("HubCfgCacheService get calculation task is null, data flow:{}", JsonUtils.toJsonStringIgnoreExp(dataFlowDO));
                        }
                    }

                    List<DataParserSourceCfg> dataParserSourceCfgs = new ArrayList<>();

                    List<DataParserSourceDO> dataParserSourceDOS = dataFlowIdToDataParserSourceIdsMap.get(dataFlowDO.getId());
                    Instance.ofNullable(dataParserSourceDOS).forEach(dataParserSourceDO -> {
                        DataParserSourceCfg dataParserSourceCfg = createDataParserCfg(parserAndPipelineMap, dataParserSourceDO);
                        dataParserSourceCfg.setTenantName(allTenantsMap.get(dataParserSourceCfg.getTenantId()) == null ? "" : allTenantsMap.get(dataParserSourceCfg.getTenantId()).getName());
                        dataParserSourceCfgs.add(dataParserSourceCfg);
                    });
                    dataFlowCfg.setDataParserSourceCfgs(dataParserSourceCfgs);
                    if (!CollectionUtils.isEmpty(dataFlowCfg.getDataParserSourceCfgs())) {
                        idToDataFlowCfgMap.put(dataFlowCfg.getId(), dataFlowCfg);
                    }
                } catch (Exception e) {
                    log.error("cache data flow failed.", e);
                    printCacheMetrics(DATAFLOW_MODULE, e, StatusEnum.FAIL.getStatus(), (System.currentTimeMillis() - startTime));
                }
            });

            // load data forward
            List<DataForwardDO> dataForwardDOList = dataForwardHandler.listAll();
            Map<String, List<DataForwardCfg>> unitTagNameToDataForwardMap = new HashMap<>();
            dataForwardDOList.forEach(dataForwardDO -> {

                DataForwardCfg dataForwardCfg = new DataForwardCfg();
                BeanUtils.copyProperties(dataForwardDO, dataForwardCfg);
                AsyncmqClusterDO inputAsyncMqClusterDO = getInnerAsyncMqCluster(tmpAsyncMqClusterIdToDetailMap, dataForwardCfg.getInputClusterId());
                if (inputAsyncMqClusterDO == null) {
                    dataForwardCfg.setInputClusterId("");
                    dataForwardCfg.setInputClusterName("");
                } else {
                    dataForwardCfg.setInputClusterId(inputAsyncMqClusterDO.getId());
                    dataForwardCfg.setInputClusterName(inputAsyncMqClusterDO.getName());
                }
                AsyncmqClusterDO outputAsyncMqClusterDO = getInnerAsyncMqCluster(tmpAsyncMqClusterIdToDetailMap, dataForwardCfg.getOutputClusterId());
                if (outputAsyncMqClusterDO == null) {
                    dataForwardCfg.setOutputClusterId("");
                    dataForwardCfg.setOutputClusterName("");
                } else {
                    dataForwardCfg.setOutputClusterId(outputAsyncMqClusterDO.getId());
                    dataForwardCfg.setOutputClusterName(outputAsyncMqClusterDO.getName());
                }
                String unitTagName = unitTagIdToNameMap.get(dataForwardDO.getUnitTagId());
                if (StringUtils.isBlank(unitTagName)
                        || DataForwardStatusEnum.disable.getValue().equals(dataForwardCfg.getStatus())) {
                    return;
                }
                dataForwardCfg.setUnitTagName(unitTagName);

                List<DataForwardCfg> dataForwards = unitTagNameToDataForwardMap.computeIfAbsent(unitTagName, k -> Lists.newArrayList());
                dataForwards.add(dataForwardCfg);
            });

            Map<String, AsyncmqClusterCfg> asyncMqClusterIdToDetailMap = new HashMap<>();
            tmpAsyncMqClusterIdToDetailMap.forEach((asyncMqClusterId, asyncMqClusterDO) -> {
                if (asyncMqClusterDO.getType() == AsyncmqClusterTypeEnum.inner.getValue()) {
                    AsyncmqClusterCfg asyncmqClusterCfg = new AsyncmqClusterCfg();
                    BeanUtils.copyProperties(asyncMqClusterDO, asyncmqClusterCfg);
                    asyncMqClusterIdToDetailMap.put(asyncMqClusterId, asyncmqClusterCfg);
                }
            });

            Map<String, List<KinesisStreamCfg>> unitToKinesisStreamCfgMap = kinesisParaService.getUnitToKinesisStreamCfgMap();

            cache = new HubCache(unitToKinesisStreamCfgMap, dataParsers, aqGroupCfgMap, aqCfgMap, parserAndPipelineMap, collectorMap, allTenantsMap, outputAqCfgMap, allKafkaClusterMap, allKafkaQueueMap, allAsyncMqQueueMap, idToDataFlowCfgMap, taskQueueIdToEnvMap, taskQueueMap, idToDataParserMap, idToDataParserSourceMap,
                    unitTagNameToDataForwardMap,
                    hubUnitTagNameToDataParserQueuesMap,
                    hubUnitTagNameToDataFlowQueuesMap,
                    asyncMqClusterIdToDetailMap,
                    idToDataParserConfigMap,
                    customerProcessorCacheDataMap);
        } catch (Exception e) {
            isSuccess = false;
            LOG.error("cacheDataParserInfo cache failed.", e);
            printCacheMetrics(ALL_MODULE, e, StatusEnum.FAIL.getStatus(), (System.currentTimeMillis() - startTime));
        }
        long endTime = System.currentTimeMillis();
        LOG.info("timing cache HubCfg success={},cost:{}ms", isSuccess, endTime - startTime);
        printCacheMetrics(ALL_MODULE, null, StatusEnum.SUCCESS.getStatus(), (endTime - startTime));
    }

    private AsyncmqClusterDO getInnerAsyncMqCluster(Map<String, AsyncmqClusterDO> asyncMqClusterIdToDetailMap, String asyncMqId) {
        AsyncmqClusterDO asyncmqClusterDO = asyncMqClusterIdToDetailMap.get(asyncMqId);
        if (asyncmqClusterDO == null) {
            return null;
        }
        return asyncMqClusterIdToDetailMap.get(asyncmqClusterDO.getRelatedClusterId());
    }

    private Map<String, List<DataParserSourceDO>> loadDataParserSource(Map<String, DataParserSourceDO> idToDataParserSourceMap) {
        Map<String, List<DataParserSourceDO>> dataFlowIdToDataParserSourceIdsMap = new HashMap<>();
        List<DataFlowDataParserRelationDO> dataFlowRelationList = dataFlowDataParserRelationHandler.listAll();
        Instance.ofNullable(dataFlowRelationList).forEach(dataFlowDataParserRelationDO -> {
            List<DataParserSourceDO> dataParserSourceList = dataFlowIdToDataParserSourceIdsMap.computeIfAbsent(dataFlowDataParserRelationDO.getDataFlowId(), k -> Lists.newArrayList());
            DataParserSourceDO dataParserSourceDO = idToDataParserSourceMap.get(dataFlowDataParserRelationDO.getDataParserId());
            if (dataParserSourceDO == null) {
                log.info("data flow data parser relation not exist, relation id:{}, data parser id:{}", dataFlowDataParserRelationDO.getId(), dataFlowDataParserRelationDO.getDataParserId());
            } else {
                dataParserSourceList.add(dataParserSourceDO);
            }
        });
        return dataFlowIdToDataParserSourceIdsMap;
    }

    public void cacheAsyncmqAccount() {
        try {
            List<AsyncmqAccountMappingDO> asyncmqAccountMappingDOS = asyncmqLoginTokenHandle.getAll();
            cache.setAsyncmqAccountMap(buildAsyncmqAccountMap(asyncmqAccountMappingDOS));
        } catch (Exception e) {
            log.error("set asyncmq account cache error, errorMsg = {}", e.getMessage());
        }
    }

    private void cacheCustomerLabelInfoFromGwatch() {
        try {
            List<CustomerLabel> customerLabelList = Lists.newArrayList();
            GwatchService.CustomerLabelIterator iterator = new GwatchService.CustomerLabelIterator(gwatchService);

            Map<String, FieldTypeEnum> labelTypeMap = Maps.newHashMap();
            while (iterator.hasNext()) {
                customerLabelList.addAll(iterator.next());
            }
            if (!CollectionUtils.isEmpty(customerLabelList)) {
                Map<String, Map<String, Object>> customerAttributes = Maps.newHashMap();
                customerLabelList.forEach(customerLabel -> {
                    String accountId = customerLabel.getAccountId();
                    Map<String, Object> attributes = customerLabel.getAttributes();
                    customerAttributes.put(accountId, attributes);
                    if (attributes != null && attributes.size() != labelTypeMap.size()) {
                        attributes.forEach((labelName, labelValue) -> {
                            FieldTypeEnum fieldType;
                            if (labelValue instanceof Number) {
                                fieldType = FieldTypeEnum.number;
                            } else {
                                // others as string
                                fieldType = FieldTypeEnum.string;
                            }
                            labelTypeMap.putIfAbsent(labelName, fieldType);
                        });
                    }
                });
                GwatchService.CustomerLabelCache.setCustomerAttributes(customerAttributes);
                GwatchService.CustomerLabelCache.setLabelTypeMap(labelTypeMap);
            } else {
                log.error("get customer labels from gwatch empty");
            }
        } catch (Exception e) {
            log.error("get customer labels from gwatch failed, errorMsg = {}", e.getMessage());
        }
    }

    public Map<String, AsyncmqAccountMappingDO> buildAsyncmqAccountMap(List<AsyncmqAccountMappingDO> asyncmqAccountMappingDOS) {
        if (CollectionUtils.isEmpty(asyncmqAccountMappingDOS)) {
            return Maps.newHashMap();
        }
        Map<String, AsyncmqAccountMappingDO> asyncmqAccountMap = new HashMap<>();
        asyncmqAccountMappingDOS.forEach(asyncmqAccountMappingDO -> {
            asyncmqAccountMap.put(asyncmqAccountMappingDO.getAsyncmqName(), asyncmqAccountMappingDO);
        });
        return asyncmqAccountMap;
    }

    private void printCacheMetrics(String module, Exception exception, String status, long cost) {
        try {
            Map<String, Object> metrics = new HashMap<>();
            metrics.put("cost", cost);
            metrics.put("ts", System.currentTimeMillis());
            metrics.put("module", module);
            metrics.put("status", status);
            metrics.put("type", MonitorTypeEnum.cacheHubLoad.name());
            metrics.put("exception", exception == null ? "" : ExceptionStackUtils.parseExceptionStackToString(exception));
            monitorLog.info(JsonUtils.toJsonStringIgnoreExp(metrics));
        } catch (Exception e) {

        }
    }


    private DataParserSourceCfg getDataParserCfg(Map<String, List<DataParserPipelineCfg>> parserAndPipelineMap, Map<String, CollectorDO> collectorMap, TenantDO tenantDO, DataParserSourceDO dataParserSourceDO) {
        DataParserSourceCfg dataParserCfg = new DataParserSourceCfg();
        BeanUtils.copyProperties(dataParserSourceDO, dataParserCfg);
        List<DataParserPipelineCfg> pipelineCfgs = Instance.ofNullable(parserAndPipelineMap.get(dataParserCfg.getId())).stream().filter(item -> null != item.getCollectorCfg()).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(pipelineCfgs)) {
            Collections.sort(pipelineCfgs);
        }

        for (DataParserPipelineCfg pipelineCfg : pipelineCfgs) {
            try {
                CollectorCfg collectorCfg = pipelineCfg.getCollectorCfg();
                CollectorDO collectorDO = collectorMap.get(collectorCfg.getId());
                if (null == collectorDO || StringUtils.isBlank(collectorDO.getTopic())) {
                    collectorCfg.setTopicName(StringUtils.lowerCase(tenantDO.getName() + CommonSplitConstants.SPLIT + DataParserCollectTypeEnum.aq.name()));
                } else {
                    collectorCfg.setTopicName(StringUtils.lowerCase(tenantDO.getName() + CommonSplitConstants.SPLIT + collectorDO.getTopic()));
                }
            } catch (Exception e) {
                LOG.error("set topic name error,pipelineCfg is {}", JsonUtils.toJsonString(pipelineCfg), e);
            }
        }
        dataParserCfg.setPipelines(pipelineCfgs);
        return dataParserCfg;
    }

    private DataParserSourceCfg createDataParserCfg(Map<String, List<DataParserPipelineCfg>> parserAndPipelineMap, DataParserSourceDO dataParserSourceDO) {
        DataParserSourceCfg dataParserCfg = new DataParserSourceCfg();
        BeanUtils.copyProperties(dataParserSourceDO, dataParserCfg);
        if (StringUtils.isNotBlank(dataParserSourceDO.getPaths())) {
            try {
                dataParserCfg.setPaths(JsonUtils.toObjectByTypeRef(dataParserSourceDO.getPaths(), new TypeReference<List<String>>() {
                }));
            } catch (Exception e) {
                LOG.error("dataParserSourceCfg setPath error,DataParserSourceDO is {}", JsonUtils.toJsonStringIgnoreExp(dataParserSourceDO));
            }
        }
        List<DataParserPipelineCfg> pipelineCfgs = Instance.ofNullable(parserAndPipelineMap.get(dataParserCfg.getId())).stream().filter(item -> null != item.getCollectorCfg()).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(pipelineCfgs)) {
            Collections.sort(pipelineCfgs);
        }
        dataParserCfg.setPipelines(pipelineCfgs);
        return dataParserCfg;
    }

    public void loadOutputAqCfgMap(String outerId, String queueType, String kafkaClusterIdParam, String protocolParam, String compressionTypeParam, Map<String, KafkaClusterDO> allKafkaClusterMap, Map<String, OutputAqCfg> outputAqCfgMap, Map<String, TaskQueueDO> taskQueueMap, Map<String, Map<String, EnvironmentTaskRelaDO>> taskQueueIdToEnvMap) {
        String kafkaClusterId = kafkaClusterIdParam;
        String protocol = protocolParam;
        String compressionType = compressionTypeParam;
        try {
            TaskQueueDO taskQueueDO = taskQueueMap.get(outerId);
            if (taskQueueDO != null) {
                kafkaClusterId = taskQueueDO.getKafkaClusterId();
                protocol = taskQueueDO.getProtocol();
                compressionType = taskQueueDO.getCompressionType();
            }

            if (DataParserQueueType.alarm_output.name().equals(queueType) ||
                    DataParserQueueType.calc_output.name().equals(queueType)) {
                Map<String, EnvironmentTaskRelaDO> envTaskRelaMap = taskQueueIdToEnvMap.get(taskQueueDO.getId());
                for (EnvironmentTaskRelaDO environmentTaskRelaDO : envTaskRelaMap.values()) {
                    if (environmentTaskRelaDO.getQueueType().equals(QueueTypeEnum.kafka.name())) {
                        addToOutputAqCfgMap(environmentTaskRelaDO.getQueueClusterId(), environmentTaskRelaDO.getProtocol(), environmentTaskRelaDO.getCompressionType(), allKafkaClusterMap, outputAqCfgMap);
                    }
                }
            }
            addToOutputAqCfgMap(kafkaClusterId, protocol, compressionType, allKafkaClusterMap, outputAqCfgMap);
        } catch (Exception e) {
            LOG.info("loadOutputAqCfgMap failed, outerId:{}, kafkaClusterId:{}, protocol:{}, compressionType:{}", outerId, kafkaClusterId, protocol, compressionType, e);
        }
    }

    private void addToOutputAqCfgMap(String kafkaClusterId, String protocol, String compressionType, Map<String, KafkaClusterDO> allKafkaClusterMap, Map<String, OutputAqCfg> outputAqCfgMap) {
        KafkaClusterDO kafkaClusterDO = allKafkaClusterMap.get(kafkaClusterId);
        if (kafkaClusterDO == null) {
            return;
        }
        String outputQueueId = generateOutPutQueueId(kafkaClusterDO.getId(), protocol, compressionType);
        if (outputAqCfgMap.get(outputQueueId) == null) {
            OutputAqCfg outputAqCfg = new OutputAqCfg();
            outputAqCfg.setId(outputQueueId);
            outputAqCfg.setName(kafkaClusterDO.getName());
            if (AuthEncrTypeEnum.no.name().equals(protocol)) {
                outputAqCfg.setServers(kafkaClusterDO.getBootstrapServersPlain());
            } else {
                if (AuthEncrTypeEnum.ssl.name().equals(protocol)) {
                    outputAqCfg.setServers(kafkaClusterDO.getBootstrapServersSsl());
                } else if (AuthEncrTypeEnum.saslSsl.name().equals(protocol)) {
                    outputAqCfg.setServers(kafkaClusterDO.getBootstrapServersSslSasl());
                    outputAqCfg.setJaasConfig(kafkaClusterDO.getSaslJaasConfig());
                    outputAqCfg.setSaslMechanism(SaslMechanismEnum.valueOf(kafkaClusterDO.getSaslMechanism()).getCode());
                } else if (AuthEncrTypeEnum.saslPlaintext.name().equals(protocol)) {
                    outputAqCfg.setServers(kafkaClusterDO.getBootstrapServersSasl());
                    outputAqCfg.setJaasConfig(kafkaClusterDO.getSaslJaasConfig());
                    outputAqCfg.setSaslMechanism(SaslMechanismEnum.valueOf(kafkaClusterDO.getSaslMechanism()).getCode());
                }
                outputAqCfg.setIdenAlgorithm(kafkaClusterDO.getSslEndpointIdentificationAlgorithm());
                outputAqCfg.setTruststorePath(kafkaClusterDO.getSslTruststoreLocation());
                outputAqCfg.setTruststorePwd(kafkaClusterDO.getSslTruststorePassword());
                if (StringUtils.isNotBlank(kafkaClusterDO.getSslKeystoreLocation())) {
                    outputAqCfg.setKeystorePath(kafkaClusterDO.getSslKeystoreLocation());
                    outputAqCfg.setKeystorePwd(kafkaClusterDO.getSslKeystorePassword());
                    outputAqCfg.setKeyPwd(kafkaClusterDO.getSslKeyPassword());
                }
            }

            outputAqCfg.setAuthEncrType(AuthEncrTypeEnum.valueOf(protocol).getValue());
            outputAqCfg.setZipType(KafkaZipTypeEnum.valueOf(compressionType).getCode());

            outputAqCfgMap.put(outputQueueId, outputAqCfg);
        }
    }

    public String generateOutPutQueueId(String kafkaClusterId, String protocol, String compressionType) {
        return new StringBuilder(kafkaClusterId).append("-").append(protocol).append("-").append(compressionType).toString();
    }

    public HubCache getCache() {
        return cache;
    }

    public class HubCache {
        private Map<String, List<KinesisStreamCfg>> unitToKinesisStreamCfgMap;
        private List<DataParserDO> dataParsers;
        //Map:key->AsyncQueueGroupId
        private Map<String, AqGroupCfg> aqGroupCfgMap;
        //Map:key->AsyncQueueGroupId
        private Map<String, List<AqCfg>> aqCfgMap;
        //Map:key->parserId
        private Map<String, List<DataParserPipelineCfg>> parserAndPipelineMap;
        //Map:key->CollectorId
        private Map<String, CollectorDO> collectorMap;
        //Map:key->TenantId
        private Map<String, TenantDO> allTenantsMap;
        //Map:key->OutputId
        private Map<String, OutputAqCfg> outputAqCfgMap;
        //Map:key->kafkaClusterId
        Map<String, KafkaClusterDO> allKafkaClusterMap;
        //Map:key->dataParserId
        Map<String, List<KafkaQueueCfg>> allKafkaQueueMap;
        //Map:key->dataParserId
        Map<String, List<AsyncMqQueueCfg>> allAsyncMqQueueMap;
        Map<String, List<DataFlowCfg>> hubUnitTagToDataFlowsMap;
        Map<String, DataFlowCfg> idToDataFlowMap;
        // taskQueueId -> envName -> EnvironmentTaskRelaDO
        Map<String, Map<String, EnvironmentTaskRelaDO>> taskQueueIdToEnvMap;
        //taskQueueId->taskQueueDO
        Map<String, TaskQueueDO> taskQueueMap;
        Map<String, DataParserDO> idToDataParserMap;
        Map<String, DataParserSourceDO> idToDataParserSourceMap;
        Map<String, List<DataForwardCfg>> unitTagNameToDataForwardMap;
        Map<String, List<InputQueueCfg>> unitTagNameToDataParserQueuesMap;

        Map<String, List<InputQueueCfg>> unitTagNameToDataFlowQueuesMap;
        Map<String, AsyncmqClusterCfg> asyncmqClusterIdToDetailMap;
        Map<String, DataParserConfig> idToDataParserConfigMap;
        Map<String, Set<CustomerLabelInfoSource>> customerProcessorCacheDataMap;

        public Map<String, AsyncmqAccountMappingDO> getAsyncmqAccountMap() {
            return asyncmqAccountMap;
        }

        public void setAsyncmqAccountMap(Map<String, AsyncmqAccountMappingDO> asyncmqAccountMap) {
            this.asyncmqAccountMap = asyncmqAccountMap;
        }

        Map<String, AsyncmqAccountMappingDO> asyncmqAccountMap = Maps.newHashMap();

        public HubCache(
                Map<String, List<KinesisStreamCfg>> unitToKinesisStreamCfgMap,
                List<DataParserDO> dataParsers,
                Map<String, AqGroupCfg> aqGroupCfgMap,
                Map<String, List<AqCfg>> aqCfgMap,
                Map<String, List<DataParserPipelineCfg>> parserAndPipelineMap,
                Map<String, CollectorDO> collectorMap,
                Map<String, TenantDO> allTenantsMap,
                Map<String, OutputAqCfg> outputAqCfgMap,
                Map<String, KafkaClusterDO> allKafkaClusterMap,
                Map<String, List<KafkaQueueCfg>> allKafkaQueueMap,
                Map<String, List<AsyncMqQueueCfg>> allAsyncMqQueueMap,
                Map<String, DataFlowCfg> idToDataFlowMap,
                Map<String, Map<String, EnvironmentTaskRelaDO>> taskQueueIdToEnvMap,
                Map<String, TaskQueueDO> taskQueueMap,
                Map<String, DataParserDO> idToDataParserMap,
                Map<String, DataParserSourceDO> idToDataParserSourceMap,
                Map<String, List<DataForwardCfg>> unitTagNameToDataForwardMap,
                Map<String, List<InputQueueCfg>> unitTagNameToDataParserQueuesMap,
                Map<String, List<InputQueueCfg>> unitTagNameToDataFlowQueuesMap,
                Map<String, AsyncmqClusterCfg> asyncmqClusterIdToDetailMap,
                Map<String, DataParserConfig> idToDataParserConfigMap,
                Map<String, Set<CustomerLabelInfoSource>> customerProcessorCacheDataMap) {
            this.unitToKinesisStreamCfgMap = unitToKinesisStreamCfgMap;
            this.dataParsers = dataParsers;
            this.aqGroupCfgMap = aqGroupCfgMap;
            this.aqCfgMap = aqCfgMap;
            this.parserAndPipelineMap = parserAndPipelineMap;
            this.collectorMap = collectorMap;
            this.allTenantsMap = allTenantsMap;
            this.outputAqCfgMap = outputAqCfgMap;
            this.allKafkaClusterMap = allKafkaClusterMap;
            this.allKafkaQueueMap = allKafkaQueueMap;
            this.allAsyncMqQueueMap = allAsyncMqQueueMap;
            this.idToDataFlowMap = idToDataFlowMap;
            this.taskQueueIdToEnvMap = taskQueueIdToEnvMap;
            this.taskQueueMap = taskQueueMap;
            this.idToDataParserMap = idToDataParserMap;
            this.idToDataParserSourceMap = idToDataParserSourceMap;
            this.unitTagNameToDataForwardMap = unitTagNameToDataForwardMap;
            this.unitTagNameToDataParserQueuesMap = unitTagNameToDataParserQueuesMap;
            this.unitTagNameToDataFlowQueuesMap = unitTagNameToDataFlowQueuesMap;
            this.asyncmqClusterIdToDetailMap = asyncmqClusterIdToDetailMap;
            this.idToDataParserConfigMap = idToDataParserConfigMap;
            this.hubUnitTagToDataFlowsMap = Maps.newHashMap();
            idToDataFlowMap.forEach((id, dataflowCfg) -> {
                List<DataFlowCfg> dataFlowCfgs = hubUnitTagToDataFlowsMap.computeIfAbsent(dataflowCfg.getHubUnitTagName(), tag -> Lists.newArrayList());
                dataFlowCfgs.add(dataflowCfg);
            });
            this.customerProcessorCacheDataMap = customerProcessorCacheDataMap;
        }

        public Map<String, List<KinesisStreamCfg>> getUnitToKinesisStreamCfgMap() {
            return unitToKinesisStreamCfgMap;
        }

        public List<DataParserDO> getDataParsers() {
            return dataParsers;
        }

        public Map<String, AqGroupCfg> getAqGroupCfgMap() {
            return aqGroupCfgMap;
        }

        public Map<String, List<AqCfg>> getAqCfgMap() {
            return aqCfgMap;
        }

        public Map<String, OutputAqCfg> getOutputAqCfgMap() {
            return outputAqCfgMap;
        }

        public Map<String, List<DataParserPipelineCfg>> getParserAndPipelineMap() {
            return parserAndPipelineMap;
        }

        public Map<String, CollectorDO> getCollectorMap() {
            return collectorMap;
        }

        public Map<String, TenantDO> getAllTenantsMap() {
            return allTenantsMap;
        }

        public Map<String, KafkaClusterDO> getAllKafkaClusterMap() {
            return allKafkaClusterMap;
        }

        public Map<String, List<KafkaQueueCfg>> getAllKafkaQueueMap() {
            return allKafkaQueueMap;
        }

        public Map<String, List<AsyncMqQueueCfg>> getAllAsyncMqQueueMap() {
            return allAsyncMqQueueMap;
        }

        public void setAllAsyncMqQueueMap(Map<String, List<AsyncMqQueueCfg>> allAsyncMqQueueMap) {
            this.allAsyncMqQueueMap = allAsyncMqQueueMap;
        }

        public Map<String, TaskQueueDO> getTaskQueueMap() {
            return taskQueueMap;
        }

        public Map<String, Map<String, EnvironmentTaskRelaDO>> getTaskQueueIdToEnvMap() {
            return taskQueueIdToEnvMap;
        }

        public Map<String, List<DataFlowCfg>> getHubUnitTagToDataFlowsMap() {
            return hubUnitTagToDataFlowsMap;
        }

        public Map<String, DataParserDO> getIdToDataParserMap() {
            return idToDataParserMap;
        }

        public Map<String, DataParserSourceDO> getIdToDataParserSourceMap() {
            return idToDataParserSourceMap;
        }

        public Map<String, List<DataForwardCfg>> getUnitTagNameToDataForwardMap() {
            return unitTagNameToDataForwardMap;
        }

        public Map<String, List<InputQueueCfg>> getUnitTagNameToDataParserQueuesMap() {
            return unitTagNameToDataParserQueuesMap;
        }

        public Map<String, AsyncmqClusterCfg> getAsyncmqClusterIdToDetailMap() {
            return asyncmqClusterIdToDetailMap;
        }

        public Map<String, DataParserConfig> getIdToDataParserConfigMap() {
            return idToDataParserConfigMap;
        }

        public Map<String, DataFlowCfg> getIdToDataFlowMap() {
            return idToDataFlowMap;
        }

        public Map<String, List<InputQueueCfg>> getUnitTagNameToDataFlowQueuesMap() {
            return unitTagNameToDataFlowQueuesMap;
        }

        public Map<String, Set<CustomerLabelInfoSource>> getCustomerProcessorCacheDataMap() {
            return customerProcessorCacheDataMap;
        }
    }
}
