package us.zoom.cube.site.biz;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.async.mq.openapi.model.base.Result;
import us.zoom.async.mq.openapi.model.result.topic.TopicResult;
import us.zoom.cube.lib.common.AsyncMqQueueSourceTypeEnum;
import us.zoom.cube.lib.common.AsyncMqQueueStatusEnum;
import us.zoom.cube.notice.biz.service.AlarmIMSender;
import us.zoom.cube.site.core.*;
import us.zoom.cube.site.infra.AsyncMqOpenApiClientWrapper;
import us.zoom.cube.site.infra.utils.AsyncMqUtils;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.IdPara;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.*;
import us.zoom.cube.site.lib.input.dataflow.BatchMigrateDataFlowInput;
import us.zoom.cube.site.lib.input.dataflow.BatchUpdateDataFlowInput;
import us.zoom.cube.site.lib.output.PageResult;
import us.zoom.cube.site.lib.output.dataflow.DataFlowOut;
import us.zoom.cube.site.lib.output.dataflow.MigratedDataFlowOut;
import us.zoom.cube.site.lib.output.dataflow.SimpleDataFlowOut;
import us.zoom.cube.site.lib.query.DataFlowQuery;
import us.zoom.cube.site.lib.query.NameQuery;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.infra.asyncmq.TopicResultWrapper;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.thread.CubeThreadPool;
import us.zoom.infra.utils.AsyncUtils;
import us.zoom.infra.utils.Instance;
import us.zoom.cube.lib.utils.JsonUtils;

import jakarta.validation.Valid;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DataFlowService {

    private DataFlowHandler dataFlowHandler;
    private AuthService authService;
    private UnitTagHandler unitTagHandler;
    private TaskQueueHandler taskQueueHandler;
    private TenantHandler tenantHandler;
    private UserHandler userHandler;

    @Autowired
    private SysParaService sysParaService;

    @Autowired
    private AlarmIMSender alarmIMSender;

    @Autowired
    private Environment environment;

    @Autowired
    private HubGroovyCheckService hubGroovyCheckService;

    @Autowired
    private AsyncmqClusterHandler asyncmqClusterHandler;

    @Autowired
    private AsyncMqQueueHandler asyncMqQueueHandler;
    
    @Autowired
    public DataFlowService(AuthService authService, DataFlowHandler dataFlowHandler, UnitTagHandler unitTagHandler, TaskQueueHandler taskQueueHandler, TenantHandler tenantHandler, UserHandler userHandler) {
        this.dataFlowHandler = dataFlowHandler;
        this.unitTagHandler = unitTagHandler;
        this.authService = authService;
        this.taskQueueHandler = taskQueueHandler;
        this.tenantHandler = tenantHandler;
        this.userHandler = userHandler;
    }

    public ResponseObject<PageResult<DataFlowOut>> searchDataFlow(PageQuery<DataFlowQuery> query) {
//        authService.mustAdmin(query.getUserId());
        authService.checkAuth(query);
        DataFlowQuery dataFlowQuery = query.getQueryPara();
        List<DataFlowDO> dataFlowList = dataFlowHandler.findByParam(
                dataFlowQuery.getName(),
                dataFlowQuery.getGroup(),
                dataFlowQuery.getStatus(),
                query.getPageIndex(), query.getPageSize());
        int total = dataFlowHandler.getCountByParam(
                dataFlowQuery.getName(),
                dataFlowQuery.getGroup(),
                dataFlowQuery.getStatus());

        Set<String> userIds = new HashSet<>();
        Instance.ofNullable(dataFlowList).forEach(dataFlowDO -> {
            if (StringUtils.isNotBlank(dataFlowDO.getCreateUserId())) {
                userIds.add(dataFlowDO.getCreateUserId());
            }
            if (StringUtils.isNotBlank(dataFlowDO.getEditUserId())) {
                userIds.add(dataFlowDO.getEditUserId());
            }
        });
        List<UserDO> userDOList = userHandler.getByIds(userIds);
        Map<String, String> userIdNameMap = new HashMap<>();
        Instance.ofNullable(userDOList).forEach(userDO -> {
            userIdNameMap.put(userDO.getId(), userDO.getName());
        });

        List<DataFlowOut> result = new ArrayList<>();
        Instance.ofNullable(dataFlowList).forEach(dataFlowDO -> {
            DataFlowOut out = new DataFlowOut();
            BeanUtils.copyProperties(dataFlowDO, out);
            if (StringUtils.isNotBlank(dataFlowDO.getCreateUserId())) {
                out.setCreateUserName(userIdNameMap.get(dataFlowDO.getCreateUserId()));
            }
            if (StringUtils.isNotBlank(dataFlowDO.getEditUserId())) {
                out.setEditUserName(userIdNameMap.get(dataFlowDO.getEditUserId()));
            }
            result.add(out);
        });
        return ResponseObject.success(query.getOperId(), new PageResult(total, result));
    }


    public ResponseObject<List<SimpleDataFlowOut>> searchAllDataFlow(DataFlowQuery query) {
//        authService.mustAdmin(query.getUserId());
        authService.checkAuth(query);
        List<SimpleDataFlowOut> result = new ArrayList<>();
        List<DataFlowDO> dataFlowList = dataFlowHandler.listAll();
        if (!CollectionUtils.isEmpty(dataFlowList)) {
            result.addAll(dataFlowList.stream().map(e -> new SimpleDataFlowOut(e.getId(), e.getName())).collect(Collectors.toList()));
        }
        return ResponseObject.success(result);
    }

    public ResponseObject addDataFlow(@Valid DataFlowInput input) {
//        authService.mustAdmin(input.getUserId());
        authService.checkAuth(input);
        input.check();
        if (hubGroovyCheckService.hasDataFlowGroovyPermission(input) == false) {
            return ResponseObject.fail("No permission to modify groovy scripts");
        }
        Assert.isTrue(!(dataFlowHandler.getCountByName(input.getName()) > 0), "name " + input.getName() + " is already exist");

        DataFlowDO dataFlowDO = new DataFlowDO();
        BeanUtils.copyProperties(input, dataFlowDO);
        dataFlowDO.setId(IdUtils.generateId());
        dataFlowDO.setCreateUserId(input.getUserId());
        dataFlowDO.setEditUserId(input.getUserId());
        dataFlowHandler.addDataFlow(dataFlowDO);
        return ResponseObject.success(dataFlowDO.getId());
    }


    public ResponseObject editDataFlow(DataFlowInput input) {
//        authService.mustAdmin(input.getUserId());
        authService.checkAuth(input);
        input.editCheck();

        Assert.isTrue(!(dataFlowHandler.getCountByNameNotId(input.getName(), input.getId()) > 0), "name " + input.getName() + " is exist");

        DataFlowDO oldDataFlowDO = dataFlowHandler.getDataFlowById(input.getId());
        Assert.isTrue(oldDataFlowDO != null, "old dataFlow is null");
        if (hubGroovyCheckService.hasDataFlowGroovyPermission(input, oldDataFlowDO) == false) {
            return ResponseObject.fail("No permission to modify groovy scripts");
        }
        DataFlowDO dataFlowDO = new DataFlowDO();
        BeanUtils.copyProperties(input, dataFlowDO);
        dataFlowDO.setEditUserId(input.getUserId());
        dataFlowHandler.editDataFlow(dataFlowDO);
        return ResponseObject.success(true);
    }

    public ResponseObject<DataFlowDO> getDataFlowByTenantId(IdPara idPara) {
        authService.hasSuchTenant(idPara.getUserId(), idPara.getTenantId());
        Assert.notNull(idPara.getId(), "id not null");
        TenantDO tenantDO = tenantHandler.getTenantById(idPara.getId());
        if (tenantDO != null && StringUtils.isNotEmpty(tenantDO.getDataFlowId())) {
            return ResponseObject.success(dataFlowHandler.getDataFlowById(tenantDO.getDataFlowId()));
        } else {
            return ResponseObject.success(null);
        }
    }

    public ResponseObject getDataFlowById(IdPara idPara) {
//        authService.mustAdmin(idPara.getUserId());
        authService.checkAuth(idPara);
        Assert.notNull(idPara.getId(), "id not null");
        DataFlowOut dataFlowOut = new DataFlowOut();
        DataFlowDO dataFlowDO = dataFlowHandler.getDataFlowById(idPara.getId());
        BeanUtils.copyProperties(dataFlowDO, dataFlowOut);

        if (StringUtils.isNotBlank(dataFlowDO.getCreateUserId())) {
            dataFlowOut.setCreateUserName(userHandler.getUserById(dataFlowDO.getCreateUserId()).getName());
        }
        if (StringUtils.isNotBlank(dataFlowDO.getEditUserId())) {
            dataFlowOut.setEditUserName(userHandler.getUserById(dataFlowDO.getEditUserId()).getName());
        }
        return ResponseObject.success(dataFlowOut);
    }

    public ResponseObject deleteDataFlowById(IdPara idPara) {
//        authService.mustAdmin(idPara.getUserId());
        authService.checkAuth(idPara);
        Assert.notNull(idPara.getId(), "id not null");
        dataFlowHandler.delDataFlowById(idPara.getId());
        tenantHandler.deleteDataFlowFormTenant(idPara.getId());
        return ResponseObject.success(true);
    }

    public ResponseObject checkDataFlowNameExist(@Valid NameQuery query) {
        int num = dataFlowHandler.getCountByNameNotId(query.getName(), query.getId());
        return ResponseObject.success(num == 0 ? false : true);
    }

    public ResponseObject batchUpdateDataFlow(BatchUpdateDataFlowInput batchUpdateDataFlowInput) {
//        authService.mustAdmin(batchUpdateDataFlowInput.getUserId());
        authService.checkAuth(batchUpdateDataFlowInput);
        Assert.isTrue(StringUtils.isNotBlank(batchUpdateDataFlowInput.getGroup()), "group is not blank");
        Assert.isTrue(!CollectionUtils.isEmpty(batchUpdateDataFlowInput.getIds()), "ids is empty");
        dataFlowHandler.batchUpdateDataFlowGroup(
                batchUpdateDataFlowInput.getIds(),
                batchUpdateDataFlowInput.getGroup());
        return ResponseObject.success(true);
    }


    public ResponseObject batchMigrateFromDataFlow(BatchMigrateDataFlowInput input) {

        authService.checkAuth(input);

        List<String> dataFlowIds = input.getDataFlowIds();

        List<MigratedDataFlowOut> migratedDataFlowOutList = dataFlowIds.stream().map(dataFlowId -> {

            MigratedDataFlowOut migratedDataFlowOut = new MigratedDataFlowOut();
            migratedDataFlowOut.setDataFlowId(dataFlowId);

            DataFlowDO dataFlowDO = dataFlowHandler.getDataFlowById(dataFlowId);

            if (dataFlowDO == null) {
                migratedDataFlowOut.setErrorMessage("dataFlow not exist");
                return migratedDataFlowOut;
            }
            migratedDataFlowOut.setDataFlowName(dataFlowDO.getName());
            List<InputTopicProfileAreaCfgs> inputTopicProfileAreaCfgsList = JsonUtils.toObjectByTypeRef(dataFlowDO.getInputTopicProfileArea(), new TypeReference<>() {
            });

            Map<String, AreaCfgInput> topicAreaCfgMap = Maps.newHashMap();

            Set<String> topicNames = inputTopicProfileAreaCfgsList.stream()
                    .map(inputTopicProfileAreaCfgs -> {
                        return inputTopicProfileAreaCfgs.getAreaCfgs()
                                .stream()
                                .map(areaCfgInput -> {
                                    String topicName = areaCfgInput.getArea() + "_" + inputTopicProfileAreaCfgs.getInputTopicProfile();
                                    topicAreaCfgMap.put(topicName, areaCfgInput);
                                    return topicName;
                                }).collect(Collectors.toSet());
                    }).flatMap(Set::stream).collect(Collectors.toSet());

            // asyncMq only support 50 query size, so need to split them into multi batches
            List<List<String>> topicNameBatchList = AsyncMqUtils.splitIntoMultipleBatches(Lists.newArrayList(topicNames));

            Set<String> topicSet = Sets.newConcurrentHashSet(asyncMqQueueHandler.getNamesBySourceIdAndAsyncMqClusterId(dataFlowId, input.getAsyncMqClusterId()));

            AsyncUtils.parallelFutureJoinWithoutResult(topicNameBatchList, topicNameBatch -> {
                        migrateTopicToInputQueue(topicNameBatch, input.getAsyncMqClusterId(), migratedDataFlowOut, dataFlowDO,
                                topicSet, topicAreaCfgMap);
                    },
                    (e, topicNameBatch) -> {
                        migratedDataFlowOut.setErrorMessage(e.getMessage());
                        migratedDataFlowOut.addFailedTopicNames(topicNameBatch);
                        log.error("migrate topic batch failed, names: {}", topicNameBatch);
                    }, CubeThreadPool.getThreadPool()
            );

            migratedDataFlowOut.setNotExistTopicCount(migratedDataFlowOut.getNotExistTopicNames().size());
            migratedDataFlowOut.setTotalCount(topicNames.size());
            migratedDataFlowOut.setFailedCount(migratedDataFlowOut.getFailedTopicNames().size());
            return migratedDataFlowOut;
        }).collect(Collectors.toList());


        return ResponseObject.success(migratedDataFlowOutList);
    }

    private void migrateTopicToInputQueue(List<String> topicNames, String asyncMqClusterId, MigratedDataFlowOut migratedDataFlowOut, DataFlowDO dataFlowDO,
                                          Set<String> topicSet, Map<String, AreaCfgInput> topicAreaCfgMap) {

        AsyncMqOpenApiClientWrapper clientWrapper = asyncmqClusterHandler.getClientByClusterId(asyncMqClusterId);

        Map<String, TopicResultWrapper> topicResultMap = Maps.newHashMap();

        Result<List<TopicResult>> result = clientWrapper.getOpenApi().listTopicsByNames(topicNames);
        if (!result.isSuccess()) {
            log.error("get topicInfo from asyncMq failed, asyncMqClusterName: {} ,endpoint: {}, code: {}, reason: {}",
                    clientWrapper.getAsyncMqClusterName(), clientWrapper.getEndpoint(), result.getCode(), result.getMessage());
            throw new RuntimeException(result.getMessage());
        } else {
            result.getData().forEach(topicResult -> {
                topicResultMap.put(topicResult.getName(),
                        new TopicResultWrapper(topicResult, clientWrapper.getAsyncMqClusterId(), clientWrapper.getAsyncMqClusterName()));
            });
        }

        List<String> migrateTopicNames = Lists.newArrayList();

        List<AsyncMqQueueDO> asyncMqQueueDOList = topicNames.stream()
                .filter(topicName -> {
                    if (!topicResultMap.containsKey(topicName)) {
                        migratedDataFlowOut.addNotExistTopic(topicName);
                        return false;
                    }
                    // if false means that topic has been migrated or in migration, no need to migrate
                    return topicSet.add(topicName);
                })
                .map(topicName -> {
                    TopicResultWrapper topicResultWrapper = topicResultMap.get(topicName);
                    AreaCfgInput areaCfgInput = topicAreaCfgMap.get(topicName);
                    AsyncMqQueueDO asyncMqQueueDO = buildAsyncMqQueueDO(topicResultWrapper, areaCfgInput, dataFlowDO);
                    migrateTopicNames.add(topicName);

                    return asyncMqQueueDO;
                }).collect(Collectors.toList());

        asyncMqQueueHandler.batchAdd(asyncMqQueueDOList);

        migratedDataFlowOut.addSuccessResult(migrateTopicNames);
    }

    private AsyncMqQueueDO buildAsyncMqQueueDO(TopicResultWrapper topicResultWrapper, AreaCfgInput areaCfgInput, DataFlowDO dataFlowDO) {

        TopicResult topicResult = topicResultWrapper.getTopicResult();

        AsyncMqQueueDO asyncMqQueueDO = asyncMqQueueHandler.buildNewAsyncMqQueueDO(topicResultWrapper.getAsyncMqClusterId(),
                StringUtils.EMPTY, AsyncMqQueueSourceTypeEnum.DataFlow.getType(), dataFlowDO.getId(), topicResult.getName(), topicResult);

        asyncMqQueueDO.setGroupId(areaCfgInput.getGroupId());
        asyncMqQueueDO.setThreadCount(areaCfgInput.getThreadCount());
        asyncMqQueueDO.setStatus(AsyncMqQueueStatusEnum.Approved.getStatus());
        asyncMqQueueDO.setAlarmQueueId(dataFlowDO.getAlarmQueueId());
        asyncMqQueueDO.setFlinkQueueId(dataFlowDO.getCalculationQueueId());
        asyncMqQueueDO.setUnitTagId(dataFlowDO.getHubUnitTagId());

        return asyncMqQueueDO;
    }
}
