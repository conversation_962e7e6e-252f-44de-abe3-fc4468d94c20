package us.zoom.cube.site.api.intercept;

import com.google.common.collect.Lists;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.core.oidc.user.OidcUser;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.site.biz.PiiMaskService;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.core.UserHandler;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.UserInCache;
import us.zoom.infra.dao.model.TenantDO;
import us.zoom.infra.dao.model.UserDO;
import us.zoom.infra.enums.UserStatusEnum;
import us.zoom.infra.thread.ThreadLocalStore;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

import static us.zoom.cube.lib.common.CubeConstants.FRONT_END_DEBUG_COOKIE_NAME;
import static us.zoom.cube.lib.common.CubeConstants.FRONT_END_DEBUG_ON;
import static us.zoom.cube.site.api.filter.AuthFilter.AUTHENTICATION;
import static us.zoom.cube.site.api.filter.AuthFilter.AUTHORIZATION;

@Component
@Slf4j
public class AuthInterceptor implements HandlerInterceptor {

    private final List<String> indexUrl = Lists.newArrayList("/", "/index.html", "/debug.html");

    @Autowired
    UserHandler userHandler;

    @Autowired
    TenantHandler tenantHandler;

    @Autowired
    private PiiMaskService piiMaskService;

    private String getAuthenticationToken(HttpServletRequest request) {
        String token = request.getHeader(AUTHENTICATION);
        return StringUtils.isBlank(token) ? request.getHeader(AUTHORIZATION) : token;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        return handle(request, response);
    }

    private boolean handle(HttpServletRequest request, HttpServletResponse response) throws IOException {

        String tenantId = request.getHeader("tenant-id");
        if (StringUtils.isNotEmpty(tenantId)) {
            List<TenantDO> tenantIdList = tenantHandler.getAllTenantFromCache().stream().filter(x -> x.getId().equals(tenantId)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(tenantIdList)) {
                ThreadLocalStore.setTenantInfoLocal(tenantId);
                TenantDO tenantDO = tenantIdList.stream().findFirst().orElse(null);
                if (tenantDO != null) {
                    ThreadLocalStore.setTenantNameLocal(tenantDO.getName());
                }
            }
        }
        handleApiPath(request.getServletPath());
        ThreadLocalStore.setRealIp(request.getHeader("X-REAL-IP"));
        String authentication = getAuthenticationToken(request);

        Object principal = null;
        try{
            principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        } catch (Exception e){
            log.error("getPrincipal error, requestUri = {}, queryString = {}", request.getRequestURI(), request.getQueryString(), e);
        }
        String username = null;
        //fix https://zoomvideo.atlassian.net/browse/ZOOM-648462, move jwt part after okta
        if(null != principal && principal instanceof OidcUser &&   StringUtils.isNotBlank(((OidcUser) principal).getUserInfo().getPreferredUsername())){
            OidcUser oidcUser= (OidcUser) principal;
            //check user from SecurityContextHolder
            username = oidcUser.getPreferredUsername();

            if(username == null){
                response.getOutputStream().println(JsonUtils.toJsonStringIgnoreExp(ResponseObject.requireToLogin()));
                response.getOutputStream().flush();
                return false;
            }
            try{
                //add the user to mysql automaticlly
                if (userHandler.getUserFromCacheByName(username) == null) {
                    userHandler.newUserAndAssginDefaultService(oidcUser.getPreferredUsername());
                }
            }catch (Exception e){
                log.error("add the user to mysql automaticlly error",e);
            }
        } else if (StringUtils.isNotEmpty(authentication)) {
            String userId = request.getParameter("userId");
            String userName = request.getParameter("userName");
            if (StringUtils.isEmpty(userName)) {
                userName = (String) request.getAttribute("userName");
            }
            String accountId = request.getParameter("accountId");
            String accountName = request.getParameter("accountName");
            String serviceName = request.getParameter("serviceName");
            UserInCache user = null;
//            UserDO user = null;
            if(StringUtils.isNotEmpty(userId)){
//                user = userHandler.getUserById(userId);
                user = userHandler.getUserFromCache(userId);
            }
            if(user == null && StringUtils.isNotEmpty(userName)){
//                user = userHandler.getUserByName(userName);
                user = userHandler.getUserFromCacheByName(userName);
            }
            if(user != null){
                ThreadLocalStore.setUserInfoLocal(user.getId());
                ThreadLocalStore.setUserNameLocal(user.getName());
            }

            TenantDO tenant = null;
            if(StringUtils.isNotBlank(serviceName)) {
                tenant = tenantHandler.getTenantByNameInLowerCaseFromCache(serviceName);
//                tenant = tenantHandler.getTenantByName(serviceName);
            }
            if(tenant == null && StringUtils.isNotEmpty(accountId)){
                tenant =  tenantHandler.getTenantJustFromCache(accountId);
//                tenant = tenantHandler.getTenantById(accountId);
            }
            if(tenant == null && StringUtils.isNotEmpty(accountName)){
                tenant = tenantHandler.getTenantByNameInLowerCaseFromCache(accountName);
//                tenant = tenantHandler.getTenantByName(accountName);
            }

            // in some scene, the tenant can be null, for example when create tenant
            if(tenant != null){
                ThreadLocalStore.setTenantInfoLocal(tenant.getId());
                ThreadLocalStore.setTenantNameLocal(tenant.getName());
            }

            return true;
        }

        request.setCharacterEncoding("UTF-8");
        setTimeZone(getPara(request,"timeZone"));

        //, index.html, 
        if(isStaticContent(request, response)){
            return true;
        }

        //set ThreadLocal
        boolean valid = validateUserAndSetLocal(username);
        if (!valid) {
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            return false;
        }
        // SRE API forwarding
        if (request.getRequestURI().startsWith("/api/sre")) {
            request.setAttribute("username", username);
            return true;
        }
        // Pass all the front-end APIs, which are appointed prefix `/api`
        if (request.getRequestURI().startsWith("/api")) {
            return true;
        }
        // if dashboard request comes from alarm with pii tags,we need to remove the masking and redirect to the dashboard url
        if(request.getRequestURI().startsWith(piiMaskService.getAlarmPiiRedirectUrlPrefix())) {
            response.sendRedirect(piiMaskService.convertMaskingBackToAlarmQueryDashboard(request));
            return true;
        }

        // Requests other than front-end APIs, will be redirected to `/index.html`, which usually has been rendered.
        // If not, we will get blank page error.
        forwardRequest(request, response, "/index.html");
        return true;
    }

    private void handleApiPath(String servletPath) {
        ThreadLocalStore.setApiPath(servletPath);
//        if(StringUtils.startsWith(servletPath,CHANNEL_PATH)){
//            ThreadLocalStore.setApiPath(CHANNEL_PATH);
//        }else{
//            ThreadLocalStore.setApiPath(servletPath);
//        }
    }

    private void forwardRequest(HttpServletRequest request, HttpServletResponse response, String uri) {
        try {
            request.getRequestDispatcher(uri).forward(request, response);
        } catch (ServletException e) {
            log.error("exception when forward, uri: {}", uri, e);
        } catch (IOException e) {
            log.error("exception when forward, uri: {}", uri, e);
        }
    }



    private boolean isDebugOn(HttpServletRequest request) {
        Cookie[] cookies = request.getCookies();
        for (Cookie cookie : cookies) {
            String cookieName = cookie.getName();
            if (FRONT_END_DEBUG_COOKIE_NAME.equals(cookieName)) {
                String cookieValue = cookie.getValue();
                return FRONT_END_DEBUG_ON.equals(cookieValue);
            }
        }
        return false;
    }

    private void setTimeZone(String timeZone) {
        if(StringUtils.isBlank(timeZone)){
            return;
        }
        ThreadLocalStore.setTimeZoneLocal(Integer.valueOf(timeZone));
    }

    private boolean validateUserAndSetLocal(String username) throws IOException {
        if (StringUtils.isBlank(username)) {
            return false;
        }
        UserDO user = userHandler.getUserByName(username);
        if (null == user || UserStatusEnum.SUSPENDED.getStatus().equals(user.getStatus())) {
            return false;
        }
        ThreadLocalStore.setUserInfoLocal(user.getId() );
        ThreadLocalStore.setUserNameLocal(user.getName() );
        ThreadLocalStore.setSessionUserLocal(user.getId());
        return true;
    }

    public static String getUserId() {
        return StringUtils.isEmpty(ThreadLocalStore.getSessionUserLocal()) ? ThreadLocalStore.getUserInfoLocal() : ThreadLocalStore.getSessionUserLocal();
    }

    public static String getApiPath(){
        return ThreadLocalStore.getApiPath();
    }

    public static String getUserName(){
        return ThreadLocalStore.getUserNameLocal();
    }

    public static String getRealIp(){
        return ThreadLocalStore.getRealIp();
    }

    public static String getTenantId(){
        return ThreadLocalStore.getTenantInfoLocal();
    }

    private String getPara(HttpServletRequest request, String key) {
        return StringUtils.isBlank(request.getHeader(key)) ? request.getParameter(key) :request.getHeader(key);
    }


    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView)   {
    }

    private void reset(HttpServletResponse response) {
        ThreadLocalStore.setUserInfoLocal(null);
        ThreadLocalStore.setUserNameLocal(null);
        ThreadLocalStore.setTenantInfoLocal(null);
        ThreadLocalStore.setTenantNameLocal(null);
        ThreadLocalStore.setTimeZoneLocal(null);
        ThreadLocalStore.setApiPath(null);
        ThreadLocalStore.setSessionUserLocal(null);
        ThreadLocalStore.setRealIp(null);
        ThreadLocalStore.setSinkContext(null);
        response.setHeader("Access-Control-Allow-Credentials","true");
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)   {
        reset(response);
    }

    public boolean isStaticContent(HttpServletRequest request, HttpServletResponse response) {
        String requestURI = request.getRequestURI();

        if (indexUrl.contains(requestURI)) {
            return true;
        }
        if (requestURI.startsWith("/static")) {
            return true;
        }

        return false;
    }
}
