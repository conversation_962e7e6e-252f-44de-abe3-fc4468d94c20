package us.zoom.cube.site.lib.dto.catalog;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import us.zoom.cube.site.infra.annotation.BeanToMapIgnore;
import us.zoom.cube.site.infra.enums.catalog.SourceType;

/**
 * 属性查询参数
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class AttributeQueryParam extends BaseQueryParam {
    
    @NotBlank(message = "sourceType can't be null")
    @BeanToMapIgnore
    private String sourceType;

    @BeanToMapIgnore
    private String attributeKey;
    
    public SourceType getSourceTypeEnum() {
        SourceType type = SourceType.fromString(sourceType);
        if (type == null) {
            throw new IllegalArgumentException("Invalid sourceType: " + sourceType);
        }
        return type;
    }
} 