package us.zoom.cube.site.api.web.alarm;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import us.zoom.cube.site.biz.alarm.AlarmInsightService;
import us.zoom.cube.site.config.annotation.RequiredPermissionsIgnoreService;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.alarm.insight.*;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@SuppressWarnings("rawtypes")
@RestController
@RequestMapping("/api/alarmInsight")
public class AlarmInsightController {

    @Autowired
    private AlarmInsightService alarmInsightService;

    @PostMapping("/getAlarming")
    @RequiredPermissionsIgnoreService
    public ResponseObject getAlarmingOverview(@RequestBody AlarmOverviewInput overviewInput) {
        return ResponseObject.success(alarmInsightService.getAlarmingOverview(overviewInput));
    }

    @PostMapping("/getAlertDistribution")
    @RequiredPermissionsIgnoreService
    public ResponseObject getAlertDistribution(@RequestBody AlarmInsightAlertDistributionInput alarmInsightAlertDistributionInput) {
        return alarmInsightService.getAlertDistribution(alarmInsightAlertDistributionInput);
    }

    @PostMapping("/getQueryTags")
    @RequiredPermissionsIgnoreService
    public ResponseObject getQueryTags(@RequestBody AlarmInsightInput alarmInsightInput) {
        return alarmInsightService.getQueryTags(alarmInsightInput);
    }

    @PostMapping("/getTagValue")
    @RequiredPermissionsIgnoreService
    public ResponseObject getTagValue(@RequestBody AlarmInsightTagValueInput alarmInsightTagValueInput) {

        return alarmInsightService.getTagValue(alarmInsightTagValueInput);
    }

    @PostMapping("/getAlertCount")
    @RequiredPermissionsIgnoreService
    public ResponseObject getAlertCount(@RequestBody AlarmInsightAlertDistributionInput alarmInsightAlertDistributionInput) {

        return alarmInsightService.getAlertCount(alarmInsightAlertDistributionInput);
    }

    @PostMapping("/getAlarmRecords")
    @RequiredPermissionsIgnoreService
    public ResponseObject getAlarmRecords(@RequestBody AlarmRecordsInput alarmRecordsInput) {
        return alarmInsightService.getAlarmRecords(alarmRecordsInput);
    }

    @PostMapping("/getAlarmRecordDetail")
    @RequiredPermissionsIgnoreService
    public ResponseObject getAlarmRecordDetail(@RequestBody @Validated AlarmDetailInput alarmDetailInput) {

        Map<String, Object> detailResult = new HashMap<>(1);
        detailResult.put("alarmRecordDetail", alarmInsightService.getAlarmRecordDetail(alarmDetailInput));
        return ResponseObject.success(detailResult);
    }

    @PostMapping("/getAlarmRecordNotifications")
    @RequiredPermissionsIgnoreService
    public ResponseObject getAlarmRecordNotifications(@RequestBody @Validated AlarmDetailInput alarmDetailInput) {
        return ResponseObject.success(alarmInsightService.getAlarmRecordNotifications(alarmDetailInput));
    }

    @PostMapping("/getSuppressedAlarmRecords")
    @RequiredPermissionsIgnoreService
    public ResponseObject getSuppressedAlarmRecords(@RequestBody AlarmRecordsInput alarmRecordsInput) {
        return ResponseObject.success(alarmInsightService.getSuppressedAlarmRecords(alarmRecordsInput));
    }
}