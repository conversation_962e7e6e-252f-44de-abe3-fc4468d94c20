package us.zoom.cube.site.lib.query.outage;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import us.zoom.cube.site.lib.BasePara;
import us.zoom.cube.site.lib.outage.OutageLevelEnum;

/**
 * OutageParaQuery
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class OutageQuery extends BasePara {
    /**
     *
     */
    private String title;
    /**
     * @see OutageLevelEnum
     */
    private String level;
    /***
     * eg: 2024-06-01 09:00:00
     */
    private String startTime;
    /**
     * eg: 2024-09-01 09:00:00
     */
    private String endTime;
    /**
     * @see us.zoom.cube.site.lib.outage.OutageStatusEnum
     */
    private String status;
    /**
     * @see us.zoom.cube.site.lib.outage.OutageTypeEnum
     */
    private String type;
}
