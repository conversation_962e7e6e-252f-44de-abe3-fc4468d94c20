package us.zoom.cube.site.lib.output.ucs;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class OrgUserOutput {
    @JsonProperty("userId")
    private String userId;

    @JsonProperty("accountId")
    private String accountId;

    @JsonProperty("picUrl")
    private String picUrl;

    @JsonProperty("email")
    private String email;

    @JsonProperty("name")
    private String name;

    @JsonProperty("firstName")
    private String firstName;

    @JsonProperty("lastName")
    private String lastName;

    @JsonProperty("dept")
    private String dept;

    @JsonProperty("sipPhoneNumber")
    private String sipPhoneNumber;

    @JsonProperty("jobTitle")
    private String jobTitle;

    @JsonProperty("address")
    private String address;

    @JsonProperty("workPhone")
    private String workPhone;

    @JsonProperty("userType")
    private int userType;

    @JsonProperty("jid")
    private String jid;

    @JsonProperty("options")
    private String options;

    @JsonProperty("timeZoneId")
    private String timeZoneId;

    @JsonProperty("aboutMe")
    private String aboutMe;

    @JsonProperty("linkedInUrl")
    private String linkedInUrl;

    @JsonProperty("pronoun")
    private String pronoun;

}
