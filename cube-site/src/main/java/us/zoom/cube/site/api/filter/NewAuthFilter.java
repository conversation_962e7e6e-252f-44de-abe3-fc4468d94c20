package us.zoom.cube.site.api.filter;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import us.zoom.commons.jwt.service.JWTService;
import us.zoom.commons.jwt.service.TokenRegisterRequest;
import us.zoom.commons.jwt.service.TokenVerifiedRequest;
import us.zoom.commons.jwt.service.VerifyResult;
import us.zoom.commons.jwt.service.impl.DefaultJWTService;
import us.zoom.cube.lib.utils.JsonUtils;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

public class NewAuthFilter implements Filter{

    private static final Logger LOG = LoggerFactory.getLogger(NewAuthFilter.class);

    private static final String AUTHENTICATION_BEARER = "Bearer ";
    public static final String CSMS_JWT_PUBLIC_KEY_PATH_LIST = "csms.jwt.public.key.path.list";

    private final JWTService jwtService = new DefaultJWTService();

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

        // asymmetric jwt token
        List<String> csmsPublicKeyPathList = Optional.ofNullable(filterConfig.getInitParameter(CSMS_JWT_PUBLIC_KEY_PATH_LIST))
                .map(s -> JsonUtils.toObjectByTypeRef(s, new TypeReference<List<String>>() {
                }))
                // default public paths, if the list is empty, the application fails to start
                .orElse(Lists.newArrayList("prod/hub", "prod/probe"));
        LOG.info("NewAuthFilter CSMS path list: " + csmsPublicKeyPathList);

        // asymmetric jwt token
        for (String path : csmsPublicKeyPathList) {
            try {
                jwtService.register(TokenRegisterRequest.newOneOfAsymmetricVerifiedKey(List.of(path), "ES256"));
            } catch (Exception e) {
                LOG.error("token register failed, path:{}", path, e);
            }
        }
    }

    private void build401(HttpServletResponse response) throws IOException {
        response.setStatus(401);
        response.addHeader("X-Content-Type-Options", "nosniff");
        response.addHeader("Pragma", "no-cache, no-store, max-age=0, must-revalidate");
        response.addHeader("Expires", "nosniff");
        response.addHeader("Strict-Transport-Security", "max-age=31536000 ; includeSubDomains");
        response.addHeader("X-Frame-Options", "DENY");
        response.addHeader("X-XSS-Protection", "1; mode=block");
        response.getWriter().write("Forbidden");
        response.getWriter().flush();
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        if(!(servletRequest instanceof HttpServletRequest)) {
            filterChain.doFilter(servletRequest, servletResponse);
            return;
        }

        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;
        String authenticationToken = request.getHeader("Authentication");
        if(StringUtils.isEmpty(authenticationToken) || authenticationToken.length() < AUTHENTICATION_BEARER.length()) {
            build401(response);
            return;
        }

        String token = authenticationToken.substring(AUTHENTICATION_BEARER.length(), authenticationToken.length());
        if(StringUtils.isEmpty(token)) {
            build401(response);
            return;
        }

        try {
            VerifyResult verifyResult = jwtService.verifyToken(TokenVerifiedRequest.newVerifyOne(token));
        } catch (Exception e) {
            build401(response);
            return;
        }

        //build session
        filterChain.doFilter(servletRequest, servletResponse);
    }
}
