package us.zoom.cube.site.lib.dto.trace;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * Messaging queue metrics data entity class
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MessagingQueueMetrics {
    
    /**
     * Timestamp
     */
    private String time;
    
    /**
     * Cluster name
     */
    private String cluster;
    
    /**
     * Region
     */
    private String region;
    
    /**
     * Topic name
     */
    private String topicName;
    
    /**
     * Message operation type
     */
    private String msgOperation;
    
    /**
     * P50 latency
     */
    private Double p50;
    
    /**
     * P75 latency
     */
    private Double p75;
    
    /**
     * P90 latency
     */
    private Double p90;
    
    /**
     * P95 latency
     */
    private Double p95;
    
    /**
     * P99 latency
     */
    private Double p99;
    
    /**
     * Error count
     */
    private Long errorCount;
    
    /**
     * Total count
     */
    private Long totalCount;
    
    /**
     * Average cost
     */
    private Long avgCost;
    
    /**
     * Maximum cost
     */
    private Long maxCost;
} 