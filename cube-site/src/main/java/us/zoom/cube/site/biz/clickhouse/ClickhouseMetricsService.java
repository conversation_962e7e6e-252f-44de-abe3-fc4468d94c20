package us.zoom.cube.site.biz.clickhouse;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.zoom.op.monitor.domain.NameAndMetricId;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import us.zoom.cube.site.api.intercept.AuthInterceptor;
import us.zoom.cube.site.biz.AuthService;
import us.zoom.cube.site.biz.MetricsService;
import us.zoom.cube.site.biz.SysParaService;
import us.zoom.cube.site.biz.syspara.clickhouse.ClickhouseAuthParaService;
import us.zoom.cube.site.biz.syspara.clickhouse.ClickhouseParaService;
import us.zoom.cube.site.core.MetricsHandler;
import us.zoom.cube.site.core.SubEnvironmentHandler;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.core.config.ClickhouseHandlerFactory;
import us.zoom.cube.site.core.config.PiiTableCacheLoader;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.output.clickhouse.TagValueOutput;
import us.zoom.cube.site.lib.query.ClickhouseMetaQuery;
import us.zoom.cube.site.lib.query.influx.TagValueQueryV2;
import us.zoom.infra.clickhouse.*;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.service.*;
import us.zoom.infra.enums.FieldTypeEnum;
import us.zoom.infra.utils.Instance;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static us.zoom.cube.site.core.parser.process.core.common.constant.Constants.DASHBOARD;
import static us.zoom.cube.site.infra.enums.WebCodeEnum.PIIPermitError;
import static us.zoom.infra.clickhouse.ClickhouseConst.*;


/**
 * <AUTHOR> Wang
 * @date 2022-05-24 10:11
 */
@Service
@Slf4j
public class ClickhouseMetricsService {

    @Autowired
    private AuthService authService;

    @Autowired
    private MetricsDAO metricsDAO;

    @Autowired
    private MetricsFieldDAO metricsFieldDAO;

    @Autowired
    private CollectorFieldDAO collectorFieldDAO;

    @Autowired
    private CollectorMetricsDAO collectorMetricsDAO;

    @Autowired
    private TenantHandler tenantHandler;


    @Autowired
    private ClickhouseHandlerFactory clickhouseHandlerFactory;

    @Autowired
    private SysParaService sysParaService;

    @Autowired
    private MetricsHandler metricsHandler;

    @Autowired
    private ClickhouseAuthParaService clickhouseAuthParaService;

    @Autowired
    private PiiTableCacheLoader piiTableLoader;

    @Autowired
    private SubEnvironmentHandler subEnvironmentHandler;

    @Autowired
    private MetricsService metricsService;

    @Autowired
    private ClickhouseParaService clickhouseParaService;

    @Autowired
    private BatchCalculateMetricDAO batchCalculateMetricDAO;

    @Autowired
    private BatchCalculateMetricFieldDAO batchCalculateMetricFieldDAO;

    private static final String TAG_VALUE = "tag_value";

    private static final String TAG_VALUE_COUNT = "tag_value_count";

    private Cache<ClickhouseTagTime, Long> tagCountCache= CacheBuilder.newBuilder()
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .maximumSize(3000)
            .build();

    private Cache<ClickhouseTagTime, List<String>> tagValueCache= CacheBuilder.newBuilder()
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .maximumSize(3000)
            .build();

    private int cachedTagValueLength=1000;


    public ResponseObject showAllClickhouseInnerTables(ClickhouseMetaQuery query){
        authService.checkAuthIgnoreTenant(query);
        String serviceName= query.getServiceName();
        authService.mustHasSuchDb(query.getUserId(),query.getServiceName());
        Assert.notNull(serviceName,"ServiceName is null when show tables");
        List<String>ret = showAllClickhouseInnerTables(serviceName);
        return ResponseObject.success(ret);
    }

    public List<String> showAllClickhouseInnerTables(String serviceName){
        List<Map<String, Object>> queryResult = clickhouseHandlerFactory.get().query(serviceName,String.format("use %s;show tables;", ClickhouseSqlUtil.toClickhouseName(tenantHandler.matchInfluxDbNameToTenantName(serviceName))));
        List<String> ret = queryResult.stream().map(u->u.get("name")).filter(Objects::nonNull).map(Object::toString)
                .collect(Collectors.toList());
        Collections.sort(ret);
        return ret;
    }


    public ResponseObject showClickhouseTables(ClickhouseMetaQuery query){
        List<String> ret = doShowClickhouseTables(query);
        return ResponseObject.success(ret);
    }

    @NotNull
    private List<String> doShowClickhouseTables(ClickhouseMetaQuery query) {
        String serviceName= query.getServiceName();
        authService.mustHasSuchDb(query.getUserId(), query.getServiceName());
        Assert.notNull(serviceName,"ServiceName is null when show tables");
        List<Map<String, Object>> queryResult = clickhouseHandlerFactory.get().query(serviceName,String.format("use %s;show tables;", ClickhouseSqlUtil.toClickhouseName(tenantHandler.matchInfluxDbNameToTenantName(serviceName))));
        List<String>ret=queryResult.stream().map(u->u.get("name")).filter(Objects::nonNull).map(Object::toString)
                .filter(u->!u.endsWith(CLICK_LOCAL_SUFFIX)).filter(u->!u.endsWith(CLICK_BUFFER_SUFFIX)).filter(u->!u.endsWith(CLICK_DESENSITIZED_SUFFIX))
                .filter(u->!u.startsWith(ClickhouseConst.ALARM_RECORD_TABLE_PREFIX))
                .filter(u->!u.startsWith(ClickhouseConst.ALARM_MATCH_RECORD_TABLE_PREFIX))
                .filter(u->!u.startsWith(ClickhouseConst.ALARM_CUBE_MATCH_PREFIX))
                .map(ChNameDecoder::decode).collect(Collectors.toList());
        return ret;
    }

    public ResponseObject showClickhouseTablesAndMetricId(ClickhouseMetaQuery query) {
        List<String> ret = doShowClickhouseTables(query);
        TenantDO tenantByName = tenantHandler.getTenantByName(query.getServiceName());
        if (null == tenantByName || CollectionUtils.isEmpty(ret)) {
            return ResponseObject.success(null);
        }
        List<MetricsDO> metricsByNames = metricsHandler.findMetricsByNames(tenantByName.getId(), ret);
        List<NameAndMetricId> metricsLists = Instance.ofNullable(metricsByNames).stream().map(x -> new NameAndMetricId(x.getMetricsName(), x.getId(), x.getDocumentLink())).sorted(Comparator.comparing(NameAndMetricId::getName)).collect(Collectors.toList());

        Set<String> existingNames = metricsLists.stream()
                .map(NameAndMetricId::getName)
                .collect(Collectors.toSet());

        List<BatchCalculateMetricsDO> batchMetricsByNames = batchCalculateMetricDAO.findMetricsByNames(tenantByName.getId(), ret);
        List<NameAndMetricId> batchMetricsLists = Instance.ofNullable(batchMetricsByNames).stream()
                .filter(item -> !existingNames.contains(item.getMetricName()))
                .map(x -> new NameAndMetricId(x.getMetricName(), x.getId(), true)).sorted(Comparator.comparing(NameAndMetricId::getName)).collect(Collectors.toList());
        metricsLists.addAll(batchMetricsLists);
        return ResponseObject.success(metricsLists);
    }

    public ResponseObject showClickhouseTableSchema(String serviceName,String tableName){
        Assert.notNull(serviceName,"ServiceName is null when show table schema");
        TenantDO tenantByName = tenantHandler.getTenantByName(serviceName);
        Map<String,Object>ret=new HashMap<>();
        List<String>tags=new LinkedList<>();
        ret.put("tags",tags);
        Map<String,String>fields=new TreeMap<>();
        ret.put("fields",fields);
        if(tenantByName==null){
            return ResponseObject.success(ret);
        }
        MetricsDO metric = metricsDAO.getMetricsByNameOfTenant(tenantByName.getId(), tableName);

        if(metric != null){
            ret.put("metricId",metric.getId());
            String tagNames = metric.getTagNames();
            if(tagNames!=null){
                tags.addAll(Arrays.stream(tagNames.split(",")).sorted().collect(Collectors.toList()));
            }
            List<MetricsFieldDO> metricsFieldDOS = metricsFieldDAO.listFieldByMetricsIds(Collections.singletonList(metric.getId()));
            boolean hasComplexStructure=false;
            for(MetricsFieldDO f:metricsFieldDOS){
                FieldTypeEnum type=FieldTypeEnum.fromMetricsFieldTypeEnum(f.getFieldType());
                fields.put(f.getFieldName(), type.name());
                if(type==FieldTypeEnum.histogram || type ==FieldTypeEnum.summary){
                    hasComplexStructure=true;
                }
            }
            if(!hasComplexStructure){
                return ResponseObject.success(ret);
            }

            String collectorId  = collectorMetricsDAO.getCollectorIdByMetricsId(metric.getId());
            if(!StringUtils.isEmpty(collectorId)){
                List<CollectorFieldDO> filedByCollectorId = collectorFieldDAO.getFiledByCollectorId(collectorId);
                for(CollectorFieldDO f:filedByCollectorId){
                    if(StringUtils.isEmpty(f.getFieldSchema())){
                        continue;
                    }
                    if(fields.containsKey(f.getTargetField())) {
                        fields.put(f.getTargetField(), f.getFieldType() + f.getFieldSchema());
                    }
                }
            }

        }

        // add large scale metric
        BatchCalculateMetricsDO batchMetricByMetricName = batchCalculateMetricDAO.getBatchMetricByMetricName(tableName, tenantByName.getId());
        if(batchMetricByMetricName != null){
            ret.put("metricId", batchMetricByMetricName.getId());
            String tagNames = batchMetricByMetricName.getTagNames();
            if(tagNames!=null){
                tags.addAll(Arrays.stream(tagNames.split(",")).sorted().collect(Collectors.toList()));
            }
            List<BatchCalculateMetricsFieldDO> metricFieldsByMetricId = batchCalculateMetricFieldDAO.getMetricFieldsByMetricId(batchMetricByMetricName.getId());
            for(BatchCalculateMetricsFieldDO f : metricFieldsByMetricId){
                FieldTypeEnum type = FieldTypeEnum.fromMetricsFieldTypeEnum(f.getTargetFieldType());
                fields.put(f.getTargetField(), type.name());
            }
        }

        return ResponseObject.success(ret);
    }

    public ResponseObject showClickhouseTableSchemaV2(String serviceName, String tableName) {
        Assert.notNull(serviceName, "ServiceName is null when show table schema");
        TenantDO tenantByName = tenantHandler.getTenantByName(serviceName);
        Map<String, Object> ret = new HashMap<>();
        List<ClickHouseFieldsOutput> tags = new LinkedList<>();
        ret.put("tags", tags);

        List<ClickHouseFieldsOutput> fields = new ArrayList<>();
        ret.put("fields", fields);
        if (tenantByName == null) {
            return ResponseObject.success(ret);
        }
        MetricsDO metric = metricsDAO.getMetricsByNameOfTenant(tenantByName.getId(), tableName);
        BatchCalculateMetricsDO batchMetricByMetricName = batchCalculateMetricDAO.getBatchMetricByMetricName(tableName, tenantByName.getId());
        if (metric == null && batchMetricByMetricName == null) {
            return ResponseObject.success(ret);
        }
        if(metric != null){
            ret.put("metricId", metric.getId());
            String tagNames = metric.getTagNames();

            String collectorId = collectorMetricsDAO.getCollectorIdByMetricsId(metric.getId());
            List<CollectorFieldDO> collectorFieldDOS = collectorFieldDAO.getFiledByCollectorId(collectorId);
            Map<String, String> filedDescriptionMap = collectorFieldDOS.stream()
                    .filter(field -> StringUtils.isNotBlank(field.getDescription()))
                    .collect(Collectors.toMap(
                            CollectorFieldDO::getSourceField,
                            dto -> Objects.requireNonNull(dto.getDescription())));
            Map<String, Integer> piiMap = collectorFieldDOS.stream()
                    .collect(Collectors.toMap(
                            CollectorFieldDO::getSourceField,
                            dto -> dto.getHasPii()));
            if (tagNames != null) {
                List<String> sourceTags = Arrays.stream(tagNames.split(",")).sorted().toList();
                sourceTags.forEach(tag -> {
                    ClickHouseFieldsOutput clickHouseFieldsOutput = new ClickHouseFieldsOutput();
                    clickHouseFieldsOutput.setIsPii(piiMap.get(tag));
                    clickHouseFieldsOutput.setName(tag);
                    if (filedDescriptionMap.containsKey(tag)) {
                        clickHouseFieldsOutput.setDescription(filedDescriptionMap.get(tag));
                    }
                    tags.add(clickHouseFieldsOutput);
                });
            }
            List<MetricsFieldDO> metricsFieldDOS = metricsFieldDAO.listFieldByMetricsIds(Collections.singletonList(metric.getId()));
            boolean hasComplexStructure = false;
            for (MetricsFieldDO f : metricsFieldDOS) {
                FieldTypeEnum type = FieldTypeEnum.fromMetricsFieldTypeEnum(f.getFieldType());
                ClickHouseFieldsOutput field = new ClickHouseFieldsOutput();
                field.setType(type.name());
                field.setName(f.getFieldName());
                field.setIsPii(piiMap.get(f.getFieldName()));
                if (filedDescriptionMap.containsKey(f.getFieldName())) {
                    field.setDescription(filedDescriptionMap.get(f.getFieldName()));
                } else {
                    field.setDescription(f.getDescription());
                }
                fields.add(field);
                if (type == FieldTypeEnum.histogram || type == FieldTypeEnum.summary) {
                    hasComplexStructure = true;
                }
            }
            if (!hasComplexStructure) {
                return ResponseObject.success(ret);
            }

            if (!StringUtils.isEmpty(collectorId)) {
                for (CollectorFieldDO f : collectorFieldDOS) {
                    if (StringUtils.isEmpty(f.getFieldSchema())) {
                        continue;
                    }
                    if (fields.stream().anyMatch(dto -> dto.getName().equals(f.getTargetField()))) {
                        fields.removeIf(dto -> dto.getName().equals(f.getTargetField()));
                        ClickHouseFieldsOutput clickHouseFieldsOutput = new ClickHouseFieldsOutput();
                        clickHouseFieldsOutput.setDescription(f.getDescription());
                        clickHouseFieldsOutput.setType(f.getFieldType() + f.getFieldSchema());
                        clickHouseFieldsOutput.setName(f.getTargetField());
                        fields.add(clickHouseFieldsOutput);
                    }
                }
            }
        }else{
            ret.put("metricId", batchMetricByMetricName.getId());
            String tagNames = batchMetricByMetricName.getTagNames();

            if (tagNames != null) {
                List<String> sourceTags = Arrays.stream(tagNames.split(",")).sorted().toList();
                sourceTags.forEach(tag -> {
                    ClickHouseFieldsOutput clickHouseFieldsOutput = new ClickHouseFieldsOutput();
                    clickHouseFieldsOutput.setIsPii(0);
                    clickHouseFieldsOutput.setName(tag);
                    tags.add(clickHouseFieldsOutput);
                });
            }
            List<BatchCalculateMetricsFieldDO> batchCalculateMetricsFieldDOS = batchCalculateMetricFieldDAO.getMetricFieldsByMetricId(batchMetricByMetricName.getId());
            boolean hasComplexStructure = false;
            for (BatchCalculateMetricsFieldDO f : batchCalculateMetricsFieldDOS) {
                FieldTypeEnum type = FieldTypeEnum.fromMetricsFieldTypeEnum(f.getTargetFieldType());
                ClickHouseFieldsOutput field = new ClickHouseFieldsOutput();
                field.setType(type.name());
                field.setName(f.getTargetField());
                field.setIsPii(0);
                fields.add(field);
                if (type == FieldTypeEnum.histogram || type == FieldTypeEnum.summary) {
                    hasComplexStructure = true;
                }
            }
            if (!hasComplexStructure) {
                return ResponseObject.success(ret);
            }
        }

        return ResponseObject.success(ret);
    }

    public ResponseObject showTagValueCardinality(String serviceName,String tableName,String fieldName,long second){
        serviceName=tenantHandler.matchInfluxDbNameToTenantName(serviceName);
        return ResponseObject.success(getTagValueCardinalityWithCache(
                new ClickhouseTagTime(ClickhouseSqlUtil.toClickhouseName(serviceName), ClickhouseSqlUtil.toClickhouseName(tableName),fieldName,second)));
    }

    public long getTagValueCardinalityWithCache(ClickhouseTagTime tag){
        try {
            String serviceName=ChNameDecoder.decode(tag.getDbName());
            String routedEnv = subEnvironmentHandler.getDataQueryEnv(serviceName);
            if (StringUtils.isEmpty(routedEnv)) {
                routedEnv = sysParaService.getClickhouseEnvRoute(serviceName);
            }
            String finalRoutedEnv = routedEnv;
            if (!piiTableLoader.auth(new ChTable(tag.getDbName(), tag.getTableName()), AuthInterceptor.getUserId(), AuthInterceptor.getRealIp())) {
                if(clickhouseParaService.enablePiiDesensitizedView()) {
                    tag.setTableName(ClickhouseSqlUtil.encodeThenToDesensitized(tag.getTableName()));
                }
            }
            return tagCountCache.get(tag,()->{
                long span=sysParaService.getClickhouseTagTimeSpan();
                String sqlFormat="select uniq(\"%s\") as c from %s.%s where time > subtractSeconds(now(), %d)";
                String sql=String.format(sqlFormat,tag.getTagName()
                        , tag.getDbName(),tag.getTableName(),span);
                try{
                    metricsService.legalSql(sql);
                    List<Map<String, Object>> queryResult = clickhouseHandlerFactory.get().query(serviceName,sql, finalRoutedEnv);
                    return Long.valueOf(queryResult.get(0).get("c").toString());
                }catch (Exception e){
                    log.error("ShowTagValueCardinality with clickhouse encounter error={} with sql={}",e,sql);
                }
                return 0L;
            });
        } catch (ExecutionException e) {
            log.error("ShowTagValueCardinality with clickhouse encounter error",e);
        }
        return 0L;
    }

    public ResponseObject searchTagValuesWithCache(String serviceName,String tableName,String fieldName,String keyWord,int limit,long second, String source){
        List<String>ret=new LinkedList<>();
        serviceName=tenantHandler.matchInfluxDbNameToTenantName(serviceName);
        //for some measurement name from influxdb
        TenantDO tenant = tenantHandler.getTenantByName(serviceName);


        String finalTableName = tableName;
        List<MetricsDO> metricNames = metricsHandler.getMetricsByTenant(tenant.getId()).stream().filter(u -> StringUtils.equalsIgnoreCase(u.getMetricsName(), finalTableName)).collect(Collectors.toList());
        if(metricNames.size()==1){
            tableName=metricNames.get(0).getMetricsName();
        }
        List<String> piiList = getPiiList(metricNames);

        ChTable chTable = new ChTable(serviceName, tableName);
        if(!piiTableLoader.auth(chTable,AuthInterceptor.getUserId(),AuthInterceptor.getRealIp())){
            if(clickhouseParaService.enablePiiDesensitizedView()){
                tableName=ClickhouseSqlUtil.encodeThenToDesensitized(tableName);
                if (piiList.contains(fieldName) && StringUtils.equals(DASHBOARD, source)) {
                    log.warn("User {} try to access {} to get {} from {}, but he/she didn't have pii permission", AuthInterceptor.getUserName(), chTable, fieldName, AuthInterceptor.getRealIp());
                    return ResponseObject.failWithCode(null, "This is a PII table. Your role is not permit to access it.", PIIPermitError.getCode());
                }
            }else {
                log.warn("User {} try to access {} from {}", AuthInterceptor.getUserName(), chTable, AuthInterceptor.getRealIp());
                return ResponseObject.failWithCode(null, "This is a PII table. Your role is not permit to access it.", PIIPermitError.getCode());
            }
        }
        try {
            String routedEnv = subEnvironmentHandler.getDataQueryEnv(serviceName);
            if (StringUtils.isEmpty(routedEnv)) {
                routedEnv = sysParaService.getClickhouseEnvRoute(serviceName);
            }
            long span = sysParaService.getClickhouseTagTimeSpan();
            if(second>0 && second<span){
                span=second;
            }
            ClickhouseTagTime tag= new ClickhouseTagTime(ClickhouseSqlUtil.toClickhouseName(serviceName), ClickhouseSqlUtil.toClickhouseName(tableName),fieldName,span);
            long cardinality = getTagValueCardinalityWithCache(tag);
            String finalRoutedEnv = routedEnv;
            if(cardinality>0 && cardinality<cachedTagValueLength){
                //use cache

                long finalSpan = span;
                ret = tagValueCache.get(tag, () -> {
                    String sqlFormat = "select distinct(\"%s\") as c from %s.%s where time > subtractSeconds(now(), %d)";
                    String sql = String.format(sqlFormat, tag.getTagName()
                            , tag.getDbName(), tag.getTableName(), finalSpan, tag.getTagName());
                    metricsService.legalSql(sql);
                    try {
                        List<Map<String, Object>> queryResult = clickhouseHandlerFactory.get().query(ChNameDecoder.decode(tag.getDbName()),sql, finalRoutedEnv);
                        return queryResult.stream().map(u -> u.get("c")).filter(Objects::nonNull).map(Object::toString).collect(Collectors.toList());
                    } catch (Exception e) {
                        log.error("getTagValue with clickhouse encounter error={} with sql={}", e, sql);
                    }
                    return new LinkedList<>();
                });
                ret=ret.stream().filter(Objects::nonNull).filter(u->u.contains(keyWord)).collect(Collectors.toList());
                ret=ret.subList(0, Math.min(ret.size(), limit));
            }else{
                String sql="";
                if(StringUtils.isEmpty(keyWord)){
                    String sqlFormat="select distinct(\"%s\") as c from %s.%s where time > subtractSeconds(now(), %d) limit %d";
                    sql=String.format(sqlFormat,tag.getTagName(),tag.getDbName(),tag.getTableName(),span,limit);
                }else{
                    String sqlFormat="select distinct(\"%s\") as c from %s.%s where time > subtractSeconds(now(), %d) and \"%s\" like '%%%s%%' limit %d";
                    sql=String.format(sqlFormat,tag.getTagName(),tag.getDbName(),tag.getTableName(),span,tag.getTagName(),keyWord,limit);
                }
                metricsService.legalSql(sql);
                try {
                    List<Map<String, Object>> queryResult = clickhouseHandlerFactory.get().query(ChNameDecoder.decode(tag.getDbName()),sql, finalRoutedEnv);
                    ret = queryResult.stream().map(u -> u.get("c")).filter(Objects::nonNull).map(Object::toString).collect(Collectors.toList());
                } catch (Exception e) {
                    log.error("getTagValue with clickhouse encounter error={} with sql={}", e, sql);
                }
            }

        } catch (ExecutionException e) {
            log.error("getTagValue with clickhouse encounter error",e);
        }

        return ResponseObject.success(ret);
    }

    @NotNull
    private List<String> getPiiList(List<MetricsDO> metricNames) {
        if (metricNames.isEmpty()) {
            return new ArrayList<>();
        }
        String collectorId = collectorMetricsDAO.getCollectorIdByMetricsId(metricNames.get(0).getId());
        if (StringUtils.isEmpty(collectorId)) {
            return new ArrayList<>();
        }
        List<CollectorFieldDO> collectorFieldDOS = collectorFieldDAO.getFiledByCollectorId(collectorId);
        if (collectorFieldDOS.isEmpty()) {
            return new ArrayList<>();
        }
        List<String> piiList = collectorFieldDOS.stream().filter(dto -> dto.getHasPii() == 1)
                .map(CollectorFieldDO::getSourceField)
                .collect(Collectors.toList());
        return piiList;
    }

    private void checkAndAdjustInput(TagValueQueryV2 tagValueQuery, boolean downGrade) {
        Assert.isTrue(StringUtils.isNotBlank(tagValueQuery.getDatabase()), "service name can not be null");
        Assert.isTrue(StringUtils.isNotBlank(tagValueQuery.getMeasurement()), "metrics name can not be null");
        Assert.isTrue(StringUtils.isNotBlank(tagValueQuery.getTag()), "metrics field can not be null");

        //check and adjust time
        long secondConf;
        Long second = tagValueQuery.getSecond();
        if(!downGrade){
            secondConf = sysParaService.getClickhouseTagTimeSpanV2();
            if (second == null || second <= 0L || second > secondConf) {
                second = secondConf;
            }
        }else{
            secondConf = sysParaService.getClickhouseTagTimeSpanV3();
            second = secondConf;
        }
        tagValueQuery.setSecond(second);

        //check and adjust limit
        int limitConf = sysParaService.getClickhouseTagLimit();
        Integer limit = tagValueQuery.getLimit();
        if (limit == null || limit <= 0 || limit > limitConf) {
            limit = limitConf;
        }
        tagValueQuery.setLimit(limit);
    }

    public ResponseObject searchTagValuesV2(TagValueQueryV2 tagValueQuery) {

        authService.checkAuth(tagValueQuery);
        List<String> ret = new LinkedList<>();
        TagValueOutput tagValueOutput = new TagValueOutput();
        //check and adjust input
        checkAndAdjustInput(tagValueQuery, false);
        //params
        String serviceName = tenantHandler.matchInfluxDbNameToTenantName(tagValueQuery.getDatabase());
        String tableName = tagValueQuery.getMeasurement();
        long second = tagValueQuery.getSecond();
        int limit = tagValueQuery.getLimit();
        String keyWord = tagValueQuery.getKeyword();

        //for some measurement name from influxdb
        TenantDO tenant = tenantHandler.getTenantByName(serviceName);
        String finalTableName = tableName;
        List<MetricsDO> metricNames = metricsHandler.getMetricsByTenant(tenant.getId()).stream().filter(u -> StringUtils.equalsIgnoreCase(u.getMetricsName(), finalTableName)).collect(Collectors.toList());
        if (metricNames.size() == 1) {
            tableName = metricNames.get(0).getMetricsName();
        }
        ChTable chTable = new ChTable(serviceName, tableName);
        if (!piiTableLoader.auth(chTable, AuthInterceptor.getUserId(), AuthInterceptor.getRealIp())) {
            if(clickhouseParaService.enablePiiDesensitizedView()){
                tableName=ClickhouseSqlUtil.encodeThenToDesensitized(tableName);
            }else {
                log.warn("User {} try to access {} from {}", AuthInterceptor.getUserName(), chTable, AuthInterceptor.getRealIp());
                return ResponseObject.fail("This is a PII table. Your role is not permit to access it.");
            }
        }
        String routedEnv = subEnvironmentHandler.getDataQueryEnv(serviceName);
        if (StringUtils.isEmpty(routedEnv)) {
            routedEnv = sysParaService.getClickhouseEnvRoute(serviceName);
        }

        ClickhouseTagTime tag = new ClickhouseTagTime(ClickhouseSqlUtil.toClickhouseName(serviceName), ClickhouseSqlUtil.toClickhouseName(tableName), tagValueQuery.getTag(), second);
        String finalRoutedEnv = routedEnv;

        try {
            long cardinality = getTagValueCardinalityWithCache(tag);
            if (Boolean.TRUE.equals(tagValueQuery.getUseCache()) && cardinality > 0 && cardinality < cachedTagValueLength) {
                //query from cache
                long finalSecond = second;
                ret = tagValueCache.get(tag, () -> {
                    String sqlFormat = "select distinct(\"%s\") as " + TAG_VALUE + " from %s.%s where time > subtractSeconds(now(), %d)";
                    String sql = String.format(sqlFormat, tag.getTagName()
                            , tag.getDbName(), tag.getTableName(), finalSecond, tag.getTagName());
                    metricsService.legalSql(sql);
                    try {
                        List<Map<String, Object>> queryResult = clickhouseHandlerFactory.get().query(ChNameDecoder.decode(tag.getDbName()), sql, finalRoutedEnv);
                        return queryResult.stream().map(u -> u.get(TAG_VALUE)).filter(Objects::nonNull).map(Object::toString).collect(Collectors.toList());
                    } catch (Exception e) {
                        log.error("getTagValue with clickhouse encounter error={} with sql={}", e, sql);
                    }
                    return new LinkedList<>();
                });
                ret = ret.stream().filter(Objects::nonNull).filter(u -> u.contains(keyWord)).collect(Collectors.toList());
                tagValueOutput.setCount((long) ret.size());
                ret = ret.subList(0, Math.min(ret.size(), limit));
            } else {
                //query from db
                ret = getTagValues(tagValueOutput, finalRoutedEnv, tag, keyWord, limit, second);
                if(!tagValueOutput.isFullResult()){
                    checkAndAdjustInput(tagValueQuery, true);
                    second = tagValueQuery.getSecond();
                    ret = getTagValues(tagValueOutput, finalRoutedEnv, tag, keyWord, limit, second);
                }
            }
        } catch (Exception e) {
            log.error("getTagValue with clickhouse encounter error",e);
            //downgrade
            checkAndAdjustInput(tagValueQuery, true);
            second = tagValueQuery.getSecond();
            tagValueOutput.setFullResult(false);
            ret = getTagValues(tagValueOutput, finalRoutedEnv, tag, keyWord, limit, second);
        }
        tagValueOutput.setValues(ret);
        return ResponseObject.success(tagValueOutput);
    }

    public List<String> getTagValues(TagValueOutput tagValueOutput, String finalRoutedEnv, ClickhouseTagTime tag, String keyWord, int limit, long second) {
        if(!tagValueOutput.isFullResult()){
            log.info("start get tag with downgrade process");
        }
        List<String> ret = new LinkedList<>();
        String sql, countSql;
        if (StringUtils.isBlank(keyWord)) {
            String sqlFormat = "select distinct(\"%s\") as " + TAG_VALUE + " from %s.%s where time > subtractSeconds(now(), %d) order by \"%s\" asc limit %d";
            String countSqlFormat = "select count(distinct \"%s\") as " + TAG_VALUE_COUNT + " from %s.%s where time > subtractSeconds(now(), %d)";
            sql = String.format(sqlFormat, tag.getTagName(), tag.getDbName(), tag.getTableName(), second, tag.getTagName(), limit);
            countSql = String.format(countSqlFormat, tag.getTagName(), tag.getDbName(), tag.getTableName(), second);
        } else {
            String sqlFormat = "select distinct(\"%s\") as " + TAG_VALUE + " from %s.%s where time > subtractSeconds(now(), %d) and \"%s\" ilike '%%%s%%' order by \"%s\" asc limit %d";
            String countSqlFormat = "select count(distinct \"%s\") as " + TAG_VALUE_COUNT + " from %s.%s where time > subtractSeconds(now(), %d) and \"%s\" ilike '%%%s%%'";
            sql = String.format(sqlFormat, tag.getTagName(), tag.getDbName(), tag.getTableName(), second, tag.getTagName(), keyWord, tag.getTagName(), limit);
            countSql = String.format(countSqlFormat, tag.getTagName(), tag.getDbName(), tag.getTableName(), second, tag.getTagName(), keyWord);
        }
        metricsService.legalSql(sql);
        metricsService.legalSql(countSql);
        try {
            List<Map<String, Object>> queryResult = clickhouseHandlerFactory.get().query(ChNameDecoder.decode(tag.getDbName()), countSql, finalRoutedEnv);
            Long count = Optional.of(queryResult.get(0)).map(m -> m.get(TAG_VALUE_COUNT)).map(o -> Long.valueOf(o.toString())).orElse(null);
            tagValueOutput.setCount(count);
        } catch (Exception e) {
            log.error("getTagValueCount with clickhouse encounter error={} with sql={}", e, countSql);
            tagValueOutput.setFullResult(false);
        }
        try {
            List<Map<String, Object>> queryResult = clickhouseHandlerFactory.get().query(ChNameDecoder.decode(tag.getDbName()), sql, finalRoutedEnv);
            ret = queryResult.stream().map(u -> u.get(TAG_VALUE)).filter(Objects::nonNull).map(Object::toString).collect(Collectors.toList());
        } catch (Exception e) {
            tagValueOutput.setFullResult(false);
            log.error("getTagValue with clickhouse encounter error={} with sql={}", e, sql);
        }
        return ret;
    }

    public Map<String,Object>parseSql(String sql){
        return ClickhouseSqlUtil.parseSql(sql,sysParaService.onlyRawSql());
    }

}
