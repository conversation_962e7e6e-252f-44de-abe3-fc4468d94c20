package us.zoom.cube.site.biz.outage;

import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHeaders;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.cloud.secrets.spring.annotation.SecretValue;
import us.zoom.cube.lib.common.CubeConstants;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.sdk.util.MonitorLogReporter;
import us.zoom.cube.site.biz.AuthService;
import us.zoom.cube.site.core.RcaHandler;
import us.zoom.cube.site.lib.IdPara;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.dto.rca.jira.Fields;
import us.zoom.cube.site.lib.dto.rca.jira.JiraTicketResponse;
import us.zoom.cube.site.lib.input.rca.RcaAddInput;
import us.zoom.cube.site.lib.input.rca.RcaCommentInput;
import us.zoom.cube.site.lib.input.rca.RcaDeleteInput;
import us.zoom.cube.site.lib.input.rca.RcaEditInput;
import us.zoom.cube.site.lib.input.rca.RcaStatusInput;
import us.zoom.cube.site.lib.monitor.RcaTicketMetric;
import us.zoom.cube.site.lib.output.PageResult;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.cube.site.lib.query.outage.RcaCommentQuery;
import us.zoom.cube.site.lib.query.outage.RcaQuery;
import us.zoom.infra.dao.model.RcaCommentDO;
import us.zoom.infra.dao.model.RcaDO;
import us.zoom.infra.utils.HttpUtils;

import java.io.IOException;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

@Service
public class RcaService {
    @Autowired
    private AuthService authService;
    @Autowired
    private RcaHandler rcaHandler;


    private static final Logger monitorLogger = LoggerFactory.getLogger("Monitor");


    private static final String JIRA_REST_ENDPOINT = "https://zoomvideo.atlassian.net/rest/api/3/search";


    private static final String JQL_JSON_TEMPLATE = "{\n" +
            "    \"startAt\": %d,\n" +
            "    \"maxResults\": %d,\n" +
            "    \"jql\": \"project = RCA ORDER BY created DESC\",\n" +
            "    \"fields\": [\n" +
            "        \"summary\",\n" +
            "        \"status\",\n" +
            "        \"description\",\n" +
            "        \"assignee\",\n" +
            "        \"reporter\",\n" +
            "        \"duedate\",\n" +
            "        \"customfield_17593\",\n" +
            "        \"customfield_16286\",\n" +
            "        \"customfield_16283\",\n" +
            "        \"customfield_16284\",\n" +
            "        \"customfield_16274\",\n" +
            "        \"customfield_16282\",\n" +
            "        \"customfield_16275\",\n" +
            "        \"customfield_17365\",\n" +
            "        \"customfield_13244\",\n" +
            "        \"customfield_17424\",\n" +
            "        \"customfield_17542\",\n" +
            "        \"customfield_15504\",\n" +
            "        \"customfield_15503\",\n" +
            "        \"customfield_15917\",\n" +
            "        \"customfield_17622\",\n" +
            "        \"customfield_17486\",\n" +
            "        \"duedate\",\n" +
            "        \"customfield_12750\",\n" +
            "        \"customfield_17711\",\n" +
            "        \"labels\",\n" +
            "        \"priority\",\n" +
            "        \"customfield_17371\",\n" +
            "        \"created\"\n" +
            "    ]\n" +
            "}";

    private static final String JQL_JSON_TEMPLATE_BY_DAY = "{\n" +
            "    \"startAt\": %d,\n" +
            "    \"maxResults\": %d,\n" +
            "    \"jql\": \"project = RCA AND created >= startOfDay(-1) AND created <= endOfDay(-1) ORDER BY created DESC\",\n" +
            "    \"fields\": [\n" +
            "        \"summary\",\n" +
            "        \"status\",\n" +
            "        \"description\",\n" +
            "        \"assignee\",\n" +
            "        \"reporter\",\n" +
            "        \"duedate\",\n" +
            "        \"customfield_17593\",\n" +
            "        \"customfield_16286\",\n" +
            "        \"customfield_16283\",\n" +
            "        \"customfield_16284\",\n" +
            "        \"customfield_16274\",\n" +
            "        \"customfield_16282\",\n" +
            "        \"customfield_16275\",\n" +
            "        \"customfield_17365\",\n" +
            "        \"customfield_13244\",\n" +
            "        \"customfield_17424\",\n" +
            "        \"customfield_17542\"\n" +
            "        \"customfield_15504\",\n" +
            "        \"customfield_15503\",\n" +
            "        \"customfield_15917\",\n" +
            "        \"customfield_17622\",\n" +
            "        \"customfield_17486\",\n" +
            "        \"duedate\",\n" +
            "        \"customfield_12750\",\n" +
            "        \"customfield_17711\",\n" +
            "        \"labels\",\n" +
            "        \"priority\",\n" +
            "        \"customfield_17371\",\n" +
            "        \"created\"\n" +
            "    ]\n" +
            "}";


    @SecretValue("jira.api.token")
    private String jiraApiToken;



    public ResponseObject searchRcaList(PageQuery<RcaQuery> pageQuery) {
//        authService.checkAuth(pageQuery);
        List<RcaDO> outageList = rcaHandler.searchRcaList(pageQuery);
        Integer totalCount = rcaHandler.getRcaCount(pageQuery);
        return ResponseObject.success(new PageResult(totalCount, outageList));
    }

    public ResponseObject add(RcaAddInput rcaAddInput) {
//        authService.checkAuth(rcaAddInput);
        return ResponseObject.success(rcaHandler.add(rcaAddInput));
    }

    public ResponseObject edit(RcaEditInput rcaEditInput) {
//        authService.checkAuth(rcaEditInput);
        rcaHandler.edit(rcaEditInput);
        return ResponseObject.success(true);
    }

    public ResponseObject getById(IdPara idPara) {
//        authService.checkAuth(idPara);
        RcaDO rcaDO=  rcaHandler.getById(idPara.getId());
        return ResponseObject.success(rcaDO);
    }

    public ResponseObject delete(IdPara idPara) {
//        authService.checkAuth(idPara);
        rcaHandler.delete(idPara.getId());
        return ResponseObject.success(true);
    }

    public ResponseObject updateStatus(RcaStatusInput rcaStatusInput) {
//        authService.checkAuth(rcaStatusInput);
        rcaHandler.updateStatus(rcaStatusInput);
        return ResponseObject.success(true);
    }

    public ResponseObject comment(RcaCommentInput rcaCommentInput) {
//        authService.checkAuth(rcaCommentInput);
        rcaHandler.comment(rcaCommentInput);
        return ResponseObject.success(true);
    }


    public ResponseObject searchRcaCommentList(RcaCommentQuery query) {
//        authService.checkAuth(query);
        List<RcaCommentDO> rcaCommentDOS = rcaHandler.searchRcaCommentList(query);
        try {
            rcaCommentDOS = buildCommentTree(rcaCommentDOS);
        }catch (Exception e){
            monitorLogger.error("buildCommentTree error Message=",e);
        }
        return ResponseObject.success(rcaCommentDOS);
    }

    public static List<RcaCommentDO> buildCommentTree(List<RcaCommentDO> comments) {
        if (comments == null || comments.isEmpty()) {
            return new ArrayList<>();
        }

        List<RcaCommentDO> commentTree = new ArrayList<>();
        for (RcaCommentDO comment : comments) {
            if (comment.getParentCommentId() == null || comment.getParentCommentId().isEmpty() || comment.getParentCommentId().equals("0")) {
                commentTree.add(comment); // Attach top-level comments directly to tree
            } else {
                addToParent(comment, comments); // Recursively add comments to their parent
            }
        }

        return commentTree; // Return the list of top-level comments
    }

    private static void addToParent(RcaCommentDO comment, List<RcaCommentDO> comments) {
        for (RcaCommentDO potentialParent : comments) {
            if(CollectionUtils.isEmpty(potentialParent.getCommentList())){
                potentialParent.setCommentList(Lists.newArrayList());
            }
            if (potentialParent.getCommentId().equals(comment.getParentCommentId())) {
                potentialParent.getCommentList().add(comment);
                return;
            }
        }
    }

    /**
     * deleteComment
     * @param rcaDeleteInput
     * @return
     */
    public ResponseObject deleteComment(RcaDeleteInput rcaDeleteInput) {
//        authService.checkAuth(rcaDeleteInput);
        rcaHandler.deleteComment(rcaDeleteInput.getRcaId(),rcaDeleteInput.getCommentId());
        return ResponseObject.success(true);
    }


    /**
     *
     * @param rcaAddInput
     * @return
     */
    public ResponseObject checkRca(RcaAddInput rcaAddInput) {
        //        authService.checkAuth(rcaAddInput);
        Assert.notNull(rcaAddInput, "rca input is null");
        Assert.isTrue(!StringUtils.isBlank(rcaAddInput.getTitle()), "rca title is blank");
        RcaDO rcaDO = rcaHandler.searchRcaByTitle(rcaAddInput.getTitle());
        if(rcaDO != null){
            return ResponseObject.success(true);
        }
        return ResponseObject.success(false);
    }



    /**
     *  searchJiraTicket
     * @return
     */
    public ResponseObject syncAllJiraData() {
        List<RcaTicketMetric> metrics = pullJiraData(JQL_JSON_TEMPLATE);

        metrics.forEach(metric -> {
            // monitor log
            MonitorLogReporter.report(monitorLogger, metric);
        });

        return ResponseObject.success(metrics);
    }

    @NotNull
    private List<RcaTicketMetric> pullJiraData(String jqlSql) {
        AtomicInteger status = new AtomicInteger();
        Map<String, String> headers = new HashMap<>();
        headers.put(HttpHeaders.AUTHORIZATION, CubeConstants.BASIC + jiraApiToken);
        List<RcaTicketMetric> allTickets = new ArrayList<>();
        int startAt = 0;
        int maxResults = 100;

        while (true) {
            String post = null;
            try {
                String jql = String.format(jqlSql, startAt, maxResults);
                post = HttpUtils.post(List.of(JIRA_REST_ENDPOINT), new HashMap<>(), headers, jql, status, null, 1);
            } catch (IOException e) {
                monitorLogger.error("send jira request error,", e);
            }

            if (post == null) {
                break;
            }

            JiraTicketResponse object = JsonUtils.toObject(post, JiraTicketResponse.class);
            if (object == null || object.getIssues().isEmpty()) {
                break;
            }

            List<RcaTicketMetric> rcaTicketMetrics = object.getIssues().stream().filter(issue -> Objects.nonNull(issue.getFields())).map(issue -> {
                Fields fields = issue.getFields();

                RcaTicketMetric metric =  RcaTicketMetric.builder().build();
                metric.setRcaKey(issue.getKey());
                if(!CollectionUtils.isEmpty(fields.getEnvironment())){
                    metric.setEnvironment(fields.getEnvironment().get(0).getValue());
                }
                if(!CollectionUtils.isEmpty(fields.getProductType())){
                    metric.setProductType(fields.getProductType().get(0).getValue());
                }
                if(!CollectionUtils.isEmpty(fields.getServiceOfProductImpact())){
                    metric.setServiceOfProductImpact(fields.getServiceOfProductImpact().get(0).getValue());
                }
                if(Objects.nonNull(fields.getFailureType()) && StringUtils.isNotBlank(fields.getFailureType().getValue())){
                    metric.setFailureType(fields.getFailureType().getValue());
                }
                if(Objects.nonNull(fields.getRootCause()) && StringUtils.isNotBlank(fields.getRootCause().getValue())){
                    metric.setRootCause(fields.getRootCause().getValue());
                }
                if(Objects.nonNull(fields.getRootCauseClass()) && StringUtils.isNotBlank(fields.getRootCauseClass().getValue())){
                    metric.setRootCauseClass(fields.getRootCauseClass().getValue());
                }
                if(Objects.nonNull(fields.getOwnerTeam()) && StringUtils.isNotBlank(fields.getOwnerTeam().getValue())){
                    metric.setOwnerTeam(fields.getOwnerTeam().getValue());
                }
                if(Objects.nonNull(fields.getSevLevel())&& StringUtils.isNotBlank(fields.getSevLevel().getValue())){
                    metric.setSevLevel(fields.getSevLevel().getValue());
                }
                if(Objects.nonNull(fields.getRcaOwner())&& StringUtils.isNotBlank(fields.getRcaOwner().getValue())){
                    metric.setRcaOwner(fields.getRcaOwner().getValue());
                }
                if(Objects.nonNull(fields.getZoomModule()) && StringUtils.isNotBlank(fields.getZoomModule().getValue())){
                    metric.setZoomModule(fields.getZoomModule().getValue());
                }
                if(Objects.nonNull(fields.getServiceEngineerTicket()) && StringUtils.isNotBlank(fields.getServiceEngineerTicket().getValue())){
                    metric.setServiceEngineerTicket(fields.getServiceEngineerTicket().getValue());
                }
                if(Objects.nonNull(fields.getStatus()) && StringUtils.isNotBlank(fields.getStatus().getName())){
                    metric.setStatus(fields.getStatus().getName());
                }
                metric.setSummary(fields.getSummary());
                if (Objects.nonNull(fields.getAssignee())&& StringUtils.isNotBlank(fields.getAssignee().getDisplayName())) {
                    metric.setAssignee(fields.getAssignee().getDisplayName());
                }
                if(Objects.nonNull(fields.getAssigneeManager())){
                    metric.setAssigneeManager(fields.getAssigneeManager());
                }
                if(Objects.nonNull(fields.getReporter())&& StringUtils.isNotBlank(fields.getReporter().getDisplayName())){
                    metric.setReporter(fields.getReporter().getDisplayName());
                }
                if(Objects.nonNull(fields.getRcaRegionReviewed()) && StringUtils.isNotBlank(fields.getRcaRegionReviewed().getValue())){
                    metric.setRcaRegionReviewed(fields.getRcaRegionReviewed().getValue());
                }
                if(Objects.nonNull(fields.getRcaReviewed()) && StringUtils.isNotBlank(fields.getRcaReviewed().getValue())){
                    metric.setRcaReviewed(fields.getRcaReviewed().getValue());
                }
                if(Objects.nonNull(fields.getNocInternalReviewer())&& StringUtils.isNotBlank(fields.getNocInternalReviewer().getValue())){
                    metric.setNocInternalReviewer(fields.getNocInternalReviewer().getValue());
                }
                if(Objects.nonNull(fields.getImpact())&& StringUtils.isNotBlank(fields.getImpact().getValue())){
                    metric.setImpact(fields.getImpact().getValue());
                }
                if(Objects.nonNull(fields.getDueDate())){
                    metric.setDueDate(fields.getDueDate());
                }
                if(Objects.nonNull(fields.getStartDate())){
                    metric.setStartDate(fields.getStartDate());
                }
                if(Objects.nonNull(fields.getLabels())){
                    metric.setLabels(String.join(",", fields.getLabels()));
                }
                if(Objects.nonNull(fields.getRcaWikiLink())){
                    metric.setRcaWikiLink(fields.getRcaWikiLink());
                }
                if(Objects.nonNull(fields.getCreated())){

                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSZ");
                    try {
                        OffsetDateTime dateTime = OffsetDateTime.parse(fields.getCreated(), formatter);

                        OffsetDateTime utcDateTime = dateTime.withOffsetSameInstant(ZoneOffset.UTC);
                        String utcTimeString = utcDateTime.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME);
                        metric.setCreated(utcTimeString);
                    } catch (Exception e) {
                        monitorLogger.info("parse date error ", e);
                    }
                }
                return metric;
            }).toList();

            allTickets.addAll(rcaTicketMetrics);
            startAt += maxResults; // update limit
        }
        return allTickets;
    }


    @Scheduled(cron = "0 0 0 * * ?")
    public void syncJiraDataByDay() {
        List<RcaTicketMetric> dayTickets = pullJiraData(JQL_JSON_TEMPLATE_BY_DAY);
        if (CollectionUtils.isEmpty(dayTickets)) {
            return;
        }
        dayTickets.forEach(rcaTicketMetric -> {
            // monitor log
            MonitorLogReporter.report(monitorLogger, rcaTicketMetric);
        });
        monitorLogger.info("syncJiraDataByDay finish size:{}", dayTickets.size());
    }



}
