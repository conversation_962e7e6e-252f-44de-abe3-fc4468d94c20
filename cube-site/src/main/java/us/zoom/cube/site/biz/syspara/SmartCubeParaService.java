package us.zoom.cube.site.biz.syspara;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import us.zoom.infra.dao.model.SysParaDO;
import us.zoom.infra.syspara.SysParaEventHandlerIntf;
import us.zoom.infra.syspara.SysParaEventService;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;


@Slf4j
@Component
public class SmartCubeParaService implements SysParaEventHandlerIntf {

    private static final String SMART_CUBE = "smart_cube";

    private final AtomicReference<Map<String, String>> paramMap = new AtomicReference<>(new HashMap<>());

    public SmartCubeParaService() {
        SysParaEventService.registeSysparaListener(this, List.of(SMART_CUBE));
    }
    
    public String getParamValue(String key) {
        return paramMap.get().get(key);
    }


    @Override
    public boolean onEventWhenHasData(List<SysParaDO> sysParas) {
        Map<String, String> temp = Optional.of(sysParas).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(SysParaDO::getParaKey, SysParaDO::getValue));
        paramMap.set(temp);
        return true;
    }

    @Override
    public boolean onEventWhenNoData() {
        return false;
    }
}