package us.zoom.cube.site.lib.output.config.metrics;

import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import us.zoom.cube.lib.integrations.MetricsFieldTypeEnum;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2020/7/20 11:20 AM
 *
 * A field or tag of the corresponding InfluxDB table, used to define the metrics
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor
public class FieldOrTag {

    public static final String tagMetaType = "tag";
    public static final String fieldMetaType = "field";

    /**
     * e.g, "ip", "host", "regionId", "request.count", "costTime.avg"
     */
    String fieldOrTagName;

    /**
     * value type of the tag, or field;
     * there are two value types: "number", "string"
     */
    String valueType;

    /**
     * there are two meta-types: "tag", "field"
     */
    String metaType;

    /**
     * if this is an extra field or tag
     */
    Boolean isExtra;

    Boolean isPi;

    public FieldOrTag(FieldOrTag other) {
        this.fieldOrTagName = other.fieldOrTagName;
        this.valueType = other.valueType;
        this.metaType = other.metaType;
        this.isExtra = other.isExtra;
    }

    public static FieldOrTag newField() {
        FieldOrTag field = new FieldOrTag();
        field.setMetaType(fieldMetaType);
        return field;
    }

    public static FieldOrTag newTag() {
        FieldOrTag tag = new FieldOrTag();
        tag.setMetaType(tagMetaType);
        return tag;
    }

    public Object getSampleValue() {
        if (MetricsFieldTypeEnum.mapString.name().equals(valueType)) {
            Map<String, Object> sampleMap = new HashMap<>();
            sampleMap.put("sampleKey", "sampleValue");
            return sampleMap;
        } else if (MetricsFieldTypeEnum.mapNumber.name().equals(valueType)) {
            Map<String, Object> sampleMap = new HashMap<>();
            sampleMap.put("sampleKey", 1.0);
            return sampleMap;
        } else {
            return MetricsFieldTypeEnum.number.name().equals(valueType) ? 1d : "1";
        }
    }

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        FieldOrTag that = (FieldOrTag) o;
        return Objects.equals(fieldOrTagName, that.fieldOrTagName);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(fieldOrTagName);
    }
}
