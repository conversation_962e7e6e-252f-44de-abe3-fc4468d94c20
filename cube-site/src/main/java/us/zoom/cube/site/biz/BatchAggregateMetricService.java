package us.zoom.cube.site.biz;

import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.site.api.intercept.AuthInterceptor;
import us.zoom.cube.site.biz.clickhouse.ChPublisher;
import us.zoom.cube.site.biz.clickhouse.ChTableEvent;
import us.zoom.cube.site.biz.client.AtlasService;
import us.zoom.cube.site.biz.syspara.RollUpParaService;
import us.zoom.cube.site.core.config.ClickhouseHandlerFactory;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.BatchAggregateMetricFieldInput;
import us.zoom.cube.site.lib.input.BatchAggregateMetricInput;
import us.zoom.cube.site.lib.output.PageResult;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.infra.clickhouse.ChNameEncoder;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.service.*;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static us.zoom.cube.site.biz.client.AtlasService.*;

/**
 * <AUTHOR>
 * @date 2025/3/26 10:35
 */
@Service
@Slf4j
public class BatchAggregateMetricService {

    @Autowired
    private BatchCalculateMetricDAO batchCalculateMetricDAO;

    @Autowired
    private AtlasService atlasService;

    @Autowired
    private ClickhouseTenantRelationDao clickhouseTenantRelationDao;

    @Autowired
    private ClickhouseClusterDao clickhouseClusterDao;

    @Autowired
    private EnvironmentRunTimeDAO environmentRunTimeDAO;

    @Autowired
    private BatchCalculateMetricFieldDAO batchCalculateMetricFieldDAO;

    @Autowired
    private RollUpParaService rollUpParaService;

    @Autowired
    private PlatformTransactionManager transactionManager;

    @Autowired
    private TenantDAO tenantDAO;

    @Autowired
    private ChPublisher chPublisher;

    @Autowired
    private MetricsDAO metricsDAO;

    @Autowired
    private ClickhouseHandlerFactory clickhouseHandlerFactory;


    private final int wizardMode = 1;
    private final int selfSupport = 2;

    public ResponseObject listBatchAggregateMetric(PageQuery<BatchAggregateMetricInput> pageQuery) {
        BatchAggregateMetricInput batchAggregateMetricInput = Optional.ofNullable(pageQuery.getQueryPara()).orElseGet(BatchAggregateMetricInput::new);
        int count = batchCalculateMetricDAO.countBatchCalculateMetric( pageQuery.getTenantId(), batchAggregateMetricInput.getMode(), batchAggregateMetricInput.getMetricName(), batchAggregateMetricInput.getStatus(), batchAggregateMetricInput.getCreator());
        if (count == 0) {
            return ResponseObject.success(pageQuery.getOperId(), new PageResult(0, Collections.emptyList()));
        }

        List<BatchCalculateMetricsDO> metricsList = batchCalculateMetricDAO.listBatchCalculateMetric(pageQuery.getTenantId(),
                batchAggregateMetricInput.getMode(),
                batchAggregateMetricInput.getMetricName(),  batchAggregateMetricInput.getStatus(), batchAggregateMetricInput.getCreator(),
                pageQuery.getPageSize() * (pageQuery.getPageIndex() - 1), pageQuery.getPageSize());

        Map<String, List<BatchCalculateMetricsFieldDO>> metricFieldMap = batchCalculateMetricFieldDAO
                .listBatchCalculateMetricFieldByMetricIds(metricsList.stream()
                        .map(BatchCalculateMetricsDO::getId).collect(Collectors.toList()))
                .stream().collect(Collectors.groupingBy(BatchCalculateMetricsFieldDO::getBatchMetricId));

        metricsList.forEach(metric -> metric.setBatchCalculateMetricsFieldDOList(metricFieldMap.get(metric.getId())));

        return ResponseObject.success(pageQuery.getOperId(), new PageResult(count, metricsList));
    }

    public ResponseObject getBatchAggregateMetricById(BatchAggregateMetricInput batchAggregateMetricInput) {
        BatchCalculateMetricsDO batchMetricById = batchCalculateMetricDAO.getBatchMetricById(batchAggregateMetricInput.getId());
        List<BatchCalculateMetricsFieldDO> batchCalculateMetricsFieldDOS = batchCalculateMetricFieldDAO
                .listBatchCalculateMetricFieldByMetricIds(Arrays.asList(batchMetricById.getId()));
        batchMetricById.setBatchCalculateMetricsFieldDOList(batchCalculateMetricsFieldDOS);
        return ResponseObject.success(batchMetricById);
    }

    public ResponseObject batchAggregateCreate(BatchAggregateMetricInput batchAggregateMetricInput) {
        preCheck(batchAggregateMetricInput);
        if(batchAggregateMetricInput.getStatus() == null) {
            batchAggregateMetricInput.setStatus(1); // default enable status
        }
        BatchCalculateMetricsDO batchMetricByMetricName = batchCalculateMetricDAO.getBatchMetricByMetricName(batchAggregateMetricInput.getMetricName(), batchAggregateMetricInput.getTenantId());
        if(batchMetricByMetricName != null) {
            throw new IllegalArgumentException("metric already exists in rollup list!");
        }
        if(batchAggregateMetricInput.getMode().equals(selfSupport)){
            MetricsDO metricsByNameOfTenant = metricsDAO.getMetricsByNameOfTenant(batchAggregateMetricInput.getTenantId(), batchAggregateMetricInput.getMetricName());
            if(metricsByNameOfTenant != null) {
                throw new IllegalArgumentException("metric already exists in metric list");
            }
        }
        String metricId = batchAggregateMetricInput.getRelatedMetricId();
        String clickhouseName = resolveClickhouseClusterName(batchAggregateMetricInput.getTenantId());
        AtlasJobParam atlasJobParam = buildAtlasJobParam(batchAggregateMetricInput, clickhouseName, batchAggregateMetricInput.getOptions());
        AtlasService.JobResult batchAggregateJob = atlasService.createBatchAggregateJob(atlasJobParam);
        String jobLink = (String)extractJobResult(batchAggregateJob).get("jobLink");
        Number jobId = (Number)extractJobResult(batchAggregateJob).get("jobId");

        TransactionStatus status = transactionManager.getTransaction(new DefaultTransactionDefinition());
        try {
            BatchCalculateMetricsDO batchCalculateMetricsDO = buildBatchMetric(batchAggregateMetricInput, jobLink, metricId, jobId.longValue());
            batchCalculateMetricDAO.add(batchCalculateMetricsDO);
            List<BatchCalculateMetricsFieldDO> batchCalculateMetricsFieldDOS = buildBatchMetricField(batchAggregateMetricInput, batchCalculateMetricsDO.getId());
            batchCalculateMetricFieldDAO.addBatchCalculateMetricField(batchCalculateMetricsFieldDOS);
            transactionManager.commit(status);
            // send event
            ChTableEvent<String> chTableEvent = new ChTableEvent<>(ChTableEvent.METRIC_AGG_CREATE_UPDATE_TYPE, batchCalculateMetricsDO.getId());
            chPublisher.publish(chTableEvent);
            return ResponseObject.success(batchCalculateMetricsDO);
        } catch (Exception e) {
            transactionManager.rollback(status);
            throw e;
        }
    }

    public ResponseObject modifyBatchAggregateMetricStatus(String id, int status) {
        batchCalculateMetricDAO.modifyStatus(id, status, AuthInterceptor.getUserName());
        BatchCalculateMetricsDO metrics = getExistingMetric(id);
        AtlasService.JobResult batchAggregateJob;
        if(status == 1) {
            List<BatchCalculateMetricsFieldDO> existingMetricFields = getExistingMetricField(metrics.getId());
            metrics.setBatchCalculateMetricsFieldDOList(existingMetricFields);
            if(metrics.getMode() == wizardMode){
                String clickhouseName = resolveClickhouseClusterName(AuthInterceptor.getTenantId());
                AtlasJobParam atlasJobParam = buildAtlasJobParam(metrics, clickhouseName);
                batchAggregateJob = atlasService.createBatchAggregateJob(atlasJobParam);
                extractJobResult(batchAggregateJob);
            }
        }else{
            batchAggregateJob = atlasService.offlineBatchAggregateJob(metrics.getTaskId());
            checkAtlasResult(batchAggregateJob);
        }
        return ResponseObject.success(null);
    }

    public ResponseObject getEarlyMetricData(String relatedMetricId) {
        MetricsDO metricsById = metricsDAO.getMetricsById(relatedMetricId);
        final String TIME_FIELD = "time";
        final String SQL_TEMPLATE = "SELECT min(time) AS time FROM %s.%s";
        final String SQL_GET_DATA = "SELECT time FROM %s.%s limit 1";

        String tenantId = AuthInterceptor.getTenantId();
        TenantDO tenant = tenantDAO.getTenantById(tenantId);

        String sql = String.format(SQL_GET_DATA, ChNameEncoder.encode(tenant.getName()), ChNameEncoder.encode(metricsById.getMetricsName()));
        List<Map<String, Object>> result = clickhouseHandlerFactory.get().query(tenant.getName(), sql);
        if(result != null && result.size() > 0) {
            sql = String.format(SQL_TEMPLATE, ChNameEncoder.encode(tenant.getName()), ChNameEncoder.encode(metricsById.getMetricsName()));
            result = clickhouseHandlerFactory.get().query(tenant.getName(), sql);
            Object earliestTime = CollectionUtils.isNotEmpty(result)
                    ? result.get(0).get(TIME_FIELD)
                    : null;
            return ResponseObject.success(earliestTime);
        }

        return ResponseObject.success(null);
    }


    public ResponseObject checkMetricName(String name, String tenantId) {
        Map<String, Object> ret = new HashMap<>();
        BatchCalculateMetricsDO batchMetricByMetricName = batchCalculateMetricDAO.getBatchMetricByMetricName(name, tenantId);
        if(batchMetricByMetricName != null) {
           ret.put("batchAggregateMetricExist", true);
           ret.put("batchAggregateMetricId", batchMetricByMetricName.getId());
        }else{
            ret.put("batchAggregateMetricExist", false);
        }

        MetricsDO metricsByNameOfTenant = metricsDAO.getMetricsByNameOfTenant(tenantId, name);
        if(metricsByNameOfTenant != null) {
            ret.put("metricExist", true);
            ret.put("metricId", metricsByNameOfTenant.getId());
        }else{
            ret.put("metricExist", false);
        }

        return ResponseObject.success(ret);
    }


    @Transactional
    public ResponseObject delBatchAggregateMetric(String id) {
        BatchCalculateMetricsDO metrics = getExistingMetric(id);
        batchCalculateMetricDAO.delete(id);
        batchCalculateMetricFieldDAO.delBatchCalculateMetricField(id);
        if(metrics != null && metrics.getMode() == wizardMode){
            JobResult jobResult = atlasService.offlineBatchAggregateJob(metrics.getTaskId());
            checkAtlasResult(jobResult);
            jobResult = atlasService.deleteBatchAggregateJob(metrics.getTaskId());
            checkAtlasResult(jobResult);
        }
        return ResponseObject.success(null);
    }

    private BatchCalculateMetricsDO getExistingMetric(String id) {
        return Optional.ofNullable(batchCalculateMetricDAO.getBatchMetricById(id))
                .orElseThrow(() -> new IllegalArgumentException("Batch metric does not exist"));
    }

    private List<BatchCalculateMetricsFieldDO> getExistingMetricField(String metricId) {
        return Optional.ofNullable(batchCalculateMetricFieldDAO.getMetricFieldsByMetricId(metricId))
                .orElseThrow(() -> new IllegalArgumentException("Batch metric field does not exist"));
    }

    @Transactional
    public ResponseObject editBatchMetric(BatchAggregateMetricInput input) {
        BatchCalculateMetricsDO metrics = getExistingMetric(input.getId());
        List<BatchCalculateMetricsFieldDO> existingMetricFields = getExistingMetricField(metrics.getId());
        List<String> exitIds = new ArrayList<>();
        Map<Boolean, List<BatchAggregateMetricFieldInput>> partitionedFields = input.getMetricsFieldList()
                .stream()
                .collect(Collectors.partitioningBy(field -> field.getId() != null));

        List<BatchAggregateMetricFieldInput> existFields = partitionedFields.get(true);
        List<BatchAggregateMetricFieldInput> newFields = partitionedFields.get(false);

        Number jobId = 0;
        if(input.getStatus() == 1 && metrics.getMode() == wizardMode){
            input.setMetricName(metrics.getMetricName());
            input.setSourceMetricName(metrics.getSourceMetricName());
            String clickhouseName = resolveClickhouseClusterName(input.getTenantId());
            AtlasJobParam atlasJobParam = buildAtlasJobParam(input, clickhouseName, input.getOptions());
            AtlasService.JobResult batchAggregateJob = atlasService.createBatchAggregateJob(atlasJobParam);
            jobId = (Number)extractJobResult(batchAggregateJob).get("jobId");
        }else{
            AtlasService.JobResult batchAggregateJob = atlasService.offlineBatchAggregateJob(metrics.getTaskId());
            checkAtlasResult(batchAggregateJob);
        }

        //update database
        if(CollectionUtils.isNotEmpty(existFields)){
            exitIds = existFields.stream().map(BatchAggregateMetricFieldInput::getId).collect(Collectors.toList());
            List<BatchCalculateMetricsFieldDO> batchCalculateMetricsFieldDOS = buildBatchMetricFieldByExistFields(existFields, input);
            batchCalculateMetricFieldDAO.updateBatchFields(batchCalculateMetricsFieldDOS);
        }
        // add new field
        if(CollectionUtils.isNotEmpty(newFields)){
            List<BatchCalculateMetricsFieldDO> batchCalculateMetricsFieldDOS = buildBatchMetricFieldByExistFields(newFields, input);
            batchCalculateMetricFieldDAO.addBatchCalculateMetricField(batchCalculateMetricsFieldDOS);
        }

        //delete field
        List<String> dbIds = existingMetricFields.stream().
                map(batchCalculateMetricsFieldDO -> batchCalculateMetricsFieldDO.getId()).collect(Collectors.toList());
        dbIds.removeAll(exitIds);
        if(CollectionUtils.isNotEmpty(dbIds)){
            batchCalculateMetricFieldDAO.deleteBatchCalculateMetricField(new ArrayList<>(dbIds));
        }

        // update tags & options
        metrics.setTagNames(String.join(",", input.getTagNames()));
        metrics.setModifier(input.getUserName());
        metrics.setOption(input.getOptions()!=null?JsonUtils.toJsonString(input.getOptions()):null);
        metrics.setSchedulerInterval(input.getSchedulerInterval());
        metrics.setOverwrite(input.getOverwrite());
        metrics.setStatus(input.getStatus());
        if(metrics.getTaskId() == null || metrics.getTaskId().equals(0l)){
            metrics.setTaskId(jobId.longValue());
        }
        batchCalculateMetricDAO.updateParams(metrics);

        // send event
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                ChTableEvent<String> chTableEvent = new ChTableEvent<>(ChTableEvent.METRIC_AGG_CREATE_UPDATE_TYPE, metrics.getId());
                chPublisher.publish(chTableEvent);
            }
        });

        return ResponseObject.success(true);
    }


    private List<BatchCalculateMetricsFieldDO> buildBatchMetricFieldByExistFields(List<BatchAggregateMetricFieldInput> fieldInputs, BatchAggregateMetricInput batchAggregateMetricInput) {
        return  fieldInputs.stream()
                .map(aggregateMetricFieldInput -> {
                    BatchCalculateMetricsFieldDO batchAggregateMetricFieldDo = new BatchCalculateMetricsFieldDO();
                    batchAggregateMetricFieldDo.setBatchMetricId(batchAggregateMetricInput.getId());
                    batchAggregateMetricFieldDo.setTargetField(aggregateMetricFieldInput.getTargetField());
                    batchAggregateMetricFieldDo.setTargetFieldType(aggregateMetricFieldInput.getTargetFieldType());
                    if(aggregateMetricFieldInput.getId() != null){
                        batchAggregateMetricFieldDo.setId(aggregateMetricFieldInput.getId());
                    }else{
                        batchAggregateMetricFieldDo.setId(IdUtils.generateId());
                    }

                    batchAggregateMetricFieldDo.setSourceField(aggregateMetricFieldInput.getSourceField());
                    batchAggregateMetricFieldDo.setFunctionName(aggregateMetricFieldInput.getFunctionName());

                    batchAggregateMetricFieldDo.setCreator(batchAggregateMetricInput.getUserName());
                    batchAggregateMetricFieldDo.setModifier(batchAggregateMetricInput.getUserName());
                    return batchAggregateMetricFieldDo;
                })
                .collect(Collectors.toList());
    }

    private List<BatchCalculateMetricsFieldDO> buildBatchMetricField(BatchAggregateMetricInput batchAggregateMetricInput, String metricId) {
        return  batchAggregateMetricInput.getMetricsFieldList().stream()
                .map(aggregateMetricFieldInput -> {
                    BatchCalculateMetricsFieldDO batchAggregateMetricFieldDo = new BatchCalculateMetricsFieldDO();
                    batchAggregateMetricFieldDo.setBatchMetricId(metricId);
                    batchAggregateMetricFieldDo.setTargetField(aggregateMetricFieldInput.getTargetField());
                    batchAggregateMetricFieldDo.setTargetFieldType(aggregateMetricFieldInput.getTargetFieldType());
                    batchAggregateMetricFieldDo.setId(IdUtils.generateId());
                    if (batchAggregateMetricInput.getMode() == 1) {
                        batchAggregateMetricFieldDo.setSourceField(aggregateMetricFieldInput.getSourceField());
                        batchAggregateMetricFieldDo.setFunctionName(aggregateMetricFieldInput.getFunctionName());
                    }
                    batchAggregateMetricFieldDo.setCreator(batchAggregateMetricInput.getUserName());
                    batchAggregateMetricFieldDo.setModifier(batchAggregateMetricInput.getUserName());
                    return batchAggregateMetricFieldDo;
                })
                .collect(Collectors.toList());
    }


    private void preCheck(BatchAggregateMetricInput batchAggregateMetricInput){
        if(batchAggregateMetricInput.getMode() == wizardMode){
            if(batchAggregateMetricInput.getSourceMetricName() == null || batchAggregateMetricInput.getSourceMetricName().equals("")) {
                throw new RuntimeException("source metric name is empty in wizard mode");
            }
        }
    }


    private BatchCalculateMetricsDO buildBatchMetric(BatchAggregateMetricInput input, String jobLink, String metricId, Long jobId) {
        return BatchCalculateMetricsDO.builder()
                .id(IdUtils.generateId())
                .tenantId(input.getTenantId())
                .metricName(input.getMetricName())
                .mode(input.getMode())
                .sourceMetricName(input.getSourceMetricName())
                .taskLink(jobLink)
                .taskId(jobId)
                .status(input.getStatus())
                .overwrite(input.getOverwrite())
                .relatedMetricId(metricId)
                .option(input.getOptions()!=null?JsonUtils.toJsonString(input.getOptions()):null)
                .modifier(input.getUserName())
                .schedulerInterval(input.getSchedulerInterval())
                .creator(input.getUserName())
                .tagNames(String.join(",", input.getTagNames()))
                .build();
    }

    private AtlasJobParam buildAtlasJobParam(BatchAggregateMetricInput input, String clickhouseName, Map<String, Object> optionMaps) {

        TenantDO tenant = tenantDAO.getTenantById(input.getTenantId());
        String db = ChNameEncoder.encode(tenant.getName());
        String dstTableName = ChNameEncoder.encode(input.getMetricName());

        AtlasJobParam atlasJobParam = new AtlasJobParam();
        String atlasChName = rollUpParaService.getParamValue(clickhouseName);
        atlasJobParam.setClusterName(atlasChName);
        atlasJobParam.setDstTable(db + "." + dstTableName);
        atlasJobParam.setServiceName(tenant.getName());


        if (input.getMode() == wizardMode) {

            atlasJobParam.setSrcTable(db + "." + ChNameEncoder.encode(input.getSourceMetricName()));
            atlasJobParam.setTags(input.getTagNames());
            atlasJobParam.setAggregateFields(input.getMetricsFieldList().stream()
                    .map(field -> new AggregateField(field.getSourceField(), field.getFunctionName().equals("uniq") ? "count_distinct" : field.getFunctionName(), field.getTargetField()))
                    .collect(Collectors.toList()));
            atlasJobParam.setReceiver(input.getUserName());
            atlasJobParam.setScheduleInterval(resolveScheduleInterval(input.getSchedulerInterval()));
            if(input.getOverwrite()){
                atlasJobParam.setSaveModel(overWritePattern);
                atlasJobParam.setPartitionValue(partitionValuePattern);
            }else{
                atlasJobParam.setSaveModel(appendPattern);
            }

            if(input.getStatus() == 0){ // disable, set scheduleInterval once
                atlasJobParam.setScheduleInterval(AtlasService.once_scheduleInterval);
            }

        } else {
            atlasJobParam.setSrcTable(db + ".srcTable"); // this is template mode, need goto atlas platform to modify it.
            atlasJobParam.setScheduleInterval(AtlasService.once_scheduleInterval);
        }

        if (optionMaps != null && !optionMaps.isEmpty()) {
            atlasJobParam.setOptions(optionMaps);
        }

        return atlasJobParam;
    }

    private AtlasJobParam buildAtlasJobParam(BatchCalculateMetricsDO batchCalculateMetricsDO, String clickhouseName) {
        TenantDO tenant = tenantDAO.getTenantById(batchCalculateMetricsDO.getTenantId());
        String db = ChNameEncoder.encode(tenant.getName());
        String dstTableName = ChNameEncoder.encode(batchCalculateMetricsDO.getMetricName());

        AtlasJobParam atlasJobParam = new AtlasJobParam();
        String atlasChName = rollUpParaService.getParamValue(clickhouseName);
        atlasJobParam.setClusterName(atlasChName);
        atlasJobParam.setDstTable(db + "." + dstTableName);
        atlasJobParam.setServiceName(tenant.getName());


        if (batchCalculateMetricsDO.getMode() == wizardMode) {
            atlasJobParam.setSrcTable(db + "." + ChNameEncoder.encode(batchCalculateMetricsDO.getSourceMetricName()));
            atlasJobParam.setTags(Arrays.asList(batchCalculateMetricsDO.getTagNames().split(",")));
            atlasJobParam.setAggregateFields(batchCalculateMetricsDO.getBatchCalculateMetricsFieldDOList().stream()
                    .map(field -> new AggregateField(field.getSourceField(), field.getFunctionName().equals("uniq") ? "count_distinct" : field.getFunctionName(), field.getTargetField()))
                    .collect(Collectors.toList()));
            atlasJobParam.setReceiver(batchCalculateMetricsDO.getCreator());
            atlasJobParam.setScheduleInterval(resolveScheduleInterval(batchCalculateMetricsDO.getSchedulerInterval()));
            if(batchCalculateMetricsDO.isOverwrite()){
                atlasJobParam.setSaveModel(overWritePattern);
                atlasJobParam.setPartitionValue(partitionValuePattern);
            }else{
                atlasJobParam.setSaveModel(appendPattern);
            }

            if(batchCalculateMetricsDO.getStatus() == 0){ // disable, set scheduleInterval once
                atlasJobParam.setScheduleInterval(AtlasService.once_scheduleInterval);
            }

        } else {
            atlasJobParam.setSrcTable(db + ".srcTable"); // this is template mode, need goto atlas platform to modify it.
            atlasJobParam.setScheduleInterval(AtlasService.once_scheduleInterval);
        }

        if (batchCalculateMetricsDO.getOption() != null) {
            Map option = JsonUtils.toObject(batchCalculateMetricsDO.getOption(), Map.class);
            atlasJobParam.setOptions(option);
        }

        return atlasJobParam;
    }


    private String resolveScheduleInterval(String interval) {
        return "daily".equals(interval) ? AtlasService.daily_scheduleInterval :
                "hourly".equals(interval) ? AtlasService.hourly_scheduleInterval :
                        AtlasService.once_scheduleInterval;
    }

    public void checkAtlasResult(AtlasService.JobResult batchAggregateJob){
        if (batchAggregateJob == null || !batchAggregateJob.isSuccess()) {
            throw new RuntimeException("Create job failed: job result is null or success is false");
        }
    }

    private Map<String, Object> extractJobResult(AtlasService.JobResult batchAggregateJob) {
        if (batchAggregateJob == null || !batchAggregateJob.isSuccess()) {
            throw new RuntimeException("Create job failed: job result is null or success is false");
        }

        return Optional.ofNullable(batchAggregateJob.getData())
                .map(data -> {
                    Object jobLink = data.get("jobLink");
                    Object jobId = data.get("jobId");

                    if (jobLink == null || jobId == null) {
                        throw new RuntimeException("Create job failed: jobLink or jobId is missing");
                    }

                    Map<String, Object> jobParaMap = new HashMap<>();
                    jobParaMap.put("jobLink", jobLink);
                    jobParaMap.put("jobId", jobId);
                    return jobParaMap;
                })
                .orElseThrow(() -> new RuntimeException("Create job failed: data is missing"));
    }

    private String resolveClickhouseClusterName(String tenantId) {
        List<ClickhouseTenantRelationDO> tenantRelations = clickhouseTenantRelationDao.listRelationForTenant(tenantId);
        List<ClickhouseClusterDO> clusterList = clickhouseClusterDao.search();
        String workingEnv = Optional.ofNullable(environmentRunTimeDAO.searchAll())
                .filter(CollectionUtils::isNotEmpty)
                .map(list -> list.get(0).getWorkingEnvironment())
                .orElse(null);

        if (workingEnv == null) {
            return "";
        }

        Map<String, ClickhouseClusterDO> envClusterMap = clusterList.stream()
                .filter(cluster -> cluster.getEnvironment().equals(workingEnv))
                .collect(Collectors.toMap(ClickhouseClusterDO::getId, Function.identity()));

        return tenantRelations.stream()
                .filter(rel -> envClusterMap.containsKey(rel.getClickhouseClusterId()))
                .map(rel -> envClusterMap.get(rel.getClickhouseClusterId()).getName())
                .findFirst()
                .orElseGet(() -> envClusterMap.values().stream()
                        .filter(ClickhouseClusterDO::getIsEnvDefault)
                        .map(ClickhouseClusterDO::getName)
                        .findFirst()
                        .orElse(""));
    }
}
