package us.zoom.cube.site.api.web;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import us.zoom.cube.site.biz.UserService;
import us.zoom.cube.site.lib.BasePara;
import us.zoom.cube.site.lib.IdListPara;
import us.zoom.cube.site.lib.IdPara;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.SetTenantInput;
import us.zoom.cube.site.lib.input.UserInput;
import us.zoom.cube.site.lib.input.outage.UcsUserInput;
import us.zoom.cube.site.lib.query.NameQuery;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.cube.site.lib.query.SearchUserQuery;

@RestController
@RequestMapping("/api/user")
@Slf4j
public class UserController {

    @Autowired
    UserService userService;


    @RequestMapping(value = "/setTenant", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject setTenant(@RequestBody SetTenantInput setTenantInput){
        return  userService.setTenant(setTenantInput);
    }

    @RequestMapping(value = "/logout", method = RequestMethod.GET)
    @ResponseBody
    public ResponseObject logout(HttpServletResponse response, HttpServletRequest request){
        return  userService.logout(response,request);
    }

    @RequestMapping(value = "/getUserInfo", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject getUserInfo(@RequestBody BasePara basePara, HttpServletRequest request){
        return userService.getUserInfo(basePara.getUserId());
    }

    @RequestMapping(value = "/listAllUserLabels", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject listAllUserLabels( @RequestBody BasePara basePara){
        return  userService.listAllUserLabels(basePara);
    }

    @RequestMapping(value="/searchUser",method= RequestMethod.POST)
    @ResponseBody
    public ResponseObject searchUser(@RequestBody PageQuery<SearchUserQuery> pageQuery) throws Exception {
        return userService.searchUser(pageQuery);
    }

    @RequestMapping(value="/searchAlarmMentionUser",method= RequestMethod.POST)
    @ResponseBody
    public ResponseObject searchAlarmMentionUser(@RequestBody PageQuery<NameQuery> pageQuery) {
        return userService.searchAlarmMentionUser(pageQuery);
    }

    @RequestMapping(value = "/addUser", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject addUser(@RequestBody  UserInput serverInput){
        return userService.addUser(serverInput);
    }


    @RequestMapping(value = "/editUser", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject editUser(@RequestBody  UserInput serverInput){
        return userService.editUser(serverInput);
    }

    @RequestMapping(value = "/getUserById", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject getUserById(@RequestBody IdPara idPara){
        return userService.getUserById(idPara);
    }

    @RequestMapping(value = "/delUser", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject delUser(@RequestBody IdListPara idPara){
        return userService.delUser(idPara);
    }

    @PostMapping(value = "/ucs/getUser")
    public ResponseObject<Object> getUser(@RequestBody UcsUserInput ucsUserInput) {
        return userService.getUser(ucsUserInput);
    }


}
