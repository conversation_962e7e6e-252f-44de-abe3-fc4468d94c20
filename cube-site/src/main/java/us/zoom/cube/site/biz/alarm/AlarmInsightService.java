package us.zoom.cube.site.biz.alarm;

import com.zoom.op.monitor.dao.alarm.AlarmDefinitionDao;
import com.zoom.op.monitor.domain.IdAndName;
import com.zoom.op.monitor.domain.alarm.AlarmDefinition;
import com.zoom.op.monitor.domain.alarm.v2.AlarmDefinitionDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.influxdb.dto.QueryResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.api.intercept.AuthInterceptor;
import us.zoom.cube.site.biz.AuthService;
import us.zoom.cube.site.biz.TimeFiller;
import us.zoom.cube.site.biz.clickhouse.ClickhouseMetricsService;
import us.zoom.cube.site.core.MetricsHandler;
import us.zoom.cube.site.core.SubEnvironmentHandler;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.core.alarm.insight.AlarmInsightRecordsHandler;
import us.zoom.cube.site.core.config.ClickhouseHandlerFactory;
import us.zoom.cube.site.core.config.PiiTableCacheLoader;
import us.zoom.cube.site.infra.enums.WebCodeEnum;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.alarm.insight.*;
import us.zoom.cube.site.lib.output.alarm.insight.*;
import us.zoom.cube.site.lib.output.config.metrics.MetricsTags;
import us.zoom.infra.clickhouse.ChTable;
import us.zoom.infra.clickhouse.ClickhouseConst;
import us.zoom.infra.clickhouse.ClickhouseSqlUtil;
import us.zoom.infra.clickhouse.InfluxResultAdapter;
import us.zoom.infra.dao.model.CollectorFieldDO;
import us.zoom.infra.dao.model.MetricsDO;
import us.zoom.infra.dao.model.SubAlarmDO;
import us.zoom.infra.dao.model.TenantDO;
import us.zoom.infra.dao.service.AlarmDefinitionV2DAO;
import us.zoom.infra.dao.service.CollectorFieldDAO;
import us.zoom.infra.dao.service.MetricsDAO;
import us.zoom.infra.influx.model.alarm.AlarmMatchRecord;
import us.zoom.infra.model.alarm.AlarmLevel;
import us.zoom.infra.utils.DateUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AlarmInsightService {

    @Autowired
    private AuthService authService;

    @Autowired
    private MetricsHandler metricsHandler;

    @Autowired
    private AlarmDefinitionDao alarmDefinitionDao;

    @Autowired
    private TenantHandler tenantHandler;

    @Autowired
    private ClickhouseMetricsService clickhouseMetricsService;

    @Autowired
    private ClickhouseHandlerFactory clickhouseHandlerFactory;

    @Autowired
    private SubEnvironmentHandler subEnvironmentHandler;

    @Autowired
    private PiiTableCacheLoader piiTableLoader;

    @Autowired
    private AlarmInsightRecordsHandler alarmInsightRecordsHandler;

    @Autowired
    private MetricsDAO metricsDAO;

    @Autowired
    private AlarmDefinitionV2DAO alarmDefinitionV2DAO;

    @Autowired
    private CollectorFieldDAO collectorFieldDAO;

    private static final String GROUP_TAG_COUNT_SQL = "select %s count(*) as count from %s.%s where \"initialStatus\" not in ('pending','suppressed','silenced') and \"alarmName\" = %s and time>=%d and time<=%d %s %s group by %s order by \"count\" desc limit 10";

    private static final String TOTAL_COUNT_SQL = "select count(*) as totalCount from %s.%s where \"initialStatus\" not in ('pending','suppressed','silenced') and \"alarmName\" = %s and time>=%d and time<=%d %s %s";

    private static final String OVERVIEW_TEMPLATE_SQL = "select alarmId,alarmName,alarmLevel,count(alarmLevel) as count from %s.%s where 1=1 and \"initialStatus\" not in ('pending','suppressed','silenced')  %s and time>=%d and time<=%d group by alarmId,alarmName,alarmLevel order by alarmId,alarmName";

    private static final String ALERT_COUNT_SQL = "select time2 as time, \"Alarm Count\" from ( select %s as time2, count(*) as \"Alarm Count\"  from %s.%s where \"initialStatus\" not in ('pending','suppressed','silenced') and \"alarmName\" = %s and time>%d and time<%d %s %s group by time2 order by time2)";

    private static final String ALERT_COUNT_SQL_LAST_WEEK = "select time2 as time, \"Alarm Count from 7 Days Ago\" from ( select %s as time2, count(*) as \"Alarm Count from 7 Days Ago\"  from %s.%s where \"initialStatus\" not in ('pending','suppressed','silenced') and \"alarmName\" = %s and time>%d and time<%d %s %s group by time2 order by time2)";

    private static final String CONSTANT_ALARM_ID = "alarmId";
    private static final String CONSTANT_ALARM_NAME = "alarmName";
    private static final String CONSTANT_ALARM_LEVEL = "alarmLevel";
    private static final String CONSTANT_COUNT = "count";

    public AlarmOverviewOutput getAlarmingOverview(AlarmOverviewInput overviewInput) {
        checkOverviewInput(overviewInput);
        final long beginSeconds = overviewInput.getBegin() / 1000;
        final long endSeconds = overviewInput.getEnd() / 1000;

        final String overviewSql = String.format(OVERVIEW_TEMPLATE_SQL,
                ClickhouseSqlUtil.encodeClickhouseName(overviewInput.getServiceName()),
                ClickhouseConst.ALARM_CUBE_MATCH_SERVICE_RECORD_TABLE,
                alarmNameCondtionForOverview(overviewInput), beginSeconds, endSeconds);
        String routedEnv = subEnvironmentHandler.getAlarmInsightEnv(overviewInput.getServiceName());
        List<Map<String, Object>> queryResult = clickhouseHandlerFactory.get().query(overviewInput.getServiceName(), overviewSql, routedEnv);
        if (CollectionUtils.isEmpty(queryResult)) {
            queryResult = new ArrayList<>();
        }
        //mapping the query result
        Map<String, AlarmOverviewOutputEntry> overviewEntryMap = new HashMap<>();
        List<String> existsAlarmIds = new ArrayList<>();
        for (Map<String, Object> m : queryResult) {
            String alarmId = Optional.ofNullable(m.get(CONSTANT_ALARM_ID)).map(Object::toString).orElse(null);
            String alarmName = Optional.ofNullable(m.get(CONSTANT_ALARM_NAME)).map(Object::toString).orElse(null);
            if (StringUtils.isNotBlank(alarmId)) {
                existsAlarmIds.add(alarmId);
            }
            if (!overviewEntryMap.containsKey(alarmName)) {
                overviewEntryMap.put(alarmName, new AlarmOverviewOutputEntry(alarmId, alarmName));
            }
            AlarmOverviewOutputEntry ooe = overviewEntryMap.get(alarmName);
            String alarmLevel = m.get(CONSTANT_ALARM_LEVEL).toString();
            String levelPeril = Objects.requireNonNull(AlarmLevel.fromLevel(alarmLevel)).getPeril();
            Long count = Long.valueOf(m.get(CONSTANT_COUNT).toString());
            ooe.getLevelCount().put(levelPeril, count);
        }
        //filter no match for sorting level which chose by user
        final List<String> levelSortWeights = overviewInput.getFilterItemValues().getLevel();
        List<AlarmOverviewOutputEntry> overviewOutputEntries = overviewEntryMap.values().stream().filter(entry -> {
            for (String levelSortWeight : levelSortWeights) {
                if (entry.getLevelCount().get(levelSortWeight) != 0) {
                    return true;
                }
            }
            return false;
        }).sorted((o1, o2) -> {
            for (String levelSortWeight : levelSortWeights) {
                int diff = (int) (o2.getLevelCount().get(levelSortWeight) - o1.getLevelCount().get(levelSortWeight));
                if (0 != diff) {
                    return diff;
                }
            }
            return 0;
        }).collect(Collectors.toList());
        //sort
        List<IdAndName> allAlarms = alarmDefinitionDao.findByTenantId(overviewInput.getTenantId());
        List<AlarmOverviewOutputEntry> emptyAlarmList = allAlarms.stream()
                .filter(e -> !existsAlarmIds.contains(e.getId()))
                .map(e -> new AlarmOverviewOutputEntry(e.getId(), e.getName()))
                .toList();
        return new AlarmOverviewOutput(overviewOutputEntries, emptyAlarmList);
    }

    private String alarmNameCondtionForOverview(AlarmOverviewInput overviewInput) {
        if (StringUtils.isNotBlank(overviewInput.getAlarmName())) {
            return String.format(" and alarmName='%s'", overviewInput.getAlarmName());
        } else {
            return StringUtils.EMPTY;
        }
    }

    private void checkOverviewInput(AlarmOverviewInput overviewInput) {
        Assert.isTrue(StringUtils.isNotBlank(overviewInput.getServiceName()), "Service name can't be empty");
        if (null == overviewInput.getFilterItemValues()) {
            overviewInput.setFilterItemValues(new AlarmOverviewInput.FilterItemValues());
        }
        if (CollectionUtils.isEmpty(overviewInput.getFilterItemValues().getLevel())) {
            overviewInput.getFilterItemValues().setLevel(Arrays.asList(
                    AlarmLevel.FATAL.getPeril(),
                    AlarmLevel.ERROR.getPeril(),
                    AlarmLevel.WARN.getPeril(),
                    AlarmLevel.INFO.getPeril()));
        } else {
            for (String levelPeril : overviewInput.getFilterItemValues().getLevel()) {
                Assert.notNull(AlarmLevel.getLevel(levelPeril), "Can't recognize this level:" + levelPeril);
            }
        }
    }

    public ResponseObject getAlertDistribution(AlarmInsightAlertDistributionInput alarmInsightAlertDistributionInput) {
        TenantDO tenant = tenantHandler.getTenantByName(alarmInsightAlertDistributionInput.getServiceName());
        Assert.notNull(tenant, "Can't find the service by id:" + alarmInsightAlertDistributionInput.getServiceName());
        boolean piiPermission = checkPIIMetrics(tenant, alarmInsightAlertDistributionInput.getAlarmName());
        StringBuilder levelFilterCondition = buildLevel(alarmInsightAlertDistributionInput);
        StringBuilder tagvalueFilterCondition = buildTagValue(alarmInsightAlertDistributionInput);

        final String dbName = ClickhouseSqlUtil.encodeClickhouseName(alarmInsightAlertDistributionInput.getServiceName());
        final String totalCountSql = String.format(TOTAL_COUNT_SQL, dbName, ClickhouseConst.ALARM_CUBE_MATCH_SERVICE_RECORD_TABLE,
                String.format("'%s'", alarmInsightAlertDistributionInput.getAlarmName()), alarmInsightAlertDistributionInput.getBegin() / 1000,
                alarmInsightAlertDistributionInput.getEnd() / 1000, levelFilterCondition, tagvalueFilterCondition);
        String routedEnv = subEnvironmentHandler.getAlarmInsightEnv(alarmInsightAlertDistributionInput.getServiceName());
        List<Map<String, Object>> query = clickhouseHandlerFactory.get().query(alarmInsightAlertDistributionInput.getServiceName(), totalCountSql, routedEnv);
        AlarmInsightAlertDistributionOutput alarmInsightAlertDistributionOutput = new AlarmInsightAlertDistributionOutput();
        alarmInsightAlertDistributionOutput.setTotalCount((query.stream()
                .map(q -> q.get("totalCount"))
                .filter(Objects::nonNull)
                .mapToLong(q -> Long.parseLong(q.toString()))
                .findFirst()
                .orElse(0L)));

        StringBuilder mapTags = new StringBuilder();
        List<String> tags = new ArrayList<>();
        if (!CollectionUtils.isEmpty(alarmInsightAlertDistributionInput.getTags())) {
            tags.addAll(alarmInsightAlertDistributionInput.getTags());
        } else {
            AlarmInsightInput alarmInsightInput = new AlarmInsightInput();
            alarmInsightInput.setAlarmName(alarmInsightAlertDistributionInput.getAlarmName());
            alarmInsightInput.setServiceName(alarmInsightAlertDistributionInput.getServiceName());
            List<String> allTags = (List<String>) getQueryTags(alarmInsightInput, false).getData();
            if (!CollectionUtils.isEmpty(allTags)) {
                tags.addAll(allTags);
            }
        }
        tags.forEach(t -> {
            mapTags.append(String.format("tagMap['%s'] as %s", t, t)).append(",");
        });
        String groupMapTags = mapTags.toString();
        if (groupMapTags.length() > 1) {
            groupMapTags = groupMapTags.substring(0, groupMapTags.length() - 1);
        }
        final String countSql = String.format(GROUP_TAG_COUNT_SQL, mapTags, dbName, ClickhouseConst.ALARM_CUBE_MATCH_SERVICE_RECORD_TABLE,
                String.format("'%s'", alarmInsightAlertDistributionInput.getAlarmName()), alarmInsightAlertDistributionInput.getBegin() / 1000,
                alarmInsightAlertDistributionInput.getEnd() / 1000, levelFilterCondition, tagvalueFilterCondition, groupMapTags);
        List<Map<String, Object>> queryCount = clickhouseHandlerFactory.get().query(alarmInsightAlertDistributionInput.getServiceName(), countSql, routedEnv);

        List<AlarmInsightAlertDistributionOutput.Series> series = new ArrayList<>();
        if (CollectionUtils.isEmpty(series)) {
            List<CollectorFieldDO> collectorFieldDOS = collectorFieldDAO.listPiiFieldByAlarm(tenant.getId(), alarmInsightAlertDistributionInput.getAlarmName());
            queryCount.forEach(q -> {
                AlarmInsightAlertDistributionOutput.Series output = new AlarmInsightAlertDistributionOutput.Series();
                q.forEach((k, v) -> {
                    if (k.equals(CONSTANT_COUNT)) {
                        output.setValues((v));
                    } else {
                        boolean isPII = collectorFieldDOS.stream().anyMatch(collectorField -> k.equals(collectorField.getTargetField()));
                        output.getTags().removeIf(tag -> tag.getName().equals(k));
                        output.getTags().add(new MetricsTags(k, isPII && !piiPermission ? "*" : v, isPII && !piiPermission));
                    }
                });
                series.add(output);
            });
        }
        alarmInsightAlertDistributionOutput.setSeries(series);
        return ResponseObject.success(alarmInsightAlertDistributionOutput);
    }

    public ResponseObject getQueryTags(AlarmInsightInput alarmInsightInput) {
        return getQueryTags(alarmInsightInput, true);
    }

    public ResponseObject getQueryTags(AlarmInsightInput alarmInsightInput, boolean needPII) {
        TenantDO tenant = tenantHandler.getTenantByName(alarmInsightInput.getServiceName());
        Assert.notNull(tenant, "Can't find the service by id:" + alarmInsightInput.getServiceName());
//        if (!checkPIIMetrics(tenant, alarmInsightInput.getAlarmName())) {
//            return ResponseObject.failWithWebCode(WebCodeEnum.PIIPermitError);
//        }
        boolean piiPermission = checkPIIMetrics(tenant, alarmInsightInput.getAlarmName());
        Optional<AlarmDefinition> alarmDefinitionOptional = alarmDefinitionDao.findByNameAndTenantId(alarmInsightInput.getAlarmName(), tenant.getId());

        if (!alarmDefinitionOptional.isPresent()) {
            return ResponseObject.success(Collections.emptyList());
        }

        AlarmDefinition alarmDefinition = alarmDefinitionOptional.get();
        MetricsDO metricsDO = metricsHandler.getMetricsById(alarmDefinition.getMetricId());
        String[] tags = metricsDO.getTagNames().split(",");

        if (needPII) {
            List<CollectorFieldDO> collectorFieldDOS = collectorFieldDAO.listPiiFieldByAlarm(tenant.getId(), alarmInsightInput.getAlarmName());

            List<MetricsTags> result = Arrays.stream(tags)
                    .map(tagName -> {
                        boolean isPII = collectorFieldDOS.stream().anyMatch(collectorField -> tagName.equals(collectorField.getTargetField()));
                        return new MetricsTags(tagName, isPII && !piiPermission);
                    })
                    .collect(Collectors.toList());

            return ResponseObject.success(result);
        } else {
            List<String> result = Arrays.asList(tags);
            return ResponseObject.success(result);
        }
    }

    public ResponseObject getTagValue(AlarmInsightTagValueInput alarmInsightTagValueInput) {
        TenantDO tenant = tenantHandler.getTenantByName(alarmInsightTagValueInput.getServiceName());
        Assert.notNull(tenant, "Can't find the service by id:" + alarmInsightTagValueInput.getServiceName());
        if (!checkPIIMetrics(tenant, alarmInsightTagValueInput.getAlarmName())) {
            long piiTagCount = collectorFieldDAO.getCountPiiByAlarmAndTag(tenant.getId(), alarmInsightTagValueInput.getAlarmName(), alarmInsightTagValueInput.getTag());
            if (piiTagCount > 0) return ResponseObject.failWithWebCode(WebCodeEnum.PIIPermitError);
        }

        Optional<AlarmDefinition> alarmDefinitionOptional = alarmDefinitionDao.findByNameAndTenantId(alarmInsightTagValueInput.getAlarmName(), tenant.getId());
        if (!alarmDefinitionOptional.isPresent()) {
            return ResponseObject.success("");
        }
        MetricsDO metricsDO = metricsHandler.getMetricsById(alarmDefinitionOptional.get().getMetricId());
        return clickhouseMetricsService.searchTagValuesWithCache(alarmInsightTagValueInput.getServiceName(), metricsDO.getMetricsName(),
                alarmInsightTagValueInput.getTag(), alarmInsightTagValueInput.getKeyword(), alarmInsightTagValueInput.getLimit(), 3600 * 24, "");
    }

    public StringBuilder buildLevel(AlarmInsightAlertDistributionInput alarmInsightAlertDistributionInput) {
        StringBuilder levelFilterCondition = new StringBuilder();
        if (Objects.nonNull(alarmInsightAlertDistributionInput.getFilterItemValues())) {
            if (!CollectionUtils.isEmpty(alarmInsightAlertDistributionInput.getFilterItemValues().getLevel())) {
                levelFilterCondition.append("and \"alarmLevel\" in (");
                List<String> alarmLevel = new ArrayList<>();
                alarmInsightAlertDistributionInput.getFilterItemValues().getLevel().forEach(a -> {
                    alarmLevel.add(Objects.requireNonNull(AlarmLevel.getLevel(a)).getLevel());
                });
                alarmLevel.forEach(level -> {
                    levelFilterCondition.append("'");
                    levelFilterCondition.append(level);
                    levelFilterCondition.append("'");
                    levelFilterCondition.append(", ");
                });
                levelFilterCondition.deleteCharAt(levelFilterCondition.length() - 2);
                levelFilterCondition.append(" )");
            }
        }
        return levelFilterCondition;
    }

    public StringBuilder buildTagValue(AlarmInsightAlertDistributionInput alarmInsightAlertDistributionInput) {
        StringBuilder tagvalueFilterCondition = new StringBuilder();
        if (!CollectionUtils.isEmpty(alarmInsightAlertDistributionInput.getFilterTagValues())) {
            for (AlarmInsightAlertDistributionInput.FilterTagValues tag : alarmInsightAlertDistributionInput.getFilterTagValues()) {
                if (!CollectionUtils.isEmpty(tag.getValues())) {
                    tagvalueFilterCondition.append("and tagMap[").append(String.format("'%s'", tag.getName())).append("] in (");
                    for (int i = 0; i < tag.getValues().size(); i++) {
                        if (i == tag.getValues().size() - 1) {
                            tagvalueFilterCondition.append(String.format("'%s'", tag.getValues().get(i)));
                        } else {
                            tagvalueFilterCondition.append(String.format("'%s'", tag.getValues().get(i))).append(",");
                        }
                    }
                    tagvalueFilterCondition.append(")");
                }
            }
        }
        return tagvalueFilterCondition;
    }

    public ResponseObject getAlertCount(AlarmInsightAlertDistributionInput alarmInsightAlertDistributionInput) {
        long startTime = System.currentTimeMillis();
        TenantDO tenant = tenantHandler.getTenantByName(alarmInsightAlertDistributionInput.getServiceName());
        Assert.notNull(tenant, "Can't find the service by id:" + alarmInsightAlertDistributionInput.getServiceName());
//        if (!checkPIIMetrics(tenant, alarmInsightAlertDistributionInput.getAlarmName())) {
//            return ResponseObject.failWithWebCode(WebCodeEnum.PIIPermitError);
//        }

        QueryResult data = null;
        String routedEnv = subEnvironmentHandler.getAlarmInsightEnv(alarmInsightAlertDistributionInput.getServiceName());
        ArrayList<String> sqlList = createSql(alarmInsightAlertDistributionInput);
        List[] qr = new List[2];
        for (int i = 0; i < 2; i++) {
            qr[i] = clickhouseHandlerFactory.get().query(alarmInsightAlertDistributionInput.getServiceName(), sqlList.get(i), routedEnv);
        }
        TimeFiller timeFiller = new TimeFiller(0, startTime);
        for (int i = 0; i < 2; i++) {
            String sql = sqlList.get(i);
            QueryResult queryResult = InfluxResultAdapter.wrapToInfluxLike(qr[i], sql, false);
            timeFiller.fillTimeWithNull(sql, queryResult);
            data = InfluxResultAdapter.merge(data, queryResult);
        }
        if (!CollectionUtils.isEmpty(data.getResults())) {
            data.getResults().forEach(d -> {
                List<List<Object>> newList = d.getSeries().get(0).getValues()
                        .stream()
                        .filter(Objects::nonNull)
                        .map(list -> list.stream().map(v -> Objects.isNull(v) ? 0 : v).collect(Collectors.toList()))
                        .collect((Collectors.toList()));
                d.getSeries().get(0).setValues(newList);
            });
        }
        return ResponseObject.success(data);
    }

    public ArrayList<String> createSql(AlarmInsightAlertDistributionInput alarmInsightAlertDistributionInput) {
        ArrayList<String> sqlList = new ArrayList<>();
        String startOfInterval = String.format("toStartOfInterval(time, INTERVAL %s)", alarmInsightAlertDistributionInput.getTimeDivsion());
        final String dbName = ClickhouseSqlUtil.encodeClickhouseName(alarmInsightAlertDistributionInput.getServiceName());
        StringBuilder levelFilterCondition = buildLevel(alarmInsightAlertDistributionInput);
        StringBuilder tagValueFilterCondition = buildTagValue(alarmInsightAlertDistributionInput);
        sqlList.add(0, String.format(ALERT_COUNT_SQL, startOfInterval, dbName, ClickhouseConst.ALARM_CUBE_MATCH_SERVICE_RECORD_TABLE,
                String.format("'%s'", alarmInsightAlertDistributionInput.getAlarmName()), alarmInsightAlertDistributionInput.getBegin() / 1000,
                alarmInsightAlertDistributionInput.getEnd() / 1000, levelFilterCondition, tagValueFilterCondition));

        sqlList.add(1, String.format(ALERT_COUNT_SQL_LAST_WEEK, startOfInterval, dbName, ClickhouseConst.ALARM_CUBE_MATCH_SERVICE_RECORD_TABLE,
                String.format("'%s'", alarmInsightAlertDistributionInput.getAlarmName()), DateUtils.addDay(new Date(alarmInsightAlertDistributionInput.getBegin()), -7) / 1000,
                DateUtils.addDay(new Date(alarmInsightAlertDistributionInput.getEnd()), -7) / 1000, levelFilterCondition, tagValueFilterCondition));
        ;
        return sqlList;
    }

    public ResponseObject getAlarmRecords(AlarmRecordsInput alarmRecordsInput) {
        TenantDO tenant = tenantHandler.getTenantByName(alarmRecordsInput.getServiceName());
        if(StringUtils.isBlank(alarmRecordsInput.getAlarmName())){
            String alarmId = alarmRecordsInput.getAlarmId();
            AlarmDefinitionDO alarm = alarmDefinitionV2DAO.getById(alarmId);
            Assert.notNull(alarm, "getAlarmRecords can't find alarm by alarmId: " + alarmId);
            alarmRecordsInput.setAlarmName(alarm.getName());
        }
        boolean piiPermission = checkPIIMetrics(tenant, alarmRecordsInput.getAlarmName());
        return ResponseObject.success(alarmInsightRecordsHandler.getAlarmRecords(alarmRecordsInput, piiPermission));
    }

    public AlarmDetailOutput getAlarmRecordDetail(AlarmDetailInput alarmDetailInput) {
        String serviceId = alarmDetailInput.getServiceId();
        TenantDO tenant = tenantHandler.getTenantById(serviceId);
        Assert.notNull(tenant, "getAlarmRecordDetail can't find the service by id:" + serviceId);
        final String serviceName = tenant.getName();
        AlarmMatchRecord alarmMatchRecord = alarmInsightRecordsHandler.getAlarmRecordById(serviceName, alarmDetailInput.getAlarmRecordId());
        Map<String, SubAlarmDO> alarmAndSubAlarms = alarmInsightRecordsHandler.getConfigSubAlarmsByServiceId(serviceId);
        return alarmInsightRecordsHandler.convertToAlarmDetailOutput(alarmMatchRecord, alarmAndSubAlarms);
    }

    public List<AlarmNotificationOutput> getAlarmRecordNotifications(AlarmDetailInput alarmDetailInput) {
        TenantDO tenant = tenantHandler.getTenantByName(alarmDetailInput.getServiceName());
        final String serviceName = alarmDetailInput.getServiceName();
        boolean auth = checkPIIMetrics(tenant, alarmDetailInput.getAlarmName());
        return alarmInsightRecordsHandler.getAlarmRecordNotifications(serviceName, alarmDetailInput.getAlarmRecordId(), auth);
    }

    public AlarmRecordsOutput getSuppressedAlarmRecords(AlarmRecordsInput alarmRecordsInput) {
        TenantDO tenant = tenantHandler.getTenantByName(alarmRecordsInput.getServiceName());
        boolean piiPermission = checkPIIMetrics(tenant, alarmRecordsInput.getAlarmName());
        alarmRecordsInput.setPiiPermission(piiPermission);
        return alarmInsightRecordsHandler.getSuppressedAlarmRecords(alarmRecordsInput);
    }

    public boolean checkPIIMetrics(TenantDO tenant, String alarmName) {
        MetricsDO metricsDO = metricsDAO.listMetricsByAlarm(tenant.getId(), alarmName);
        Assert.notNull(metricsDO, "The alarm name does not exist, alarm name is " + alarmName);
        ChTable chTable = new ChTable(tenant.getName(), metricsDO.getMetricsName());
        return piiTableLoader.auth(chTable, AuthInterceptor.getUserId(), AuthInterceptor.getRealIp());
    }
}