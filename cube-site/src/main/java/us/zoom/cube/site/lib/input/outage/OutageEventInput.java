package us.zoom.cube.site.lib.input.outage;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import us.zoom.cube.site.lib.BasePara;

import java.util.Date;

@Data
public class OutageEventInput extends BasePara {
   private String id;
   private String outageId;
   private String content;
   private String operator;
   @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
   private Date operationTime;
   private String mentionedUserIds;
   private String mailContent;

}
