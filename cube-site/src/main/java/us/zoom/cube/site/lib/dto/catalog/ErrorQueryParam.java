package us.zoom.cube.site.lib.dto.catalog;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import us.zoom.cube.site.infra.annotation.BeanToMapIgnore;
import us.zoom.cube.site.infra.enums.catalog.ErrorScope;
import us.zoom.cube.site.lib.dto.QueryFilter;

import java.util.List;

/**
 * 错误查询参数
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class ErrorQueryParam extends BaseQueryParam {
    
    @NotBlank(message = "errorScope cannot be null")
    @BeanToMapIgnore
    private String errorScope;
    
    @NotNull(message = "pageSize cannot be null")
    @BeanToMapIgnore
    private Integer pageSize;
    
    @NotNull(message = "pageIndex cannot be null")
    @BeanToMapIgnore
    private Integer pageIndex;

    @BeanToMapIgnore
    private List<QueryFilter> filters;
    
    public ErrorScope getErrorScopeEnum() {
        ErrorScope scope = ErrorScope.fromString(errorScope);
        if (scope == null) {
            throw new IllegalArgumentException("Invalid errorScope: " + errorScope);
        }
        return scope;
    }
    
    /**
     * 验证分页参数
     */
    public void validatePagination() {
        if (pageSize != null && pageSize <= 0) {
            throw new IllegalArgumentException("pageSize should be greater than 0");
        }
        if (pageIndex != null && pageIndex <= 0) {
            throw new IllegalArgumentException("pageIndex should be greater than 0");
        }
    }
} 