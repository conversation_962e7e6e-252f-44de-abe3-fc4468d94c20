package us.zoom.cube.site.biz.alarm.rule.baseimpl.alarm;

import com.zoom.op.monitor.domain.alarm.ConditionType;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import us.zoom.cube.lib.integrations.MetricsFieldTypeEnum;
import us.zoom.cube.site.biz.alarm.rule.AbstractBaseCheckService;
import us.zoom.cube.site.biz.alarm.rule.ValidationResult;
import us.zoom.cube.site.biz.alarm.rule.impl.CheckUtils;
import us.zoom.cube.site.biz.syspara.AlarmParaService;
import us.zoom.cube.site.lib.input.alarm.batch.AlarmRuleInput;
import us.zoom.cube.site.lib.input.alarm.batch.RuleConditionInput;
import us.zoom.cube.site.lib.output.config.metrics.FieldOrTag;
import us.zoom.infra.dao.model.AdCfgDO;
import us.zoom.infra.dao.model.MetricsFieldDO;
import us.zoom.infra.dao.service.AdCfgDAO;
import us.zoom.infra.enums.AlarmOperatorEnum;
import us.zoom.infra.utils.CommonSplitConstants;
import us.zoom.cube.lib.utils.expression.CustomAviatorUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: <EMAIL>
 * @date: 2024-07-22 14:18
 **/
@Service
@RequiredArgsConstructor
public class RuleConditionValuesCheck extends AbstractBaseCheckService<AlarmRuleInput, RuleConditionInput, AlarmCheckDTO> {
    private static final String PRE = "_pre_";
    private static final String NAME = ".name";
    private static final String OPERATOR = ".operator";
    private static final String CONDITION_TYPE = ".conditionType";
    private static final String EXPRESSION = ".expression";
    private static final String THRESHOLD = ".threshold";

    private final AlarmParaService alarmParaService;

    @Override
    public ValidationResult doVerification(String filedName, AlarmRuleInput alarmRule, RuleConditionInput ruleCondition, AlarmCheckDTO alarmCheckDTO) {

        ValidationResult result = new ValidationResult();

        List<FieldOrTag> fieldOrTags = alarmCheckDTO.getFieldList();
        if (CollectionUtils.isEmpty(fieldOrTags)) {
            result.put(filedName, "query metrics fields error,Please check the metrics name.");
            return result;
        }
        if (Objects.isNull(alarmCheckDTO.getMetricsDO()) && CollectionUtils.isEmpty(alarmCheckDTO.getFieldList())) {
            result.put(filedName, "metrics does not exist");
            return result;
        }
        Map<String, Object> fieldOrTagMap = fieldOrTags.stream().collect(Collectors.toMap(FieldOrTag::getFieldOrTagName, FieldOrTag::getSampleValue, (a, b) -> a));
        Map<String, Integer> metricFieldAndTypes = alarmCheckDTO.getFieldList().stream().collect(Collectors.toMap(FieldOrTag::getFieldOrTagName, fieldOrTag -> MetricsFieldTypeEnum.fromName(fieldOrTag.getValueType()).getValue()));

        //check RuleCondition.conditionType
        checkConditionType(result, filedName + CONDITION_TYPE, ruleCondition);
        //check RuleCondition.expression
        if (ConditionType.EXPRESSION.name().equals(ruleCondition.getConditionType())) {
            checkAviatorExpression(result, filedName + EXPRESSION, ruleCondition.getExpression(), fieldOrTagMap);
        }
        //check RuleCondition.name RuleCondition.operator RuleCondition.threshold
        if (Objects.equals(ConditionType.FIELD.name(), ruleCondition.getConditionType())) {
            //RuleCondition.name
            CheckUtils.check(result, filedName + NAME, () -> {
                Assert.isTrue(metricFieldAndTypes.containsKey(ruleCondition.getName()), String.format("Metric doesn't have this field:[%s]", ruleCondition.getName()));

            });
            //RuleCondition.operator
            CheckUtils.check(result, filedName + OPERATOR, () -> {
                Integer value = metricFieldAndTypes.get(ruleCondition.getName());
                Assert.isTrue(Objects.nonNull(value) && AlarmOperatorEnum.containsOperation(ruleCondition.getOperator(), MetricsFieldTypeEnum.fromValue(value))
                        , String.format("%s operator is not allowed against the specified field type or the field type is not defined", ruleCondition.getOperator()));
            });
            //RuleCondition.threshold
            CheckUtils.check(result, filedName + THRESHOLD, () -> {
                checkFieldRule(fieldOrTagMap, ruleCondition);
            });
        }
        if (Objects.equals(ConditionType.TAG.name(), ruleCondition.getConditionType())) {
            List<String> metricTagNames = (List<String>) org.springframework.util.CollectionUtils.arrayToList(Optional.ofNullable(alarmCheckDTO.getMetricsDO().getTagNames()).orElse(StringUtils.EMPTY).split(CommonSplitConstants.COMMA_SPLIT));
            //RuleCondition.name
            CheckUtils.check(result, filedName + NAME, () -> {
                Assert.isTrue(metricTagNames.contains(ruleCondition.getName()), String.format("Metric doesn't have this tag:[%s]", ruleCondition.getName()));
            });
            //RuleCondition.operator
            CheckUtils.check(result, filedName + OPERATOR, () -> {
                Assert.isTrue(AlarmOperatorEnum.containsOperation(ruleCondition.getOperator(), MetricsFieldTypeEnum.string), String.format("%s operator is not allowed in Tag", ruleCondition.getOperator()));
            });
        }
        if (ConditionType.AI.name().equals(ruleCondition.getConditionType())) {
            CheckUtils.check(result, filedName + THRESHOLD, () ->
                    checkAiFieldThreshold(ruleCondition)
            );
        }
        //todo spc and ai not check
        return result;
    }


    private void checkConditionType(ValidationResult result, String fieldsName, RuleConditionInput ruleCondition) {
        CheckUtils.check(result, fieldsName, () -> {
            try {
                Enum.valueOf(ConditionType.class, ruleCondition.getConditionType());
            } catch (Exception e) {
                throw new IllegalArgumentException("conditionType type does not match");
            }
        });

    }


    public void checkAviatorExpression(ValidationResult result, String filedName, String expression, Map<String, Object> fieldMap) {
        CheckUtils.check(result, filedName, () -> {
            if (StringUtils.isEmpty(expression)) {
                return;
            }
            try {
                List<String> variableNames = CustomAviatorUtils.compile(expression, true).getVariableFullNames();
                variableNames.forEach(v -> {
                    String tmp = v;
                    String preVarPrefix = alarmParaService.getParamValue(AlarmParaService.PRE_VAR_PREFIX, PRE);
                    if (v.startsWith(preVarPrefix)) {
                        tmp = v.substring(preVarPrefix.length());
                    }
                    if (!fieldMap.containsKey(tmp)) {
                        throw new IllegalArgumentException("expression[" + expression + "] is invalid, the error msg is " + "Could not find variable " + v);
                    }
                });
                CustomAviatorUtils.execute(expression, fieldMap, true);
            } catch (Exception e) {
                throw new IllegalArgumentException("expression[" + expression + "] is invalid, the error msg is " + e.getMessage());
            }
        });
    }

    protected void checkFieldRule(Map<String, Object> fieldOrTagMap, RuleConditionInput ruleCondition) {
        Object value = fieldOrTagMap.get(ruleCondition.getName());
        Assert.notNull(value, "field:" + ruleCondition.getName() + " not exist!");
        //check number field threshold
        if (value instanceof Double) {
            try {
                Double.parseDouble(ruleCondition.getThreshold());
            } catch (Exception e) {
                Assert.isTrue(false, "Field:" + ruleCondition.getName() + " Threshold" + ruleCondition.getThreshold() + " needs to be a number!");
            }
        }
    }

    public void checkAiFieldThreshold(RuleConditionInput ruleCondition) {
        String thresholdStr = ruleCondition.getThreshold();
        Assert.isTrue(StringUtils.isNotBlank(thresholdStr),
                "Ai rule " + ruleCondition.getName() + " threshold cannot be null or empty!");
        try {
            int threshold = Integer.parseInt(thresholdStr);
            Assert.isTrue(threshold >= 1 && threshold <= 5,
                    String.format("Ai rule %s threshold %s must be an integer between 1 and 5!",
                            ruleCondition.getName(), thresholdStr));
        } catch (NumberFormatException e) {
            Assert.isTrue(false, "Ai rule " + ruleCondition.getName() + " threshold " + ruleCondition.getThreshold() + " needs to be a integer!");
        }
    }
}
