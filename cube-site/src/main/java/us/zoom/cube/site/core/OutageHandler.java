package us.zoom.cube.site.core;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.common.CubeConstants;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.site.infra.enums.WebCodeEnum;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.SiteException;
import us.zoom.cube.site.lib.input.outage.OutageActionEditInput;
import us.zoom.cube.site.lib.input.outage.OutageAddInput;
import us.zoom.cube.site.lib.input.outage.OutageAlarmBatchInput;
import us.zoom.cube.site.lib.input.outage.OutageAlarmDeleteInput;
import us.zoom.cube.site.lib.input.outage.OutageCommentInput;
import us.zoom.cube.site.lib.input.outage.OutageEditInput;
import us.zoom.cube.site.lib.input.outage.OutageEventDeleteInput;
import us.zoom.cube.site.lib.input.outage.OutageEventInput;
import us.zoom.cube.site.lib.input.outage.OutageLinkInput;
import us.zoom.cube.site.lib.input.outage.OutageOwnerInput;
import us.zoom.cube.site.lib.input.outage.OutageStatusInput;
import us.zoom.cube.site.lib.input.outage.SmartOutageInput;
import us.zoom.cube.site.lib.outage.CommentTypeEnum;
import us.zoom.cube.site.lib.outage.OutageEditTypeEnum;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.cube.site.lib.query.outage.OutageActionQuery;
import us.zoom.cube.site.lib.query.outage.OutageAlarmQuery;
import us.zoom.cube.site.lib.query.outage.OutageCommentQuery;
import us.zoom.cube.site.lib.query.outage.OutageEventQuery;
import us.zoom.cube.site.lib.query.outage.OutageQuery;
import us.zoom.infra.dao.model.OutageActionDO;
import us.zoom.infra.dao.model.OutageAggDO;
import us.zoom.infra.dao.model.OutageAlarmDO;
import us.zoom.infra.dao.model.OutageCommentDO;
import us.zoom.infra.dao.model.OutageDO;
import us.zoom.infra.dao.model.OutageEventDO;
import us.zoom.infra.dao.model.OutageLinkDO;
import us.zoom.infra.dao.model.OutageOwnerDO;
import us.zoom.infra.dao.model.RcaSummaryOutput;
import us.zoom.infra.dao.service.OutageDAO;
import us.zoom.infra.notification.channel.email.EmailChannelEngine;
import us.zoom.infra.utils.JWTUtils;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
public class OutageHandler {

    @Autowired
    private OutageDAO outageDAO;

    @Autowired
    private UserHandler userHandler;

    @Autowired
    private EmailChannelEngine emailChannelEngine;


    @Value("${cube.outage.url.alert:}")
    private String cubeOutageUrlAlert;

    @Value("${cube.outage.url.agent}")
    private  String agentUrl;
    @Value("${cube.outage.agent.id}")
    private  String agentId;

    private static final Logger monitorLogger = LoggerFactory.getLogger("Monitor");



    public List<OutageDO> searchOutageList(PageQuery<OutageQuery> pageQuery) {
        Assert.notNull(pageQuery, "input is null");
        Assert.notNull(pageQuery.getQueryPara(), "queryPara is null");
        return outageDAO.searchOutageList(pageQuery.getQueryPara().getTitle(), pageQuery.getQueryPara().getLevel(), pageQuery.getQueryPara().getStatus(), pageQuery.getQueryPara().getType(), pageQuery.getQueryPara().getStartTime(), pageQuery.getQueryPara().getEndTime(), pageQuery.getPageSize() * (pageQuery.getPageIndex() - 1), pageQuery.getPageSize());
    }

    public Integer getOutageParaCount(PageQuery<OutageQuery> pageQuery) {
        Assert.notNull(pageQuery, "input is null");
        Assert.notNull(pageQuery.getQueryPara(), "queryPara is null");
        return outageDAO.getOutageListCount(pageQuery.getQueryPara().getTitle(), pageQuery.getQueryPara().getLevel(), pageQuery.getQueryPara().getStatus(), pageQuery.getQueryPara().getType(),pageQuery.getQueryPara().getStartTime(), pageQuery.getQueryPara().getEndTime(), pageQuery.getPageSize() * (pageQuery.getPageIndex() - 1), pageQuery.getPageSize());
    }

    /**
     * add
     * @param outageAddInput
     * @return outageId
     */
    public String add(OutageAddInput outageAddInput) {
        check(outageAddInput);
        OutageDO outageDO = new OutageDO();
        BeanUtils.copyProperties(outageAddInput, outageDO);
        outageDO.setId(IdUtils.generateId());
        String userName = userHandler.getNameById(outageAddInput.getUserId());
        outageDO.setCreator(userName);
        outageDO.setLastModifiedBy(userName);
        if (StringUtils.isBlank(outageDO.getSummary())) {
            outageDO.setSummary(StringUtils.EMPTY);
        }
        if (StringUtils.isBlank(outageDO.getCustomerImpact())) {
            outageDO.setCustomerImpact(StringUtils.EMPTY);
        }
        if (StringUtils.isBlank(outageDO.getDoWell())) {
            outageDO.setDoWell(StringUtils.EMPTY);
        }
        if (StringUtils.isBlank(outageDO.getDoPoorly())) {
            outageDO.setDoPoorly(StringUtils.EMPTY);
        }
        if (StringUtils.isBlank(outageDO.getRootCause())) {
            outageDO.setRootCause(StringUtils.EMPTY);
        }
        if (StringUtils.isBlank(outageDO.getFiveWhy())) {
            outageDO.setFiveWhy(StringUtils.EMPTY);
        }
        outageDO.setEditSource(OutageEditTypeEnum.Manual.getCode());
        outageDAO.add(outageDO);
        return outageDO.getId();
    }

    private void check(OutageAddInput outageAddInput) {
        Assert.notNull(outageAddInput, "outage input is null");
        Assert.isTrue(!StringUtils.isBlank(outageAddInput.getTitle()), "outage title is blank");
        OutageDO outageDO = this.searchOutageByTitle(outageAddInput.getTitle());
        if(outageDO != null){
            throw new SiteException(WebCodeEnum.EntityExisted);
        }
    }

    @Transactional
    public void edit(OutageEditInput outageEditInput) {
        Assert.isTrue(!StringUtils.isBlank(outageEditInput.getId()), "id is blank");
        OutageDO outageDO = new OutageDO();
        BeanUtils.copyProperties(outageEditInput, outageDO);
        String userName = userHandler.getNameById(outageEditInput.getUserId());
        outageDO.setLastModifiedBy(userName);
        if(StringUtils.isNotBlank(outageEditInput.getTitle())){
            OutageDO outage = this.searchOutageByTitle(outageEditInput.getTitle());
            if(outage != null && !outage.getId().equals(outageEditInput.getId())){
                throw new SiteException(WebCodeEnum.EntityExisted);
            }
        }
        outageDO.setEditSource(OutageEditTypeEnum.Manual.getCode());
        outageDAO.edit(outageDO);

        if (!CollectionUtils.isEmpty(outageEditInput.getOutageLinks())) {
            outageDAO.deleteLinks(outageEditInput.getId());
            List<OutageLinkInput> outageLinks = outageEditInput.getOutageLinks();
            outageEditInput.getOutageLinks().forEach(outageLinkInput -> {
                if(StringUtils.isBlank(outageLinkInput.getLinkUrl()) || StringUtils.isBlank(outageLinkInput.getLinkDesc())){
                    throw new SiteException(WebCodeEnum.EntityIsNull);
                }
            });
            List<OutageLinkDO> outageLinkDOS = outageLinks.stream().map(outageLinkInput -> OutageLinkDO.builder().id(IdUtils.generateId()).outageId(outageEditInput.getId()).linkUrl(outageLinkInput.getLinkUrl()).linkDesc(outageLinkInput.getLinkDesc()).build()).collect(Collectors.toList());
            outageDAO.addLinks(outageLinkDOS);
        }

        if (!CollectionUtils.isEmpty(outageEditInput.getOutageOwners())) {
            outageDAO.deleteOwners(outageEditInput.getId());
            List<OutageOwnerInput> outageOwners = outageEditInput.getOutageOwners();
            List<OutageOwnerDO> outageOwnerDOS = outageOwners.stream().map(outageOwnerInput -> OutageOwnerDO.builder().id(IdUtils.generateId()).outageId(outageEditInput.getId()).owner(outageOwnerInput.getOwner()).managers(outageOwnerInput.getManagers()).build()).collect(Collectors.toList());
            outageDAO.addOwners(outageOwnerDOS);
        }

        if(!CollectionUtils.isEmpty(outageEditInput.getOutageCommentInputs())){
            List<OutageCommentInput> commentInputs = outageEditInput.getOutageCommentInputs();
            for (OutageCommentInput commentInput : commentInputs) {

                commentInput.setOutageId(outageEditInput.getId());
                commentInput.setCommentId(IdUtils.generateId());
                commentInput.setUserId(outageEditInput.getUserId());
                commentInput.setType(CommentTypeEnum.Inner.getCode());
                commentInput.setContent(StringUtils.EMPTY);

                // handle userIds
                String mentionedUserIds = commentInput.getMentionedUserIds();
                String distinctMentionedUserIds = Arrays.stream(mentionedUserIds.split(","))
                        .distinct()
                        .collect(Collectors.joining(","));

                notice(commentInput.getOutageId(), distinctMentionedUserIds, userName, commentInput.getMailContent(),commentInput.getContent(), commentInput.getMentionModuleType(), "edit");
            }
        }

    }

    public OutageDO getById(String id) {
        Assert.isTrue(!StringUtils.isBlank(id), "id is blank");
        OutageDO outageDO = outageDAO.getById(id);
        List<OutageLinkDO> outageLinkDOS = outageDAO.selectLinks(id);
        if(!CollectionUtils.isEmpty(outageLinkDOS)){
            outageDO.setOutageLinks(outageLinkDOS);
        }
        List<OutageOwnerDO> outageOwnerDOS = outageDAO.selectOwners(id);
        if(!CollectionUtils.isEmpty(outageOwnerDOS)){
            outageDO.setOutageOwners(outageOwnerDOS);
        }
        return outageDO;
    }

    public void delete(String id) {
        Assert.isTrue(!StringUtils.isBlank(id), "id is blank");
        outageDAO.delete(id);
    }

    public void updateStatus(OutageStatusInput outageStatusInput) {
        Assert.isTrue(!StringUtils.isBlank(outageStatusInput.getId()), "id is blank");
        Assert.isTrue(!StringUtils.isBlank(outageStatusInput.getStatus()), "status is blank");
        OutageDO outageDO=new OutageDO();
        BeanUtils.copyProperties(outageStatusInput,outageDO);
        String userName = userHandler.getNameById(outageStatusInput.getUserId());
        outageDO.setLastModifiedBy(userName);
        String  fromStatus = outageDAO.getById(outageDO.getId()).getStatus();
        outageDAO.updateStatus(outageDO.getStatus(),userName,outageDO.getId(),fromStatus);
    }

    @Transactional
    public void comment(OutageCommentInput commentInput) {
        Assert.isTrue(StringUtils.isNotBlank(commentInput.getCommentId()), "commentId is blank");
        Assert.isTrue(StringUtils.isNotBlank(commentInput.getOutageId()), "outageId is blank");
        Assert.isTrue(StringUtils.isNotBlank(commentInput.getContent()), "content is blank");
        Assert.isTrue(StringUtils.isNotBlank(commentInput.getMailContent()), "mailContent is blank");
        Assert.isTrue(StringUtils.isNotBlank(commentInput.getType()), "type is blank");
        OutageCommentDO outageCommentDO = new OutageCommentDO();
        BeanUtils.copyProperties(commentInput, outageCommentDO);
        String userName = userHandler.getNameById(commentInput.getUserId());
        outageCommentDO.setCreator(userName);
        outageCommentDO.setLastModifiedBy(userName);
        outageCommentDO.setId(IdUtils.generateId());
        //comment notice
        if (StringUtils.isNotBlank(commentInput.getMentionedUserIds())) {
            String mentionedUserIds = commentInput.getMentionedUserIds();
            List<String> mentionedUserIdList = Arrays.asList(mentionedUserIds.split(","));
            mentionedUserIds = mentionedUserIdList.stream()
                    .distinct()
                    .collect(Collectors.joining(","));
            outageCommentDO.setMentionedUserIds(mentionedUserIds);
            notice(commentInput.getOutageId(), mentionedUserIds, userName, commentInput.getMailContent(),commentInput.getContent(), Optional.ofNullable(commentInput.getParentCommentId()).orElse(commentInput.getCommentId()),"mention");
        }else{
            notice(commentInput.getOutageId(), userName, userName, commentInput.getMailContent(),commentInput.getMailCommentContent(), Optional.ofNullable(commentInput.getParentCommentId()).orElse(commentInput.getCommentId()),"comment");
        }
        if(StringUtils.isBlank(outageCommentDO.getParentCommentId())){
            outageCommentDO.setParentCommentId("0");
        }
        if(StringUtils.isBlank(outageCommentDO.getMentionedUserIds())){
            outageCommentDO.setMentionedUserIds(StringUtils.EMPTY);
        }
        outageDAO.comment(outageCommentDO);
    }

    /**
     *
     * @param commentInput
     */
    public void editComment(OutageCommentInput commentInput) {
        Assert.isTrue(StringUtils.isNotBlank(commentInput.getId()), "id is blank");
        Assert.isTrue(StringUtils.isNotBlank(commentInput.getContent()), "content is blank");
        OutageCommentDO outageCommentDO = new OutageCommentDO();
        BeanUtils.copyProperties(commentInput, outageCommentDO);
        String userName = userHandler.getNameById(commentInput.getUserId());
        outageCommentDO.setCreator(userName);
        outageCommentDO.setLastModifiedBy(userName);
        //comment notice
        if (StringUtils.isNotBlank(commentInput.getMentionedUserIds())) {
            String mentionedUserIds = commentInput.getMentionedUserIds();
            List<String> mentionedUserIdList = Arrays.asList(mentionedUserIds.split(","));
            mentionedUserIds = mentionedUserIdList.stream()
                    .distinct()
                    .collect(Collectors.joining(","));
            outageCommentDO.setMentionedUserIds(mentionedUserIds);
            notice(commentInput.getOutageId(), mentionedUserIds, userName, commentInput.getMailContent(),commentInput.getContent(), Optional.ofNullable(commentInput.getParentCommentId()).orElse(commentInput.getCommentId()),"mention");
        }else{
            notice(commentInput.getOutageId(), userName, userName, commentInput.getMailContent(),commentInput.getContent(), Optional.ofNullable(commentInput.getParentCommentId()).orElse(commentInput.getCommentId()),"comment");
        }
        outageDAO.updateContentById(outageCommentDO.getId(),outageCommentDO.getContent());
    }

    public void notice(String outageId, String mentionedUserIds, String userName,
                       String mailContent, String commentContent,
                       String relatedId, String moduleName) {

        OutageDO outageDO = outageDAO.getById(outageId);
        if (outageDO == null) {
            monitorLogger.warn("OutageDO not found for id: {}", outageId);
            return;
        }

        String rcaTitle = outageDO.getTitle();
        String detailsLink = getNoticeDetailLink(outageId, relatedId, moduleName);
        String time = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String emailUserName = getEmailPrefix(userName);

        // distinct
        List<String> mentionedUserIdList = Stream.of(mentionedUserIds.split(","))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .toList();

        // notice
        for (String mentionId : mentionedUserIdList) {
            Map<String, String> parameters = Map.of("Recipients", mentionId);
            String htmlMessage;

            if ("comment".equalsIgnoreCase(moduleName)) {
                htmlMessage = buildCommentNoticeHtml(emailUserName, detailsLink, rcaTitle, time, mailContent, commentContent);
            } else if("asyncSmartAddOutage".equalsIgnoreCase(moduleName)){
                htmlMessage = buildAsyncOutageHtml(emailUserName, detailsLink, mailContent, time);
            }else {
                htmlMessage = buildMentionNoticeHtml(emailUserName, detailsLink, rcaTitle, time);
            }

            try {
                String title = "New Notification from \"Cube Outage Center\"";
                emailChannelEngine.sendHtmlEmail(parameters, title, htmlMessage);
            } catch (Exception e) {
                monitorLogger.error("Mentioned notice error for user {}: {}", mentionId, e.getMessage(), e);
            }
        }
    }

    private String buildAsyncOutageHtml(String userName, String link, String title, String time) {
        return String.format("""
        <!DOCTYPE html>
        <html lang="en-US">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
            <title>New outage in Cube Outage Center</title>
        </head>
        <body style="margin:0;padding:0;width:100%%;">
            <div style="background-color:#fff;margin:0 auto;max-width:1024px;">
                
                <!-- Header -->
                <div style="background-color:#0b5cff;padding:28px 32px;">
                    <img alt="LOGO" src="https://st1.zoom.us/zoom-docs/email/assets/logo_white.png" style="height:24px;">
                </div>
                
                <!-- Content -->
                <div style="font-family:Inter,sans-serif;font-size:16px;max-width:580px;padding:32px;margin:auto;">
                    
                    <!-- Cube Icon and Label -->
                    <div style="display:flex;align-items:center;">
                        <img src="https://file-paa.zoom.us/xqmuP22rQPuGwV7ocsfMeg/MS4yLiECN-QGFhJmvbltua3t5_oVk2M4tAfBku0wyhlCiM3q/cube-icon.png" style="height:32px;">
                        <div style="margin-left:10px;color:#696969;font-size:20px;font-weight:700;">Cube</div>
                    </div>
                    
                    <!-- Title -->
                    <h1 style="font-size:30px;font-weight:700;margin:32px 0 0 0;">
                        New outage created in "Cube Outage Center"
                    </h1>
                    
                    <!-- Subheading with link -->
                    <h2 style="font-size:20px;font-weight:500;margin:10px 0;">
                        %s created a new outage successfully:<br>
                        <a href="%s" style="color:#0b5cff;text-decoration:none;">%s</a>
                    </h2>

                    <!-- Timestamp -->
                    <p style="color:#6e7680;font-size:16px;margin:8px 0 24px 0;">%s (UTC)</p>
                    
                    <!-- Button -->
                    <div style="margin-top:32px;text-align:center;">
                        <a href="%s" style="background:#0b5cff;border-radius:20px;color:#fff;padding:12px 40px;text-decoration:none;">
                            View outage
                        </a>
                    </div>
                </div>
                
                <!-- Footer -->
                <div style="font-size:12px;text-align:center;padding:24px 32px;color:rgba(4,4,19,0.56);">
                    <p>&copy;2024 Zoom Video Communications, Inc.</p>
                    <p>55 Almaden Blvd, 6th Floor, San Jose, CA 95113<br>+1.888.799.9666</p>
                </div>
            </div>
        </body>
        </html>
        """, userName, link, title, time, link);
    }




    private String buildCommentNoticeHtml(String userName, String link, String title,
                                          String time, String summary, String comment) {
        return String.format("""
        <!DOCTYPE html>
        <html lang="en-US">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
            <title>New notification in "Cube Outage Center"</title>
        </head>
        <body style="margin:0;padding:0;width:100%%;">
            <div style="background-color:#fff;margin:0 auto;max-width:1024px;">
                <div style="background-color:#0b5cff;padding:28px 32px;">
                    <img alt="LOGO" src="https://st1.zoom.us/zoom-docs/email/assets/logo_white.png" style="height:24px;">
                </div>
                <div style="font-family:Inter,sans-serif;font-size:16px;max-width:580px;padding:32px;margin:auto;">
                    <div style="display:flex;align-items:center;">
                        <img src="https://file-paa.zoom.us/xqmuP22rQPuGwV7ocsfMeg/MS4yLiECN-QGFhJmvbltua3t5_oVk2M4tAfBku0wyhlCiM3q/cube-icon.png" style="height:32px;">
                        <div style="margin-left:10px;color:#696969;font-size:20px;font-weight:700;">Cube</div>
                    </div>
                    <h1 style="font-size:30px;font-weight:700;margin:32px 0 0 0;">New notification in "Cube Outage Center"</h1>
                    <h2 style="font-size:20px;font-weight:500;margin:10px 0;">%s commented on outage <a href="%s" style="color:#0b5cff;text-decoration:none;">%s</a></h2>
                    <time style="color:#6e7680;font-size:16px;">%s (UTC)</time>
                    <blockquote style="border-left:4px solid #ffd100;padding-left:10px;margin:20px 0;">%s</blockquote>
                    <h5 style="margin-top:32px;font-size:14px;">%s</h5>
                    <pre style="white-space:pre-wrap;">%s</pre>
                    <div style="margin-top:32px;text-align:center;">
                        <a href="%s" style="background:#0b5cff;border-radius:20px;color:#fff;padding:12px 40px;text-decoration:none;">View comment</a>
                    </div>
                </div>
                <div style="font-size:12px;text-align:center;padding:24px 32px;color:rgba(4,4,19,0.56);">
                    <p>&copy;2024 Zoom Video Communications, Inc.</p>
                    <p>55 Almaden Blvd, 6th Floor, San Jose, CA 95113<br>+1.888.799.9666</p>
                </div>
            </div>
        </body>
        </html>
        """, userName, link, title, time, summary, userName, comment, link);
    }


    private String buildMentionNoticeHtml(String userName, String link, String title, String time) {
        return String.format("""
        <!DOCTYPE html>
        <html lang="en-US">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
            <title>New notification in "Cube Outage Center"</title>
        </head>
        <body style="margin:0;padding:0;width:100%%;">
            <div style="background-color:#fff;margin:0 auto;max-width:1024px;">
                <div style="background-color:#0b5cff;padding:28px 32px;">
                    <img alt="LOGO" src="https://st1.zoom.us/zoom-docs/email/assets/logo_white.png" style="height:24px;">
                </div>
                <div style="font-family:Inter,sans-serif;font-size:16px;max-width:580px;padding:32px;margin:auto;">
                    <div style="display:flex;align-items:center;">
                        <img src="https://file-paa.zoom.us/xqmuP22rQPuGwV7ocsfMeg/MS4yLiECN-QGFhJmvbltua3t5_oVk2M4tAfBku0wyhlCiM3q/cube-icon.png" style="height:32px;">
                        <div style="margin-left:10px;color:#696969;font-size:20px;font-weight:700;">Cube</div>
                    </div>
                    <h1 style="font-size:30px;font-weight:700;margin:32px 0 0 0;">New notification in "Cube Outage Center"</h1>
                    <h2 style="font-size:20px;font-weight:500;margin:10px 0;">%s mentioned you in <a href="%s" style="color:#0b5cff;text-decoration:none;">%s</a></h2>
                    <time style="color:#6e7680;font-size:16px;">%s (UTC)</time>
                    <div style="margin-top:32px;text-align:center;">
                        <a href="%s" style="background:#0b5cff;border-radius:20px;color:#fff;padding:12px 40px;text-decoration:none;">View mention</a>
                    </div>
                </div>
                <div style="font-size:12px;text-align:center;padding:24px 32px;color:rgba(4,4,19,0.56);">
                    <p>&copy;2024 Zoom Video Communications, Inc.</p>
                    <p>55 Almaden Blvd, 6th Floor, San Jose, CA 95113<br>+1.888.799.9666</p>
                </div>
            </div>
        </body>
        </html>
        """, userName, link, title, time, link);
    }




    public static String getEmailPrefix(String email) {
        if (email == null || !email.contains("@")) {
            throw new IllegalArgumentException("Invalid email address");
        }

        int atIndex = email.indexOf('@');

        return email.substring(0, atIndex);
    }

    private String getNoticeDetailLink(String outageId, String relatedId, String moduleName) {
        String detailsLink;
        if (moduleName.equals("comment") || moduleName.equals("mention")) {
            detailsLink = String.format("%soutage-center?outageId=%s&commentId=%s", cubeOutageUrlAlert, outageId, relatedId);
        } else if (moduleName.equals("edit")) {
            detailsLink = String.format("%soutage-center?outageId=%s&mentionModuleType=%s", cubeOutageUrlAlert, outageId, relatedId);
        } else if(moduleName.equals("asyncSmartAddOutage")){
            detailsLink = String.format("%soutage-center?id=%s", cubeOutageUrlAlert, outageId);
        }else {
            detailsLink = String.format("%soutage-center?outageId=%s&eventId=%s", cubeOutageUrlAlert, outageId, relatedId);
        }
        return detailsLink;
    }


    public List<OutageCommentDO> searchOutageCommentList(OutageCommentQuery commentQuery) {
        Assert.notNull(commentQuery, "input is null");
        Assert.isTrue(!StringUtils.isBlank(commentQuery.getOutageId()), "outageId is blank");
        return outageDAO.searchOutageCommentList(commentQuery.getOutageId());

    }


    public void deleteComment(String outageId, String commentId) {
        Assert.isTrue(!StringUtils.isBlank(outageId), "outageId is blank");
        Assert.isTrue(!StringUtils.isBlank(commentId), "commentId is blank");
        outageDAO.deleteParentComment(outageId,commentId);
        outageDAO.deleteChildComment(outageId,commentId);
    }

    public List<OutageEventDO> searchOutageEventList(OutageEventQuery query) {
        Assert.isTrue(!StringUtils.isBlank(query.getOutageId()), "outageId is blank");
        return outageDAO.searchOutageEventList(query.getOutageId());
    }

    public String eventAdd(OutageEventInput outageAddInput) {
        Assert.isTrue(!StringUtils.isBlank(outageAddInput.getOutageId()), "outageId is blank");
        Assert.isTrue(!StringUtils.isBlank(outageAddInput.getContent()), "content is blank");
        Assert.isTrue(!StringUtils.isBlank(outageAddInput.getOperator()), "operator is blank");
        Assert.isTrue(!(outageAddInput.getOperationTime()==null),"operationTime is null");
        Assert.isTrue(!StringUtils.isBlank(outageAddInput.getMailContent()), "mail content is blank");
        OutageEventDO outageEventDO = new OutageEventDO();
        BeanUtils.copyProperties(outageAddInput, outageEventDO);
        outageEventDO.setId(IdUtils.generateId());
        String userName = userHandler.getNameById(outageAddInput.getUserId());
        outageEventDO.setCreator(userName);
        outageEventDO.setLastModifiedBy(userName);
        //event notice
        if (StringUtils.isNotBlank(outageAddInput.getMentionedUserIds())) {
            String mentionedUserIds = outageAddInput.getMentionedUserIds();
            notice(outageAddInput.getOutageId(), mentionedUserIds, userName,outageAddInput.getMailContent(),outageAddInput.getMailContent(),outageEventDO.getId(),"event");
        }
        if(StringUtils.isBlank(outageAddInput.getOperator())){
            outageEventDO.setOperator("system");
        }
        outageDAO.addEvent(outageEventDO);
        return outageEventDO.getId();
    }

    public void eventEdit(OutageEventInput outageAddInput) {
        Assert.isTrue(!StringUtils.isBlank(outageAddInput.getId()), "eventId is blank");
        Assert.isTrue(!StringUtils.isBlank(outageAddInput.getOutageId()), "outageId is blank");
        Assert.isTrue(!StringUtils.isBlank(outageAddInput.getContent()), "content is blank");
        Assert.isTrue(!(outageAddInput.getOperationTime()==null),"operationTime is null");
        Assert.isTrue(!StringUtils.isBlank(outageAddInput.getMailContent()), "mail content is blank");
        OutageEventDO outageEventDO = new OutageEventDO();
        BeanUtils.copyProperties(outageAddInput, outageEventDO);
        String userName = userHandler.getNameById(outageAddInput.getUserId());
        outageEventDO.setLastModifiedBy(userName);
        outageEventDO.setEditSource(OutageEditTypeEnum.Manual.getCode());
        //event notice
        if (StringUtils.isNotBlank(outageAddInput.getMentionedUserIds())) {
            String mentionedUserIds = outageAddInput.getMentionedUserIds();
            notice(outageAddInput.getOutageId(), mentionedUserIds, userName,outageAddInput.getMailContent(),outageAddInput.getContent(),outageEventDO.getId(),"event");
        }
        if(StringUtils.isBlank(outageAddInput.getOperator())){
            outageEventDO.setOperator("system");
        }
        outageDAO.editEvent(outageEventDO);
    }

    public void eventDelete(OutageEventDeleteInput outageEventDeleteInput) {
        Assert.isTrue(!StringUtils.isBlank(outageEventDeleteInput.getId()), "id is blank");
        Assert.isTrue(!StringUtils.isBlank(outageEventDeleteInput.getOutageId()), "outageId is blank");
        OutageEventDO outageEventDO=new OutageEventDO();
        BeanUtils.copyProperties(outageEventDeleteInput,outageEventDO);
        outageDAO.deleteEvent(outageEventDO);
    }

    public List<OutageAlarmDO> searchOutageAlarmList(PageQuery<OutageAlarmQuery> pageQuery) {
        Assert.isTrue(!StringUtils.isBlank(pageQuery.getQueryPara().getOutageId()), "outageId is blank");
        return outageDAO.searchOutageAlarmList(pageQuery.getQueryPara().getOutageId(), pageQuery.getPageSize() * (pageQuery.getPageIndex() - 1), pageQuery.getPageSize());
    }

    public Integer searchOutageAlarmCount(PageQuery<OutageAlarmQuery> pageQuery) {

        return outageDAO.getOutageAlarmListCount(pageQuery.getQueryPara().getOutageId(), pageQuery.getPageSize() * (pageQuery.getPageIndex() - 1), pageQuery.getPageSize());
    }


    public List<String> alarmAdd(OutageAlarmBatchInput alarmInput) {
        Assert.isTrue(!CollectionUtils.isEmpty(alarmInput.getOutageAlarmInputLists()), "outageAlarmInput is blank");
        String userName = userHandler.getNameById(alarmInput.getUserId());
        List<OutageAlarmDO> outageAlarmDOs = alarmInput.getOutageAlarmInputLists().stream().map(alarm -> {
            if(StringUtils.isBlank(alarm.getServiceId()) || StringUtils.isBlank(alarm.getServiceName())){
                throw new SiteException(WebCodeEnum.EntityNotExisted.getCode(),"serviceId or serviceName is null,please check!");
            }
            OutageAlarmDO outageAlarmDO = new OutageAlarmDO();
            BeanUtils.copyProperties(alarm, outageAlarmDO);
            outageAlarmDO.setId(IdUtils.generateId());
            outageAlarmDO.setCreator(userName);
            outageAlarmDO.setTriggerTime(alarm.getTriggerTime());
            outageAlarmDO.setDateTime(alarm.getDateTime());
            return outageAlarmDO;
        }).collect(Collectors.toList());
        outageDAO.addAlarms(outageAlarmDOs);
        return outageAlarmDOs.stream().map(OutageAlarmDO::getId).collect(Collectors.toList());
    }


    public void alarmDelete(OutageAlarmDeleteInput input) {
        Assert.isTrue(!StringUtils.isBlank(input.getOutageId()), "outageId is blank");
        Assert.isTrue(!CollectionUtils.isEmpty(input.getIds()), "ids is empty");
        OutageAlarmDO outageAlarmDO=new OutageAlarmDO();
        BeanUtils.copyProperties(input,outageAlarmDO);
        outageDAO.deleteAlarms(outageAlarmDO.getOutageId(),input.getIds());
    }


    public List<OutageActionDO> searchOutageActionList(OutageActionQuery query) {
        Assert.isTrue(!StringUtils.isBlank(query.getOutageId()), "outageId is blank");
        return outageDAO.searchOutageActionList(query.getOutageId());

    }



    @Transactional
    public void editAction(OutageActionEditInput actionEditInput) {
        Assert.isTrue(!StringUtils.isBlank(actionEditInput.getOutageId()), "outageId is blank");
        outageDAO.batchDeleteAction(actionEditInput.getOutageId());
        List<OutageActionDO> actionList = actionEditInput.getActionList();
        if(CollectionUtils.isEmpty(actionEditInput.getActionList())){
            return;
        }
        String userName = userHandler.getNameById(actionEditInput.getUserId());
        actionList = actionList.stream().peek(outageActionDO -> {
            outageActionDO.setId(IdUtils.generateId());
            outageActionDO.setOutageId(actionEditInput.getOutageId());
            outageActionDO.setCreator(userName);
            if(StringUtils.isBlank(outageActionDO.getTitle())){
                outageActionDO.setTitle(StringUtils.EMPTY);
            }
            if(StringUtils.isBlank(outageActionDO.getComplexity())){
                outageActionDO.setComplexity(StringUtils.EMPTY);
            }
            if(StringUtils.isBlank(outageActionDO.getSeverity())){
                outageActionDO.setSeverity(StringUtils.EMPTY);
            }
        }).collect(Collectors.toList());
        outageDAO.batchInsertAction(actionList);
    }


    public OutageDO searchOutageByTitle(String title) {

        return outageDAO.searchOutageByTitle(title);
    }


    @Transactional
    public OutageAggDO smartAddOutage(SmartOutageInput input, String userId) {
//        authService.checkAuth(input);
        OutageAggDO outageAggDO= null;
        input.setRcaWiki(input.getRcaWiki().trim());
        try{
            //1.pull rcaWiki
            HttpResponse<String> response = pullRcaWiki(input,agentId,agentUrl);
            outageAggDO = DeserializeOutageAgg(response);
            //2.import outage
            outageAggImport(input, outageAggDO,userId);
        }catch (Exception e){
            monitorLogger.error("ai hub analysis rca fail",e);
        }
        return outageAggDO;
    }

    private static OutageAggDO DeserializeOutageAgg(HttpResponse<String> response) {
        String responseBody  = response.body();
        RcaSummaryOutput rcaSummaryOutput = JsonUtils.toObject(responseBody, RcaSummaryOutput.class);
        String data = rcaSummaryOutput.getData();
        return JsonUtils.toObject(data, OutageAggDO.class);
    }


    private static HttpResponse<String> pullRcaWiki(SmartOutageInput input,String agentId,String agentUrl) throws JsonProcessingException {
        Map<String, Object> requestBody = Map.of(
                "agentId", agentId,
                "question", input.getRcaWiki()
        );
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonBody = objectMapper.writeValueAsString(requestBody);

        String appkey = CubeConstants.JWT_ISSUER;
        // Replace with real token
        String token = "Bearer "+ JWTUtils.generateToken("edo_api", "edo", 3600*24L, null, null);
        HttpClient client = HttpClient.newHttpClient();
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(agentUrl))
                .header("x-edo-appkey", appkey)
                .header("Authorization", token)
                .header("Content-Type", "application/json")
                .POST(HttpRequest.BodyPublishers.ofString(jsonBody, StandardCharsets.UTF_8))
                .build();

        HttpResponse<String> response = null;

        try {
            response = client.send(request, HttpResponse.BodyHandlers.ofString());
            monitorLogger.info("Response Code: " + response.statusCode());
            monitorLogger.info("Response Body: " + response.body());
        } catch (IOException | InterruptedException e) {
            monitorLogger.error("ai hub analysis fail",e);
        }
        return response;
    }

    private void outageAggImport(SmartOutageInput input, OutageAggDO outageAggDO, String userId) {
        //2.create outage
        //2.1 add outage
        checkDuplicateOutage(input);
        OutageDO outageDO = outageAggDO.getOutageDO();
        String userName = userHandler.getNameById(userId);
        outageAggDO.setUserName(userName);
        buildOutage(outageDO, userName, input);
        outageDAO.addByRca(outageDO);

        addRcaLink(input, outageDO);
        addRcaOwner(outageDO);

        //2.2 add timeline event
        addRcaEvent(outageAggDO, outageDO, userName);

        //2.3 add action
        addRcaAction(outageAggDO, outageDO, userName);

        //2.4 add alarm
        addRcaAlarm(outageAggDO, outageDO, userName);
    }

    private void addRcaAlarm(OutageAggDO outageAggDO, OutageDO outageDO, String userName) {
        if(!CollectionUtils.isEmpty(outageAggDO.getOutageAlarmDOS())){
            outageAggDO.getOutageAlarmDOS().forEach(outageAlarmDO -> {
                outageAlarmDO.setId(IdUtils.generateId());
                outageAlarmDO.setOutageId(outageDO.getId());
                outageAlarmDO.setCreator(userName);
            });
            outageDAO.addAlarms(outageAggDO.getOutageAlarmDOS());
        }
    }

    private void addRcaAction(OutageAggDO outageAggDO, OutageDO outageDO, String userName) {
        for(OutageActionDO actionDO: outageAggDO.getOutageActionDOs()){
            actionDO.setId(IdUtils.generateId());
            actionDO.setOutageId(outageDO.getId());
            if(StringUtils.isBlank(actionDO.getCreator())){
                actionDO.setCreator(userName);
            }
            if(StringUtils.isBlank(actionDO.getTitle())){
                actionDO.setTitle(StringUtils.EMPTY);
            }
            if(StringUtils.isBlank(actionDO.getComplexity())){
                actionDO.setComplexity(StringUtils.EMPTY);
            }
            if(StringUtils.isBlank(actionDO.getSeverity())){
                actionDO.setSeverity(StringUtils.EMPTY);
            }
            if(StringUtils.isBlank(actionDO.getType())){
                actionDO.setType(StringUtils.EMPTY);
            }
            if(StringUtils.isBlank(actionDO.getLink())){
                actionDO.setLink(StringUtils.EMPTY);
            }
            if(StringUtils.isBlank(actionDO.getDueDate())){
                actionDO.setDueDate(null);
            }
        }
        if(!CollectionUtils.isEmpty(outageAggDO.getOutageActionDOs())){
            outageDAO.batchInsertAction(outageAggDO.getOutageActionDOs());
        }
    }

    private void addRcaEvent(OutageAggDO outageAggDO, OutageDO outageDO, String userName) {
        List<OutageEventDO> validEvents = outageAggDO.getOutageEventDOs().stream()
                .filter(event -> StringUtils.isNotBlank(event.getContent()) && !ObjectUtils.isEmpty(event.getOperationTime()))
                .map(event -> {
                    OutageEventDO newEvent = new OutageEventDO();
                    newEvent.setId(IdUtils.generateId());
                    newEvent.setOutageId(outageDO.getId());
                    newEvent.setContent(event.getContent());
                    newEvent.setOperationTime(event.getOperationTime());
                    newEvent.setLastModifiedBy(userName);
                    newEvent.setCreator(userName);

                    newEvent.setOperator(StringUtils.isBlank(event.getOperator()) ? "system" : event.getOperator());
                    newEvent.setMentionedUserIds(StringUtils.defaultIfBlank(event.getMentionedUserIds(), StringUtils.EMPTY));
                    newEvent.setEditSource(OutageEditTypeEnum.AI.getCode());
                    return newEvent;
                })
                .collect(Collectors.toList());

        if (!validEvents.isEmpty()) {
            outageDAO.addEvents(validEvents);
        }
    }

    private void addRcaOwner(OutageDO outageDO) {
        if (!CollectionUtils.isEmpty(outageDO.getOutageOwners())) {
            List<OutageOwnerDO> outageOwnerDOS =  outageDO.getOutageOwners().stream().map(outageOwnerInput -> OutageOwnerDO.builder().id(IdUtils.generateId()).outageId(outageDO.getId()).owner(outageOwnerInput.getOwner()).managers(outageOwnerInput.getManagers()).build()).collect(Collectors.toList());
            outageDAO.addOwners(outageOwnerDOS);
        }
    }

    private void addRcaLink(SmartOutageInput input, OutageDO outageDO) {
        if (!CollectionUtils.isEmpty(outageDO.getOutageLinks())) {
            List<OutageLinkDO> outageLinks = outageDO.getOutageLinks();
            for (OutageLinkDO outageLinkDO : outageDO.getOutageLinks()) {
                if (StringUtils.isBlank(outageLinkDO.getLinkUrl()) || StringUtils.isBlank(outageLinkDO.getLinkDesc())) {
                    throw new SiteException(WebCodeEnum.EntityIsNull);
                }
            }
            List<OutageLinkDO> outageLinkDOS = outageLinks.stream().map(outageLinkInput -> OutageLinkDO.builder().id(IdUtils.generateId()).outageId(outageDO.getId()).linkUrl(outageLinkInput.getLinkUrl()).linkDesc(outageLinkInput.getLinkDesc()).build()).collect(Collectors.toList());
            outageLinkDOS.add(OutageLinkDO.builder().id(IdUtils.generateId()).outageId(outageDO.getId()).linkDesc("RCA Wiki").linkUrl(input.getRcaWiki()).build());
            outageDAO.addLinks(outageLinkDOS);
        }
    }

    private void checkDuplicateOutage(SmartOutageInput input) {
        List<OutageDO> outageDOs = outageDAO.listByRcaWikiUrl(input.getRcaWiki());
        if(!CollectionUtils.isEmpty(outageDOs)){
            outageDAO.deleteByRcaWikiUrl(input.getRcaWiki());
        }
    }

    private static void buildOutage(OutageDO outageDO, String userName,SmartOutageInput input) {
        outageDO.setRcaWikiUrl(input.getRcaWiki());
        outageDO.setId(IdUtils.generateId());
        outageDO.setCreator(userName);
        outageDO.setLastModifiedBy(userName);
        outageDO.setEditSource(OutageEditTypeEnum.AI.getCode());
        if (StringUtils.isBlank(outageDO.getTitle())) {
            outageDO.setTitle(StringUtils.EMPTY);
        }
        if (StringUtils.isBlank(outageDO.getSummary())) {
            outageDO.setSummary(StringUtils.EMPTY);
        }
        if (StringUtils.isBlank(outageDO.getLevel())) {
            outageDO.setLevel(StringUtils.EMPTY);
        }
        if (StringUtils.isBlank(outageDO.getStatus())) {
            outageDO.setStatus("Draft");
        }
        if (StringUtils.isBlank(outageDO.getCustomerImpact())) {
            outageDO.setCustomerImpact(StringUtils.EMPTY);
        }
        if (StringUtils.isBlank(outageDO.getServiceIds())) {
            outageDO.setServiceIds(StringUtils.EMPTY);
        }
        if (StringUtils.isBlank(outageDO.getRootCause())) {
            outageDO.setRootCause(StringUtils.EMPTY);
        }
        if (StringUtils.isBlank(outageDO.getOutageHandler())) {
            outageDO.setOutageHandler(StringUtils.EMPTY);
        }
        if (StringUtils.isBlank(outageDO.getDoWell())) {
            outageDO.setDoWell(StringUtils.EMPTY);
        }
        if (StringUtils.isBlank(outageDO.getDoPoorly())) {
            outageDO.setDoPoorly(StringUtils.EMPTY);
        }
        if (StringUtils.isBlank(outageDO.getFiveWhy())) {
            outageDO.setFiveWhy(StringUtils.EMPTY);
        }
        if (StringUtils.isBlank(outageDO.getType())) {
            outageDO.setType(StringUtils.EMPTY);
        }
        if (StringUtils.isBlank(outageDO.getImpactedService())) {
            outageDO.setImpactedService(StringUtils.EMPTY);
        }
        if (StringUtils.isBlank(outageDO.getDetectDesc())) {
            outageDO.setDetectDesc(StringUtils.EMPTY);
        }
        if (StringUtils.isBlank(outageDO.getDetectChannel())) {
            outageDO.setDetectChannel(StringUtils.EMPTY);
        }
        if (StringUtils.isBlank(outageDO.getRecoveryMode())) {
            outageDO.setRecoveryMode(StringUtils.EMPTY);
        }
        if (StringUtils.isBlank(outageDO.getEarlyDetectedBy())) {
            outageDO.setEarlyDetectedBy(StringUtils.EMPTY);
        }
        if (StringUtils.isBlank(outageDO.getCreator())) {
            outageDO.setCreator(StringUtils.EMPTY);
        }
        if (StringUtils.isBlank(outageDO.getLastModifiedBy())) {
            outageDO.setLastModifiedBy(StringUtils.EMPTY);
        }
    }
}
