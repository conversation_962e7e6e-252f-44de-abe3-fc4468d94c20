package us.zoom.cube.site.biz.ai;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Forward configuration class
 * Used to manage whitelist configuration for forward requests
 */
@Data
@Slf4j
public class ForwardConfig {
    
    private String userName;
    private String tenantId;
    private List<String> whitelistUrls;
    
    /**
     * Parse configuration from JSON string
     * 
     * @param configJson configuration JSON string
     * @return ForwardConfig configuration object
     * @throws IllegalArgumentException when configuration format is invalid
     */
    public static ForwardConfig fromJson(String configJson) {
        if (!StringUtils.hasText(configJson)) {
            throw new IllegalArgumentException("Configuration JSON cannot be empty");
        }
        
        try {
            JSONObject jsonObject = JSONObject.parseObject(configJson);
            ForwardConfig config = new ForwardConfig();
            
            config.setUserName(jsonObject.getString("userName"));
            config.setTenantId(jsonObject.getString("tenantId"));
            
            JSONArray urlArray = jsonObject.getJSONArray("url");
            if (urlArray != null && !urlArray.isEmpty()) {
                List<String> urls = urlArray.stream()
                        .map(Object::toString)
                        .filter(StringUtils::hasText)
                        .collect(Collectors.toList());
                config.setWhitelistUrls(urls);
            } else {
                config.setWhitelistUrls(Collections.emptyList());
            }
            
            return config;
        } catch (Exception e) {
            log.error("Failed to parse forward config JSON: {}", configJson, e);
            throw new IllegalArgumentException("Invalid configuration format", e);
        }
    }
    
    /**
     * Check if target address is in whitelist
     * 
     * @param target target address
     * @return boolean whether target is in whitelist
     */
    public boolean isTargetAllowed(String target) {
        if (!StringUtils.hasText(target) || whitelistUrls == null || whitelistUrls.isEmpty()) {
            return false;
        }
        
        return whitelistUrls.stream().anyMatch(target::contains);
    }
    
    /**
     * Validate configuration validity
     * 
     * @return boolean whether configuration is valid
     */
    public boolean isValid() {
        return whitelistUrls != null && !whitelistUrls.isEmpty();
    }
    
    /**
     * Get configuration summary information (for logging)
     * 
     * @return String configuration summary
     */
    public String getConfigSummary() {
        return String.format("userName=%s, tenantId=%s, whitelistSize=%d", 
                userName, tenantId, whitelistUrls != null ? whitelistUrls.size() : 0);
    }
} 