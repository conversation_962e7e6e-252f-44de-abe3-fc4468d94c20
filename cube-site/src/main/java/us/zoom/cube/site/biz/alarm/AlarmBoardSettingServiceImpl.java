package us.zoom.cube.site.biz.alarm;

import com.fasterxml.jackson.core.type.TypeReference;
import com.zoom.op.monitor.domain.alarm.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.biz.SysParaService;
import us.zoom.cube.site.core.AlarmBoardFilterHandler;
import us.zoom.cube.site.core.AlarmBoardSettingHandler;
import us.zoom.cube.site.core.MetricsHandler;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.core.config.ClickhouseHandlerFactory;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.BusinessException;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.dto.AlarmBoardSettingDTO;
import us.zoom.cube.site.lib.input.AlarmBoardDropdownValuesInput;
import us.zoom.cube.site.lib.output.alarm.AlarmBoardFilterSummary;
import us.zoom.cube.site.lib.output.alarm.AlarmBoardFilterSummaryData;
import us.zoom.cube.site.lib.query.TimeRangeQuery;
import us.zoom.infra.dao.model.MetricsDO;
import us.zoom.infra.dao.model.TenantDO;
import us.zoom.infra.thread.NamedThreadFactory;
import us.zoom.cube.lib.utils.JsonUtils;

import jakarta.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static us.zoom.cube.site.biz.SysParaService.SETTING_TAG;
import static us.zoom.cube.site.biz.alarm.AlarmBoardCheckServiceImpl.ALLOW_TAG;
import static us.zoom.cube.site.biz.alarm.AlarmBoardCheckServiceImpl.NP_LIMIT_SERVICE;

@Service
@Slf4j
@Lazy
public class AlarmBoardSettingServiceImpl implements AlarmBoardSettingService {

    public static final String SETTING_DTO = "settingDTO";

    public static final String SETTING_ITEM_DTO = "settingItemDTO";

    public static final String DEFAULT_LEVEL = "Level";

    public static final String DEFAULT_ALARM = "Alarm";

    public static final String DEFAULT_METRIC = "Metric";

    @Autowired
    private AlarmBoardSettingHandler alarmBoardSettingHandler;

    @Autowired
    private AlarmBoardCheckService alarmBoardCheckService;

    @Autowired
    private TenantHandler tenantHandler;

    @Autowired
    private MetricsHandler metricsHandler;

    @Autowired
    private SysParaService sysParaService;

    @Autowired
    private ClickhouseHandlerFactory clickhouseHandlerFactory;

    @Autowired
    private AlarmBoardFilterHandler alarmBoardFilterHandler;

    @PostConstruct
    public void init() {
        Executors.newScheduledThreadPool(1, new NamedThreadFactory("alarm insight scheduler ")).scheduleAtFixedRate(new Runnable() {
            @Override
            public void run() {
                try {
                    sync();
                } catch (Exception e) {
                    log.error("sync default alarm board setting error: ", e);
                }
            }
        }, 5, 5, TimeUnit.MINUTES);
    }

    @Transactional(rollbackFor = Exception.class)
    public void sync() {
        List<String> exists = alarmBoardSettingHandler.getAll();
        tenantHandler.listAll().stream().map(TenantDO::getId).filter(a -> !exists.contains(a)).forEach(this::insert);
    }

    @Override
    public ResponseObject update(AlarmBoardSettingDTO alarmBoardSettingDTO) {
        AlarmBoardSetting alarmBoardSetting = new AlarmBoardSetting();
        BeanUtils.copyProperties(alarmBoardSettingDTO, alarmBoardSetting);
        alarmBoardSetting.setHighlightingAlarmList(JsonUtils.toJsonString(alarmBoardSettingDTO.getHighlightingAlarmList()));

        alarmBoardCheckService.checkAlarmBoardSettingDTO(alarmBoardSettingDTO);
        List<AlarmBoardSettingItem> alarmBoardSettingItemList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(alarmBoardSettingDTO.getItems())) {
            alarmBoardSettingDTO.getItems().forEach(alarmBoardSettingItemDTO -> {
                AlarmBoardSettingItem alarmBoardSettingItem = new AlarmBoardSettingItem();
                BeanUtils.copyProperties(alarmBoardSettingItemDTO, alarmBoardSettingItem);
                alarmBoardSettingItemList.add(alarmBoardSettingItem);
            });
        }

        boolean needUpdate = StringUtils.isNotEmpty(alarmBoardSettingDTO.getId()) || !CollectionUtils.isEmpty(alarmBoardSettingDTO.getItems());
        if (needUpdate) {
            update(alarmBoardSetting, alarmBoardSettingItemList);
        } else {
            insert(alarmBoardSettingDTO.getServiceId());
        }

        return ResponseObject.success(true);
    }

    @Transactional(rollbackFor = Exception.class)
    public void update(AlarmBoardSetting alarmBoardSetting, List<AlarmBoardSettingItem> alarmBoardSettingItemList) {
        alarmBoardSettingHandler.update(alarmBoardSetting);

        // alarm_board_setting_item, delete first than insert
        alarmBoardSettingHandler.deleteItemBySettingId(alarmBoardSetting.getId());
        alarmBoardSettingItemList.forEach(alarmBoardSettingItem -> {
            if (StringUtils.isEmpty(alarmBoardSettingItem.getId())) {
                alarmBoardSettingItem.setId(IdUtils.generateId());
                alarmBoardSettingItem.setAlarmBoardSettingId(alarmBoardSetting.getId());
            }
            alarmBoardSettingHandler.saveItem(alarmBoardSettingItem);
        });

    }

    public void insert(String serviceId) {
        Map<String, Object> result = createAlarmBoardSettingDTO(serviceId);
        alarmBoardSettingHandler.saveDefaultSetting((AlarmBoardSetting) result.get(SETTING_DTO), (List<AlarmBoardSettingItem>) result.get(SETTING_ITEM_DTO));
    }

    @Override
    public ResponseObject<AlarmBoardSettingDTO> get(String serviceId) {
        try {
            insertDefaultValue(serviceId);
            AlarmBoardSettingDTO alarmBoardSettingDTO = new AlarmBoardSettingDTO();
            AlarmBoardSetting alarmBoardSettingResponse = alarmBoardSettingHandler.getSettingByServiceId(serviceId);
            if (Objects.isNull(alarmBoardSettingResponse)) {
                return ResponseObject.fail("failed to create configuration, please try again");
            }

            BeanUtils.copyProperties(alarmBoardSettingResponse, alarmBoardSettingDTO);
            if (StringUtils.isNotEmpty(alarmBoardSettingResponse.getHighlightingAlarmList())) {
                alarmBoardSettingDTO.setHighlightingAlarmList(JsonUtils.toObjectByTypeRef
                        (alarmBoardSettingResponse.getHighlightingAlarmList(), new TypeReference<List<AlarmBoardSettingDTO.HighlightingAlarm>>() {
                        }));
            }

            List<AlarmBoardSettingItem> alarmBoardSettingItemList = alarmBoardSettingHandler.getItemBySettingId(alarmBoardSettingDTO.getId());
            if (!CollectionUtils.isEmpty(alarmBoardSettingItemList)) {
                List<AlarmBoardSettingDTO.Item> items = new ArrayList<>();
                alarmBoardSettingItemList.forEach(alarmBoardSettingItem -> {
                    AlarmBoardSettingDTO.Item item = new AlarmBoardSettingDTO.Item();
                    BeanUtils.copyProperties(alarmBoardSettingItem, item);
                    items.add(item);
                });
                alarmBoardSettingDTO.setItems(items);
            }
            return ResponseObject.success(alarmBoardSettingDTO);
        } catch (Exception e) {
            log.error("get alarmboardsetting error, error reason is = {}, serviceId= {}", e.getMessage(), serviceId);
            return ResponseObject.fail(e.getMessage());
        }
    }

    @Override
    public ResponseObject<List<String>> getAllTagByServiceId(String serviceId) {
        TenantDO tenantDO = tenantHandler.getTenantById(serviceId);
        Assert.notNull(tenantDO, "serviceId is wrong");
        List<MetricsDO> metricsDOS = metricsHandler.getMetricsByTenant(serviceId);
        List<String> result = new ArrayList<>();
        Optional.ofNullable(metricsDOS).orElse(Collections.emptyList()).forEach(m -> {
            String[] tag = m.getTagNames().split(",");
            result.addAll(Arrays.asList(tag));
        });
        Map alarmBoardCache = sysParaService.getAlarmBoardCache(SETTING_TAG);
        if (alarmBoardCache.isEmpty()) {
            return ResponseObject.success(new ArrayList<>());
        }
        List<String> tagList = Arrays.asList(alarmBoardCache.get(ALLOW_TAG).toString().split(","));
        List<String> noLimitSerive = Arrays.asList(alarmBoardCache.get(NP_LIMIT_SERVICE).toString().split(","));
        if (noLimitSerive.contains(tenantDO.getName())) {
            return ResponseObject.success(result.stream().distinct().collect(Collectors.toList()));
        } else {
            return ResponseObject.success(result.stream()
                    .distinct()
                    .filter(list -> tagList.contains(list))
                    .collect(Collectors.toList()));
        }
    }

    @Override
    public ResponseObject<Map<String, Object>> getDropdownValues(AlarmBoardDropdownValuesInput alarmBoardDropdownValuesInput) {
        AlarmBoardSettingItem alarmBoardSettingItem = alarmBoardSettingHandler.getItemById(alarmBoardDropdownValuesInput.getSettingItemId());
        if (Objects.isNull(alarmBoardSettingItem) || AlarmBoardSettingItemType.system.value() == alarmBoardSettingItem.getType()
                || AlarmBoardSettingItemSubType.text.value() == alarmBoardSettingItem.getSubType()) {
            return ResponseObject.fail("This item cannot get tagvalues");
        }
        String optionsOrder = alarmBoardSettingItem.getOptionsOrder();
        switch (alarmBoardSettingItem.getSubType()) {
            case 0:
                return getTagValues(alarmBoardDropdownValuesInput, optionsOrder);
            case 1:
                Map<String, Object> result = new HashMap<>();
                result.put("settingItemId", alarmBoardSettingItem.getAlarmBoardSettingId());
                result.put("valueList", getSortList(Arrays.asList(optionsOrder.split(",")), Arrays.asList(alarmBoardSettingItem.getConfigValue().split(","))));
                return ResponseObject.success(result);
            default:
                return ResponseObject.fail("sorry, invalid input");
        }
    }

    public static List<String> getSortList(List<String> firstList, List<String> secondList) {
        List<String> result = new ArrayList<>();
        result.addAll(firstList);
        result.addAll(secondList);
        result.stream().distinct().collect(Collectors.toList());
        result.removeIf(StringUtils::isEmpty);
        return result;
    }

    private ResponseObject<Map<String, Object>> getTagValues(AlarmBoardDropdownValuesInput alarmBoardDropdownValuesInput, String optionsOrder) {
        Map<String, Object> result = new HashMap<>();
        List<String> userDefinition = Arrays.asList(optionsOrder.split(","));

        TimeRangeQuery timeRangeQuery = new TimeRangeQuery();
        timeRangeQuery.setBegin(alarmBoardDropdownValuesInput.getBegin());
        timeRangeQuery.setEnd(alarmBoardDropdownValuesInput.getEnd());
        timeRangeQuery.setServiceId(alarmBoardDropdownValuesInput.getServiceId());
        List<AlarmBoardFilterSummary> alarmBoardFilterSummaries = alarmBoardFilterHandler.getFilterAggregationSummary(timeRangeQuery, alarmBoardDropdownValuesInput.getSettingItemId());

        List<String> tableValue = new ArrayList<>();
        if (!CollectionUtils.isEmpty(alarmBoardFilterSummaries)) {
            List<AlarmBoardFilterSummaryData> data = alarmBoardFilterSummaries.get(0).getData();
            data.forEach(d -> {
                tableValue.add(d.getLabel());
            });
        }

        List<String> finalList = getSortList(userDefinition, tableValue);
        result.put("settingItemId", alarmBoardDropdownValuesInput.getSettingItemId());
        result.put("valueList", finalList);
        return ResponseObject.success(result);
    }

    @Override
    public void ifNotExistInsert(String serviceId) {
        insertDefaultValue(serviceId);
    }

    private boolean insertDefaultValue(String serviceId) {
        try {
            boolean exist = alarmBoardCheckService.checkNotExistDefaultSetting(serviceId);
            if (exist) {
                Map<String, Object> result = createAlarmBoardSettingDTO(serviceId);
                alarmBoardSettingHandler.saveDefaultSetting((AlarmBoardSetting) result.get(SETTING_DTO), (List<AlarmBoardSettingItem>) result.get(SETTING_ITEM_DTO));
            }
            return true;
        } catch (Exception e) {
            log.error("saveDefaultSetting error, serviceId = {}", serviceId, e);
            throw new BusinessException("save default setting error, reason = {}", e.getMessage());
        }
    }

    private Map<String, Object> createAlarmBoardSettingDTO(String serviceId) {
        Map<String, Object> result = new HashMap<>();
        AlarmBoardSetting alarmBoardSetting = new AlarmBoardSetting();
        alarmBoardSetting.setServiceId(serviceId);
        String alarmBoardSettingId = IdUtils.generateId();
        alarmBoardSetting.setId(alarmBoardSettingId);
        alarmBoardSetting.setDefaultTimeRangeInSecond(86400L);
        alarmBoardSetting.setAlarmingTimeInMinute(5L);
        alarmBoardSetting.setRefreshDurationInSecond(60L);
        result.put(SETTING_DTO, alarmBoardSetting);

        List<AlarmBoardSettingItem> alarmBoardSettingItemList = new ArrayList<>();
        AlarmBoardSettingItem alarmBoardSettingItemLevel = new AlarmBoardSettingItem();
        alarmBoardSettingItemLevel.setId(IdUtils.generateId());
        alarmBoardSettingItemLevel.setName(DEFAULT_LEVEL);
        alarmBoardSettingItemLevel.setOrder(0);
        alarmBoardSettingItemLevel.setAlarmBoardSettingId(alarmBoardSettingId);
        alarmBoardSettingItemLevel.setType(AlarmBoardSettingItemType.system.value());
        alarmBoardSettingItemLevel.setRenderOption(AlarmBoardSettingItemRenderOption.list.value());
        alarmBoardSettingItemList.add(alarmBoardSettingItemLevel);

        AlarmBoardSettingItem alarmBoardSettingItemAlarm = new AlarmBoardSettingItem();
        alarmBoardSettingItemAlarm.setName(DEFAULT_ALARM);
        alarmBoardSettingItemAlarm.setId(IdUtils.generateId());
        alarmBoardSettingItemAlarm.setOrder(1);
        alarmBoardSettingItemAlarm.setAlarmBoardSettingId(alarmBoardSettingId);
        alarmBoardSettingItemAlarm.setType(AlarmBoardSettingItemType.system.value());
        alarmBoardSettingItemAlarm.setRenderOption(AlarmBoardSettingItemRenderOption.dropdown.value());
        alarmBoardSettingItemList.add(alarmBoardSettingItemAlarm);

        AlarmBoardSettingItem alarmBoardSettingItemMetric = new AlarmBoardSettingItem();
        alarmBoardSettingItemMetric.setName(DEFAULT_METRIC);
        alarmBoardSettingItemMetric.setId(IdUtils.generateId());
        alarmBoardSettingItemMetric.setOrder(2);
        alarmBoardSettingItemMetric.setAlarmBoardSettingId(alarmBoardSettingId);
        alarmBoardSettingItemMetric.setType(AlarmBoardSettingItemType.system.value());
        alarmBoardSettingItemMetric.setRenderOption(AlarmBoardSettingItemRenderOption.dropdown.value());
        alarmBoardSettingItemList.add(alarmBoardSettingItemMetric);
        result.put(SETTING_ITEM_DTO, alarmBoardSettingItemList);
        return result;
    }
}
