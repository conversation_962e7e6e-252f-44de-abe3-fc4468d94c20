package us.zoom.cube.site.biz;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.async.mq.openapi.model.base.Result;
import us.zoom.async.mq.openapi.model.params.topic.TopicGroupParam;
import us.zoom.async.mq.openapi.model.result.consumer.ConsumerGroupDescriptionResult;
import us.zoom.async.mq.openapi.model.result.datacenter.DcResult;
import us.zoom.async.mq.openapi.model.result.topic.TopicResult;
import us.zoom.cube.lib.common.*;
import us.zoom.cube.site.biz.syspara.InputQueueRecommendParaService;
import us.zoom.cube.site.core.*;
import us.zoom.cube.site.infra.AsyncMqOpenApiClientWrapper;
import us.zoom.cube.site.infra.enums.WebCodeEnum;
import us.zoom.cube.site.infra.utils.AsyncMqUtils;
import us.zoom.cube.site.infra.utils.EnvUtils;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.IdPara;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.*;
import us.zoom.cube.site.lib.output.AsyncMQSubscriberInfoOut;
import us.zoom.cube.site.lib.output.PageResult;
import us.zoom.cube.site.lib.query.AsyncMQSubscribeQuery;
import us.zoom.cube.site.lib.query.AsyncMqQueueQuery;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.service.DistributedLockDAO;
import us.zoom.infra.thread.CubeThreadPool;
import us.zoom.infra.utils.AsyncUtils;
import us.zoom.infra.utils.Instance;
import us.zoom.infra.utils.IpUtils;
import us.zoom.cube.lib.utils.JsonUtils;

import jakarta.validation.Valid;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> zheng
 */
@Service
@Slf4j
public class AsyncMQService {

    @Autowired
    private AuthService authService;

    @Autowired
    private AsyncMqQueueHandler asyncMqQueueHandler;

    @Autowired
    private UnitTagHandler unitTagHandler;

    @Autowired
    private DataParserHandler dataParserHandler;

    @Autowired
    private AsyncmqClusterHandler asyncmqClusterHandler;

    @Autowired
    private InputQueueApprovalRecommendService inputQueueApprovalRecommendService;

    @Autowired
    private DataFlowHandler dataFlowHandler;

    @Autowired
    private DistributedLockDAO distributedLockDAO;

    @Autowired
    private InputQueueRecommendParaService inputQueueRecommendParaService;

    private static final int LOCK_TIMEOUT = 60;

    public static final String REFRESH_TOPIC_LOCK_TYPE = "input_queue_refresh";

    @Scheduled(cron = "0 0 */1 * * ?")
    public void syncAllTopicInfoFromAsyncMq() {
        try {
            List<DistributedLockDO> distributedLockDOS = distributedLockDAO.listByType(REFRESH_TOPIC_LOCK_TYPE);
            if (CollectionUtils.isEmpty(distributedLockDOS)) {
                return;
            }
            Integer lockResult = distributedLockDAO.lockWithType(REFRESH_TOPIC_LOCK_TYPE, distributedLockDOS.get(0).getHandler(), IpUtils.getLocalIP(), LOCK_TIMEOUT);
            if (lockResult > 0) {
                syncTopicInfoFromAsyncMq(Maps.newHashMap());
            }
        } catch (Exception e) {
            log.error("sync data from asyncMq error", e);
        }
    }


    private void syncTopicInfoFromAsyncMq(Map<String, List<String>> mqClusterIdTopicNamesMap) {
        log.info("start sync inputQueue topicInfo from asyncMq");

        // refresh all if map is empty
        if (MapUtils.isEmpty(mqClusterIdTopicNamesMap)) {
            Map<String, AsyncMqOpenApiClientWrapper> idToOpenApiClientMap = asyncmqClusterHandler.getClusterIdToOpenApiClientMap();
            idToOpenApiClientMap.forEach((asyncMqClusterId, clientWrapper) -> {
                syncTopicInfoFromAsyncMq(asyncMqClusterId, clientWrapper, Lists.newArrayList());
            });
        } else {
            mqClusterIdTopicNamesMap.forEach((asyncMqClusterId, refreshTopicNames) -> {
                syncTopicInfoFromAsyncMq(asyncMqClusterId, asyncmqClusterHandler.getClientByClusterId(asyncMqClusterId), refreshTopicNames);
            });
        }
    }

    private void syncTopicInfoFromAsyncMq(String asyncMqClusterId, AsyncMqOpenApiClientWrapper wrapper, List<String> refreshTopicNames) {
        List<AsyncMqQueueDO> asyncMqQueueDOS;
        if (CollectionUtils.isEmpty(refreshTopicNames)) {
            asyncMqQueueDOS = asyncMqQueueHandler.listAll(asyncMqClusterId);
        } else {
            asyncMqQueueDOS = asyncMqQueueHandler.getByInputTopics(refreshTopicNames);
        }

        if (CollectionUtils.isEmpty(asyncMqQueueDOS)) {
            log.info("have no inputQueue in this cluster, clusterId: {}", asyncMqClusterId);
            return;
        }
        // group queue by topic name
        Map<String, List<AsyncMqQueueDO>> topicNameToQueuesMap = asyncMqQueueDOS.stream().collect(Collectors.groupingBy(AsyncMqQueueDO::getTopic));

        List<String> topicNameList = Lists.newArrayList(topicNameToQueuesMap.keySet());

        List<List<String>> topicNameBatchList = AsyncMqUtils.splitIntoMultipleBatches(topicNameList);

        Set<String> changedTopicSet = Sets.newConcurrentHashSet();

        AsyncUtils.parallelFutureJoinWithoutResult(topicNameBatchList, topicNames -> {
            // get topic result by topic names
            Result<List<TopicResult>> topicRestResultList = wrapper
                    .getOpenApi()
                    .listTopicsByNames(topicNames);

            if (!topicRestResultList.isSuccess()) {
                log.error("get topic from asyncMq failed, endpoint: {}, error: {}, data: {}",
                        wrapper.getEndpoint(), topicRestResultList.getMessage(), topicRestResultList.getData());
                return;
            }

            Map<String, TopicResult> topicResultMap = Optional.ofNullable(topicRestResultList.getData()).orElse(Lists.newArrayList())
                    .stream().collect(Collectors.toMap(TopicResult::getName, Function.identity()));

            List<AsyncMqQueueDO> updateAsyncMqQueueDOList = topicNames.stream().map(topicName -> {
                TopicResult topicResult = topicResultMap.get(topicName);
                List<AsyncMqQueueDO> asyncMqQueueDOList = topicNameToQueuesMap.get(topicName);
                return asyncMqQueueDOList.stream()
                        .filter(asyncMqQueueDO -> {
                            return AsyncMqUtils.isChanged(asyncMqQueueDO.getTopicStatus(), asyncMqQueueDO.getDcName(),
                                    asyncMqQueueDO.getKafkaClusterName(), asyncMqQueueDO.getTopicId(), asyncMqQueueDO.getPartition(),
                                    topicResult);
                        }).peek(asyncMqQueueDO -> {
                            if (topicResult == null) {
                                asyncMqQueueDO.setTopicStatus(AsyncMqQueueTopicStatusEnum.notExist.getStatus());
                            } else {
                                asyncMqQueueDO.setDcName(topicResult.getDcName());
                                asyncMqQueueDO.setAsyncmqClusterId(wrapper.getAsyncMqClusterId());
                                asyncMqQueueDO.setKafkaClusterName(topicResult.getClusterName());
                                asyncMqQueueDO.setTopicStatus(AsyncMqQueueTopicStatusEnum.fromEnabledStatus(topicResult.getEnabled()).getStatus());
                                asyncMqQueueDO.setPartition(topicResult.getPartitions());
                                asyncMqQueueDO.setTopicId(topicResult.getId());
                            }
                            changedTopicSet.add(topicName);
                        }).toList();
            }).flatMap(List::stream).toList();
            asyncMqQueueHandler.batchUpdate(updateAsyncMqQueueDOList);

        }, (exception, topicNames) -> log.error("sync asyncMq data failed, topicNames: {}", topicNames, exception), CubeThreadPool.getThreadPool());
        log.info("sync from asyncMq finished, clusterId: {}, clusterEndpoint: {}, total: {}, changed: {}, changed count: {}",
                asyncMqClusterId, wrapper.getEndpoint(), asyncMqQueueDOS.size(), changedTopicSet, changedTopicSet.size());
    }

    public ResponseObject<String> syncTopicInfoFromAsyncMq(AsyncMqQueueBatchRefreshInput refreshInput) {
        authService.checkAuth(refreshInput);
        syncTopicInfoFromAsyncMq(refreshInput.getClusterIdQueueMap());
        return ResponseObject.success("success");
    }

    public ResponseObject<PageResult<AsyncMqQueueDO>> search(PageQuery<AsyncMqQueueQuery> pageQuery) {
        authService.checkAuth(pageQuery);
        AsyncMqQueueQuery query = pageQuery.getQueryPara() == null ? new AsyncMqQueueQuery() : pageQuery.getQueryPara();
        List<AsyncMqQueueDO> asyncMqQueueDOList = asyncMqQueueHandler.findByParam(query, pageQuery.getPageIndex(), pageQuery.getPageSize());
        Integer countByParam = asyncMqQueueHandler.getCountByParam(query);
        asyncMqQueueDOList.forEach(asyncMqQueueDO -> {
            if (asyncMqQueueDO.getDataParserName() != null) {
                asyncMqQueueDO.setSourceName(asyncMqQueueDO.getDataParserName());
            } else if (asyncMqQueueDO.getDataFlowName() != null) {
                asyncMqQueueDO.setSourceName(asyncMqQueueDO.getDataFlowName());
            }
        });
        return ResponseObject.success(pageQuery.getOperId(), new PageResult(countByParam, asyncMqQueueDOList));
    }

    public ResponseObject<List<AsyncMqQueueDO>> searchBySourceId(IdPara sourceId) {
        authService.checkAuth(sourceId);
        Assert.isTrue(StringUtils.isNotBlank(sourceId.getId()), "sourceId is blank");
        List<AsyncMqQueueDO> asyncMqQueueDOList = asyncMqQueueHandler.getBySourceIds(Sets.newHashSet(sourceId.getId()));
        return ResponseObject.success(asyncMqQueueDOList);
    }

    /**
     * from data parser page
     *
     * @param asyncMqQueueInput
     * @return
     * @throws Exception
     */
    public ResponseObject<String> add(@Valid AsyncMqQueueInput asyncMqQueueInput) {
        authService.checkAuth(asyncMqQueueInput);
        asyncMqQueueInput.check();
        Set<String> topicNames = asyncMqQueueHandler.getNamesBySourceIdAndAsyncMqClusterId(asyncMqQueueInput.getSourceId(), asyncMqQueueInput.getAsyncmqClusterId());
        if (topicNames.contains(asyncMqQueueInput.getTopic())) {
            return ResponseObject.fail("topic exist");
        }
        AsyncMqOpenApiClientWrapper clientWrapper = asyncmqClusterHandler.getClientByClusterId(asyncMqQueueInput.getAsyncmqClusterId());
        Result<TopicResult> restResult = clientWrapper.getOpenApi().getTopicByName(asyncMqQueueInput.getTopic());
        if (!restResult.isSuccess()) {
            throw new RuntimeException("query asyncMq failed, message: " + restResult.getMessage());
        }
        TopicResult topicResult = restResult.getData();
        if (topicResult == null) {
            throw new RuntimeException("topic not exist in asyncMq");
        }
        AsyncMqQueueDO asyncMqQueueDO = asyncMqQueueHandler.buildNewAsyncMqQueueDO(asyncMqQueueInput.getAsyncmqClusterId(),
                asyncMqQueueInput.getTenantId(), asyncMqQueueInput.getSourceType(), asyncMqQueueInput.getSourceId(),
                asyncMqQueueInput.getTopic(), topicResult);
        asyncMqQueueDO.setId(IdUtils.generateId());
        List<AsyncMqQueueDO> asyncMqQueueList = Lists.newArrayList(asyncMqQueueDO);
        inputQueueApprovalRecommendService.setRecommendConfig(asyncMqQueueList);
        asyncMqQueueHandler.batchAdd(asyncMqQueueList);
        return ResponseObject.success(asyncMqQueueDO.getId());
    }

    /**
     * edit from input queue page
     *
     * @param input
     * @return
     * @throws Exception
     */
    public ResponseObject<String> edit(@Valid UpdateAsyncMqQueueInput input) {
        authService.checkAuth(input);
        input.check();
        AsyncMqQueueDO asyncMqQueueDO = new AsyncMqQueueDO();
        BeanUtils.copyProperties(input, asyncMqQueueDO);
        asyncMqQueueDO.setEditor(input.getUserName());
        asyncMqQueueHandler.batchUpdate(Lists.newArrayList(asyncMqQueueDO));
        return ResponseObject.success("success");
    }

    public ResponseObject<String> batchUpdate(@Valid AsyncMqBatchUpdateInput input) {
        authService.checkAuth(input);
        input.check();
        asyncMqQueueHandler.batchUpdateByIds(input);
        return ResponseObject.success("success");
    }

    public ResponseObject deleteById(IdPara idPara) {
        authService.checkAuth(idPara);
        asyncMqQueueHandler.deleteByIds(Sets.newHashSet(idPara.getId()));
        return ResponseObject.success(true);
    }

    public ResponseObject batchAdd(AsyncMqQueueBatchInput batchInput) {
        authService.checkAuth(batchInput);
        batchInput.check();

        Set<String> blacklistTopicSet = getBlacklistTopic(batchInput);
        if (!CollectionUtils.isEmpty(blacklistTopicSet)) {
            return ResponseObject.failWithCode(null, JsonUtils.toJsonString(blacklistTopicSet), WebCodeEnum.BlacklistTopicError.getCode());
        }

        Map<String, List<AsyncMqQueueInput>> asyncMqClusterIdToInputListMap = batchInput.getQueueList().stream()
                .collect(Collectors.groupingBy(AsyncMqQueueInput::getAsyncmqClusterId));

        List<AsyncMqQueueDO> asyncMqQueueDOList = Lists.newArrayList();

        asyncMqClusterIdToInputListMap.forEach((asyncMqClusterId, asyncMqQueueInputList) -> {
            List<String> topicNames = Lists.newArrayList();
            Set<String> topicNameSet = asyncMqQueueHandler.getNamesBySourceIdAndAsyncMqClusterId(batchInput.getSourceId(), asyncMqClusterId);
            asyncMqQueueInputList.removeIf(input -> {
                boolean exist = topicNameSet.contains(input.getTopic());
                if (!exist) {
                    topicNames.add(input.getTopic());
                }
                return exist;
            });
            Result<List<TopicResult>> restResult = asyncmqClusterHandler.getClientByClusterId(asyncMqClusterId).getOpenApi().listTopicsByNames(topicNames);
            if (!restResult.isSuccess()) {
                throw new RuntimeException("query asyncMq failed, message: " + restResult.getMessage());
            }
            Map<String, TopicResult> topicResultMap = Optional.ofNullable(restResult.getData()).orElse(Lists.newArrayList())
                    .stream()
                    .collect(Collectors.toMap(TopicResult::getName, Function.identity()));
            asyncMqQueueInputList.forEach(input -> {
                TopicResult topicResult = topicResultMap.get(input.getTopic());
                input.setSourceId(batchInput.getSourceId());
                AsyncMqQueueDO asyncMqQueueDO = asyncMqQueueHandler.buildNewAsyncMqQueueDO(input.getAsyncmqClusterId(),
                        input.getTenantId(), input.getSourceType(), input.getSourceId(), input.getTopic(), topicResult);
                asyncMqQueueDOList.add(asyncMqQueueDO);
            });
        });
        inputQueueApprovalRecommendService.setRecommendConfig(asyncMqQueueDOList);
        asyncMqQueueHandler.batchAdd(asyncMqQueueDOList);
        return ResponseObject.success("success");
    }

    private Set<String> getBlacklistTopic(AsyncMqQueueBatchInput batchInput) {
        Set<String> blacklistTopicSet = new HashSet<>();
        batchInput.getQueueList().forEach(asyncMqQueueInput -> {
            if (inputQueueRecommendParaService.getInputQueueRecommendParam().getBlacklistTopicSet().contains(asyncMqQueueInput.getTopic())) {
                blacklistTopicSet.add(asyncMqQueueInput.getTopic());
            }
        });
        return blacklistTopicSet;
    }

    public ResponseObject<List<String>> getAsyncMqDcList() {
        Map<String, String> failedReason = Maps.newHashMap();
        Map<String, AsyncMqOpenApiClientWrapper> clientWrapperMap = asyncmqClusterHandler.getClusterIdToOpenApiClientMap();
        Set<String> dcNameSet = clientWrapperMap.values().stream()
                .map(clientWrapper -> {
                    Result<List<DcResult>> restResult = clientWrapper.getOpenApi().getDcList();
                    if (!restResult.isSuccess()) {
                        failedReason.put(clientWrapper.getAsyncMqClusterName(), restResult.getMessage());
                    }
                    return restResult.getData();
                })
                .filter(org.apache.commons.collections4.CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .map(DcResult::getName).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(dcNameSet)) {
            return ResponseObject.fail(JsonUtils.toJsonString(failedReason));
        } else {
            ResponseObject<List<String>> responseObject = ResponseObject.success(Lists.newArrayList(dcNameSet));
            if (!failedReason.isEmpty()) {
                responseObject.setMessage(JsonUtils.toJsonString(failedReason));
            }
            return responseObject;
        }
    }

    public ResponseObject getSubscriberInfo(PageQuery<AsyncMQSubscribeQuery> asyncMQSubscribePageQuery) {
//        authService.mustAdmin(asyncMQSubscribePageQuery.getUserId());
        authService.checkAuth(asyncMQSubscribePageQuery);

        AsyncMQSubscribeQuery query = asyncMQSubscribePageQuery.getQueryPara();
        checkQueryParam(query);
        String env = StringUtils.isBlank(query.getEnv()) ? "" : query.getEnv();
        String unitTagId = "";
        if (StringUtils.isNotBlank(query.getUnitTag())) {
            List<UnitTagDO> unitTagDOs = unitTagHandler.findByParam(query.getUnitTag(), TaskTypeEnum.hub.name(), 1, 1);
            Assert.isTrue(unitTagDOs != null && unitTagDOs.size() > 0, "can not find this hub unit");
            unitTagId = unitTagDOs.get(0).getId();
        }
        List<AsyncMqQueueDO> asyncMqQueueDOList = asyncMqQueueHandler.searchByParams(unitTagId, query.getTopic(), query.getGroup(), asyncMQSubscribePageQuery.getPageIndex(), asyncMQSubscribePageQuery.getPageSize());
        int total = asyncMqQueueHandler.countByParams(unitTagId, query.getTopic(), query.getGroup());

        Map<String, Map<String, AsyncMqQueueDO>> asyncMqMap = new HashMap<>();
        Map<String, List<TopicGroupParam>> clusterIdTopicGroupListMap = Maps.newHashMap();

        Set<String> dataParserIds = Sets.newHashSet();
        Set<String> dataFlowIds = Sets.newHashSet();
        Map<String, AsyncMqQueueDO> topicGroupAsyncMqMap = Maps.newHashMap();

        for (AsyncMqQueueDO asyncMqQueueDO : asyncMqQueueDOList) {
            Map<String, AsyncMqQueueDO> groupMap = asyncMqMap.computeIfAbsent(asyncMqQueueDO.getTopic(), f -> new HashMap<>());
            groupMap.put(asyncMqQueueDO.getGroupId() + env, asyncMqQueueDO);
            asyncMqMap.put(asyncMqQueueDO.getTopic(), groupMap);
            List<TopicGroupParam> topicGroupParams = clusterIdTopicGroupListMap.computeIfAbsent(asyncMqQueueDO.getAsyncmqClusterId(), id -> Lists.newArrayList());
            topicGroupParams.add(new TopicGroupParam(asyncMqQueueDO.getTopic(), asyncMqQueueDO.getGroupId() + env));

            String topicGroupKey = getTopicGroupKey(asyncMqQueueDO.getTopic(), asyncMqQueueDO.getGroupId(), env, true);
            topicGroupAsyncMqMap.put(topicGroupKey, asyncMqQueueDO);
            if (AsyncMqQueueSourceTypeEnum.DataFlow.getType().equals(asyncMqQueueDO.getSourceType())) {
                dataParserIds.add(asyncMqQueueDO.getSourceId());
            } else {
                dataFlowIds.add(asyncMqQueueDO.getSourceId());
            }
        }

        Set<String> hubUnitTagIds = Instance.ofNullable(asyncMqQueueDOList).stream().map(AsyncMqQueueDO::getUnitTagId).collect(Collectors.toSet());
        List<UnitTagDO> unitTagDOList = unitTagHandler.getByIds(hubUnitTagIds);
        Map<String, String> unitTagMap = Instance.ofNullable(unitTagDOList).stream().collect(Collectors.toMap(UnitTagDO::getId, UnitTagDO::getName));

        List<DataParserDO> dataParserDOList = dataParserHandler.getByIds(dataParserIds);
        Map<String, DataParserDO> dataParserDOMap = Instance.ofNullable(dataParserDOList).stream().collect(Collectors.toMap(DataParserDO::getId, Function.identity()));

        List<DataFlowDO> dataFlowDOList = dataFlowHandler.getDataFlowByIds(dataFlowIds);
        Map<String, DataFlowDO> dataFlowDoMap = Instance.ofNullable(dataFlowDOList).stream().collect(Collectors.toMap(DataFlowDO::getId, Function.identity()));

        List<AsyncMQSubscriberInfoOut> asyncMQSubscriberInfoOuts = new ArrayList<>();

        Map<String, String> failedReason = Maps.newHashMap();

        clusterIdTopicGroupListMap.forEach((clusterId, topicGroupList) -> {
            AsyncMqOpenApiClientWrapper clientWrapper = asyncmqClusterHandler.getClientByClusterId(clusterId);
            if (clientWrapper == null) {
                failedReason.put(clusterId, "openApiClient not exists");
                return;
            }
            Result<List<ConsumerGroupDescriptionResult>> result = clientWrapper.getOpenApi().listConsumerGroupDescriptionsByNames(topicGroupList, Maps.newHashMap());
            if (result.isSuccess()) {
                List<ConsumerGroupDescriptionResult> consumerGroupResults = result.getData();
                consumerGroupResults.forEach(consumerGroupDescription -> {
                    if (query.getLag() != null && consumerGroupDescription.getTotalLag() < query.getLag()) {
                        return;
                    }
                    AsyncMQSubscriberInfoOut asyncMQSubscriberInfoOut = new AsyncMQSubscriberInfoOut();
                    AsyncMqQueueDO asyncMqQueueDO = topicGroupAsyncMqMap.get(getTopicGroupKey(consumerGroupDescription.getTopicName(),
                            consumerGroupDescription.getGroupName(), env, false));
                    asyncMQSubscriberInfoOut.setUnitTag(unitTagMap.get(asyncMqQueueDO.getUnitTagId()));

                    asyncMQSubscriberInfoOut.setGroup(consumerGroupDescription.getGroupName());
                    asyncMQSubscriberInfoOut.setTopic(consumerGroupDescription.getTopicName());
                    if (AsyncMqQueueSourceTypeEnum.DataParser.getType().equals(asyncMqQueueDO.getSourceType())) {
                        DataParserDO dataParserDO = dataParserDOMap.get(asyncMqQueueDO.getSourceId());
                        asyncMQSubscriberInfoOut.setSourceName(dataParserDO.getName());
                        asyncMQSubscriberInfoOut.setPurposeType(DataParserPurposeEnum.fromValue(dataParserDO.getPurpose()).name());
                    } else {
                        DataFlowDO dataFlowDO = dataFlowDoMap.get(asyncMqQueueDO.getSourceId());
                        asyncMQSubscriberInfoOut.setSourceName(dataFlowDO.getName());
                    }

                    asyncMQSubscriberInfoOut.setTopicGroupDescribe(consumerGroupDescription);
                    asyncMQSubscriberInfoOuts.add(asyncMQSubscriberInfoOut);
                });
            } else {
                failedReason.put(clusterId, result.getMessage());
            }
        });


        if (!asyncMQSubscriberInfoOuts.isEmpty()) {
            ResponseObject<PageResult> responseObject = ResponseObject.success(asyncMQSubscribePageQuery.getOperId(), new PageResult(total, asyncMQSubscriberInfoOuts));
            responseObject.setMessage(JsonUtils.toJsonString(failedReason));
            return responseObject;
        } else {
            log.error("describeGroup from async mq failed, reason:" + JsonUtils.toJsonString(failedReason));
            return ResponseObject.fail("describeGroup from async mq failed");
        }
    }

    private String getTopicGroupKey(String topicName, String group, String env, boolean isRawGroup) {

        String realGroupName = isRawGroup ? EnvUtils.generateGroupId(group, env) : group;
        return topicName + "+" + realGroupName;

    }

    private void checkQueryParam(AsyncMQSubscribeQuery asyncMQSubscribeQuery) {
        String topic = asyncMQSubscribeQuery.getTopic();
        String group = asyncMQSubscribeQuery.getGroup();
        if (StringUtils.isBlank(asyncMQSubscribeQuery.getUnitTag()) && StringUtils.isBlank(group) && StringUtils.isBlank(topic)) {
            Assert.isTrue(false, "param is empty");
        }
        if (StringUtils.isNotBlank(group) && StringUtils.isBlank(topic)) {
            Assert.isTrue(false, "topic is empty");
        }
    }

    public ResponseObject listAsyncMQTopic(PageQuery<AsyncMQSubscribeQuery> asyncMQTopicPageQuery) {
//        authService.mustAdmin(asyncMQTopicPageQuery.getUserId());
        authService.checkAuth(asyncMQTopicPageQuery);
        List<String> topics = new ArrayList<>();
        int total = 0;
        if (StringUtils.isBlank(asyncMQTopicPageQuery.getQueryPara().getUnitTag())) {
            AsyncMqOpenApiClientWrapper clientWrapper = asyncmqClusterHandler.getClientByClusterId(StringUtils.EMPTY);
            Map<String, String> queryMap = Maps.newHashMap();
            queryMap.put("page", String.valueOf(asyncMQTopicPageQuery.getPageIndex() - 1));
            queryMap.put("size", String.valueOf(asyncMQTopicPageQuery.getPageSize()));
            queryMap.put("name", asyncMQTopicPageQuery.getQueryPara().getTopic());
            Result<us.zoom.async.mq.openapi.model.page.PageResult<TopicResult>> result = clientWrapper.getOpenApi().pageQueryTopics(queryMap);

            if (result.isSuccess()) {
                List<TopicResult> topicResults = result.getData().getContent();
                for (TopicResult topicResult : topicResults) {
                    topics.add(topicResult.getName());
                }
                total = (int) result.getData().getTotal();
            } else {
                log.error("searchTopicByNameLike from async mq failed, reason:" + result.getMessage());
            }
        } else {
            List<UnitTagDO> unitTagDOs = unitTagHandler.findByParam(asyncMQTopicPageQuery.getQueryPara().getUnitTag(), TaskTypeEnum.hub.name(), 1, 1);
            Assert.isTrue(unitTagDOs == null || unitTagDOs.size() > 0, "can not find this hub unit");
            String unitTagId = unitTagDOs.get(0).getId();
            topics = asyncMqQueueHandler.searchTopicByUnitTag(unitTagId, asyncMQTopicPageQuery.getQueryPara().getTopic(), asyncMQTopicPageQuery.getPageIndex() - 1, asyncMQTopicPageQuery.getPageSize());
            total = asyncMqQueueHandler.countTopicByUnitTag(unitTagId, asyncMQTopicPageQuery.getQueryPara().getTopic());
        }
        return ResponseObject.success(asyncMQTopicPageQuery.getOperId(), new PageResult(total, topics));
    }

    public ResponseObject listAsyncMQGroup(PageQuery<AsyncMQSubscribeQuery> asyncMQSubscribePageQuery) {
        List<String> groups = new ArrayList<>();
        AsyncMQSubscribeQuery query = asyncMQSubscribePageQuery.getQueryPara();
//        authService.mustAdmin(asyncMQSubscribePageQuery.getUserId());
        authService.checkAuth(asyncMQSubscribePageQuery);

        String unitTagId = "";
        if (StringUtils.isNotBlank(query.getUnitTag())) {
            List<UnitTagDO> unitTagDOs = unitTagHandler.findByParam(query.getUnitTag(), TaskTypeEnum.hub.name(), 1, 1);
            Assert.isTrue(unitTagDOs == null || unitTagDOs.size() > 0, "can not find this hub unit");
            unitTagId = unitTagDOs.get(0).getId();
        }
        List<AsyncMqQueueDO> asyncMqQueueDOList = asyncMqQueueHandler.searchByParams(unitTagId, query.getTopic(), null, asyncMQSubscribePageQuery.getPageIndex(), asyncMQSubscribePageQuery.getPageSize());
        asyncMqQueueDOList.forEach(asyncMqQueueDO -> {
            groups.add(asyncMqQueueDO.getGroupId());
        });
        int total = asyncMqQueueHandler.countByParams(unitTagId, query.getTopic(), null);
        return ResponseObject.success(asyncMQSubscribePageQuery.getOperId(), new PageResult(total, groups));
    }
}
