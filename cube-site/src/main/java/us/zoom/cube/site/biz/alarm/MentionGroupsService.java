package us.zoom.cube.site.biz.alarm;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.site.biz.template.TemplateConstant;
import us.zoom.cube.site.infra.constants.AuthConstant;
import us.zoom.cube.site.infra.enums.WebCodeEnum;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.external.xms.output.XmsMentionGroupsResponse;
import us.zoom.cube.site.lib.output.alarm.channel.MentionGroupOutput;
import us.zoom.cube.site.lib.output.alarm.channel.OpUserInfoOutput;
import us.zoom.infra.utils.JWTUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@Slf4j
public class MentionGroupsService {

    private static final String WEBHOOKS_AUDIENCE = "integration-adler";
    private static final String XMS_AUDIENCE = "xms";
    private static final String WEBOP_AUDIENCE = "web-op";

    private static final String ENDPOINT_QUERY_SQL = "SELECT value FROM alarm_channel_parameter WHERE channel_id = ? AND name = ?";
    private static final Pattern CONNECTION_ID_PATTERN = Pattern.compile("/([^/?]+)\\?");
    
    private static final String WEBHOOKS_API_PATH_CHAT = "/chat/webhooks/internal_incomingwebhook/";
    private static final String XMS_API_PATH_MENTION_GROUPS = "/xms/channel/mentionGroups/backend";
    private static final String WEBOP_API_PATH_USER_INFO = "/api/userinfo";
    
    private static final int HTTP_OK = 200;
    private static final long TOKEN_EXPIRATION_SECONDS = 3600L;

    private static final int XMS_PERMISSION_DENIED_CODE = 9;
    private static final String XMS_SERVICE_TYPE_KEY = "xms-svc-ty";
    private static final String XMS_SERVICE_TYPE_VALUE = "bkd";
    private static final String USER_ID_KEY = "uid";
    private static final String ACCOUNT_ID_KEY = "aid";
    private static final String GROUP_ID_KEY = "groupId";

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Value("${webhooks.api.endpoint}")
    private String webhooksApiEndpoint;

    @Value("${xms.api.endpoint}")
    private String xmsApiEndpoint;

    @Value("${webop.api.endpoint}")
    private String webopApiEndpoint;

    public ResponseObject<List<MentionGroupOutput>> getMentionGroupsByChannel(String channelId, String creator, String owner) {
        try {
            // get connectionId through channelId
            String connectionId = getConnectionIdByChannelId(channelId);
            if (StringUtils.isBlank(connectionId)) {
                return ResponseObject.failWithWebCode(WebCodeEnum.MentionGroupsConnectionIdNotFound);
            }

            // get groupId through connectionId
            String groupId = getGroupIdByConnectionId(connectionId);
            if (StringUtils.isBlank(groupId)) {
                return ResponseObject.failWithWebCode(WebCodeEnum.MentionGroupsGroupIdNotFound);
            }

            // get user info through owner or creator
            String userEmail = StringUtils.isNotBlank(owner) ? owner : creator;
            OpUserInfoOutput userInfo = getUserInfoByEmail(userEmail);
            if (userInfo == null) {
                return ResponseObject.failWithWebCode(WebCodeEnum.MentionGroupsUserInfoNotFound);
            }

            // get mentionGroups through groupId
            List<MentionGroupOutput> mentionGroups = getMentionGroupsByGroupId(groupId, userInfo.getUser_id(), userInfo.getAccount_id());
            if (mentionGroups == null) {
                return ResponseObject.failWithWebCode(WebCodeEnum.MentionGroupsXMSCallFailed);
            }

            if (mentionGroups.isEmpty()) {
                return ResponseObject.failWithWebCode(WebCodeEnum.MentionGroupsEmpty);
            }
            return ResponseObject.success(mentionGroups);
        } catch (MentionGroupsException e) {
            log.error("Failed to get mentionGroups, channelId: {}, error: {}", channelId, e.getMessage());
            return ResponseObject.failWithWebCode(e.getWebCodeEnum());
        } catch (Exception e) {
            log.error("Failed to get mentionGroups, channelId: {}", channelId, e);
            return ResponseObject.fail("Failed to get mentionGroups: " + e.getMessage());
        }
    }

    private OpUserInfoOutput getUserInfoByEmail(String email) {
        if (StringUtils.isBlank(email)) {
            log.error("User email is empty");
            return null;
        }

        if (StringUtils.isBlank(webopApiEndpoint)) {
            log.error("WebOp API endpoint is not configured");
            return null;
        }

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            String token = JWTUtils.generateToken("webop_api", WEBOP_AUDIENCE, TOKEN_EXPIRATION_SECONDS, null, null);
            String url = buildWebopApiUrl(email, token);
            
            HttpGet httpGet = new HttpGet(url);
            httpGet.setHeader("Content-Type", "application/json");

            try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
                return handleOpApiResponse(response, email);
            }
        } catch (Exception e) {
            log.error("Failed to call WebOp API, email: {}", email, e);
            return null;
        }
    }

    private String buildWebopApiUrl(String email, String token) {
        return webopApiEndpoint + WEBOP_API_PATH_USER_INFO + "?email=" + email + "&token=" + token;
    }

    private OpUserInfoOutput handleOpApiResponse(CloseableHttpResponse response, String email) throws Exception {
        if (response.getStatusLine().getStatusCode() == HTTP_OK) {
            String responseBody = EntityUtils.toString(response.getEntity());
            log.debug("WebOp API response: {}", responseBody);

            OpUserInfoOutput userInfo = JsonUtils.toObject(responseBody, OpUserInfoOutput.class);
            if (userInfo != null && StringUtils.isNotBlank(userInfo.getUser_id()) && StringUtils.isNotBlank(userInfo.getAccount_id())) {
                return userInfo;
            } else {
                log.error("Incomplete user information returned by WebOp API: {}", responseBody);
                return null;
            }
        } else {
            log.error("WebOp API request failed, status: {}, response: {}",
                    response.getStatusLine().getStatusCode(),
                    EntityUtils.toString(response.getEntity()));
            return null;
        }
    }

    private String getConnectionIdByChannelId(String channelId) {
        try {
            List<String> results = jdbcTemplate.queryForList(ENDPOINT_QUERY_SQL, String.class, channelId, TemplateConstant.ENDPOINT);

            if (results.isEmpty()) {
                log.error("Cannot find Endpoint parameter for channel {}", channelId);
                return null;
            }

            String endpointValue = results.get(0);
            if (StringUtils.isBlank(endpointValue)) {
                log.error("Endpoint parameter value is empty for channel {}", channelId);
                return null;
            }

            return extractConnectionId(endpointValue);
        } catch (Exception e) {
            log.error("Failed to query Endpoint parameter, channelId: {}", channelId, e);
            return null;
        }
    }

    private String extractConnectionId(String endpointValue) {
        Matcher matcher = CONNECTION_ID_PATTERN.matcher(endpointValue);
        if (matcher.find()) {
            return matcher.group(1);
        }
        log.error("Cannot extract connectionId from endpoint URL: {}", endpointValue);
        return null;
    }

    private String getGroupIdByConnectionId(String connectionId) throws MentionGroupsException {
        if (StringUtils.isBlank(webhooksApiEndpoint)) {
            log.error("Webhooks API endpoint is not configured");
            return null;
        }

        String url = webhooksApiEndpoint + WEBHOOKS_API_PATH_CHAT + connectionId;

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet httpGet = new HttpGet(url);
            String token = JWTUtils.generateToken("webhooks_api", WEBHOOKS_AUDIENCE, TOKEN_EXPIRATION_SECONDS, null, null);
            httpGet.setHeader(AuthConstant.AUTHORIZATION, AuthConstant.AUTHENTICATION_BEARER + token);
            httpGet.setHeader("Content-Type", "application/json");

            try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
                return handleWebhooksApiResponse(response, connectionId);
            }
        } catch (MentionGroupsException e) {
            throw e;
        } catch (Exception e) {
            log.error("Failed to call Webhooks API, connectionId: {}", connectionId, e);
            return null;
        }
    }

    private String handleWebhooksApiResponse(CloseableHttpResponse response, String connectionId) throws Exception {
        if (response.getStatusLine().getStatusCode() == HTTP_OK) {
            String responseBody = EntityUtils.toString(response.getEntity());
            log.debug("Webhooks API response: {}", responseBody);
            return responseBody;
        } else {
            int statusCode = response.getStatusLine().getStatusCode();
            log.error("Webhooks API request failed, status: {}, connectionId: {}, response: {}",
                    statusCode, connectionId, EntityUtils.toString(response.getEntity()));
            
            if (statusCode == 404 || statusCode == 403) {
                throw new MentionGroupsException(WebCodeEnum.MentionGroupsEnvMismatch);
            }
            return null;
        }
    }

    private List<MentionGroupOutput> getMentionGroupsByGroupId(String groupId, String uid, String aid) throws MentionGroupsException {
        if (StringUtils.isBlank(xmsApiEndpoint)) {
            log.error("XMS API endpoint is not configured");
            return null;
        }

        String url = xmsApiEndpoint + XMS_API_PATH_MENTION_GROUPS;

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(url);
            setupXmsApiRequest(httpPost, groupId, uid, aid);

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                return handleXmsApiResponse(response, groupId);
            }
        } catch (MentionGroupsException e) {
            throw e;
        } catch (Exception e) {
            log.error("Failed to call XMS API, groupId: {}", groupId, e);
            return null;
        }
    }

    private void setupXmsApiRequest(HttpPost httpPost, String groupId, String uid, String aid) throws Exception {
        Map<String, Object> payloadClaims = new HashMap<>();
        payloadClaims.put(USER_ID_KEY, uid);
        payloadClaims.put(ACCOUNT_ID_KEY, aid);
        payloadClaims.put(XMS_SERVICE_TYPE_KEY, XMS_SERVICE_TYPE_VALUE);

        String token = JWTUtils.generateToken("xms_api", XMS_AUDIENCE, TOKEN_EXPIRATION_SECONDS, null, payloadClaims);
        httpPost.setHeader(AuthConstant.AUTHORIZATION, AuthConstant.AUTHENTICATION_BEARER + token);
        httpPost.setHeader("Content-Type", "application/json");

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put(GROUP_ID_KEY, groupId);
        String requestJson = JsonUtils.toJsonString(requestBody);
        httpPost.setEntity(new StringEntity(requestJson, "UTF-8"));
    }

    private List<MentionGroupOutput> handleXmsApiResponse(CloseableHttpResponse response, String groupId) throws Exception {
        if (response.getStatusLine().getStatusCode() == HTTP_OK) {
            String responseBody = EntityUtils.toString(response.getEntity());
            log.debug("XMS API response: {}", responseBody);

            XmsMentionGroupsResponse xmsResponse = JsonUtils.toObject(responseBody, XmsMentionGroupsResponse.class);
            if (xmsResponse != null) {
                if (xmsResponse.getResult() != null && xmsResponse.getResult() == XMS_PERMISSION_DENIED_CODE) {
                    log.warn("Insufficient XMS API permission, groupId: {}, response: {}", groupId, responseBody);
                    throw new MentionGroupsException(WebCodeEnum.MentionGroupsPermissionDenied);
                }

                if (xmsResponse.getData() != null) {
                    List<MentionGroupOutput> mentionGroups = xmsResponse.getData().getMentionGroups();
                    if (mentionGroups != null) {
                        if (mentionGroups.isEmpty()) {
                            log.info("XMS API returned empty mentionGroups, groupId: {}", groupId);
                        }
                        return mentionGroups;
                    }
                }
            }
        } else {
            log.error("XMS API request failed, status: {}, response: {}",
                    response.getStatusLine().getStatusCode(),
                    EntityUtils.toString(response.getEntity()));
        }
        return null;
    }

    private static class MentionGroupsException extends Exception {
        private final WebCodeEnum webCodeEnum;

        public MentionGroupsException(WebCodeEnum webCodeEnum) {
            super(webCodeEnum.getErrMsg());
            this.webCodeEnum = webCodeEnum;
        }

        public WebCodeEnum getWebCodeEnum() {
            return webCodeEnum;
        }
    }

} 