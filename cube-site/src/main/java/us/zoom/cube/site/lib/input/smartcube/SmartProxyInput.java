package us.zoom.cube.site.lib.input.smartcube;

import com.alibaba.fastjson.JSONObject;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import us.zoom.cube.site.lib.BasePara;

import java.io.Serial;
import java.io.Serializable;

@Data
public class SmartProxyInput extends BasePara implements Serializable {
    @Serial
    private static final long serialVersionUID = 1431089534006236767L;
    @NotEmpty(message = "body not empty")
    private JSONObject body;
    @NotEmpty(message = "api not empty")
    private String api;

}
