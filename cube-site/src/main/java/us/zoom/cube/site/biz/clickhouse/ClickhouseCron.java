package us.zoom.cube.site.biz.clickhouse;

import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Iterables;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;
import us.zoom.cube.site.biz.SysParaService;
import us.zoom.cube.site.biz.clickhouse.task.TableCheckTask;
import us.zoom.cube.site.biz.syspara.clickhouse.*;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.core.config.ClickhouseHandlerFactory;
import us.zoom.infra.clickhouse.*;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.service.*;
import us.zoom.infra.enums.AggregationOperatorEnum;
import us.zoom.infra.enums.FieldTypeEnum;
import us.zoom.infra.enums.MetricsTypeEnum;
import us.zoom.infra.loader.PiiTableLoader;
import us.zoom.infra.thread.NamedThreadFactory;
import us.zoom.infra.utils.CommonSplitConstants;
import us.zoom.infra.utils.IpUtils;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.infra.utils.TimeUtils;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;

import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import java.util.concurrent.CompletableFuture;

import static us.zoom.infra.clickhouse.ClickhouseConst.*;
import static us.zoom.infra.clickhouse.ClickhouseSchema.TIME_COLUMN_NAME;
import static us.zoom.infra.clickhouse.ClickhouseSqlUtil.*;

/**
 * <AUTHOR> Wang
 * @date 2022/01/19 1:46 PM
 */

@Component
@Slf4j
@Lazy(value = false)
public class ClickhouseCron {

    private static final int INITIAL_DELAY_IN_SECONDS = 120;

    private static final int INTERVAL_IN_SECONDS = 60;

    public static final  String CLICKHOUSE_CRON="clickhouse_cron";

    public static final  String CLICKHOUSE_CRON_INC="clickhouse_cron_incremental";

    public static final  String NEW_CLUSTER_CLICKHOUSE_CRON="new_cluster_clickhouse_cron";

    private static final int LOCK_TIME_OUT_MIN = (INTERVAL_IN_SECONDS / 60);

    private static final String ALL_TABLE="all";

    private final Logger monitorLog = LoggerFactory.getLogger("Monitor");

    @Value("${cube.dataSource.clickhouse.dbcreator.thread.min-num:5}")
    private int dbCreatorThreadMinNum;

    @Value("${cube.dataSource.clickhouse.dbcreator.thread.max-num:10}")
    private int dbCreatorThreadMaxNum;

    @Value("${cube.dataSource.clickhouse.dbcreator.queue.num:500}")
    private int dbCreatorThreadQueueNum;

    @Autowired
    private DistributedLockDAO distributedLockDAO;

    @Autowired
    private MetricsDAO metricsDAO;

    @Autowired
    private MetricsFieldDAO metricsFieldDAO;

    @Autowired
    private CollectorFieldDAO collectorFieldDAO;

    @Autowired
    private CollectorMetricsDAO collectorMetricsDAO;

    @Autowired
    private TenantHandler tenantHandler;

    @Autowired
    private ClickhouseHandlerFactory clickhouseHandlerFactory;

    @Autowired
    private SysParaService sysParaService;

    @Autowired
    private ClickhouseDropParaService clickhouseDropParaService;

    private ExecutorService databaseCreatorExecutor;

    private final int INCREMENTAL_RANGE_MINUTE=20;

    @Autowired
    private ClickhouseParaService clickhouseParaService;

    @Autowired
    private ClickHouseSync clickHouseSync;

    @Autowired
    private ClickhouseClusterService clickhouseClusterService;

    @Autowired
    private ExtraTenantAndClusterRelationService extraTenantAndClusterRelationService;

    @Autowired
    private PiiTableLoader piiTableLoader;

    @Autowired
    private ClickhouseDropService clickhouseDropService;

    @Autowired
    private TLLDefaultTimeService tllDefaultTimeService;

    @Autowired
    private BatchCalculateMetricDAO batchCalculateMetricDAO;

    @Autowired
    private BatchCalculateMetricFieldDAO batchCalculateMetricFieldDAO;

    private long beginOfIncremantal;
    @Autowired
    private CollectorDAO collectorDAO;

    // Performance optimization configuration
    @Value("${cube.dataSource.clickhouse.table.create.concurrent.enabled:false}")
    private boolean concurrentTableCreationEnabled;

    @Value("${cube.dataSource.clickhouse.table.create.timeout.seconds:1800}")
    private int tableCreationTimeoutSeconds;

    @Value("${cube.dataSource.clickhouse.table.create.batch.size:10}")
    private int tableCreationBatchSize;

    @Value("${cube.dataSource.clickhouse.table.create.error.propagation.enabled:false}")
    private boolean errorPropagationEnabled;

    @PostConstruct
    public void startSync() {

        databaseCreatorExecutor = new ThreadPoolExecutor(
                dbCreatorThreadMinNum,
                dbCreatorThreadMaxNum,
                2,TimeUnit.MINUTES,
                new ArrayBlockingQueue<>(dbCreatorThreadQueueNum),
                new NamedThreadFactory("clickhouse-creator"),
                new ThreadPoolExecutor.CallerRunsPolicy());

        ScheduledExecutorService scheduler
                = Executors.newScheduledThreadPool(1,new NamedThreadFactory("clickhouse scheduler "));
        scheduler.scheduleWithFixedDelay(() -> {
            try {
                if (!lock()) {
                    log.info("do not get the FULL lock");
                    return;
                }

                sync(false);
            } catch (Exception e){
                log.error("Unknown exception in ClickhouseCron",e);
            }
        }, INITIAL_DELAY_IN_SECONDS, INTERVAL_IN_SECONDS, TimeUnit.SECONDS);

//        ScheduledExecutorService schedulerInc
//                = Executors.newScheduledThreadPool(1,new NamedThreadFactory("clickhouse scheduler Inc"));
//        schedulerInc.scheduleWithFixedDelay(() -> {
//            try {
//                if (!lockInc()) {
//                    log.info("do not get the INC lock");
//                    return;
//                }
//
//                sync(true);
//            } catch (Exception e) {
//                log.error("Unknown exception in ClickhouseCron",e);
//            }
//        }, INITIAL_DELAY_IN_SECONDS, INTERVAL_IN_SECONDS, TimeUnit.SECONDS);

        ScheduledExecutorService newClusterScheduler
                = Executors.newScheduledThreadPool(1,new NamedThreadFactory("new cluster clickhouse scheduler "));
        newClusterScheduler.scheduleWithFixedDelay(() -> {
            try {
                clickhouseHandlerFactory.get().actualizeAll();
                if (!clusterLock()) {
                    log.info("do not get the cluster lock");
                    return;
                }

                clickHouseSync.sync();
            } catch (Exception e) {
                log.error("Unknown exception in ClickhouseCron",e);
            }
        }, INITIAL_DELAY_IN_SECONDS, INTERVAL_IN_SECONDS, TimeUnit.SECONDS);
    }

    @PreDestroy
    public void destroy() {
        databaseCreatorExecutor.shutdownNow();
    }

    public void sync(boolean incrementalMode) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("ClickhousePreparationWatch");

        log.info("Start sync metric to clickhouse in {} mode",incrementalMode?"incrementalMode":"full");
        piiTableLoader.load();
        //connect to CH
        List<ClickhouseHandler> multiHandlers =
                clickhouseHandlerFactory.get().getAllHandlers()
                        .stream().filter(u->!sysParaService.getSyncIgnoreCluster().contains(u.getName())).collect(Collectors.toList());
        ClickhouseSyncMonitor totalMonitor = new ClickhouseSyncMonitor();
        //check if stop syncing
        String clickhouseSyncIgnoreListString = sysParaService.getClickhouseSyncIgnoreListString();
        if(ALL_TABLE.equals(clickhouseSyncIgnoreListString)){
            log.warn("Syncing metric to clickhouse is on pause");
            return;
        }

        Set<String>clickhouseSyncIgnoreSet = getClickhouseSyncIgnoreSet();

        //scan for empty tables in CH
        EmptyTableScanResult emptyTableScanResult=new EmptyTableScanResult();
        if(incrementalMode){
            beginOfIncremantal = System.currentTimeMillis();
        }
        //Get all metrics
        List<MetricsFieldDO> metricsFieldDOS = metricsFieldDAO.listAll();
        Map<String, List<MetricsFieldDO>> metricId2metricsField = metricsFieldDOS.stream().filter(u->validColumnName(u.getFieldName())).collect(Collectors.groupingBy(MetricsFieldDO::getMetricsId));
        //create db influx db/measurement name can be overlapped after convert to clickhouse name
        List<TenantDO> tenantList = tenantHandler.listAll();
        Map<String, String> tenantId2Name = tenantList
                .stream().collect(Collectors.toMap(TenantDO::getId, u -> toClickhouseName(u.getName())));
        Map<String, Date> tenant2CreateTime = tenantList.stream().collect(Collectors.toMap(TenantDO::getName, TenantDO::getCreateTime));
        List<MetricsDO> metricsDOS = metricsDAO.listAll().stream()
                .filter(u->tenantId2Name.containsKey(u.getTenantId()) && tenantId2Name.get(u.getTenantId())!=null)
                .collect(Collectors.toList());
        Map<String,List<MetricsDO>> db2Table= metricsDOS.stream().collect(Collectors.groupingBy(u -> tenantId2Name.get(u.getTenantId())));

        Map<String, List<String>> serviceName2ClusterNames = clickhouseClusterService.getTenantAndClusterRelation(tenantList);

        List<Future<ClickhouseSyncMonitor>> monitorFutureList = new ArrayList<>();

        String managerService = sysParaService.getManagerService();

        //load batch metric
        List<BatchCalculateMetricsDO> batchCalculateMetricsDOS = batchCalculateMetricDAO.listAllBatchCalculateMetric();
        List<BatchCalculateMetricsFieldDO> batchCalculateMetricFieldDAOS = batchCalculateMetricFieldDAO.listBatchCalculateMetricField();

        Map<String, List<BatchCalculateMetricsDO>> batchMetricMap = batchCalculateMetricsDOS.stream().collect(Collectors.groupingBy(u -> tenantId2Name.get(u.getTenantId())));
        Map<String, List<BatchCalculateMetricsFieldDO>> batchMetricFieldMap = batchCalculateMetricFieldDAOS.stream().filter(u -> validColumnName(u.getTargetField())).collect(Collectors.groupingBy(BatchCalculateMetricsFieldDO::getBatchMetricId));
        for (Map.Entry<String, List<BatchCalculateMetricsDO>> en : batchMetricMap.entrySet()) {
            String db = en.getKey();
            List<BatchCalculateMetricsDO> ms = en.getValue();
            String serviceName = ChNameDecoder.decode(db);
            List<ClickhouseHandler> filteredHandlers = filterHandlerByTenantId(serviceName, serviceName2ClusterNames, multiHandlers);
            for (ClickhouseHandler handler : filteredHandlers) {
                Future<ClickhouseSyncMonitor> monitorFuture = databaseCreatorExecutor.submit(() -> {
                    ClickhouseSyncMonitor monitor=new ClickhouseSyncMonitor();
                    handler.createDbOnCluster(db);

                    for (BatchCalculateMetricsDO table : ms) {
                        String tableName = toClickhouseName(table.getMetricName());
                        if (StringUtils.isEmpty(tableName) || clickhouseSyncIgnoreSet.contains(tableName)) {
                            continue;
                        }

                        try {
                            //The order is very important now for aggregation field creatiion
                            LinkedHashMap<String, FieldTypeEnum> colDef = new LinkedHashMap<>();
                            //all tags fileds are string type for now
                            if (!StringUtils.isEmpty(table.getTagNames())) {
                                Arrays.stream(table.getTagNames().split(","))
                                        .filter(this::validColumnName).forEach(v -> colDef.put(v, FieldTypeEnum.string));
                            }
                            if (batchMetricFieldMap.containsKey(table.getId())) {
                                batchMetricFieldMap.get(table.getId()).forEach(u -> colDef.put(u.getTargetField(), FieldTypeEnum.fromMetricsFieldTypeEnum(u.getTargetFieldType())));
                            }
                            LinkedHashMap<String, FieldTypeEnum> finalColDef = colDef;

                            if(!incrementalMode ||  inIncrementalRange(table.getGmtModify().getTime())){
                                TableCreator tableCreator = new TableCreator(db, tableName, finalColDef, colDef, handler, false, true);
                                log.info("batch aggregate table creator: {}",JsonUtils.toJsonString(tableCreator));
                                createAndMonitor(monitor, tableCreator);
                            }
                        } catch (Exception e) {
                            log.error("Create table {}.{} encounter unkonwn error {}", db, tableName, e);
                            monitor.addFailTable(db, tableName);
                        }
                    }
                    return monitor;
                });
                monitorFutureList.add(monitorFuture);
            }
        }

        stopWatch.stop();
        log.info("The watch task[{}] costs [{}] seconds", stopWatch.lastTaskInfo().getTaskName(), stopWatch.lastTaskInfo().getTimeSeconds());

        stopWatch.start("ClickhouseCreateTableWatch");
        for (Map.Entry<String, List<MetricsDO>> en : db2Table.entrySet()) {
            String db = en.getKey();
            List<MetricsDO> ms = en.getValue();
            String serviceName = ChNameDecoder.decode(db);
            List<ClickhouseHandler> filteredHandlers = filterHandlerByTenantId(serviceName, serviceName2ClusterNames, multiHandlers);
            for (ClickhouseHandler handler : filteredHandlers) {
                validateUselessTables(incrementalMode, serviceName, db, ms, handler);

                Future<ClickhouseSyncMonitor> monitorFuture = databaseCreatorExecutor.submit(() -> {
                    ClickhouseSyncMonitor monitor=new ClickhouseSyncMonitor();
                    handler.createDbOnCluster(db);

                    for (MetricsDO table : ms) {
                        String tableName = toClickhouseName(table.getMetricsName());
                        if (StringUtils.isEmpty(tableName) || clickhouseSyncIgnoreSet.contains(tableName)) {
                            continue;
                        }

                        try {
                            //The order is very important now for aggregation field creation
                            LinkedHashMap<String, FieldTypeEnum> colDef = new LinkedHashMap<>();
                            //all tags fields are string type for now
                            if (!StringUtils.isEmpty(table.getTagNames())) {
                                Arrays.stream(table.getTagNames().split(","))
                                        .filter(this::validColumnName).forEach(v -> colDef.put(v, FieldTypeEnum.string));
                            }
                            if (metricId2metricsField.containsKey(table.getId())) {
                                metricId2metricsField.get(table.getId()).forEach(u -> colDef.put(u.getFieldName(), FieldTypeEnum.fromMetricsFieldTypeEnum(u.getFieldType())));
                            }
                            LinkedHashMap<String, FieldTypeEnum> finalColDef = arrangeAggFieldDef(table, colDef);

                            if(!incrementalMode ||  inIncrementalRange(table.getModifyTime().getTime())){
                                TableCreator tableCreator = new TableCreator(db, tableName, finalColDef, colDef, handler, false);
                                createAndMonitor(monitor, tableCreator);
                            }
                        } catch (Exception e) {
                            log.error("Create table {}.{} encounter unknown error", db, tableName, e);
                            monitor.addFailTable(db, tableName);
                        }
                    }
                    //create new common alarm table for every service
                    Date tenantCreateTime = tenant2CreateTime.get(ChNameDecoder.decode(db));
                    if(!incrementalMode || (tenantCreateTime!=null && inIncrementalRange(tenantCreateTime.getTime()))) {
                        for (Map.Entry<String, Map<String, FieldTypeEnum>> entry : COMMON_ALARM_TABLE_SCHEMAS.entrySet()) {
                            TableCreator tableCreator = new TableCreator(db, entry.getKey(), entry.getValue(), handler, false);
                            createAndMonitor(monitor, tableCreator);
                        }
                    }
                    return monitor;
                });
                monitorFutureList.add(monitorFuture);

                //create summary alarm record table for all service
                if(StringUtils.isNotBlank(managerService) && managerService.equals(db)) {
                    //new table
                    Map<String, FieldTypeEnum> newAllSchema = new HashMap<>(ALL_ALARM_RECORDS_SCHEMA);
                    createAndMonitor(new ClickhouseSyncMonitor(), new TableCreator(db, ALARM_CUBE_MATCH_ALL_SERVICE_RECORD_TABLE, newAllSchema, handler, false));

                    //ai origin result table
                    Map<String, FieldTypeEnum> aiOriginResultSchema = new HashMap<>(AI_ORIGIN_ALL_RESULT_SCHEMA);
                    createAndMonitor(new ClickhouseSyncMonitor(), new TableCreator(db, AI_ORIGIN_DETECT_RESULT_ALL_SERVICE_RECORD_TABLE, aiOriginResultSchema, handler, false));
                }
            }
        }
        for(Future<ClickhouseSyncMonitor> future : monitorFutureList) {
            try {
                ClickhouseSyncMonitor syncMonitor = future.get();
                totalMonitor.merge(syncMonitor);
            } catch (Exception e) {
                log.error("Error occurs in clickhouse database and table multi-thread creation", e);
            }
        }
        if(incrementalMode){
            totalMonitor.setType("incremental");
        }
        Set<String>allTables=new HashSet<>(totalMonitor.getTables());
        monitorLog.info(JsonUtils.toJsonStringIgnoreExp(totalMonitor.finish()));
        if(!incrementalMode){
            //begin search for unused tables, only runs for full range mode
//            clearEmpty(emptyTableScanResult,allTables);

            clickhouseDropService.asyncTriggerDropTable();
            //clickhouseDropService.asyncTriggerDropColumn();
        }
        stopWatch.stop();
        log.info("The watch task[{}] in incremental:{} mode costs [{}] seconds", stopWatch.lastTaskInfo().getTaskName(), incrementalMode, stopWatch.lastTaskInfo().getTimeSeconds());
    }

    /**
     * For Aggregation metrics, need to add the original field name with
     * @param metrics
     * @param colDef
     * @return
     */
    private LinkedHashMap<String, FieldTypeEnum> arrangeAggFieldDef(MetricsDO metrics, LinkedHashMap<String, FieldTypeEnum> colDef) {
        if(MetricsTypeEnum.ORIGINAL == MetricsTypeEnum.fromValue(metrics.getType())) {
            return colDef;
        }
        Set<String> originalFieldNameSet = colDef.keySet().stream()
                .filter(fieldName -> fieldName.contains(CommonSplitConstants.METRICS_FIELD_SPLIT))
                .map(fieldName -> fieldName.substring(0, fieldName.lastIndexOf(CommonSplitConstants.METRICS_FIELD_SPLIT)))
                .filter(originalField -> !colDef.keySet().contains(originalField))
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(originalFieldNameSet)) {
           return colDef;
        } else {
            LinkedHashMap<String, FieldTypeEnum> newColDef = new LinkedHashMap<>();
            originalFieldNameSet.forEach(originalFieldName -> newColDef.put(originalFieldName, FieldTypeEnum.string));
            newColDef.putAll(colDef);
            return newColDef;
        }
    }

    private boolean isColumnCleanNeeded(String service, MetricsDO metricsDO, boolean incrementalMode) {
        return !incrementalMode
                && MetricsTypeEnum.fromValue(metricsDO.getType()) == MetricsTypeEnum.AGGREGATION
                && clickhouseDropParaService.isDropColumnOpen()
                && canaryDroppingServices(service)
                && !skipDroppingServices(service)
                && checkAllowDropColumnInterval(metricsDO);
    }

    private boolean canaryDroppingServices(String service) {
        List<String> canaryServices = clickhouseDropParaService.getCanaryServices();
        return CollectionUtils.isEmpty(canaryServices) || canaryServices.contains(service);
    }

    private boolean skipDroppingServices(String service) {
        List<String> toSkipServices = clickhouseDropParaService.getSkipServices();
        return !CollectionUtils.isEmpty(toSkipServices) && toSkipServices.contains(service);
    }

    /**
     * skip the services that meet the following conditions:
     * 1. in incremental mode
     * 2. dropping-table switch is closed
     * 3. some services we don't want to handle table dropping
     */
    private void validateUselessTables(boolean isIncrementalMode, String service, String db, List<MetricsDO> metricsList, ClickhouseHandler handler) {
        try {
            if (isIncrementalMode || !clickhouseDropParaService.isDropTableOpen() || skipDroppingServices(service) || !canaryDroppingServices(service)) {
                return;
            }
            DropTableExecutor.submitTask(TableCheckTask.builder().service(service).db(db).metricsList(metricsList).handler(handler)
                    .clickhouseService(clickhouseDropParaService.getClickhouseService())
                    .storageMetrics(clickhouseDropParaService.getClickhouseStorageMetrics())
                    .queryIntervalMinutes(clickhouseDropParaService.getStorageQueryIntervalMinute())
                    .allowDropTableIntervalMinute(clickhouseDropParaService.getAllowDropTableIntervalMinute())
                    .build());
        }catch (Exception e) {
            log.error("Clickhouse cron task validateUselessTables failed", e);
        }
    }

    private Set<String> getClickhouseSyncIgnoreSet() {
        String ignore = sysParaService.getClickhouseSyncIgnoreListString();
        if (ALL_TABLE.equals(ignore)) {
            log.warn("Syncing metric to clickhouse is on pause");
            return Set.of(ALL_TABLE);
        }
        return StringUtils.isEmpty(ignore)
                ? Set.of()
                : new HashSet<>(Arrays.asList(ignore.split(",")));
    }

    private void createClickhouseTable(
            String tenantId,
            String metricName,
            MetricsDO metrics,
            LinkedHashMap<String, FieldTypeEnum> fieldMap,
            Set<String> pillFields,
            boolean isBatchAgg
    ) {
        TenantDO tenantById = tenantHandler.getTenantById(tenantId);
        String db = toClickhouseName(tenantById.getName());
        String serviceName = ChNameDecoder.decode(db);

        List<ClickhouseHandler> multiHandlers = clickhouseHandlerFactory.get().getAllHandlers()
                .stream()
                .filter(u -> !sysParaService.getSyncIgnoreCluster().contains(u.getName()))
                .collect(Collectors.toList());

        Map<String, List<String>> serviceToCluster = clickhouseClusterService.getTenantAndClusterRelation(List.of(tenantById));
        List<ClickhouseHandler> filteredHandlers = filterHandlerByTenantId(serviceName, serviceToCluster, multiHandlers);

        Set<String> clickhouseSyncIgnoreSet = getClickhouseSyncIgnoreSet();
        String tableName = toClickhouseName(metricName);
        if (StringUtils.isEmpty(tableName) || clickhouseSyncIgnoreSet.contains(tableName)) return;

        // Optimization 1: Batch database creation to avoid duplicate operations
        createDatabaseBatch(filteredHandlers, db);
        
        // Optimization 2: Concurrent table creation
        createTablesConcurrently(filteredHandlers, db, tableName, metrics, fieldMap, pillFields, isBatchAgg);
    }

    /**
     * Batch database creation to avoid duplicate operations
     */
    private void createDatabaseBatch(List<ClickhouseHandler> handlers, String db) {
        // Each handler needs to create database on its own cluster
        // Different handlers may belong to different clusters
        // Use concurrent execution to improve performance
        List<CompletableFuture<Void>> futures = handlers.stream()
                .map(handler -> CompletableFuture.runAsync(() -> {
                    try {
                        handler.createDbOnCluster(db);
                    } catch (Exception e) {
                        log.warn("Failed to create database {} on handler {}", db, handler.getName(), e);
                    }
                }))
                .collect(Collectors.toList());
        
        // Wait for all database creation operations to complete

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

    /**
     * Concurrent table creation to improve performance
     */
    private void createTablesConcurrently(
            List<ClickhouseHandler> handlers, 
            String db, 
            String tableName, 
            MetricsDO metrics, 
            LinkedHashMap<String, FieldTypeEnum> fieldMap, 
            Set<String> pillFields, 
            boolean isBatchAgg
    ) {
        if (handlers.isEmpty()) {
            return;
        }

        // If concurrent mode is not enabled, use the original sequential approach
        if (!concurrentTableCreationEnabled) {
            createTablesSequentially(handlers, db, tableName, metrics, fieldMap, pillFields, isBatchAgg);
            return;
        }

        // Prepare table creation tasks
        List<Callable<ClickhouseSyncMonitor>> tasks = new ArrayList<>();
        
        for (ClickhouseHandler handler : handlers) {
            tasks.add(() -> {
                ClickhouseSyncMonitor monitor = new ClickhouseSyncMonitor();
                try {
                    LinkedHashMap<String, FieldTypeEnum> finalColDef;
                    if (!isBatchAgg) {
                        finalColDef = arrangeAggFieldDef(metrics, fieldMap);
                    } else {
                        finalColDef = fieldMap;
                    }
                    
                    TableCreator tableCreator = new TableCreator(db, tableName, finalColDef, fieldMap, handler, false, isBatchAgg);
                    long startTime = System.currentTimeMillis();
                    createAndMonitor(monitor, tableCreator, pillFields);
                    long endTime = System.currentTimeMillis();
                    log.info("Create clickhouse table {} on handler {} success, cost:{}ms", tableName, handler.getName(), endTime - startTime);
                } catch (Exception e) {
                    log.error("Create table {}.{} on handler {} failed", db, tableName, handler.getName(), e);
                    monitor.addFailTable(db, tableName);
                }
                return monitor;
            });
        }

        // Execute table creation tasks concurrently
        try {
            log.info("Starting concurrent table creation for {}.{} with {} handlers, timeout: {} seconds", 
                    db, tableName, handlers.size(), tableCreationTimeoutSeconds);
            
            List<Future<ClickhouseSyncMonitor>> futures = databaseCreatorExecutor.invokeAll(tasks, tableCreationTimeoutSeconds, TimeUnit.SECONDS);
            
            // Collect results and statistics
            int successCount = 0;
            int failCount = 0;
            ClickhouseSyncMonitor totalMonitor = new ClickhouseSyncMonitor();
            
            for (int i = 0; i < futures.size(); i++) {
                Future<ClickhouseSyncMonitor> future = futures.get(i);
                ClickhouseHandler handler = handlers.get(i);
                try {
                    ClickhouseSyncMonitor monitor = future.get();
                    // Merge individual monitor into total monitor
                    totalMonitor.merge(monitor);
                    if (monitor.getSuccessTableCount() > 0) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                } catch (java.util.concurrent.CancellationException e) {
                    failCount++;
                    log.error("Table creation task cancelled due to timeout for handler: {}", handler.getName(), e);
                } catch (Exception e) {
                    failCount++;
                    log.error("Error in concurrent table creation for handler: {}", handler.getName(), e);
                }
            }
            
            log.info("Concurrent table creation completed for {}.{}: success={}, failed={}, total_handlers={}, total_success={}, total_failed={}", 
                    db, tableName, successCount, failCount, handlers.size(), 
                    totalMonitor.getSuccessTableCount(), totalMonitor.getFailTableCount());
                    
        } catch (InterruptedException e) {
            log.error("Concurrent table creation interrupted", e);
            Thread.currentThread().interrupt();
        }
    }

    /**
     * Sequential table creation (original approach)
     */
    private void createTablesSequentially(
            List<ClickhouseHandler> handlers, 
            String db, 
            String tableName, 
            MetricsDO metrics, 
            LinkedHashMap<String, FieldTypeEnum> fieldMap, 
            Set<String> pillFields, 
            boolean isBatchAgg
    ) {
        for (ClickhouseHandler handler : handlers) {
            ClickhouseSyncMonitor monitor = new ClickhouseSyncMonitor();
            try {
                LinkedHashMap<String, FieldTypeEnum> finalColDef;
                if (!isBatchAgg) {
                    finalColDef = arrangeAggFieldDef(metrics, fieldMap);
                } else {
                    finalColDef = fieldMap;
                }
                
                TableCreator tableCreator = new TableCreator(db, tableName, finalColDef, fieldMap, handler, false, isBatchAgg);
                long startTime = System.currentTimeMillis();
                createAndMonitor(monitor, tableCreator, pillFields);
                long endTime = System.currentTimeMillis();
                log.info("Create clickhouse table {} on handler {} success, cost:{}ms", tableName, handler.getName(), endTime - startTime);
            } catch (Exception e) {
                log.error("Create table {}.{} on handler {} failed", db, tableName, handler.getName(), e);
                monitor.addFailTable(db, tableName);
            }
        }
    }

    public void addMetrics(String metricId) {
        MetricsDO metricsById = metricsDAO.getMetricsById(metricId);
        log.info("add metrics {}", JsonUtils.toJsonString(metricsById));
        List<MetricsFieldDO> metricsFieldDOS = metricsFieldDAO.listFieldByMetricsId(metricId);

        LinkedHashMap<String, FieldTypeEnum> colDef = new LinkedHashMap<>();
        if (!StringUtils.isEmpty(metricsById.getTagNames())) {
            Arrays.stream(metricsById.getTagNames().split(","))
                    .filter(this::validColumnName)
                    .forEach(tag -> colDef.put(tag, FieldTypeEnum.string));
        }

        metricsFieldDOS.forEach(field ->
                colDef.put(field.getFieldName(), FieldTypeEnum.fromMetricsFieldTypeEnum(field.getFieldType()))
        );

        List<CollectorFieldDO> piFieldByMetricId = collectorFieldDAO.getPiiFieldByMetricId(metricsById.getId());

        Set<String> metricColumns = new HashSet<>();
        metricColumns.addAll(metricsFieldDAO.listFieldByMetricsId(metricId).stream().map(MetricsFieldDO::getFieldName).collect(Collectors.toSet()));

        Set<String> piiCollectorFieldsName = piFieldByMetricId.stream().map(CollectorFieldDO::getTargetField).collect(Collectors.toSet());

        Set<String> aggPiiFields = new HashSet<>();
        for (String s : piiCollectorFieldsName) {
            for (AggregationOperatorEnum a : AggregationOperatorEnum.values()) {
                aggPiiFields.add(String.format("%s.%s", s, a.name()));
            }
        }
        piiCollectorFieldsName.addAll(aggPiiFields);

//        log.info("addMetric colDef:{}", JsonUtils.toJsonString(colDef));
//        log.info("addMetric piiCollectorFieldsName:{}", JsonUtils.toJsonString(piiCollectorFieldsName));

        createClickhouseTable(
                metricsById.getTenantId(),
                metricsById.getMetricsName(),
                metricsById,
                colDef,
                piiCollectorFieldsName,
                false // isBatchAgg
        );
    }

    public void addAggBatchMetric(String metricId){
        BatchCalculateMetricsDO batchMetricById = batchCalculateMetricDAO.getBatchMetricById(metricId);
        List<BatchCalculateMetricsFieldDO> fieldDOS = batchCalculateMetricFieldDAO.getMetricFieldsByMetricId(metricId);

        LinkedHashMap<String, FieldTypeEnum> colDef = new LinkedHashMap<>();
        if (!StringUtils.isEmpty(batchMetricById.getTagNames())) {
            Arrays.stream(batchMetricById.getTagNames().split(","))
                    .forEach(tag -> colDef.put(tag, FieldTypeEnum.string));
        }

        fieldDOS.forEach(field ->
                colDef.put(field.getTargetField(), FieldTypeEnum.fromMetricsFieldTypeEnum(field.getTargetFieldType()))
        );

        createClickhouseTable(
                batchMetricById.getTenantId(),
                batchMetricById.getMetricName(),
                null,
                colDef,
                null,
                true // isBatchAgg
        );
    }

    private boolean checkAllowDropColumnInterval(MetricsDO metricsDO) {
        if(null == metricsDO.getModifyTime()) {
            return true;
        }
        return (System.currentTimeMillis() - metricsDO.getModifyTime().getTime())/1000/60 > clickhouseDropParaService.getAllowDropColumnIntervalMinute();
    }

    public List<ClickhouseHandler> filterHandlerByTenantId(String serviceName, Map<String, List<String>> serviceName2ClusterNames, List<ClickhouseHandler> multiHandlers) {

        List<String> syncClusterList = serviceName2ClusterNames.get(serviceName);

        List<String> clusterNameList = extraTenantAndClusterRelationService.getClusterNameByTenantId(serviceName);
        if (!CollectionUtils.isEmpty(clusterNameList)) {
            syncClusterList.addAll(clusterNameList);
        }

        return multiHandlers.stream().filter(handler -> syncClusterList.contains(handler.getName())).collect(Collectors.toList());
    }

    private EmptyTableScanResult scanForEmptyTables(){
        EmptyTableScanResult r=new EmptyTableScanResult();
        try {
            ClickhouseMultiHandler handlerByEnv = clickhouseHandlerFactory.get().getHandlerByEnv();
            Map<String, String> local2View = r.getLocal2View();
            Set<String> nonEmptyTables = r.getNonEmptyTables();
            for (ClickhouseHandler h : handlerByEnv.getAll()) {
                List<Map<String, Object>> dte = h.query("select database ,name,splitByChar(',',engine_full)[3]localTableName from system.tables where engine ='Distributed' ");
                for (Map<String, Object> m : dte) {
                    try {
                        String db = m.get("database").toString();
                        String table = m.get("name").toString();
                        String localTableName = m.get("localTableName").toString().trim();
                        localTableName = localTableName.substring(1, localTableName.length() - 1);
                        local2View.put(ClickhouseSqlUtil.concatFullTableName(db, localTableName), ClickhouseSqlUtil.concatFullTableName(db, table));
                    } catch (Exception e) {
                        log.warn("Cannot parse view structure for {}.{}", m.get("database"), m.get("name"));
                    }
                }
                List<Map<String, Object>> noEmpty = h.query("select database ,name from system.tables  where engine ='ReplicatedMergeTree' and total_rows!=0");
                for (Map<String, Object> m : noEmpty) {
                    String name = ClickhouseSqlUtil.concatFullTableName(m.get("database").toString(), m.get("name").toString());
                    nonEmptyTables.add(name);
                }
            }
            Set<String> nonEmptyView = new HashSet<>();
            for (Map.Entry<String, String> v : local2View.entrySet()) {
                String local = v.getKey();
                String view = v.getValue();
                if (nonEmptyTables.contains(local)) {
                    nonEmptyView.add(view);
                }
            }
            Set<String> emptyView = local2View.values().stream().filter(u -> !nonEmptyView.contains(u)).collect(Collectors.toSet());
            r.setEmptyView(emptyView);
        }catch (Exception e){
            log.error("Unknown exception in ClickhouseCron when scanForEmptyTables",e);
        }
        return r;
    }

    public Set<String> filterPrefix(Set<String> set1, Set<String> set2) {
        Set<String> filteredSet = new HashSet<>();
        for (String s1 : set1) {
            boolean hasPrefix = false;
            for (String s2 : set2) {
                if (s1.startsWith(s2)) {
                    hasPrefix = true;
                    break;
                }
            }
            if (!hasPrefix) {
                filteredSet.add(s1);
            }
        }
        return filteredSet;
    }

    private void clearEmpty(EmptyTableScanResult emptyTableScanResult, Set<String>allMetrics){
        log.info("Begin to check {} local tables with {} metrics and {} emptyViews"
                ,emptyTableScanResult.getLocal2View().size(),allMetrics.size(),emptyTableScanResult.getEmptyView().size());
        if(emptyTableScanResult.getLocal2View().size()==0){
            log.info("There is no local table");
            return;
        }

        ClickHouseEmptyTableMonitor emptyMonitor=new ClickHouseEmptyTableMonitor(clickhouseHandlerFactory.get().getEnvironment());
        emptyMonitor.setTotalTableCount(emptyTableScanResult.getLocal2View().size());
        emptyMonitor.setNonEmptyTableCount(emptyTableScanResult.getNonEmptyTables().size());
        emptyMonitor.setMetricCount(allMetrics.size());
        Set<String> emptyView = emptyTableScanResult.getEmptyView();
        emptyMonitor.setEmptyInClickHouseCount(emptyView.size());
        emptyView.removeAll(allMetrics);
        Set<String> noDropDb = clickhouseParaService.getNoDropDb();
        log.info("No drop tables: {}",String.join(",",noDropDb));
        emptyView=filterPrefix(emptyView,noDropDb);
        emptyMonitor.setEmptyAndHaveNoDefCount(emptyView.size());
        emptyMonitor.setEmptyAndHaveNoDefString(String.join(",",emptyView));
        if(emptyMonitor.getEmptyAndHaveNoDefString().length()>200*1000){
            emptyMonitor.setEmptyAndHaveNoDefString(emptyMonitor.getEmptyAndHaveNoDefString().substring(0,200*1000));
        }
        monitorLog.info(JsonUtils.toJsonStringIgnoreExp(emptyMonitor));
        int tableToClearCount=sysParaService.getTableToDropLimit();
        Set<String> tableToClear = ImmutableSet.copyOf(Iterables.limit(emptyView, tableToClearCount));
        for(String v:tableToClear){
            List<String> locals = emptyTableScanResult.getLocal2View().entrySet().stream().filter(u -> StringUtils.equals(u.getValue(), v))
                    .map(u -> u.getKey()).collect(Collectors.toList());
            ClickhouseMultiHandler handlerByEnv = clickhouseHandlerFactory.get().getHandlerByEnv();
            List<String>viewAndLocals=new LinkedList<>(locals);
            viewAndLocals.add(v);
            Collection<ClickhouseHandler> handlers = handlerByEnv.getAll();
            for(String vl:viewAndLocals){
                ClickHouseDropTableMonitor dm=new ClickHouseDropTableMonitor(clickhouseHandlerFactory.get().getEnvironment(),vl);
                for(ClickhouseHandler h:handlers){
                    String db=vl.split("\\.")[0];
                    String table=vl.split("\\.")[1];
                    h.dropTable(db,table);
                }
                monitorLog.info(JsonUtils.toJsonStringIgnoreExp(dm));
            }
        }
    }

    private boolean inIncrementalRange(long time) {
        return beginOfIncremantal-time<INCREMENTAL_RANGE_MINUTE*TimeUtils.MILLIS_SECONDS_OF_ONE_MINUTE;
    }

    private void createAndMonitor(ClickhouseSyncMonitor monitor,  TableCreator tableCreator) {
        String fullName = ClickhouseSqlUtil.concatFullTableName(tableCreator.getDb(), tableCreator.getTableName());
        monitor.addTable(fullName);
        boolean needBuffer = clickhouseParaService.getBufferTableCreate().contains(fullName);
        tableCreator.setNeedBuffer(needBuffer);
        tableCreator.setSavingTime(tllDefaultTimeService.getValue());
        Set<String> piiFields = piiTableLoader.getPiiField(ChNameDecoder.decode(tableCreator.getDb()), ChNameDecoder.decode(tableCreator.getTableName()));
        tableCreator.setPiiFields(piiFields);
        tableCreator.setBufferConf(clickhouseParaService.getBufferTableConf());
        boolean success = tableCreator.createAndUpdate();
        if(success){
            monitor.addSuccess();
        } else {
            monitor.addFailTable(tableCreator.getDb(), tableCreator.getTableName());
        }
    }

    private void createAndMonitor(ClickhouseSyncMonitor monitor,  TableCreator tableCreator, Set<String> piiFields) {
        String fullName = ClickhouseSqlUtil.concatFullTableName(tableCreator.getDb(), tableCreator.getTableName());
        monitor.addTable(fullName);
        boolean needBuffer = clickhouseParaService.getBufferTableCreate().contains(fullName);
        tableCreator.setNeedBuffer(needBuffer);
        tableCreator.setSavingTime(tllDefaultTimeService.getValue());
//        Set<String> piiFields = piiTableLoader.getPiiField(ChNameDecoder.decode(tableCreator.getDb()), ChNameDecoder.decode(tableCreator.getTableName()));
        tableCreator.setPiiFields(piiFields);
        tableCreator.setBufferConf(clickhouseParaService.getBufferTableConf());
        boolean success = tableCreator.createAndUpdate();
        if(success){
            monitor.addSuccess();
        } else {
            monitor.addFailTable(tableCreator.getDb(), tableCreator.getTableName());
        }
    }

    private boolean validColumnName(String name){
        return !StringUtils.isEmpty(name) && !name.equals(TIME_COLUMN_NAME) && !name.contains("`");
    }


    public boolean lock() {
        List<DistributedLockDO> distributedLocks=distributedLockDAO.listByType(CLICKHOUSE_CRON);
        if(CollectionUtils.isEmpty(distributedLocks)){
            return false;
        }
        return  distributedLockDAO.lockWithType(CLICKHOUSE_CRON,distributedLocks.get(0).getHandler(), IpUtils.getLocalIP(), LOCK_TIME_OUT_MIN)>0;
    }

    public boolean lockInc() {
        List<DistributedLockDO> distributedLocks=distributedLockDAO.listByType(CLICKHOUSE_CRON_INC);
        if(CollectionUtils.isEmpty(distributedLocks)){
            return false;
        }
        return  distributedLockDAO.lockWithType(CLICKHOUSE_CRON_INC,distributedLocks.get(0).getHandler(), IpUtils.getLocalIP(), LOCK_TIME_OUT_MIN)>0;
    }

    public boolean clusterLock() {
        List<DistributedLockDO> distributedLocks = distributedLockDAO.listByType(NEW_CLUSTER_CLICKHOUSE_CRON);
        if(CollectionUtils.isEmpty(distributedLocks)){
            return false;
        }
        return distributedLockDAO.lockWithType(NEW_CLUSTER_CLICKHOUSE_CRON,distributedLocks.get(0).getHandler(), IpUtils.getLocalIP(), LOCK_TIME_OUT_MIN)>0;
    }
}
