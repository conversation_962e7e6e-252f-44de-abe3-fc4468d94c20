package us.zoom.cube.site.lib.input.outage;

import lombok.Data;
import us.zoom.cube.site.lib.BasePara;

@Data
public class OutageCommentInput extends BasePara {
   private String id;
   private String outageId;
   private String commentId;
   private String parentCommentId;
   /***
    * Strikethrough comment content
    */
   private String content;
   /***
    * The content corresponding to the underline will not be saved in the disk
    */
   private String mailContent;
   /**
    * Corresponding comment content, only used for mail notification
    */
   private String mailCommentContent;
   /**
    * If there are multiple, separate them with commas. If they are the same user, remove the duplicates.
    */
   private String mentionedUserIds;
   /**
    * @see us.zoom.cube.site.lib.outage.CommentTypeEnum
    */
   private String type;
   /**
    * @see us.zoom.cube.site.lib.outage.MentionModuleType
    */
   private String mentionModuleType;
}
