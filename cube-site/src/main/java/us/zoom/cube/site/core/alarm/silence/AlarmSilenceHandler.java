package us.zoom.cube.site.core.alarm.silence;

import com.okta.commons.lang.Collections;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import us.zoom.infra.dao.model.AlarmGroupServiceItemDO;
import us.zoom.infra.dao.model.AlarmGroupTagItemDO;
import us.zoom.infra.dao.model.AlarmSilenceDO;
import us.zoom.infra.dao.service.AlarmGroupTagItemDAO;
import us.zoom.infra.dao.service.AlarmSilenceDAO;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 01/05/2023 19:33
 * @Description:
 */
@Service
public class AlarmSilenceHandler {

    @Autowired
    private AlarmSilenceDAO alarmSilenceDAO;

    public List<AlarmSilenceDO> findByParam(String name,Integer status, Integer type, Integer visibility){
        return alarmSilenceDAO.findByParam(name, status, type, visibility);
    }


    public AlarmSilenceDO findById(String id){
        return alarmSilenceDAO.findById(id);
    }

    public AlarmSilenceDO findByName(String name){
        return alarmSilenceDAO.findByName(name);
    }

    public List<AlarmSilenceDO> findByNameList(List<String> nameList){
        if(Collections.isEmpty(nameList)){
            return new ArrayList<>();
        }
        return alarmSilenceDAO.findByNameList(nameList);
    }

    public int insert(AlarmSilenceDO alarmSilenceDO) {
        return alarmSilenceDAO.insert(alarmSilenceDO);
    }

    public int update(AlarmSilenceDO alarmSilenceDO) {
        if (alarmSilenceDO.getAutoDelete() == null) {
            alarmSilenceDO.setAutoDelete(0);
        }
        return alarmSilenceDAO.update(alarmSilenceDO);
    }

    public void deleteById(String id){
        alarmSilenceDAO.deleteById(id);
    }

    public void deleteByName(String name){
        alarmSilenceDAO.deleteByName(name);
    }

    public List<AlarmSilenceDO> findByAlarmGroupId(String id){
        return alarmSilenceDAO.findByAlarmGroupId(id);
    }


    public List<AlarmSilenceDO> findAll(){
        return alarmSilenceDAO.findAll();
    }

}
