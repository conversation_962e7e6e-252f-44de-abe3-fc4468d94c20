package us.zoom.cube.site.biz.trace;

import com.clickhouse.data.value.UnsignedLong;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import io.vavr.Tuple;
import io.vavr.Tuple3;
import jakarta.annotation.PostConstruct;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.sdk.model.MonitorLog;
import us.zoom.cube.site.core.SysParaHandler;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.core.config.ClickhouseHandlerFactory;
import us.zoom.cube.site.infra.enums.trace.AggregateOperator;
import us.zoom.cube.site.infra.enums.trace.AttributeKeyDataType;
import us.zoom.cube.site.infra.enums.trace.AttributeKeyType;
import us.zoom.cube.site.infra.enums.trace.DataSource;
import us.zoom.cube.site.infra.enums.trace.FilterOperator;
import us.zoom.cube.site.infra.enums.trace.PanelType;
import us.zoom.cube.site.infra.enums.trace.QueryType;
import us.zoom.cube.site.infra.utils.ConvertUtils;
import us.zoom.cube.site.infra.utils.TraceUtils;
import us.zoom.cube.site.lib.dto.QueryFilter;
import us.zoom.cube.site.lib.dto.TimeStep;
import us.zoom.cube.site.lib.dto.trace.BasicTraceQueryParam;
import us.zoom.cube.site.lib.dto.trace.ClusterAndRegion;
import us.zoom.cube.site.lib.dto.trace.DataWarning;
import us.zoom.cube.site.lib.dto.trace.ErrorWithSpan;
import us.zoom.cube.site.lib.dto.trace.GetErrorParams;
import us.zoom.cube.site.lib.dto.trace.ListErrorsParams;
import us.zoom.cube.site.lib.dto.trace.NextPrevErrorIDs;
import us.zoom.cube.site.lib.dto.trace.SearchSpanResponseItem;
import us.zoom.cube.site.lib.dto.trace.SearchSpansResult;
import us.zoom.cube.site.lib.dto.trace.ServiceItem;
import us.zoom.cube.site.lib.dto.trace.ServiceMapDependencyResponseItem;
import us.zoom.cube.site.lib.dto.trace.SmartAlgorithmConfig;
import us.zoom.cube.site.lib.dto.trace.TagQuery;
import us.zoom.cube.site.lib.dto.trace.TagQueryParam;
import us.zoom.cube.site.lib.dto.trace.TopLevelOperationsQueryParam;
import us.zoom.cube.site.lib.dto.trace.TopOperationsItem;
import us.zoom.cube.site.lib.dto.trace.TraceDependencyRequest;
import us.zoom.cube.site.lib.dto.trace.TraceError;
import us.zoom.cube.site.lib.dto.trace.TraceQueryOperator;
import us.zoom.cube.site.lib.dto.trace.TraceQueryRequest;
import us.zoom.cube.site.lib.dto.trace.TraceServiceMapNodeType;
import us.zoom.cube.site.lib.input.trace.BuilderQuery;
import us.zoom.cube.site.lib.input.trace.BuilderQuery.AttributeKey;
import us.zoom.cube.site.lib.input.trace.BuilderQuery.FilterItem;
import us.zoom.cube.site.lib.input.trace.BuilderQuery.FilterSet;
import us.zoom.cube.site.lib.input.trace.CompositeQuery;
import us.zoom.cube.site.lib.input.trace.TraceQueryRangeParam;
import us.zoom.cube.site.lib.output.PageResult;
import us.zoom.cube.site.lib.output.trace.TraceDependencyEdge;
import us.zoom.cube.site.lib.output.trace.TraceDependencyNode;
import us.zoom.cube.site.lib.output.trace.TraceDependencyResponse;
import us.zoom.cube.site.lib.output.trace.TraceHistogram;
import us.zoom.cube.site.lib.output.trace.TraceItem;
import us.zoom.cube.site.lib.output.trace.queryrange.QueryRangeResult;
import us.zoom.cube.site.lib.output.trace.queryrange.Series;
import us.zoom.cube.site.lib.query.SysParaQuery;
import us.zoom.infra.dao.model.SysParaDO;
import us.zoom.infra.thread.NamedThreadFactory;
import us.zoom.infra.thread.ThreadLocalStore;
import us.zoom.infra.utils.AsyncUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.BinaryOperator;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.StringUtils.EMPTY;
import static us.zoom.cube.lib.common.CubeConstants.AND;
import static us.zoom.cube.site.infra.constants.trace.TraceConstant.TRACE_DATABASE_NAME;
import static us.zoom.cube.site.infra.constants.trace.TraceConstant.TRACE_DEPENDENCY_GRAPH_TABLE;
import static us.zoom.cube.site.infra.constants.trace.TraceConstant.TRACE_TOP_LEVEL_OPERATIONS_TABLE;
import static us.zoom.cube.site.infra.utils.TraceUtils.getTraceSpanTable;

/**
 * @author: eason.jia
 * @date: 2024/8/17
 */
@Component
public class TraceQueryService {

    private static final Logger monitorLogger = LoggerFactory.getLogger("Monitor");
    private static final Logger logger = LoggerFactory.getLogger(TraceQueryService.class);

    private static final String SKIP_SERVICE_CONFIGS = "skipServiceOperations";
    private static final String SMART_ALGORITHM_CONFIG = "smartAlgorithmConfig";

    private static final int POOL_SIZE = Runtime.getRuntime().availableProcessors() * 2;

    private static final long KEEP_ALIVE_TIME = 60 * 1000;

    private static final int DEFAULT_QUERY_TASK_SIZE = 1000;
    private static final String SOURCE_FIELD = "src";
    private static final String DESTINATION_FIELD = "dest";

    private static final String CLUSTER_AND_REGION_QUERY = "SELECT stringTagMap['appcluster'] as `cluster`, stringTagMap['regionId'] as `region` from %s.%s" +
            " where timestamp >= NOW() - INTERVAL 30 MINUTE AND serviceName = '%s' AND has(stringTagMap, 'appcluster') AND has(stringTagMap, 'regionId') group by `cluster`,`region`";

    private static final String DEPENDENCY_QUERY_TEMPLATE = "WITH " +
            "quantilesMergeState(0.5, 0.75, 0.9, 0.95, 0.99, 0.999)(duration_quantiles_state) AS duration_quantiles_state, " +
            "finalizeAggregation(duration_quantiles_state) AS result " +
            "SELECT " +
            "src as parent, " +
            "dest as child, " +
            "result[1] AS p50, " +
            "result[2] AS p75, " +
            "result[3] AS p90, " +
            "result[4] AS p95, " +
            "result[5] AS p99, " +
            "sum(total_count) as callCount, " +
            "sum(total_count)/ %d AS callRate, " +
            "sum(error_count)/sum(total_count) * 100 as errorRate " +
            "FROM %s.%s WHERE timestamp >= toDateTime(%d) AND timestamp <= toDateTime(%d) AND (src = '%s' OR dest = '%s') GROUP BY src, dest";

    private static final String DEPENDENCY_CALL_RATE_QUERY_TEMPLATE =
            "SELECT " +
                    "sum(total_count)/ %d AS callRate, " +
                    "sum(error_count)/sum(total_count) * 100 as errorRate " +
                    "FROM %s.%s WHERE timestamp >= toDateTime(%d) AND timestamp <= toDateTime(%d) AND (%s = '%s')";

    private static final String MIDDLEWARE_DEPENDENCY_INCOMING_CALL_RATE_QUERY_TEMPLATE =
            DEPENDENCY_CALL_RATE_QUERY_TEMPLATE + " AND src = '%s'";

    @Autowired
    private ClickhouseHandlerFactory clickhouseHandlerFactory;

    @Value("${smartAlgo.enabled:true}")
    private boolean smartAlgoEnabled;

    @Autowired
    private SysParaHandler sysParaHandler;

    @Autowired
    private TraceV3Service traceV3Service;

    @Autowired
    private TraceQueryBuilder traceQueryBuilder;

    private ThreadPoolExecutor queryExecutor;
    @Autowired
    private TenantHandler tenantHandler;

    @PostConstruct
    public void init() {
        queryExecutor = new ThreadPoolExecutor(POOL_SIZE, POOL_SIZE,
                KEEP_ALIVE_TIME, TimeUnit.MILLISECONDS, new ArrayBlockingQueue<>(DEFAULT_QUERY_TASK_SIZE),
                new NamedThreadFactory("trace_query_executor"), new ThreadPoolExecutor.DiscardPolicy());
    }

    public List<SearchSpansResult> searchTraces(String traceId, String spanId, int levelUp, int levelDown, int spanRenderLimit, int maxSpansInTrace) {
        List<SearchSpansResult> searchSpansResults = searchTraces(traceId, spanId, levelUp, levelDown, spanRenderLimit, maxSpansInTrace, false);
        if (CollectionUtils.isNotEmpty(searchSpansResults) && CollectionUtils.isNotEmpty(searchSpansResults.get(0).getEvents())) {
            return searchSpansResults;
        }
        return searchTraces(traceId, spanId, levelUp, levelDown, spanRenderLimit, maxSpansInTrace, true);
    }

    public List<SearchSpansResult> searchTraces(String traceId, String spanId, int levelUp, int levelDown, int spanRenderLimit, int maxSpansInTrace, boolean isCold) {
        String countSql = String.format("SELECT count() as count FROM %s.%s WHERE traceID = '%s'", TRACE_DATABASE_NAME, getTraceSpanTable(isCold), traceId);
        List<Map<String, Object>> countResult = clickhouseHandlerFactory.get().query(TRACE_DATABASE_NAME, countSql);
        if (CollectionUtils.size(countResult) != 1) {
            throw new IllegalStateException("error in processing sql query: " + countSql);
        }
        Map<String, Object> countMap = countResult.get(0);
        if (countMap.get("count") == null) {
            throw new IllegalStateException("error in processing sql query result: " + JsonUtils.toJsonString(countMap));
        }
        long count = Long.parseLong(String.valueOf(countMap.get("count")));
        if (count > maxSpansInTrace) {
            monitorLogger.info(JsonUtils.toJsonStringIgnoreExp(MonitorLog.builder()
                    .withCubeVer("1.0.0")
                    .withMeasure("exceed_max_span_limit")
                    .addTag("traceId", traceId)
                    .addField("count", count)
                    .withTs(System.currentTimeMillis())
                    .build()));
            throw new IllegalStateException(String.format("max spans allowed in trace limit reached, limit: %d, count: %d", maxSpansInTrace, count));
        }

        String querySql = String.format("SELECT timestamp, traceID, model FROM %s.%s WHERE traceID = '%s'", TRACE_DATABASE_NAME, getTraceSpanTable(isCold), traceId);
        List<Map<String, Object>> queryResult = clickhouseHandlerFactory.get().query(TRACE_DATABASE_NAME, querySql);

        SearchSpansResult searchSpansResult = buildSearchSpansResult(queryResult, spanId, levelUp, levelDown, spanRenderLimit);
        return Lists.newArrayList(searchSpansResult);
    }

    private SearchSpansResult buildSearchSpansResult(List<Map<String, Object>> queryResult, String spanId, int levelUp, int levelDown, int spanRenderLimit) {
        long startTime = 0, endTime = 0, durationNano = 0;
        List<SearchSpanResponseItem> spanResponseItems = new ArrayList<>();
        TraceServiceMapBuilder serviceMapBuilder = new TraceServiceMapBuilder();
        for (Map<String, Object> rowData : queryResult) {
            SearchSpanResponseItem searchSpanResponseItem = JsonUtils.toObject(String.valueOf(rowData.get("model")), SearchSpanResponseItem.class);
            searchSpanResponseItem.setTimestamp(((Timestamp) rowData.get("timestamp")).getTime());
            if (startTime == 0 || searchSpanResponseItem.getTimestamp() < startTime) {
                startTime = searchSpanResponseItem.getTimestamp();
            }
            if (endTime == 0 || searchSpanResponseItem.getTimestamp() > endTime) {
                endTime = searchSpanResponseItem.getTimestamp();
            }
            if (durationNano == 0 || searchSpanResponseItem.getDurationNano() > durationNano) {
                durationNano = searchSpanResponseItem.getDurationNano();
            }
            if (CollectionUtils.isNotEmpty(searchSpanResponseItem.getReferences())) {
                serviceMapBuilder.addItem(searchSpanResponseItem);
            }
            spanResponseItems.add(searchSpanResponseItem);
        }
        SearchSpansResult searchSpansResult = SearchSpansResult.builder()
                .columns(Arrays.asList("__time", "SpanId", "TraceId", "ServiceName", "Name", "Kind", "DurationNano", "TagsKeys", "TagsValues", "References", "Events", "HasError", "StatusMessage", "StatusCodeString", "SpanKind"))
                .isSubTree(false)
                .build();
        SmartAlgorithmConfig config = sysParaHandler.listByType(new SysParaQuery(SMART_ALGORITHM_CONFIG)).stream().findFirst().map(SysParaDO::getValue)
                .map(value -> JsonUtils.toObjectByTypeRef(value, new TypeReference<SmartAlgorithmConfig>() {
                })).orElse(SmartAlgorithmConfig.DEFAULT);
        if (spanResponseItems.size() > Math.max(spanRenderLimit, config.getLimit()) && config.isEnable()) {
            searchSpansResult = SmartTraceAlgorithm.smartSearchTrace(spanResponseItems, spanId, levelUp, levelDown, spanRenderLimit);
        } else {
            List<List<Object>> events = new ArrayList<>();
            for (SearchSpanResponseItem item : spanResponseItems) {
                List<String> refList = Optional.ofNullable(item.getReferences())
                        .orElse(new ArrayList<>())
                        .stream()
                        .map(reference -> String.format("{TraceId=%s, SpanId=%s, RefType=%s}", reference.getTraceId(), reference.getSpanId(), reference.getRefType()))
                        .collect(Collectors.toList());
                List<Object> event = Lists.newArrayList(item.getTimestamp(),
                        item.getSpanId(),
                        item.getTraceId(),
                        item.getServiceName(),
                        item.getName(),
                        String.valueOf(item.getKind()),
                        String.valueOf(item.getDurationNano()),
                        item.getTagMap().keySet(),
                        item.getTagMap().values(),
                        refList,
                        Optional.ofNullable(item.getEvent()).orElse(new ArrayList<>()),
                        item.isHasError(),
                        Optional.ofNullable(item.getStatusMessage()).orElse(EMPTY),
                        item.getStatusCodeString(),
                        item.getSpanKind());
                events.add(event);
            }
            searchSpansResult.setEvents(events);
        }
        searchSpansResult.setTraceGraph(serviceMapBuilder.build());
        searchSpansResult.setStartTimestampMillis(startTime - (durationNano / 1000000));
        searchSpansResult.setEndTimestampMillis(endTime + (durationNano / 1000000));
        return searchSpansResult;
    }

    public List<ServiceItem> searchServices(BasicTraceQueryParam getServicesParams) {

        TopLevelOperationsQueryParam param = new TopLevelOperationsQueryParam();
        param.setStart(getServicesParams.getStart());
        param.setEnd(getServicesParams.getEnd());
        Map<String, List<String>> topLevelOperations = getTopLevelOperations(param);

        long period = TimeUnit.NANOSECONDS.toSeconds(getServicesParams.getEnd() - getServicesParams.getStart());

        return AsyncUtils.parallelFutureJoinWithResult(topLevelOperations.entrySet(), entry -> {
                    String serviceName = entry.getKey();
                    List<String> operations = entry.getValue();
                    return processServiceItem(getServicesParams, operations, serviceName);
                }, (e, entry) -> null, queryExecutor)
                .stream()
                .filter(Objects::nonNull)
                .peek(item -> {
                    item.setCallRate((double) item.getNumCalls() / period);
                    item.setErrorRate(item.getNumCalls() > 0
                            ? (double) item.getNumErrors() * 100 / item.getNumCalls()
                            : 0);
                })
                .toList();
    }

    private ServiceItem processServiceItem(BasicTraceQueryParam getServicesParams, List<String> ops, String serviceName) {
        if (CollectionUtils.isEmpty(ops)) {
            return null;
        }
        String subQuery = buildTagQuerySql(getServicesParams.getTags());
        String opsString = Joiner.on("','").join(ops.subList(0, Math.min(1500, ops.size())));
        String query = String.format(
                "SELECT quantile(0.99)(durationNano) as p99, avg(durationNano) as avgDuration, count(*) as numCalls " +
                        "FROM %s.%s WHERE serviceName = '%s' AND name IN ('%s') AND timestamp >= '%s' AND timestamp <= '%s'",
                TRACE_DATABASE_NAME, TraceUtils.getTraceIndexTable(false), serviceName,
                opsString, getServicesParams.getStart(),
                getServicesParams.getEnd()) + subQuery;
        String errorQuery = String.format(
                "SELECT count(*) as numErrors FROM %s.%s WHERE serviceName = '%s' AND name IN ('%s') AND timestamp >= '%s' AND timestamp <= '%s' AND statusCode = 2",
                TRACE_DATABASE_NAME, TraceUtils.getTraceIndexTable(false), serviceName,
                opsString, getServicesParams.getStart(),
                getServicesParams.getEnd()) + subQuery;

        List<Map<String, Object>> rows = clickhouseHandlerFactory.get().query(TRACE_DATABASE_NAME, query);
        ServiceItem serviceItem = rows.stream().map(row -> {
            ServiceItem tmpServiceItem = new ServiceItem();
            mapToBean(row, tmpServiceItem);
            return tmpServiceItem;
        }).findFirst().orElse(null);

        if (serviceItem == null || serviceItem.getNumCalls() == 0) {
            return null;
        }
        List<Map<String, Object>> errorRows = clickhouseHandlerFactory.get().query(TRACE_DATABASE_NAME, errorQuery);
        Long numErrors = errorRows.stream().map(row -> ((UnsignedLong) row.get("numErrors")).longValue()).findFirst().orElse(0L);
        serviceItem.setServiceName(serviceName);
        serviceItem.setNumErrors(numErrors);
        serviceItem.setDataWarning(new DataWarning(ops));
        return serviceItem;
    }

    public Map<String, List<String>> getTopLevelOperations(TopLevelOperationsQueryParam queryParam) {


        Map<String, List<String>> operationMap = new HashMap<>();

        StringBuilder queryBuilder = new StringBuilder(String.format("SELECT name, serviceName, max(time) as ts FROM %s.%s WHERE time >= '%s'",
                TRACE_DATABASE_NAME, TRACE_TOP_LEVEL_OPERATIONS_TABLE, TimeUnit.NANOSECONDS.toSeconds(queryParam.getStart())));

//        if (CollectionUtils.isNotEmpty(queryParam.getServiceNames())) {
//            queryBuilder.append(" AND serviceName IN ('").append(Joiner.on("','").join(queryParam.getServiceNames())).append("')");
//        }
        queryBuilder.append(" AND serviceName ='").append(ThreadLocalStore.getTenantNameLocal()).append("'");
        queryBuilder.append(" GROUP BY name, serviceName ORDER BY ts DESC LIMIT 5000");
        List<Map<String, Object>> rows = clickhouseHandlerFactory.get().query(TRACE_DATABASE_NAME, queryBuilder.toString());


        List<SysParaDO> sysParaDOS = sysParaHandler.listByType(new SysParaQuery(SKIP_SERVICE_CONFIGS));
        Map<String, Set<String>> skipConfig = sysParaDOS.stream()
                .filter(sysParaDO -> StringUtils.isNotEmpty(sysParaDO.getValue()))
                .flatMap(sysParaDO -> {
                    Map<String, Set<String>> traceServiceSkipConfigs = JsonUtils.toObjectByTypeRef(sysParaDO.getValue(), new TypeReference<>() {
                    });
                    return traceServiceSkipConfigs.entrySet().stream();
                }).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, new BinaryOperator<Set<String>>() {
                    @Override
                    public Set<String> apply(Set<String> oldOne, Set<String> newOne) {
                        return Sets.union(oldOne, newOne);
                    }
                }));

        for (Map<String, Object> row : rows) {
            String operationName = (String) row.get("name");
            String serviceName = (String) row.get("serviceName");

            List<String> topLevelOperations = operationMap.computeIfAbsent(serviceName, s -> Lists.newArrayList());

            if (skipConfig.containsKey(serviceName) && skipConfig.get(serviceName).contains(operationName)) {
                continue;
            }

            topLevelOperations.add(operationName);
        }
        return operationMap;
    }

    public List<TopOperationsItem> queryServiceTopOperations(BasicTraceQueryParam params) {
        StringBuilder queryBuilder = new StringBuilder(String.format(
                "SELECT quantile(0.5)(durationNano) as p50, " +
                        "quantile(0.95)(durationNano) as p95, " +
                        "quantile(0.99)(durationNano) as p99, " +
                        "COUNT(*) as numCalls, " +
                        "countIf(statusCode=2) as errorCount, " +
                        "name " +
                        "FROM %s.%s " +
                        "WHERE serviceName = '%s' AND timestamp >= '%s' AND timestamp <= '%s'",
                TRACE_DATABASE_NAME, TraceUtils.getTraceIndexTable(false), ThreadLocalStore.getTenantNameLocal(), params.getStart(),
                params.getEnd()));
        String subQuery = buildTagQuerySql(params.getTags());
        queryBuilder.append(subQuery).append(" GROUP BY name ORDER BY p99 DESC");
        if (params.getLimit() != null && params.getLimit() > 0) {
            queryBuilder.append(" LIMIT ").append(params.getLimit());
        }
        List<Map<String, Object>> rows = clickhouseHandlerFactory.get().query(TRACE_DATABASE_NAME, queryBuilder.toString());
        return rows.stream().map(row -> {
            TopOperationsItem topOperationsItem = new TopOperationsItem();

            mapToBean(row, topOperationsItem);
            return topOperationsItem;
        }).toList();
    }

    private List<TagQuery> buildTagQueryCondition(List<TagQueryParam> tags) {
        return tags.stream().flatMap(tag -> {
            TagQuery<Boolean> booleanTagQuery = TagQuery.fromQueryParam(tag.getKey(), tag.getOperator(), tag.getTagType(), tag.getBoolValues());
            TagQuery<String> stringTagQuery = TagQuery.fromQueryParam(tag.getKey(), tag.getOperator(), tag.getTagType(), tag.getStringValues());
            TagQuery<Double> numberTagQuery = TagQuery.fromQueryParam(tag.getKey(), tag.getOperator(), tag.getTagType(), tag.getNumberValues());
            List<TagQuery> queries = Lists.newArrayList();
            Optional.ofNullable(booleanTagQuery).ifPresent(i -> queries.add(i));
            Optional.ofNullable(stringTagQuery).ifPresent(i -> queries.add(i));
            Optional.ofNullable(numberTagQuery).ifPresent(i -> queries.add(i));
            return queries.stream();
        }).toList();
    }

    private String buildTagQuerySql(List<TagQueryParam> tags) {
        List<TagQuery> tagQueries = buildTagQueryCondition(tags);
        List<String> subQueries = tagQueries.stream().map(this::buildOneSubTagQuery).toList();
        return Joiner.on(EMPTY).join(subQueries);
    }

    private String buildOneSubTagQuery(TagQuery item) {
        String notStr = StringUtils.EMPTY;
        TraceQueryOperator operator = item.getOperator();
        List<String> tagValuePair = new ArrayList<>();
        List<String> values = item.getValues();
        for (String value : values) {
            String sql;
            switch (operator) {
                case Exists:
                    sql = String.format("mapContains(%s, '%s')", item.getTagMapColumn(), item.getKey());
                    break;
                case NotExists:
                    sql = String.format("NOT mapContains(%s, '%s')", item.getTagMapColumn(), item.getKey());
                    break;
                case Contains:
                case NotContains:
                case StartsWith:
                case NotStartsWith:
                    value = "%" + value + "%";
                default:
                    sql = String.format("%s['%s'] %s '%s'", item.getTagMapColumn(), item.getKey(), operator, value);
                    break;
            }
            tagValuePair.add(sql);
        }

        return String.format(" AND (%s)", notStr, String.join(" OR ", tagValuePair));
    }

    private void mapToBean(Map<String, Object> map, Object bean) {
        try {
            BeanUtils.copyProperties(bean, map);
        } catch (Throwable e) {
            logger.error("copy to bean failed, row: {}", map, e);
        }
    }

    public List<TraceError> listTraceErrors(ListErrorsParams params) {
        StringBuilder queryBuilder = new StringBuilder("SELECT any(exceptionMessage) as exceptionMessage, count() AS exceptionCount, min(timestamp) as firstSeen, max(timestamp) as lastSeen, groupID");
        String serviceConditionSql = EMPTY;
        String exceptionTypeConditionSql = EMPTY;
        String groupSql = " GROUP BY groupID";
        if (StringUtils.isNotEmpty(params.getExceptionType())) {
            exceptionTypeConditionSql = String.format(" AND exceptionType ilike '%s'", "%" + params.getExceptionType() + "%");
            queryBuilder.append(", exceptionType");
            groupSql += ", exceptionType";
        } else {
            queryBuilder.append(", any(exceptionType) as exceptionType");
        }
        queryBuilder.append(String.format(" FROM %s.%s WHERE timestamp >= '%s' AND timestamp <= '%s'", TRACE_DATABASE_NAME, TraceUtils.getTraceErrorIndexTable(), params.getStart(),
                        params.getEnd()))
                .append(serviceConditionSql)
                .append(exceptionTypeConditionSql)
                .append(buildTagQuerySql(params.getTags()))
                .append(groupSql);

        boolean usingServiceName = StringUtils.equals(params.getOrderParam(), "serviceName");
        if (usingServiceName) {
            params.setOrderParam("exceptionCount");
        }
        if (StringUtils.isNotEmpty(params.getOrderParam())) {
            queryBuilder.append(" ORDER BY ").append(params.getOrderParam()).append(StringUtils.SPACE).append(params.getOrder().getValue());
        }

        if (params.getLimit() != null && params.getLimit() > 0) {
            queryBuilder.append(" LIMIT ").append(params.getLimit());
        }

        if (params.getOffset() != null && params.getOffset() > 0) {
            queryBuilder.append(" OFFSET ").append(params.getOffset());
        }

        List<Map<String, Object>> rows = clickhouseHandlerFactory.get().query(TRACE_DATABASE_NAME, queryBuilder.toString());

        return rows.stream().map(row -> {
            TraceError traceError = new TraceError();
            mapToBean(row, traceError);
            traceError.setServiceName(ThreadLocalStore.getTenantNameLocal());
            return traceError;
        }).toList();
    }

    public Long countErrors(ListErrorsParams params) {
        StringBuilder queryBuilder = new StringBuilder(String.format("SELECT count(distinct(groupID)) AS numErrors FROM %s.%s " +
                        "WHERE timestamp >= '%s' AND timestamp <= '%s'", TRACE_DATABASE_NAME, TraceUtils.getTraceErrorIndexTable(), params.getStart(),
                params.getEnd()));
        if (StringUtils.isNotEmpty(params.getExceptionType())) {
            queryBuilder.append(" AND exceptionType ilike '%").append(params.getExceptionType()).append("%'");
        }
        queryBuilder.append(buildTagQuerySql(params.getTags()));
        List<Map<String, Object>> errorRows = clickhouseHandlerFactory.get().query(TRACE_DATABASE_NAME, queryBuilder.toString());
        return errorRows.stream().map(row -> ((UnsignedLong) row.get("numErrors")).longValue()).findFirst().orElse(0L);
    }

    public ErrorWithSpan getErrorWithSpanList(GetErrorParams params) {
        StringBuilder queryBuilder = new StringBuilder(String.format("SELECT errorID, exceptionType, exceptionStacktrace, exceptionEscaped, exceptionMessage, " +
                        "timestamp, spanID, traceID, groupID FROM %s.%s WHERE timestamp = '%s'", TRACE_DATABASE_NAME, TraceUtils.getTraceErrorIndexTable(),
                params.getTimestamp()));
        if (StringUtils.isNotEmpty(params.getGroupID())) {
            queryBuilder.append(" AND groupID = '").append(params.getGroupID()).append("'");
        }
        if (StringUtils.isNotEmpty(params.getErrorID())) {
            queryBuilder.append(" AND errorID = '").append(params.getErrorID()).append("'");
        }
        queryBuilder.append(" LIMIT 1");
        List<Map<String, Object>> errorRows = clickhouseHandlerFactory.get().query(TRACE_DATABASE_NAME, queryBuilder.toString());

        if (CollectionUtils.isEmpty(errorRows)) {
            return new ErrorWithSpan();
        }
        return errorRows.stream().map(row -> {
            ErrorWithSpan errorWithSpan = new ErrorWithSpan();
            mapToBean(row, errorWithSpan);
            errorWithSpan.setServiceName(ThreadLocalStore.getTenantNameLocal());
            return errorWithSpan;
        }).findFirst().orElse(new ErrorWithSpan());
    }

    public NextPrevErrorIDs getNextPrevErrorID(GetErrorParams params) {


        NextPrevErrorIDs nextErrorID = getNextOrPrevErrorID(params, true);
        NextPrevErrorIDs prevErrorID = getNextOrPrevErrorID(params, false);

        NextPrevErrorIDs result = new NextPrevErrorIDs();
        result.setGroupID(params.getGroupID());

        if (nextErrorID != null) {
            result.setNextErrorID(nextErrorID.getNextErrorID());
            result.setNextTimestamp(nextErrorID.getNextTimestamp());
        }
        if (prevErrorID != null) {
            result.setPrevErrorID(prevErrorID.getPrevErrorID());
            result.setPrevTimestamp(prevErrorID.getPrevTimestamp());
        }
        return result;
    }

    private NextPrevErrorIDs getNextOrPrevErrorID(GetErrorParams params, boolean next) {

        StringBuilder queryBuilder = new StringBuilder(String.format("SELECT errorID as nextErrorID, timestamp as nextTimestamp " +
                        "FROM %s.%s WHERE timestamp %s '%s'", TRACE_DATABASE_NAME, TraceUtils.getTraceErrorIndexTable(), next ? ">=" : "<=",
                params.getTimestamp()));
        if (StringUtils.isNotEmpty(params.getGroupID())) {
            queryBuilder.append(" AND groupID = '").append(params.getGroupID()).append("'");
        }
        if (StringUtils.isNotEmpty(params.getErrorID())) {
            queryBuilder.append(" AND errorID != '").append(params.getErrorID()).append("'");
        }
        queryBuilder.append(String.format(" ORDER BY timestamp %s LIMIT 2", next ? "ASC" : "DESC"));
        List<Map<String, Object>> rows = clickhouseHandlerFactory.get().query(TRACE_DATABASE_NAME, queryBuilder.toString());
        List<NextPrevErrorIDs> nextPrevErrorIDsList = rows.stream().map(row -> {
            NextPrevErrorIDs nextPrevErrorIDs = new NextPrevErrorIDs();
            mapToBean(row, nextPrevErrorIDs);
            return nextPrevErrorIDs;
        }).toList();
        if (CollectionUtils.isEmpty(nextPrevErrorIDsList)) {
            return null;
        } else if (nextPrevErrorIDsList.size() == 1) {
            return nextPrevErrorIDsList.get(0);
        } else {
            NextPrevErrorIDs currentErrorIDs = nextPrevErrorIDsList.get(0);
            NextPrevErrorIDs nextErrorIDs = nextPrevErrorIDsList.get(1);
            if (currentErrorIDs.getTimestamp() == nextErrorIDs.getTimestamp()) {
                queryBuilder = new StringBuilder(String.format("SELECT errorID as nextErrorID, timestamp as nextTimestamp " +
                                "FROM %s.%s WHERE timestamp = '%s'", TRACE_DATABASE_NAME, TraceUtils.getTraceErrorIndexTable(),
                        params.getTimestamp()));
                if (StringUtils.isNotEmpty(params.getGroupID())) {
                    queryBuilder.append(" AND groupID = '").append(params.getGroupID()).append("'");
                }
                if (StringUtils.isNotEmpty(params.getErrorID())) {
                    queryBuilder.append(String.format(" AND errorID %s '", next ? ">" : "<")).append(params.getErrorID()).append("'");
                }
                queryBuilder.append(String.format(" ORDER BY errorID %s LIMIT 1", next ? "ASC" : "DESC"));
                rows = clickhouseHandlerFactory.get().query(TRACE_DATABASE_NAME, queryBuilder.toString());
                nextPrevErrorIDsList = rows.stream().map(row -> {
                    NextPrevErrorIDs nextPrevErrorIDs = new NextPrevErrorIDs();
                    mapToBean(row, nextPrevErrorIDs);
                    return nextPrevErrorIDs;
                }).toList();
                if (CollectionUtils.isEmpty(nextPrevErrorIDsList)) {
                    queryBuilder = new StringBuilder(String.format("SELECT errorID as nextErrorID, timestamp as nextTimestamp " +
                                    "FROM %s.%s WHERE timestamp %s '%s'", TRACE_DATABASE_NAME, TraceUtils.getTraceErrorIndexTable(), next ? ">" : "<",
                            params.getTimestamp()));
                    if (StringUtils.isNotEmpty(params.getGroupID())) {
                        queryBuilder.append(" AND groupID = '").append(params.getGroupID()).append("'");
                    }
                    queryBuilder.append(String.format(" ORDER BY timestamp %s LIMIT 1", next ? "ASC" : "DESC"));
                    rows = clickhouseHandlerFactory.get().query(TRACE_DATABASE_NAME, queryBuilder.toString());
                    nextPrevErrorIDsList = rows.stream().map(row -> {
                        NextPrevErrorIDs nextPrevErrorIDs = new NextPrevErrorIDs();
                        mapToBean(row, nextPrevErrorIDs);
                        return nextPrevErrorIDs;
                    }).toList();
                    if (CollectionUtils.isEmpty(nextPrevErrorIDsList)) {
                        return null;
                    } else {
                        return nextPrevErrorIDsList.get(0);
                    }
                } else {
                    return nextPrevErrorIDsList.get(0);
                }
            } else {
                return currentErrorIDs;
            }
        }
    }

    public List<ClusterAndRegion> getTraceServiceMetaData() {
        List<Map<String, Object>> rows = clickhouseHandlerFactory.get().query(TRACE_DATABASE_NAME, String.format(CLUSTER_AND_REGION_QUERY, TRACE_DATABASE_NAME,
                TraceUtils.getTraceIndexTable(false), ThreadLocalStore.getTenantNameLocal()));
        return rows.stream().map(clusterRegionMap -> {
            ClusterAndRegion clusterAndRegion = new ClusterAndRegion();
            mapToBean(clusterRegionMap, clusterAndRegion);
            return clusterAndRegion;
        }).toList();
    }

    public List<ServiceMapDependencyResponseItem> getDependencyGraph(BasicTraceQueryParam param) {
        long startSecondTime = TimeUnit.NANOSECONDS.toSeconds(param.getStart());
        long endSecondTime = TimeUnit.NANOSECONDS.toSeconds(param.getEnd());
        String subSql = EMPTY;
        Set<ServiceMapDependencyResponseItem> items = Sets.newConcurrentHashSet();
        String serviceName = ThreadLocalStore.getTenantNameLocal();
        List<CompletableFuture<Void>> futures = Collections.emptyList();
        if (serviceName != null) {
            List<Supplier<List<ServiceMapDependencyResponseItem>>> queries = Lists.newArrayList();
            queries.add(() -> queryForUpOrDownStreamService(param.getStart(), param.getEnd(), serviceName, true));
            queries.add(() -> queryForUpOrDownStreamService(param.getStart(), param.getEnd(), serviceName, false));
            futures = AsyncUtils.asyncParallelExecuteWithoutResult(queries, s -> {
                List<ServiceMapDependencyResponseItem> serviceMapDependencyResponseItems = s.get();
                if (CollectionUtils.isNotEmpty(serviceMapDependencyResponseItems)) {
                    items.addAll(serviceMapDependencyResponseItems);
                }
            }, (e, c) -> logger.error("query mq dependency for service {} failed", serviceName, e), queryExecutor);
            subSql = "AND (src = '%s' OR dest = '%s') AND (dest != 'asyncmq')".formatted(serviceName, serviceName);
        }
        String query = String.format("WITH " +
                        "quantilesMergeState(0.5, 0.75, 0.9, 0.95, 0.99)(duration_quantiles_state) AS duration_quantiles_state, " +
                        "finalizeAggregation(duration_quantiles_state) AS result " +
                        "SELECT " +
                        "src as parent, " +
                        "dest as child, " +
                        "result[1] AS p50, " +
                        "result[2] AS p75, " +
                        "result[3] AS p90, " +
                        "result[4] AS p95, " +
                        "result[5] AS p99, " +
                        "sum(total_count) as callCount, " +
                        "sum(total_count)/ %d AS callRate, " +
                        "sum(error_count)/sum(total_count) * 100 as errorRate " +
                        "FROM %s.%s " +
                        "WHERE toUInt64(toDateTime(timestamp)) >= %d AND toUInt64(toDateTime(timestamp)) <= %d %s GROUP BY src, dest", endSecondTime - startSecondTime,
                TRACE_DATABASE_NAME, TRACE_DEPENDENCY_GRAPH_TABLE, startSecondTime, endSecondTime, subSql);
        List<Map<String, Object>> rows = clickhouseHandlerFactory.get().query(TRACE_DATABASE_NAME, query);
        items.addAll(rows.stream().map(row -> {
            ServiceMapDependencyResponseItem item = new ServiceMapDependencyResponseItem();
            mapToBean(row, item);
            return item;
        }).toList());
        futures.forEach(CompletableFuture::join);
        return items.stream().toList();
    }

    private List<ServiceMapDependencyResponseItem> queryForUpOrDownStreamService(long start, long end, String serviceName, boolean downStream) {
        List<ServiceMapDependencyResponseItem> items = Lists.newArrayList();
        List<QueryRangeResult> result = traceV3Service.queryRange(buildRangeQueryParam(start, end, serviceName, downStream, null, Lists.newArrayList(AggregateOperator.P99, AggregateOperator.COUNT))).getResult();
        Map<String, Map<String, Integer>> topicNameToStatusCoumtMap = Maps.newHashMap();
        Map<String, Double> topicNameToP99LatencyMap = Maps.newHashMap();
        List<String> topicNames = null;
        for (QueryRangeResult queryRangeResult : result) {
            if (queryRangeResult.getQueryName().equals(AggregateOperator.COUNT.getOperator())) {
                for (Series series : queryRangeResult.getSeries()) {
                    Map<String, Integer> statusToCountMap = topicNameToStatusCoumtMap.computeIfAbsent(series.getLabels().get("messaging.destination.name"), t -> Maps.newHashMap());
                    statusToCountMap.putIfAbsent(series.getLabels().get("hasError"), series.getValues().stream().findFirst().map(point -> Integer.valueOf(point.getValue())).orElse(0));
                }
                topicNames = Lists.newArrayList(topicNameToStatusCoumtMap.keySet());
            } else {
                topicNameToP99LatencyMap.putAll(queryRangeResult.getSeries().stream()
                        .collect(Collectors.toMap(rs -> rs.getLabels().get("messaging.destination.name"), r -> {
                            return r.getValues().stream().findFirst().map(point -> Double.valueOf(point.getValue())).orElse(0d);
                        })));
            }
        }
        if (CollectionUtils.isEmpty(topicNames)) {
            return items;
        }
        Set<String> bothExistTopics = Sets.newHashSet();
        result = Lists.partition(topicNames, 100).stream().flatMap(subTopicNames -> {
            return traceV3Service.queryRange(buildRangeQueryParam(start, end, serviceName, !downStream, subTopicNames, Lists.newArrayList(AggregateOperator.P99, AggregateOperator.COUNT))).getResult().stream();
        }).toList();
        Map<String, Map<String, Map<String, Integer>>> topicNameServiceNameToStatusCoumtMap = Maps.newHashMap();
        Map<String, Map<String, Double>> topicNameServiceNameToP99LatencyMap = Maps.newHashMap();
        for (QueryRangeResult queryRangeResult : result) {
            if (queryRangeResult.getQueryName().equals(AggregateOperator.COUNT.getOperator())) {
                for (Series series : queryRangeResult.getSeries()) {
                    String topicName = series.getLabels().get("messaging.destination.name");
                    bothExistTopics.add(topicName);
                    Map<String, Map<String, Integer>> serviceStatusToCountMap = topicNameServiceNameToStatusCoumtMap.computeIfAbsent(topicName, t -> Maps.newHashMap());
                    Map<String, Integer> statusCountMap = serviceStatusToCountMap.computeIfAbsent(series.getLabels().get("serviceName"), s -> Maps.newHashMap());
                    statusCountMap.putIfAbsent(series.getLabels().get("hasError"), series.getValues().stream().findFirst().map(point -> Integer.valueOf(point.getValue())).orElse(0));
                }
            } else {
                for (Series series : queryRangeResult.getSeries()) {
                    Map<String, Double> serviceNameToP99Map = topicNameServiceNameToP99LatencyMap.computeIfAbsent(series.getLabels().get("messaging.destination.name"), t -> Maps.newHashMap());
                    serviceNameToP99Map.putIfAbsent(series.getLabels().get("serviceName"), series.getValues().stream().findFirst().map(point -> Double.valueOf(point.getValue())).orElse(0d));
                }
            }
        }
        if (CollectionUtils.isEmpty(bothExistTopics)) {
            return items;
        }
        long durationSeconds = TimeUnit.NANOSECONDS.toSeconds(end) - TimeUnit.NANOSECONDS.toSeconds(start);
        items.addAll(bothExistTopics.stream().flatMap(topicName -> {
            List<ServiceMapDependencyResponseItem> subItems = Lists.newArrayList();
            Map<String, Integer> statusToCountMap = topicNameToStatusCoumtMap.get(topicName);
            Integer successCount = statusToCountMap.getOrDefault(Boolean.FALSE.toString(), 0);
            Integer failureCount = statusToCountMap.getOrDefault(Boolean.TRUE.toString(), 0);
            subItems.add(buildServiceDependencyResponseItem(downStream, successCount + failureCount, failureCount, topicName,
                    serviceName, durationSeconds, topicNameToP99LatencyMap.get(topicName)));

            Map<String, Map<String, Integer>> serviceStatusCountMap = topicNameServiceNameToStatusCoumtMap.get(topicName);
            if (serviceStatusCountMap == null) {
                return subItems.stream();
            }
            serviceStatusCountMap.forEach((dependentService, statusCountMap) -> {
                Integer dependentSuccessCount = statusCountMap.getOrDefault(Boolean.FALSE.toString(), 0);
                Integer dependentFailureCount = statusCountMap.getOrDefault(Boolean.TRUE.toString(), 0);
                subItems.add(buildServiceDependencyResponseItem(!downStream, dependentSuccessCount + dependentFailureCount, dependentFailureCount, topicName, dependentService,
                        durationSeconds, topicNameServiceNameToP99LatencyMap.get(topicName).get(dependentService)));
            });
            return subItems.stream();
        }).toList());
        return items;
    }

    private ServiceMapDependencyResponseItem buildServiceDependencyResponseItem(boolean downStream, Integer callCount, Integer failureCount, String topicName, String serviceName,
                                                                                long durationSeconds, double p99) {
        ServiceMapDependencyResponseItem item = new ServiceMapDependencyResponseItem();
        if (downStream) {
            item.setParent(serviceName);
            item.setChild(topicName);
        } else {
            item.setParent(topicName);
            item.setChild(serviceName);
        }
        item.setCallCount(callCount);
        item.setErrorRate((double) failureCount / callCount);
        item.setCallRate((double) callCount / durationSeconds);
        item.setP99(p99);
        return item;
    }

    private TraceQueryRangeParam buildRangeQueryParam(long start, long end, String serviceName, boolean downStream, Collection<String> topicNames,
                                                      List<AggregateOperator> aggregateOperators) {
        TraceQueryRangeParam param = new TraceQueryRangeParam();
        param.setStart(start);
        param.setEnd(end);
        CompositeQuery compositeQuery = new CompositeQuery();
        compositeQuery.setPanelType(PanelType.TABLE.getType());
        compositeQuery.setQueryType(QueryType.BUILDER.getType());
        Map<String, BuilderQuery> builderQueries = Maps.newHashMap();
        compositeQuery.setBuilderQueries(builderQueries);
        param.setCompositeQuery(compositeQuery);
        for (AggregateOperator aggregateOperator : aggregateOperators) {
            BuilderQuery builderQuery = buildQueryParam(serviceName, aggregateOperator.getOperator(), aggregateOperator, downStream, topicNames);
            builderQueries.put(aggregateOperator.getOperator(), builderQuery);
        }
        return param;
    }

    private BuilderQuery buildQueryParam(String serviceName, String queryName, AggregateOperator aggregateOperator,
                                         boolean producer, Collection<String> topicNames) {
        BuilderQuery builderQuery = new BuilderQuery();
        builderQuery.setDataSource(DataSource.TRACES.getValue());
        builderQuery.setQueryName(queryName);
        builderQuery.setStepInterval(60);
        builderQuery.setLimit(10000);
        builderQuery.setHaving(Collections.emptyList());
        builderQuery.setAggregateOperator(aggregateOperator.getOperator());
        builderQuery.setFunctions(Collections.emptyList());
        builderQuery.setExpression(queryName);
        builderQuery.setGroupBy(Lists.newArrayList(buildAttributeKey("messaging.destination.name", AttributeKeyDataType.STRING, AttributeKeyType.TAG, false)));
        builderQuery.setOrderBy(Lists.newArrayList(BuilderQuery.OrderBy.builder().columnName("timestamp").order("desc").build()));
        if (aggregateOperator == AggregateOperator.COUNT) {
            builderQuery.getGroupBy().add(buildAttributeKey("hasError", AttributeKeyDataType.BOOL, AttributeKeyType.TAG, true));
            builderQuery.setAggregateAttribute(buildAttributeKey(EMPTY, AttributeKeyDataType.UNSPECIFIED, AttributeKeyType.UNSPECIFIED, false));
        } else if (aggregateOperator == AggregateOperator.P99) {
            builderQuery.setAggregateAttribute(buildAttributeKey("durationNano", AttributeKeyDataType.UNSPECIFIED, AttributeKeyType.UNSPECIFIED, false));
        } else if (aggregateOperator == AggregateOperator.NO_OP) {

        }
        BuilderQuery.FilterSet filterSet = new BuilderQuery.FilterSet();
        List<BuilderQuery.FilterItem> filterItems = Lists.newArrayList();
        filterSet.setOp("AND");
        filterSet.setItems(filterItems);
        filterItems.add(buildFilterItem("spanKind", producer ? "SPAN_KIND_PRODUCER" : "SPAN_KIND_CONSUMER", FilterOperator.EQUAL, AttributeKeyDataType.STRING, AttributeKeyType.TAG, true));
        // query by topic names
        if (CollectionUtils.isNotEmpty(topicNames)) {
            builderQuery.getGroupBy().add(buildAttributeKey("serviceName", AttributeKeyDataType.STRING, AttributeKeyType.TAG, true));
            // query not self services
            filterItems.add(buildFilterItem("serviceName", serviceName, FilterOperator.NOT_EQUAL, AttributeKeyDataType.STRING, AttributeKeyType.TAG, true));
            filterItems.add(buildFilterItem("messaging.destination.name", topicNames, FilterOperator.IN, AttributeKeyDataType.STRING, AttributeKeyType.TAG, false));
        } else {
            filterItems.add(buildFilterItem("serviceName", serviceName, FilterOperator.EQUAL, AttributeKeyDataType.STRING, AttributeKeyType.TAG, true));
            filterItems.add(buildFilterItem("messaging.destination.name", "internal_topic%", FilterOperator.NOT_LIKE, AttributeKeyDataType.STRING, AttributeKeyType.TAG, false));
        }
        builderQuery.setFilters(filterSet);
        return builderQuery;
    }

    private static BuilderQuery.FilterItem buildFilterItem(String key, Object value, FilterOperator op, AttributeKeyDataType dataType, AttributeKeyType keyType, boolean column) {
        BuilderQuery.FilterItem filterItem = new BuilderQuery.FilterItem();
        filterItem.setOp(op.getOperator());
        filterItem.setValue(value);
        filterItem.setKey(buildAttributeKey(key, dataType, keyType, column));
        return filterItem;
    }

    private static BuilderQuery.AttributeKey buildAttributeKey(String key, AttributeKeyDataType dataType, AttributeKeyType keyType, boolean column) {
        return BuilderQuery.AttributeKey.builder()
                .dataType(dataType.getDataType())
                .key(key)
                .type(keyType.getType())
                .isColumn(column)
                .build();
    }

    /**
     * Query traces
     *
     * @param request Query request
     * @return Query result
     */
    public PageResult<TraceItem> queryTraces(TraceQueryRequest request) {
        // Build base filter conditions
        String baseFilterSql = buildBaseFilterSql(request);

        // Prepare queries for async execution
        String countSql = "SELECT count() as total " + baseFilterSql;
        int offset = (request.getPageIndex() - 1) * request.getPageSize();
        String dataSql = "SELECT traceID, spanID, stringTagMap['trackingId'] AS trackingId, durationNano, timestamp, serviceName, name, spanKind, hasError, responseStatusCode, statusMessage "
                + baseFilterSql + " ORDER BY timestamp DESC LIMIT " + request.getPageSize() + " OFFSET " + offset;

        // Execute queries asynchronously
        CompletableFuture<List<Map<String, Object>>> countFuture = CompletableFuture.supplyAsync(() ->
                clickhouseHandlerFactory.get().query(TRACE_DATABASE_NAME, countSql), queryExecutor);

        CompletableFuture<List<Map<String, Object>>> dataFuture = CompletableFuture.supplyAsync(() ->
                clickhouseHandlerFactory.get().query(TRACE_DATABASE_NAME, dataSql), queryExecutor);

        // Wait for queries to complete
        CompletableFuture.allOf(countFuture, dataFuture).join();

        // Process results
        int total = countFuture.join().stream().mapToInt(row -> Integer.parseInt(String.valueOf(row.get("total")))).findFirst().orElse(0);
        List<Map<String, Object>> queryResult = dataFuture.join();

        // Build response
        List<TraceItem> items = queryResult.stream().map(this::buildTraceItem).collect(Collectors.toList());

        return new PageResult<>(total, items);
    }

    /**
     * Query trace histogram data
     *
     * @param request Query request
     * @return Histogram data
     */
    public List<TraceHistogram> queryTraceHistogram(TraceQueryRequest request) {
        // Build base filter conditions
        String baseFilterSql = buildBaseFilterSql(request);

        // Build histogram SQL
        String histogramSql = buildHistogramSql(request.getStart(), request.getEnd(), baseFilterSql);

        // Execute histogram query
        List<Map<String, Object>> histogramResult = clickhouseHandlerFactory.get().query(TRACE_DATABASE_NAME, histogramSql);

        // Build histogram response
        return buildHistogramsFromResult(histogramResult);
    }

    /**
     * Build base filter SQL for trace queries
     *
     * @param request Query request
     * @return Base filter SQL string
     */
    private String buildBaseFilterSql(TraceQueryRequest request) {
        StringBuilder filterBuilder = new StringBuilder();
        filterBuilder.append(" FROM ").append(TRACE_DATABASE_NAME).append(".").append(TraceUtils.getTraceIndexTable(false));
        filterBuilder.append(" WHERE time >= toDateTime(").append(request.getStart()).append(" / 1000) AND time <= toDateTime(")
                .append(request.getEnd()).append(" / 1000) AND serviceName = '").append(ThreadLocalStore.getTenantNameLocal()).append("'").append(" AND kind IN (2,5)");

        // Get attribute key mapping
        Map<String, AttributeKey> attributeKeyMap = traceV3Service.getSpanAttributeKeys();

        // Build filter conditions
        FilterSet filterSet = buildFilterSet(request, attributeKeyMap);
        String filterQuery = traceQueryBuilder.buildTracesFilterQuery(filterSet, attributeKeyMap);
        if (StringUtils.isNotEmpty(filterQuery)) {
            filterBuilder.append(filterQuery);
        }

        return filterBuilder.toString();
    }

    private TraceItem buildTraceItem(Map<String, Object> row) {
        String traceId = (String) row.get("traceID");
        String trackingId = (String) row.get("trackingId");
        if (StringUtils.equals(trackingId, "null")) {
            trackingId = null;
        }
        String spanId = (String) row.get("spanID");
        Long durationNano = Long.parseLong(String.valueOf(row.get("durationNano")));
        Timestamp timestamp = (Timestamp) row.get("timestamp");
        String name = (String) row.get("name");
        String spanKind = (String) row.get("spanKind");
        Boolean hasError = Boolean.parseBoolean(String.valueOf(row.get("hasError")));
        Object responseStatusCodeOrigin = row.get("responseStatusCode");
        Integer statusCode = null;
        if (responseStatusCodeOrigin != null && StringUtils.isNotBlank((String) responseStatusCodeOrigin)) {
            statusCode = Integer.parseInt((String) responseStatusCodeOrigin);
        }
        String[] split = spanKind.split("_");
        spanKind = split[split.length - 1].toLowerCase();

        return TraceItem.builder()
                .traceId(traceId)
                .trackingId(trackingId)
                .spanId(spanId)
                .durationNano(durationNano)
                .startTimeUtc(new Date(timestamp.getTime()))
                .kind(spanKind)
                .hasError(hasError)
                .name(name)
                .statusCode(statusCode)
                .build();
    }

    private FilterSet buildFilterSet(TraceQueryRequest request, Map<String, AttributeKey> attributeKeyMap) {
        List<FilterItem> filterItems = new ArrayList<>();

        // Add region filter
        if (StringUtils.isNotEmpty(request.getRegion())) {
            FilterItem regionFilter = FilterItem.builder()
                    .key(AttributeKey.builder()
                            .key("regionId")
                            .type(AttributeKeyType.TAG.getType())
                            .dataType(AttributeKeyDataType.STRING.getDataType())
                            .isColumn(false)
                            .build())
                    .op(FilterOperator.EQUAL.getOperator())
                    .value(request.getRegion())
                    .build();
            filterItems.add(regionFilter);
        }

        // Add cluster filter
        if (StringUtils.isNotEmpty(request.getCluster())) {
            FilterItem clusterFilter = FilterItem.builder()
                    .key(AttributeKey.builder()
                            .key("appcluster")
                            .type(AttributeKeyType.TAG.getType())
                            .dataType(AttributeKeyDataType.STRING.getDataType())
                            .isColumn(false)
                            .build())
                    .op(FilterOperator.EQUAL.getOperator())
                    .value(request.getCluster())
                    .build();
            filterItems.add(clusterFilter);
        }

        // Add custom filter conditions
        if (CollectionUtils.isNotEmpty(request.getFilters())) {
            // Group filters by key and operator to handle multiple EQUAL filters for the same key
            List<FilterItem> queryFilterItems = QueryFilter.toQueryFilterItems(request.getFilters(), attributeKeyMap);
            filterItems.addAll(queryFilterItems);
        }

        return FilterSet.builder()
                .op(AND.toUpperCase())
                .items(filterItems)
                .build();
    }


    private String buildHistogramSql(Long start, Long end, String filter) {
        // Calculate appropriate time step
        TimeStep timeStep = calculateStep(start, end);

        // Build histogram SQL with toStartOfInterval
        return String.format(
                "SELECT toStartOfInterval(timestamp, INTERVAL 1 %s) AS time_bucket, count() as count %s" +
                        "GROUP BY time_bucket " +
                        "ORDER BY time_bucket",
                timeStep.getUnit(), filter);
    }

    private List<TraceHistogram> buildHistogramsFromResult(List<Map<String, Object>> result) {
        List<TraceHistogram> histograms = new ArrayList<>();

        for (Map<String, Object> row : result) {
            Object timestampObj = row.get("time_bucket");
            long timestamp;
            if (timestampObj instanceof Long) {
                timestamp = (Long) timestampObj;
            } else if (timestampObj instanceof Timestamp) {
                timestamp = ((Timestamp) timestampObj).getTime();
            } else {
                timestamp = Long.parseLong(String.valueOf(timestampObj));
            }

            Integer count = Integer.parseInt(String.valueOf(row.get("count")));

            histograms.add(TraceHistogram.builder()
                    .timestamp(timestamp)
                    .count(count)
                    .build());
        }

        return histograms;
    }

    private TimeStep calculateStep(Long startTm, Long endTm) {
        Assert.notNull(startTm, "startTm can't be null");
        Assert.notNull(endTm, "endTm can't be null");
        if (endTm - startTm > 2 * TimeStep.Day.getMillSecond()) {
            return TimeStep.Day;
        } else if (endTm - startTm > 2 * TimeStep.Hour.getMillSecond()) {
            return TimeStep.Hour;
        } else if (endTm - startTm > 2 * TimeStep.Minute.getMillSecond()) {
            return TimeStep.Minute;
        } else {
            return TimeStep.Second;
        }
    }

    /**
     * Query service dependency graph
     *
     * @param request TraceDependencyRequest
     * @return TraceDependencyResponse
     */
    public TraceDependencyResponse queryTraceDependency(TraceDependencyRequest request) {
        logger.info("Querying trace dependency for request: {}", request);
        long startSecondTime = TimeUnit.MILLISECONDS.toSeconds(request.getStart());
        long endSecondTime = TimeUnit.MILLISECONDS.toSeconds(request.getEnd());
        long durationSeconds = endSecondTime - startSecondTime;

        // Query dependency graph data from ClickHouse
        String query = String.format(DEPENDENCY_QUERY_TEMPLATE,
                durationSeconds, TRACE_DATABASE_NAME, TRACE_DEPENDENCY_GRAPH_TABLE, startSecondTime, endSecondTime,
                ThreadLocalStore.getTenantNameLocal(), ThreadLocalStore.getTenantNameLocal());

        List<Map<String, Object>> rows = clickhouseHandlerFactory.get().query(TRACE_DATABASE_NAME, query);

        // Build nodes and edges from query results
        Map<String, TraceDependencyNode> nodeMap = Maps.newConcurrentMap();
        List<TraceDependencyEdge> edges = Lists.newArrayList();
        List<CompletableFuture<Void>> queryTasks = Lists.newArrayList();
        for (Map<String, Object> row : rows) {
            // Process dependency row and get another service
            String parent = (String) row.get("parent");
            String child = (String) row.get("child");
            // Create or update parent node
            TraceDependencyNode parentNode = nodeMap.computeIfAbsent(parent, k ->
                    TraceDependencyNode.builder()
                            .id(UUID.randomUUID().toString())
                            .name(parent)
                            .type(determineNodeType(parent, true))
                            .build()
            );
            // Create or update child node
            TraceDependencyNode childNode = nodeMap.computeIfAbsent(child, k ->
                    TraceDependencyNode.builder()
                            .id(UUID.randomUUID().toString())
                            .name(child)
                            .type(determineNodeType(child, false))
                            .build()
            );

            TraceDependencyEdge edge = ConvertUtils.mapToBean(row, TraceDependencyEdge.class);
            edge.setParentId(parentNode.getId());
            edge.setChildId(childNode.getId());
            edges.add(edge);
            String anotherService;
            if (StringUtils.equals(parent, ThreadLocalStore.getTenantNameLocal())) {
                anotherService = child;
            } else {
                anotherService = parent;
            }
            TraceDependencyNode anotherNode = nodeMap.get(anotherService);
            if (StringUtils.equals(anotherNode.getType(), TraceServiceMapNodeType.APPLICATION.getDesc())) {
                queryTasks.add(CompletableFuture.runAsync(() -> updateNodeCallRatesForBothDirections(anotherNode, durationSeconds, startSecondTime, endSecondTime)));
            } else {
                updateNodeCallRates(null, anotherNode, edge.getCallRate(), edge.getErrorRate());
            }
        }
        TraceDependencyNode currentServiceNode = nodeMap.get(ThreadLocalStore.getTenantNameLocal());
        updateNodeCallRatesForBothDirections(currentServiceNode, durationSeconds, startSecondTime, endSecondTime);
        queryTasks.forEach(CompletableFuture::join);
        return TraceDependencyResponse.builder()
                .nodes(Lists.newArrayList(nodeMap.values()))
                .edges(edges)
                .build();
    }


    /**
     * Update node call rates for both source and destination directions
     *
     * @param node            target node to update
     * @param durationSeconds duration in seconds
     * @param startSecondTime start time in seconds
     * @param endSecondTime   end time in seconds
     */
    private void updateNodeCallRatesForBothDirections(TraceDependencyNode node, long durationSeconds, long startSecondTime, long endSecondTime) {
        updateNodeCallRatesFromQuery(node, SOURCE_FIELD, durationSeconds, startSecondTime, endSecondTime, true);
        updateNodeCallRatesFromQuery(node, DESTINATION_FIELD, durationSeconds, startSecondTime, endSecondTime, false);
    }

    /**
     * Update middleware dependency incoming call rates from query results
     *
     * @param node            target node to update
     * @param serviceName     service name
     * @param durationSeconds duration in seconds
     * @param startSecondTime start time in seconds
     * @param endSecondTime   end time in seconds
     */
    private void updateMiddlewareDependencyIncomingCallRates(TraceDependencyNode node, String serviceName,
                                                             long durationSeconds, long startSecondTime, long endSecondTime) {
        String query = String.format(MIDDLEWARE_DEPENDENCY_INCOMING_CALL_RATE_QUERY_TEMPLATE,
                durationSeconds, TRACE_DATABASE_NAME, TRACE_DEPENDENCY_GRAPH_TABLE, startSecondTime, endSecondTime,
                DESTINATION_FIELD, node.getName(), serviceName);
        executeQueryAndUpdateNodeRates(query, node, false);
    }


    /**
     * Update node call rates from query results
     *
     * @param node            target node to update
     * @param field           field type (SOURCE_FIELD or DESTINATION_FIELD)
     * @param durationSeconds duration in seconds
     * @param startSecondTime start time in seconds
     * @param endSecondTime   end time in seconds
     * @param isSrc           is source service
     */
    private void updateNodeCallRatesFromQuery(TraceDependencyNode node, String field,
                                              long durationSeconds, long startSecondTime, long endSecondTime, boolean isSrc) {
        String subQuery = String.format(DEPENDENCY_CALL_RATE_QUERY_TEMPLATE,
                durationSeconds, TRACE_DATABASE_NAME, TRACE_DEPENDENCY_GRAPH_TABLE, startSecondTime, endSecondTime,
                field, node.getName());
        executeQueryAndUpdateNodeRates(subQuery, node, isSrc);
    }

    /**
     * Execute query and update node rates based on query results
     *
     * @param query SQL query to execute
     * @param node  target node to update
     * @param isSrc whether to update as source (true) or destination (false)
     */
    private void executeQueryAndUpdateNodeRates(String query, TraceDependencyNode node, boolean isSrc) {
        List<Map<String, Object>> subResults = clickhouseHandlerFactory.get().query(TRACE_DATABASE_NAME, query);
        Map<String, Object> data = subResults.stream().findFirst().orElse(null);
        if (data == null) {
            return;
        }
        double callRate = (double) data.get("callRate");
        double errorRate = (double) data.get("errorRate");
        if (isSrc) {
            updateNodeCallRates(node, null, callRate, errorRate);
        } else {
            updateNodeCallRates(null, node, callRate, errorRate);
        }
    }

    /**
     * Update incoming and outcoming call rates for parent and child nodes
     *
     * @param parentNode parent node
     * @param childNode  child node
     * @param callRate   call rate between parent and child
     * @param errorRate  error rate between parent and child
     */
    private void updateNodeCallRates(TraceDependencyNode parentNode, TraceDependencyNode childNode, double callRate, double errorRate) {
        if (parentNode != null) {
            parentNode.setOutcomingCallRate(callRate);
            parentNode.setErrorRate(errorRate);
        }
        if (childNode != null) {
            childNode.setIncomingCallRate(callRate);
            childNode.setErrorRate(errorRate);
        }
    }

    /**
     * Determine node type based on service name
     *
     * @param serviceName service name
     * @return node type string
     */
    private String determineNodeType(String serviceName, boolean isParent) {
        if (StringUtils.equals("asyncmq", serviceName)) {
            return TraceServiceMapNodeType.MESSAGE_QUEUE.getDesc();
        }
        if (isParent || serviceName == null || tenantHandler.existTenantByName(serviceName)) {
            return TraceServiceMapNodeType.APPLICATION.getDesc();
        } else {
            return TraceServiceMapNodeType.DATABASE.getDesc();
        }
    }

}
