package us.zoom.cube.site.api.web.outage;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import us.zoom.cube.site.biz.outage.RcaService;
import us.zoom.cube.site.lib.IdPara;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.rca.RcaAddInput;
import us.zoom.cube.site.lib.input.rca.RcaCommentInput;
import us.zoom.cube.site.lib.input.rca.RcaDeleteInput;
import us.zoom.cube.site.lib.input.rca.RcaEditInput;
import us.zoom.cube.site.lib.input.rca.RcaStatusInput;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.cube.site.lib.query.outage.RcaCommentQuery;
import us.zoom.cube.site.lib.query.outage.RcaQuery;

/**
 */
@RestController
@RequestMapping("/api/rca")
public class RcaController {

    @Autowired
    private RcaService rcaService;


    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject searchRcaList(@RequestBody PageQuery<RcaQuery> pageQuery) {
        return  rcaService.searchRcaList(pageQuery);
    }


    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject addRca(@RequestBody RcaAddInput rcaAddInput) {
        return  rcaService.add(rcaAddInput) ;
    }


    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject editRcaList(@RequestBody RcaEditInput rcaEditInput) {
        return  rcaService.edit(rcaEditInput)  ;
    }


    @RequestMapping(value = "/detail", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject detailRca(@RequestBody IdPara idPara) {
        return  rcaService.getById(idPara);
    }


    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject deleteRca(@RequestBody IdPara idPara) {
        return  rcaService.delete(idPara)  ;
    }

    @RequestMapping(value = "/check", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject checkDuplicateRca(@RequestBody RcaAddInput rcaAddInput) {
        return  rcaService.checkRca(rcaAddInput)  ;
    }



    @RequestMapping(value = "/updateStatus", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject updateStatus(@RequestBody RcaStatusInput rcaStatusInput) {
        return  rcaService.updateStatus(rcaStatusInput);
    }


    @RequestMapping(value = "/comment", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject comment(@RequestBody RcaCommentInput rcaCommentInput) {
        return  rcaService.comment(rcaCommentInput);
    }


    @RequestMapping(value = "/comment/list", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject commentList(@RequestBody RcaCommentQuery query) {
        return  rcaService.searchRcaCommentList(query);
    }


    @RequestMapping(value = "/comment/delete", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject deleteComment(@RequestBody RcaDeleteInput rcaDeleteInput) {
        return  rcaService.deleteComment(rcaDeleteInput);
    }


    @RequestMapping(value="/syncAllJiraData",method= RequestMethod.GET)
    @ResponseBody
    public ResponseObject syncAllJiraData() {
        return rcaService.syncAllJiraData();
    }

}
