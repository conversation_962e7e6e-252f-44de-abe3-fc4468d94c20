package us.zoom.cube.site.api.intercept;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import us.zoom.cloud.secrets.util.GsonUtil;
import us.zoom.commons.jwt.service.JWTService;
import us.zoom.commons.jwt.service.TokenRegisterRequest;
import us.zoom.commons.jwt.service.TokenVerifiedRequest;
import us.zoom.commons.jwt.service.VerifyResult;
import us.zoom.commons.jwt.service.impl.DefaultJWTService;
import us.zoom.cube.site.infra.constants.AuthConstant;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.infra.dao.model.TenantDO;
import us.zoom.infra.dao.model.UserDO;
import us.zoom.infra.dao.service.TenantDAO;
import us.zoom.infra.dao.service.UserDAO;
import us.zoom.infra.thread.ThreadLocalStore;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * @author: Starls Ding
 * @date: 2022/5/13 10:37
 * @desc:
 */
@Slf4j
@Component
public class SingleJwtAuthInterceptor implements HandlerInterceptor, InitializingBean {

    public static final String DEFAULT_AUTH_FAIL_MESSAGE = "Authorization failed!";
    @Autowired
    private MonitoringRequestMetrics metrics;
    private final JWTService jwtService = new DefaultJWTService();

    @Autowired
    private UserDAO userDAO;
    @Autowired
    private TenantDAO tenantDAO;

    @Value("${auth.zcp2cube.audience.name:cube}")
    private String zcpJwtAudience;
    @Value("${auth.zcp2cube.issuer.name:zcp-iam}")
    private String zcpJwtIssue;
    @Value("${auth.zcp2cube.secret.key:dummy}")
    private String zcpJwtSecret;


    @Override
    public void afterPropertiesSet() throws Exception {
        //attendtion: this JwtService's context is not the same as the us.zoom.cube.site.api.filter.AuthFilter or NewAuthFilter's JwtService's
        jwtService.register(TokenRegisterRequest.newOne(zcpJwtIssue, zcpJwtAudience, zcpJwtSecret, "HS256"));
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        request.setAttribute(metrics.REQUEST_TIME_KEY, System.currentTimeMillis());
        handleApiPath(request.getServletPath());
        AuthResult authResult = verifyAuth(request, response, handler);
        return authResult.success ? authResult.success : returnAuthFailResponse(response, authResult.getMessage());

    }

    private void handleApiPath(String servletPath) {
        ThreadLocalStore.setApiPath(servletPath);
    }

    private AuthResult verifyAuth(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // compatible with JWT key named as Authentication or Authorization in different application
        String authFullToken = StringUtils.isNotBlank(request.getHeader(AuthConstant.AUTHORIZATION))
                ? request.getHeader(AuthConstant.AUTHORIZATION) : request.getHeader(AuthConstant.AUTHENTICATION);

        if (StringUtils.isBlank(authFullToken)) {
            return AuthResult.fail();
        }

        String jwtToken = authFullToken.substring(AuthConstant.AUTHENTICATION_BEARER.length());
        //log.info("jwt token:[{}]", jwtToken);
        if (StringUtils.isEmpty(jwtToken)) {
            return AuthResult.fail("Authorization failed! Wrong token format!");
        }

        try {
            // newOne(check=false) and newVerifyOne（check=true）
            VerifyResult verifyResult = jwtService.verifyToken(TokenVerifiedRequest.newVerifyOne(jwtToken));

            //User check: acquired only by jwt token
            UserDO user = null;
            String userId = verifyResult.getClaim(AuthConstant.USER_ID).asString();
            if (StringUtils.isNotBlank(userId)) {
                user = userDAO.getUserById(userId);
            }
            String userName = verifyResult.getClaim(AuthConstant.USER_NAME).asString();
            if (null == user && StringUtils.isNotBlank(userName)) {
                user = userDAO.getUserByName(userName);
            }
            if (null == user) {
                return AuthResult.fail("Authorization failed! Wrong token claim!");
            }
            ThreadLocalStore.setUserInfoLocal(user.getId());
            ThreadLocalStore.setUserNameLocal(user.getName());


            String tenantId = request.getHeader("tenant-id");
            if (StringUtils.isNotEmpty(tenantId)) {
                TenantDO tenantById = tenantDAO.getTenantById(tenantId);
                if (tenantById != null) {
                    ThreadLocalStore.setTenantNameLocal(tenantById.getName());
                }
                ThreadLocalStore.setTenantInfoLocal(tenantId);
            } else {
                // Service（Tenant）: http request param or jwt token claim as not required always
                TenantDO serviceTenant = null;
                String serviceId = getStringFromReqParamOrTokenClaim(request, verifyResult, "accountId");
                if (StringUtils.isNotBlank(serviceId)) {
                    serviceTenant = tenantDAO.getTenantById(serviceId);
                }
                String serviceName = getStringFromReqParamOrTokenClaim(request, verifyResult, "accountName");
                if (null == serviceTenant && StringUtils.isNotBlank(serviceName)) {
                    serviceTenant = tenantDAO.getTenantByName(serviceName);
                }

                if (null != serviceTenant) {
                    ThreadLocalStore.setTenantInfoLocal(serviceTenant.getId());
                    ThreadLocalStore.setTenantNameLocal(serviceTenant.getName());
                } else {
                    ThreadLocalStore.setTenantInfoLocal(user.getCurrentTenant());
                }
            }

            //other request configs copied from us.zoom.cube.site.api.intercept.AuthInterceptor
            request.setCharacterEncoding("UTF-8");
            String timeZone = request.getParameter("timeZone");
            if (StringUtils.isNotBlank(timeZone)) {
                ThreadLocalStore.setTimeZoneLocal(Integer.valueOf(timeZone));
            }

        } catch (Exception e) {
            log.error("JWT Token is not authorized", e);
            return AuthResult.fail(e.getMessage());
        }

        return AuthResult.success();
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        ThreadLocalStore.setUserInfoLocal(null);
        ThreadLocalStore.setUserNameLocal(null);
        ThreadLocalStore.setTenantInfoLocal(null);
        ThreadLocalStore.setTenantNameLocal(null);
        ThreadLocalStore.setTimeZoneLocal(null);
        HandlerInterceptor.super.afterCompletion(request, response, handler, ex);
        metrics.auditLog(request, response);
    }

    private String getStringFromReqParamOrTokenClaim(HttpServletRequest request, VerifyResult verifyResult, String key) {
        String value = request.getParameter(key);
        if(StringUtils.isBlank(value)) {
            value = request.getHeader(key);
        }
        return StringUtils.isNotBlank(value) ? value : verifyResult.getClaim(key).asString();
    }


    private boolean returnAuthFailResponse(HttpServletResponse response) throws Exception {
        return returnAuthFailResponse(response, DEFAULT_AUTH_FAIL_MESSAGE);
    }

    private boolean returnAuthFailResponse(HttpServletResponse response, String message) throws Exception {
        response.setStatus(HttpStatus.UNAUTHORIZED.value());
        response.setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_UTF8_VALUE);
        response.getWriter().append(GsonUtil.toJson(ResponseObject.fail(message)));
        return false;
    }

    public JWTService getJwtService() {
        return jwtService;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    static class AuthResult {
        boolean success;
        String message;

        public static AuthResult success() {
            return new AuthResult(true, null);
        }

        public static AuthResult fail(String message) {
            return new AuthResult(false, message);
        }

        public static AuthResult fail() {
            return new AuthResult(false, DEFAULT_AUTH_FAIL_MESSAGE);
        }

    }

}
