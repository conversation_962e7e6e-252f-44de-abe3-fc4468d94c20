package us.zoom.cube.site.biz.clickhouse;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import us.zoom.cube.site.biz.clickhouse.task.ColumnCheckTask;
import us.zoom.infra.clickhouse.ClickHouseDataSourceProxy;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import us.zoom.infra.clickhouse.ClickhouseHandler;
import us.zoom.infra.clickhouse.ClickhouseSchema;
import us.zoom.infra.clickhouse.ClickhouseSqlUtil;
import us.zoom.infra.enums.FieldTypeEnum;

import java.sql.Connection;
import java.sql.Statement;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static us.zoom.infra.clickhouse.ClickhouseSchema.TIME_COLUMN_NAME;

/**
 * <AUTHOR>
 * @date 2022-04-28 10:11
 */
@Slf4j
public class TableCreator {

    @Setter
    private int savingTime = 30;

    private int savingYear = 3;

    private String db;
    private String tableName;
    private String localTableName;
    private ClickhouseHandler handler;
    private Map<String, FieldTypeEnum> colDef;
    private Map<String, FieldTypeEnum> viewColDef;
    private boolean clearUselessColumn;
    private boolean needBuffer;
    private String bufferConf;
    private Map<String, Set<String>> configMap = Maps.newHashMap();
    private Set<String> piiFields = new HashSet<>();
    private boolean batchTable = false;

    public TableCreator(String db, String tableName, Map<String, FieldTypeEnum>colDef, ClickhouseHandler handler, boolean clearUselessColumn) {
        this.db = db;
        this.tableName = tableName;
        this.handler = handler;
        this.colDef=colDef;
        this.clearUselessColumn = clearUselessColumn;
    }

    public TableCreator(String db, String tableName, Map<String, FieldTypeEnum>colDef, Map<String, FieldTypeEnum> viewColDef, ClickhouseHandler handler, boolean clearUselessColumn) {
        this.db = db;
        this.tableName = tableName;
        this.handler = handler;
        this.colDef=colDef;
        this.viewColDef=viewColDef;
        this.clearUselessColumn = clearUselessColumn;
    }

    public TableCreator(String db, String tableName, Map<String, FieldTypeEnum>colDef, Map<String, FieldTypeEnum> viewColDef, ClickhouseHandler handler, boolean clearUselessColumn, boolean batchTable) {
        this.db = db;
        this.tableName = tableName;
        this.handler = handler;
        this.colDef=colDef;
        this.viewColDef=viewColDef;
        this.clearUselessColumn = clearUselessColumn;
        this.batchTable = batchTable;
    }

    public TableCreator(String db, String tableName, Map<String, FieldTypeEnum>colDef, ClickhouseHandler handler, Map<String, Set<String>> configMap) {
        this.db = db;
        this.tableName = tableName;
        this.handler = handler;
        this.colDef=colDef;
        this.configMap = configMap;
    }

    public boolean syncTable() {
        Map<String, ClickhouseSchema> distributedSchemas = handler.getTableSchema(db, tableName);

        Set<String> localTableNames = distributedSchemas.values().stream().map(ClickhouseSchema::getLocalTable).filter(Objects::nonNull).collect(Collectors.toSet());
        List<ClickHouseDataSourceProxy> enableDataSources = handler.getEnableDataSources();

        //some distributed table may be missed. First need to identified localTableName
        if (localTableNames.size()==1) {
            //create distributed table
            localTableName = localTableNames.stream().findAny().orElse(null);
            if (localTableName==null) {
                log.error("Cannot parse local table name from distributed table schema {}.{}",db,tableName);
                return false;
            }
            enableDataSources.stream().filter(u->!distributedSchemas.keySet().contains(u.getUrl()))
                    .forEach(u-> copyExecute(distributedSchemas, u));
        }

        Map<String, ClickhouseSchema> localTableSchemas = handler.getTableSchema(db, localTableName);
        Set<String> zkPathCollect = localTableSchemas.values().stream().map(ClickhouseSchema::getZkPath).filter(Objects::nonNull).collect(Collectors.toSet());
        if(zkPathCollect.size()==1){
            //only one zkPath, create on missed instance using any existing SQL
            enableDataSources.stream().filter(u->!localTableSchemas.keySet().contains(u.getUrl()))
                    .forEach(u-> copyExecute(localTableSchemas, u));
        }

        Map<String, ClickhouseSchema> bufferTableSchemas = new HashMap<>();
        if(needBuffer){
            String bufferName = ClickhouseSqlUtil.fromLocalToBuffer(localTableName);
            bufferTableSchemas.putAll(handler.getTableSchema(db, bufferName));
            if(bufferTableSchemas.size()>0){
                enableDataSources.stream().filter(u->!bufferTableSchemas.keySet().contains(u.getUrl()))
                        .forEach(u-> copyExecute(bufferTableSchemas, u));
            }
        }

        Map<String, ClickhouseSchema> desensitized = new HashMap<>();
        if(piiFields.size()>0){
            String desensitizedName = ClickhouseSqlUtil.fromTableToDesensitized(tableName);
            desensitized.putAll(handler.getTableSchema(db, desensitizedName));
            if(desensitized.size()>0){
                enableDataSources.stream().filter(u->!desensitized.keySet().contains(u.getUrl()))
                        .forEach(u-> copyExecute(desensitized, u));
            }
        }

        return true;
    }

    public boolean createAndUpdate(){
        String cluster=String.join(",",handler.getDataSource().getAllClickhouseUrls());
        if(colDef.isEmpty()){
            log.warn("Table {}.{} column definition is empty, skip table creation",db,tableName);
            return true;
        }
        
        // Optimization 1: Batch schema retrieval to reduce database query count
        Map<String, ClickhouseSchema> distributedSchemas = handler.getTableSchema(db, tableName);
        
        //check distributed tables
        if (distributedSchemas.size()==0) {
            //distributed table is not yet created. Need to create local and distribute table
            localTableName = ClickhouseSqlUtil.getLocalTableNaming(tableName);
            handler.createTableOnCluster(db, tableName, localTableName, colDef, batchTable ? savingYear: savingTime,getPiiFields(), batchTable);
            return true;
        }
        
        Set<String> localTableNames = distributedSchemas.values().stream().map(ClickhouseSchema::getLocalTable).filter(Objects::nonNull).collect(Collectors.toSet());
        localTableName=localTableNames.stream().findAny().orElse(null);
        if (localTableName==null) {
            log.error("Cannot parse local table name from distributed table schema {}.{}",db,tableName);
            return false;
        }
        
        // Optimization 2: Batch retrieval of all related table schemas
        Map<String, ClickhouseSchema> localTableSchemas = handler.getTableSchema(db, localTableName);
        List<ClickHouseDataSourceProxy> enableDataSources = handler.getEnableDataSources();

        //some distributed table may be missed. First need to identified localTableName
        if (localTableNames.size()!=1) {
            //More than one local tableName
            log.error("More than one local tableName for {}.{} on {}",db,tableName,cluster);
            return false;
        } else {
            //create distributed table
            enableDataSources.stream().filter(u->!distributedSchemas.keySet().contains(u.getUrl()))
                    .forEach(u-> copyExecute(distributedSchemas, u));
        }
        
        //so for, all distributed views are created. Move on and check local table
        //check local zk path
        Set<String> zkPathCollect = localTableSchemas.values().stream().map(ClickhouseSchema::getZkPath).filter(Objects::nonNull).collect(Collectors.toSet());
        if(zkPathCollect.size()==0){
            //no local table exist, need to create
            handler.createLocalTableBatch(db,localTableName,colDef, savingTime, batchTable);
        }else if(zkPathCollect.size()==1){
            //only one zkPath, create on missed instance using any existing SQL
            enableDataSources.stream().filter(u->!localTableSchemas.keySet().contains(u.getUrl()))
                    .forEach(u-> copyExecute(localTableSchemas, u));
        }else{
            //abnormal
            log.error("More than one zkPath for {}.{} on {}, will drop the table on clickhouse server with zk path minority!",db,localTableName,cluster);
            doZkPathConfilict(localTableSchemas,db,localTableName);
            return false;
        }
        
        // Optimization 3: Batch processing of buffer tables and desensitized tables
        processBufferAndDesensitizedTables(enableDataSources);
        
        //distributed and local tables should all be created by now. Corresponding local table name is now stored in localTableName
        //Now move on to check schema one by one
        addColumn(localTableSchemas);
        addColumn(distributedSchemas);
        
        // Optimization 4: Asynchronously process column drop tasks
        if(clearUselessColumn) {
            submitColumnDropTaskAsync(localTableSchemas, distributedSchemas);
        }
        return true;
    }

    /**
     * Batch process buffer tables and desensitized tables
     */
    private void processBufferAndDesensitizedTables(List<ClickHouseDataSourceProxy> enableDataSources) {
        // Process buffer tables
        Map<String, ClickhouseSchema> bufferTableSchemas = new HashMap<>();
        if(needBuffer){
            String bufferName= ClickhouseSqlUtil.fromLocalToBuffer(localTableName);
            bufferTableSchemas.putAll(handler.getTableSchema(db, bufferName));
            if(bufferTableSchemas.size()>0){
                enableDataSources.stream().filter(u->!bufferTableSchemas.keySet().contains(u.getUrl()))
                        .forEach(u-> copyExecute(bufferTableSchemas, u));
            }else{
                handler.createBufferTable(db,bufferName,localTableName,bufferConf);
            }
            addColumn(bufferTableSchemas);
        }
        
        // Process desensitized tables
        if(piiFields.size()>0){
            if (Objects.nonNull(viewColDef)) {
                handler.createDesensitizedTableBatch(db, tableName, viewColDef, piiFields);
            } else {
                handler.createDesensitizedTableBatch(db, tableName, colDef, piiFields);
            }
        }
    }

    /**
     * Asynchronously submit column drop task
     */
    private void submitColumnDropTaskAsync(Map<String, ClickhouseSchema> localTableSchemas, Map<String, ClickhouseSchema> distributedSchemas) {
        try {
            Map<String, ClickhouseSchema> bufferTableSchemas = new HashMap<>();
            if(needBuffer){
                String bufferName= ClickhouseSqlUtil.fromLocalToBuffer(localTableName);
                bufferTableSchemas.putAll(handler.getTableSchema(db, bufferName));
            }
            
            ColumnCheckTask dropColumnTask = ColumnCheckTask.builder()
                    .setTableCreator(this)
                    .addClickhouseSchema(bufferTableSchemas)
                    .addClickhouseSchema(distributedSchemas)
                    .addClickhouseSchema(localTableSchemas)
                    .build();
            DropColumnExecutor.submitTask(dropColumnTask);
        } catch (Exception e) {
            log.error("Clickhouse cron task submit column drop task failed", e);
        }
    }

    public void doZkPathConfilict(Map<String, ClickhouseSchema> schemas,String db,String localTableName){
        Set<String> zkPathCollect = schemas.values().stream().map(ClickhouseSchema::getZkPath).filter(Objects::nonNull).collect(Collectors.toSet());
        if(zkPathCollect.size()>1){
            Map<String, Long> count = schemas.values().stream().map(ClickhouseSchema::getZkPath).collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
            long asLong = count.values().stream().mapToLong(u -> u).max().getAsLong();
            Set<String>zkToRemove=new HashSet<>();
            boolean theOne=true;
            for(Map.Entry<String,Long> entry:count.entrySet()){
                if(!entry.getValue().equals(asLong)){
                    zkToRemove.add(entry.getKey());
                    continue;
                }
                if(theOne){
                    theOne=false;
                    continue;
                }
                zkToRemove.add(entry.getKey());
            }
            Set<String>urls=new HashSet<>();
            for(Map.Entry<String,ClickhouseSchema>us:schemas.entrySet()){
                if(zkToRemove.contains(us.getValue().getZkPath())){
                    urls.add(us.getKey());
                }
            }
            for(ClickHouseDataSourceProxy d:handler.getEnableDataSources()){
                if(urls.contains(d.getUrl())){
                    log.info("delete {}.{} on {}",db,localTableName, d.getHost());
                    try (Connection conn = d.getConnection()) {
                        Statement stmt = conn.createStatement();
                        stmt.executeQuery(String.format("drop table %s.%s  ;", db, localTableName));

                    }catch (Exception e){
                        log.error("Error when drop",e);
                    }
                }
            }
        }
    }

    private void copyExecute(Map<String, ClickhouseSchema> sourceSchemas, ClickHouseDataSourceProxy u) {
        ClickhouseSchema sourceSchema = findSourceSchema(sourceSchemas, u);
        handler.executeOnInstance(sourceSchema.getRawSql(), u);
        sourceSchemas.put(u.getUrl(), sourceSchema);
    }

    private ClickhouseSchema findSourceSchema(Map<String, ClickhouseSchema> sourceSchemas, ClickHouseDataSourceProxy u) {
        if (CollectionUtils.isEmpty(configMap.values())) {
            return sourceSchemas.values().stream().findAny().get();
        }

        Set<String> targetShardSet = Sets.newHashSet();
        for (Set<String> shardSet : configMap.values()) {
            if (CollectionUtils.isNotEmpty(targetShardSet)) {
                break;
            }
            for (String ip : shardSet) {
                if (u.getUrl().contains(ip)) {
                    targetShardSet.addAll(shardSet);
                    break;
                }
            }
        }

        String sourceUrl = "";
        for (String url : sourceSchemas.keySet()) {
            if (StringUtils.isNotEmpty(sourceUrl)) {
                break;
            }
            for (String ip : targetShardSet) {
                if (url.contains(ip)) {
                    sourceUrl = url;
                    break;
                }
            }
        }

        if (StringUtils.isNotEmpty(sourceUrl)) {
            return sourceSchemas.get(sourceUrl);
        }

        return sourceSchemas.values().stream().findAny().get();
    }

    private void addColumn(Map<String, ClickhouseSchema> schemas) {
        for(Map.Entry<String,ClickhouseSchema>en: schemas.entrySet()){
            Map<String, FieldTypeEnum> existedCol = en.getValue().getColDef();
            Set<String> prefix = existedCol.keySet().stream().filter(u -> u.contains(".")).map(u -> u.split("\\.")[0]).collect(Collectors.toSet());
            String tableName=en.getValue().getTableName();
            existedCol.remove(TIME_COLUMN_NAME);
            if(!colDef.equals(existedCol)){
                for(Map.Entry<String, FieldTypeEnum>col:colDef.entrySet()){
                    String name = col.getKey();
                    FieldTypeEnum type = col.getValue();
                    if(existedCol.containsKey(name)){
                        if(!existedCol.get(name).equals(type)){
                            log.warn("{}.{} col={} has change type from {} to {}",
                                    db,tableName, name,existedCol.get(name), type);
                        }
                    }else if(prefix.contains(name)){
                        //skip existed agg prefix
                        continue;
                    }else{//add column
                        handler.addColumnOnInstance(db, tableName, name, type,en.getKey());
                    }
                }
            }
        }
    }

    public String getDb() {
        return db;
    }

    public TableCreator setDb(String db) {
        this.db = db;
        return this;
    }

    public String getTableName() {
        return tableName;
    }

    public TableCreator setTableName(String tableName) {
        this.tableName = tableName;
        return this;
    }

    public void dropColumns(String tableName, String... fields) {
        this.handler.dropColumn(db, tableName, fields);
    }

    public boolean isNeedBuffer() {
        return needBuffer;
    }

    public TableCreator setNeedBuffer(boolean needBuffer) {
        this.needBuffer = needBuffer;
        return this;
    }


    public String getBufferConf() {
        return bufferConf;
    }

    public TableCreator setBufferConf(String bufferConf) {
        this.bufferConf = bufferConf;
        return this;
    }

    public Set<String> getPiiFields() {
        return piiFields;
    }

    public TableCreator setPiiFields(Set<String> piiFields) {
        if(piiFields==null){
            return this;
        }
        this.piiFields = piiFields;
        return this;
    }

    public Map<String, FieldTypeEnum> getColDef() {
        return colDef;
    }
}
