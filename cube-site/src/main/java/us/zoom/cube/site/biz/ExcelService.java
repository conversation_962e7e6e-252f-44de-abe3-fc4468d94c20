package us.zoom.cube.site.biz;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.sax.handler.BeanRowHandler;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.cube.lib.common.ProbeTaskStatusEnum;
import us.zoom.cube.lib.common.ProbeTypeEnum;
import us.zoom.cube.site.api.web.StatusEnum;
import us.zoom.cube.site.core.ProbeGroupHandler;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.ProbeTaskInput;
import us.zoom.infra.dao.model.ProbeGroupDO;
import us.zoom.infra.dao.model.TenantDO;
import us.zoom.infra.thread.NamedThreadFactory;
import us.zoom.cube.lib.utils.JsonUtils;

import jakarta.annotation.PostConstruct;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.StringUtils.EMPTY;

/**
 * @author: eason.jia
 * @date: 2022/8/15
 */
@Component
public class ExcelService {

    private static final Logger logger = LoggerFactory.getLogger(ExcelService.class);

    private ThreadPoolExecutor executor;

    @Autowired
    private ProbeTaskService probeTaskService;
    @Autowired
    private TenantHandler tenantHandler;
    @Autowired
    private ProbeGroupHandler probeGroupHandler;

    private Map<String, String> serviceMapping = new HashMap<>();
    private List<ProbeGroupDO> allProbeGroupDOList;
    private List<ProbeGroupDO> usProbeGroupDOList;
    private List<ProbeGroupDO> otherProbeGroupDOList;
    private Random random = new Random();

    private Map<String, Pair<String, DnsTaskInfo>> failedTasks;

    @PostConstruct
    public void init() {
        serviceMapping = tenantHandler.listAll()
                .stream()
                .collect(Collectors.toMap(TenantDO::getName, TenantDO::getId));

        allProbeGroupDOList = probeGroupHandler.findByParam(null, null, null, null, null, null, null, null, null, null, 1, Integer.MAX_VALUE);
        usProbeGroupDOList = new ArrayList<>();
        otherProbeGroupDOList = new ArrayList<>();
        for (ProbeGroupDO probeGroupDO : allProbeGroupDOList) {
            if (StringUtils.equalsIgnoreCase("us", probeGroupDO.getCountry())) {
                usProbeGroupDOList.add(probeGroupDO);
            } else {
                otherProbeGroupDOList.add(probeGroupDO);
            }
        }

        int cores = Runtime.getRuntime().availableProcessors();
        executor = new ThreadPoolExecutor(
                cores * 2,
                cores * 2,
                1, TimeUnit.MINUTES,
                new LinkedBlockingQueue(2000),
                new NamedThreadFactory("probe-task-batch-create"),
                new ThreadPoolExecutor.CallerRunsPolicy());

        failedTasks = new LinkedHashMap<>();
    }

    public void asyncParseBase64AndCreateTask(String base64Str) {
        ExcelUtil.readBySax(decode(base64Str), -1, new ExcelRowHandler(0, 1, Integer.MAX_VALUE, DnsTaskInfo.class));
    }

    private InputStream decode(String base64Str) {
        byte[] byteArray = Base64.getDecoder().decode(base64Str);
        return new ByteArrayInputStream(byteArray);
    }

    @Data
    public static class DnsTaskInfo {
        private String Service;
        private String DNS;
        private String Port;
    }

    private class ExcelRowHandler extends BeanRowHandler<DnsTaskInfo> {

        public ExcelRowHandler(int headerRowIndex, int startRowIndex, int endRowIndex, Class<DnsTaskInfo> clazz) {
            super(headerRowIndex, startRowIndex, endRowIndex, clazz);
        }

        @Override
        public void handleData(int sheetIndex, long rowIndex, DnsTaskInfo data) {
            if (data == null
                    || StringUtils.isBlank(data.getService())
                    || StringUtils.isBlank(data.getDNS())
                    || !NumberUtil.isInteger(data.getPort())) {
                return;
            }
            executor.submit(() -> {
                int retryCount = 0;
                boolean success = false;
                while (retryCount < 3 && !success) {
                    try {
                        ProbeTaskInput probeTaskInput = convert(data, sheetIndex, rowIndex);
                        if (probeTaskInput == null) {
                            throw new IllegalArgumentException("illegal argument: " + JsonUtils.toJsonStringIgnoreExp(data));
                        }
                        ResponseObject<String> response = probeTaskService.addProbeTask(probeTaskInput);
                        if (StringUtils.equals(StatusEnum.SUCCESS.getStatus(), response.getStatus())) {
                            logger.info("probe task created successfully, sheet:{}, row:{}, task info: {}", sheetIndex, rowIndex, JsonUtils.toJsonStringIgnoreExp(data));
                            success = true;
                        } else {
                            throw new RuntimeException(JsonUtils.toJsonStringIgnoreExp(response));
                        }
                    } catch (Exception e) {
                        logger.error(String.format("fail to create probe task created, sheet:%d, row:%d, task info: %s", sheetIndex, rowIndex, JsonUtils.toJsonStringIgnoreExp(data)), e);
                        failedTasks.put(sheetIndex + "#" + rowIndex, ImmutablePair.of(e.getMessage(), data));
                        retryCount++;
                    }
                }
            });
            try {
                TimeUnit.MILLISECONDS.sleep(50);
            } catch (InterruptedException e) {

            }
        }

        private ProbeTaskInput convert(DnsTaskInfo dnsTaskInfo, long sheetIndex, long rowIndex) {
            ProbeTaskInput probeTaskInput = new ProbeTaskInput();
            probeTaskInput.setName(StringUtils.left(sheetIndex + "_" + rowIndex + "#" + dnsTaskInfo.getDNS() + "#" + dnsTaskInfo.getPort(), 64));
            String serviceId = serviceMapping.get(dnsTaskInfo.getService());
            if (StringUtils.isBlank(serviceId)) {
                return null;
            }
            probeTaskInput.setServiceId(serviceId);
            probeTaskInput.setInterval("1m");
            probeTaskInput.setStatus(ProbeTaskStatusEnum.ENABLE.getDesc());
            probeTaskInput.setProbePointGroupIds(buildProbeGroupIds());
            List<TcpConfig> steps = Lists.newArrayList(convertToTcpConfig(dnsTaskInfo));
            probeTaskInput.setSteps(JsonUtils.toJsonStringIgnoreExp(steps));
            Date now = new Date();
            probeTaskInput.setGmtCreate(now);
            probeTaskInput.setGmtModify(now);
            return probeTaskInput;
        }
    }

    @Data
    private class TcpConfig {
        String probeType;
        int timeout;
        String name;
        String responseParser;
        Map<String, String> customFields;
        String ip;
        int port;
        String dataType;
        String data;
    }

    public TcpConfig convertToTcpConfig(DnsTaskInfo dnsTaskInfo) {
        TcpConfig tcpConfig = new TcpConfig();
        tcpConfig.setProbeType(ProbeTypeEnum.TCP.getType());
        tcpConfig.setName(dnsTaskInfo.getDNS() + ":" + dnsTaskInfo.getPort());
        tcpConfig.setTimeout(30);
        tcpConfig.setResponseParser(EMPTY);
        tcpConfig.setCustomFields(new HashMap<>());
        tcpConfig.setIp(dnsTaskInfo.getDNS());
        tcpConfig.setPort(Integer.parseInt(dnsTaskInfo.getPort()));
        tcpConfig.setDataType("string");
        tcpConfig.setData(EMPTY);
        return tcpConfig;
    }

    private List<String> buildProbeGroupIds() {
        ProbeGroupDO usProbeGroupDO = selectOne(usProbeGroupDOList);
        ProbeGroupDO otherProbeGroupDO = selectOne(otherProbeGroupDOList);
        return Lists.newArrayList(usProbeGroupDO.getId(), otherProbeGroupDO.getId());
    }

    private ProbeGroupDO selectOne(List<ProbeGroupDO> probeGroupDOList) {
        int total = probeGroupDOList.size();
        if (total == 1) {
            return probeGroupDOList.get(0);
        }
        return probeGroupDOList.get(random.nextInt(total));
    }

    public Map<String, Pair<String, DnsTaskInfo>> getFailedTasks() {
        return failedTasks;
    }
}
