package us.zoom.cube.site.lib.input.outage;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;


@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class Must {
    private List<String> accountId;
    private Map<String,String> accountClusterMap;
    private String searchMethod;
    private List<String> queryFields;
    private List<String> emailExact;
}

