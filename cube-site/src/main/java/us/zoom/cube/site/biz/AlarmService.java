package us.zoom.cube.site.biz;

import com.google.common.collect.Lists;
import com.okta.commons.lang.Assert;
import com.zoom.op.monitor.dao.alarm.AlarmDefinitionDao;
import com.zoom.op.monitor.domain.alarm.AlarmDefinition;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.integrations.MetricsFieldTypeEnum;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.site.biz.alarm.group.AlarmGroupService;
import us.zoom.cube.site.biz.alarm.silence.AlarmSilenceUtilService;
import us.zoom.cube.site.core.*;
import us.zoom.cube.site.core.alarm.group.AlarmGroupAlarmItemHandler;
import us.zoom.cube.site.core.alarm.group.AlarmGroupHandler;
import us.zoom.cube.site.core.alarm.group.AlarmGroupServiceItemHandler;
import us.zoom.cube.site.core.alarm.group.AlarmGroupTagItemHandler;
import us.zoom.cube.site.core.alarm.silence.AlarmSilenceHandler;
import us.zoom.cube.site.core.auth.AuthHandler;
import us.zoom.cube.site.infra.utils.ExceptionUtils;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.*;
import us.zoom.cube.site.lib.input.AlarmInput;
import us.zoom.cube.site.lib.input.AlarmRuleContentInput;
import us.zoom.cube.site.lib.input.AlarmRuleInput;
import us.zoom.cube.site.lib.input.AlarmRuleSingleContentInput;
import us.zoom.cube.site.lib.input.alarm.group.AlarmGroupTagItemInput;
import us.zoom.cube.site.lib.input.alarm.silence.AlarmRelatedSilenceInput;
import us.zoom.cube.site.lib.output.PageResult;
import us.zoom.cube.site.lib.output.alarm.*;
import us.zoom.cube.site.lib.output.alarm.group.AlarmGroupOutput;
import us.zoom.cube.site.lib.output.alarm.group.AlarmGroupServiceItemOutput;
import us.zoom.cube.site.lib.output.alarm.group.AlarmGroupTagItemOutput;
import us.zoom.cube.site.lib.output.alarm.silence.AlarmRelatedSilenceOutput;
import us.zoom.cube.site.lib.query.AlarmQuery;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.enums.AlarmSilenceStatusEnum;
import us.zoom.infra.enums.AlarmSilenceTypeEnum;
import us.zoom.infra.enums.FieldTypeEnum;
import us.zoom.infra.model.alarm.CompareType;
import us.zoom.infra.utils.AlarmConstants;
import us.zoom.infra.utils.Instance;

import java.util.*;
import java.util.stream.Collectors;

import static us.zoom.cube.site.infra.enums.WebCodeEnum.InnerError;
import static us.zoom.infra.utils.AlarmGroupConstants.ALARM_GROUP;
import static us.zoom.infra.utils.AlarmSilenceConstants.ALARM_SILENCE;

@Service
@Slf4j
public class AlarmService {

    @Autowired
    private AlarmHandler alarmHandler;

    @Autowired
    private AlarmDefinitionHandler alarmDefinitionHandler;

    @Autowired
    private MetricsHandler metricsHandler;

    @Autowired
    private AuthService authService;

    @Autowired
    private AlarmDefinitionDao alarmDefinitionDao;

    @Autowired
    private AlarmSilenceHandler alarmSilenceHandler;

    @Autowired
    private AuthHandler authHandler;

    @Autowired
    private AlarmGroupHandler alarmGroupHandler;

    @Autowired
    private AlarmSilenceUtilService alarmSilenceUtilService;

    @Autowired
    private UserHandler userHandler;

    @Autowired
    private AlarmGroupServiceItemHandler alarmGroupServiceItemHandler;

    @Autowired
    private AlarmGroupAlarmItemHandler alarmGroupAlarmItemHandler;

    @Autowired
    private AlarmGroupTagItemHandler alarmGroupTagItemHandler;

    @Autowired
    private TenantHandler tenantHandler;

    @Autowired
    private AlarmGroupService alarmGroupService;

    private static final org.slf4j.Logger logger = LoggerFactory.getLogger(AgentService.class.getName());

    private static final int MAX_ALARM_COUNT_LIMIT = 5;

    public ResponseObject findByAlarmNameLike(PageQuery<AlarmQuery> pageQuery) throws Exception {
        authService.checkAuth(pageQuery);
        if (pageQuery.getQueryPara() == null) {
            pageQuery.setQueryPara(new AlarmQuery());
        }
        int pageIndex= pageQuery.getPageIndex();
        int pageSize = pageQuery.getPageSize();
        List<AlarmDO> alarmResultList = alarmHandler.findByAlarmNameLike(pageQuery.getQueryPara().getAlarmName(),pageQuery.getTenantId(),pageIndex,pageSize);
        List<AlarmOut> alarmOutList = Lists.newArrayList();
        Instance.ofNullable(alarmResultList).forEach(alarmDO -> {
            AlarmOut alarmOut = new AlarmOut();
            BeanUtils.copyProperties(alarmDO, alarmOut);
            alarmOut.setAlarmCycle(JsonUtils.parseArray(alarmDO.getAlarmCycle(), String.class));
            alarmOutList.add(alarmOut);
        });
        int count=alarmHandler.getCountByAlarmNameLike(pageQuery.getQueryPara().getAlarmName(),pageQuery.getTenantId());
        return ResponseObject.success(pageQuery.getOperId(),new PageResult(count, alarmOutList));
    }

    @Transactional
    public ResponseObject<Boolean> addAlarm(AlarmInput<AlarmRuleInput> alarmInput) throws Exception {

        authService.checkAuth(alarmInput);
        alarmInput.check();
        checkAlarm(alarmInput);
        String metricsId = alarmInput.getMetricsId();

        List<String> tenantIds = Lists.newArrayList(alarmInput.getTenantId());
        org.springframework.util.Assert.isTrue(!alarmHandler.hasSameAlarmName(alarmInput.getAlarmName(), tenantIds), "Exists Alarm with the same name");

        List<AlarmRuleDO> alarmRuleDOList = Lists.newArrayList();
        AlarmDO alarmDO = new AlarmDO();
        BeanUtils.copyProperties(alarmInput,alarmDO);
        alarmDO.setId(IdUtils.generateId());
        alarmDO.setAlarmCycle(JsonUtils.toJsonStringIgnoreExp(alarmInput.getAlarmCycle()));
        alarmDO.setCreateTime(new Date());
        alarmDO.setModifyTime(new Date());

        checkAlarmRule(alarmInput);
        List<AlarmRuleRelationDO> alarmRuleRelationDOList = new ArrayList<>();

        MetricsAlarmDO metricsAlarmDO = new MetricsAlarmDO();
        metricsAlarmDO.setMetricsId(metricsId);
        metricsAlarmDO.setAlarmId(alarmDO.getId());
        metricsAlarmDO.setId(IdUtils.generateId());

        for (AlarmRuleInput alarmRuleInput : alarmInput.getAlarmRules()) {
            AlarmRuleDO alarmRuleDO = new AlarmRuleDO();
            BeanUtils.copyProperties(alarmRuleInput, alarmRuleDO);
            AlarmRuleContentInput alarmRuleContent = alarmRuleInput.getAlarmRuleContent();
            String alarmRuleContentJson = transformRuleContent(alarmRuleContent, metricsId);
            alarmRuleDO.setAlarmRuleContent(alarmRuleContentJson);
            alarmRuleDOList.add(alarmRuleDO);
        }
        for(int i=0;i<alarmRuleDOList.size();i++){
            alarmRuleDOList.get(i).setId(IdUtils.generateId());
            AlarmRuleRelationDO alarmRuleRelationDO = new AlarmRuleRelationDO();
            alarmRuleRelationDO.setId(IdUtils.generateId());
            alarmRuleRelationDO.setAlarmId(alarmDO.getId());
            alarmRuleRelationDO.setAlarmRuleId(alarmRuleDOList.get(i).getId());
            alarmRuleRelationDOList.add(alarmRuleRelationDO);
        }
        AlarmImRelationDO alarmImRelationDO = null;
        String instanceMessagingChannelId = alarmInput.getInstanceMessagingChannelId();
        if (instanceMessagingChannelId != null) {
            alarmImRelationDO = new AlarmImRelationDO();
            alarmImRelationDO.setId(IdUtils.generateId());
            alarmImRelationDO.setAlarmId(alarmDO.getId());
            alarmImRelationDO.setInstanceMessagingChannelId(instanceMessagingChannelId);
            alarmImRelationDO.setCreateTime(new Date());
            alarmImRelationDO.setModifyTime(new Date());
        }

        alarmHandler.addAlarm(alarmDO, metricsAlarmDO, alarmRuleDOList, alarmRuleRelationDOList, alarmImRelationDO);
        return ResponseObject.success(alarmInput.getOperId(),true);
    }

    private void checkAlarm(AlarmInput<AlarmRuleInput> alarmInput) {
        int index=alarmInput.getAlarmName().indexOf(" ");
        if(index != -1){
            throw new IllegalArgumentException("alarn name contains blank char");
        }
    }

    private void checkAlarmRule(AlarmInput<AlarmRuleInput> alarmInput) {
        if(CollectionUtils.isEmpty(alarmInput.getAlarmRules())){
            throw new IllegalArgumentException("Alarm rule is empty");
        }

        for(AlarmRuleInput rule:alarmInput.getAlarmRules()){
            rule.check();
            AlarmRuleContentInput alarmRuleContent = rule.getAlarmRuleContent();

            List<AlarmRuleSingleContentInput> tagRules = alarmRuleContent.getTagRules();
            Instance.ofNullable(tagRules).forEach(tagRule -> {
                if (tagRule.getTag() == null || tagRule.getOperator() == null || tagRule.getCmpValue() == null) {
                    throw new IllegalArgumentException("Tag value, operator or cmpValue cannot be empty!");
                }
            });

            List<AlarmRuleSingleContentInput> fieldRules = alarmRuleContent.getFieldRules();
            if (CollectionUtils.isEmpty(fieldRules)) {
                throw new IllegalArgumentException("Field condition cannot be empty!");
            }
            for (AlarmRuleSingleContentInput fieldRule : fieldRules) {
                if (fieldRule.getField() == null || fieldRule.getOperator() == null || fieldRule.getCmpValue() == null) {
                    throw new IllegalArgumentException("Field value, fieldType, operator or cmpValue cannot be empty!");
                }
            }

            List<AlarmRuleSingleContentInput> functionRules = alarmRuleContent.getFunctionRules();
            Instance.ofNullable(functionRules).forEach(functionRule -> {
                if (functionRule.getExpression() == null) {
                    throw new IllegalArgumentException("Function expression cannot be empty!");
                }
            });
        }
    }

    private String transformRuleContent(AlarmRuleContentInput alarmRuleContent, String metricsId) {
        List<AlarmRuleSingleContentInput> singleContentInputList = Lists.newArrayList();
        List<AlarmRuleSingleContentInput> tagRules = alarmRuleContent.getTagRules();
        Instance.ofNullable(tagRules).forEach(tagRule -> {
            tagRule.setType(CompareType.TAG_COMPARE.getValue());
            String expression = tagRule.getTag() + " " + tagRule.getOperator() + " '" + tagRule.getCmpValue() + "'";
            tagRule.setExpression(expression);
            singleContentInputList.add(tagRule);
        });
        List<MetricsFieldDO> metricsFields = metricsHandler.listFieldsByMetricsIds(Lists.newArrayList(metricsId));
        Map<String, Integer> metricsFieldNameAndTypeMap = metricsFields.stream().collect(Collectors.toMap(MetricsFieldDO::getFieldName, MetricsFieldDO::getFieldType, (key1, key2) -> key1));
        List<AlarmRuleSingleContentInput> fieldRules = alarmRuleContent.getFieldRules();
        Instance.ofNullable(fieldRules).forEach(fieldRule -> {
            String operator = fieldRule.getOperator();
            if (AlarmConstants.CHAINED_RATIO_INCREASE_EXPR.equals(operator) || AlarmConstants.CHAINED_RATIO_DECREASE_EXPR.equals(operator)) {
                fieldRule.setType(CompareType.CHAIN_RATIO.getValue());
            } else {
                fieldRule.setType(CompareType.THRESHOLD.getValue());
            }
            String field = fieldRule.getField();
            Integer metricsFieldType = metricsFieldNameAndTypeMap.get(field);
            if (metricsFieldType == MetricsFieldTypeEnum.string.getValue()) {
                String expression = field + " " + operator + " '" + fieldRule.getCmpValue() + "'";
                fieldRule.setExpression(expression);
                fieldRule.setFieldType(FieldTypeEnum.string.name());
            }
            if (metricsFieldType == MetricsFieldTypeEnum.number.getValue()) {
                String expression = field + " " + operator + " " + fieldRule.getCmpValue();
                fieldRule.setExpression(expression);
                fieldRule.setFieldType(FieldTypeEnum.number.name());
            }
            if (metricsFieldType == MetricsFieldTypeEnum.mapString.getValue()) {
                String expression = field + "['" + fieldRule.getMapKey() + "'] " + operator + " '" + fieldRule.getCmpValue() + "'";
                fieldRule.setExpression(expression);
                fieldRule.setFieldType(FieldTypeEnum.mapString.name());
            }
            if (metricsFieldType == MetricsFieldTypeEnum.mapNumber.getValue()) {
                String expression = field + "['" + fieldRule.getMapKey() + "'] " + operator + " " + fieldRule.getCmpValue();
                fieldRule.setExpression(expression);
                fieldRule.setFieldType(FieldTypeEnum.mapNumber.name());
            }
            singleContentInputList.add(fieldRule);
        });
        List<AlarmRuleSingleContentInput> functionRules = alarmRuleContent.getFunctionRules();
        Instance.ofNullable(functionRules).forEach(functionRule -> {
            functionRule.setType(CompareType.FUNC_COMPARE.getValue());
            singleContentInputList.add(functionRule);
        });

        return JsonUtils.toJsonStringIgnoreExp(singleContentInputList);
    }

    @Transactional
    public ResponseObject editAlarmById(AlarmInput<AlarmRuleInput> alarmInput) throws Exception {
//        authService.mustTenantAdminOrAdmin(alarmInput.getUserId(),alarmInput.getTenantId());
        authService.checkAuth(alarmInput);
        authService.checkHasAlarmThrowExpWhenNotHave(alarmInput,alarmInput.getId());

        alarmInput.check();

        String metricsId = alarmInput.getMetricsId();

        List<String> tenantIds = Lists.newArrayList(alarmInput.getTenantId());
        AlarmDO oldAlarmDO = alarmHandler.getAlarmById(alarmInput.getId());
        org.springframework.util.Assert.isTrue(oldAlarmDO != null, "Old alarm is null!");
        if (!alarmInput.getAlarmName().equals(oldAlarmDO.getName())) {
            org.springframework.util.Assert.isTrue(!alarmHandler.hasSameAlarmName(alarmInput.getAlarmName(), tenantIds), "Exists Alarm with the same name");
        }

        List<AlarmRuleDO> alarmRuleDOList = new ArrayList<>();
        AlarmDO alarmDO = new AlarmDO();
        BeanUtils.copyProperties(alarmInput,alarmDO);
        alarmDO.setAlarmCycle(JsonUtils.toJsonStringIgnoreExp(alarmInput.getAlarmCycle()));
        alarmDO.setModifyTime(new Date());
        List<AlarmRuleRelationDO> alarmRuleRelationDOList = new ArrayList<>();

        MetricsAlarmDO metricsAlarmDO = new MetricsAlarmDO();
        metricsAlarmDO.setAlarmId(alarmDO.getId());
        metricsAlarmDO.setMetricsId(metricsId);
        checkAlarmRule(alarmInput);

        for (AlarmRuleInput alarmRuleInput : alarmInput.getAlarmRules()) {
            AlarmRuleDO alarmRuleDO = new AlarmRuleDO();
            BeanUtils.copyProperties(alarmRuleInput, alarmRuleDO);
            AlarmRuleContentInput alarmRuleContent = alarmRuleInput.getAlarmRuleContent();
            String alarmRuleContentJson = transformRuleContent(alarmRuleContent, metricsId);
            alarmRuleDO.setAlarmRuleContent(alarmRuleContentJson);
            alarmRuleDOList.add(alarmRuleDO);
        }
        for(int i=0;i<alarmRuleDOList.size();i++){
            AlarmRuleRelationDO alarmRuleRelationDO = new AlarmRuleRelationDO();
            if(StringUtils.isBlank(alarmRuleDOList.get(i).getId())) {
                alarmRuleDOList.get(i).setId(IdUtils.generateId());
                alarmRuleRelationDO.setId(IdUtils.generateId());
            }
            alarmRuleRelationDO.setAlarmId(alarmDO.getId());
            alarmRuleRelationDO.setId(IdUtils.generateId());
            alarmRuleRelationDO.setAlarmRuleId(alarmRuleDOList.get(i).getId());
            alarmRuleRelationDOList.add(alarmRuleRelationDO);
        }
        AlarmImRelationDO alarmImRelationDO = null;
        String instanceMessagingChannelId = alarmInput.getInstanceMessagingChannelId();
        if (instanceMessagingChannelId != null) {
            alarmImRelationDO = new AlarmImRelationDO();
            alarmImRelationDO.setId(IdUtils.generateId());
            alarmImRelationDO.setAlarmId(alarmDO.getId());
            alarmImRelationDO.setInstanceMessagingChannelId(instanceMessagingChannelId);
            alarmImRelationDO.setCreateTime(new Date());
            alarmImRelationDO.setModifyTime(new Date());
        }
        alarmHandler.editAlarmById(alarmDO, metricsAlarmDO, alarmRuleDOList, alarmRuleRelationDOList, alarmImRelationDO);
        return ResponseObject.success(alarmInput.getOperId(),null);
    }

    public ResponseObject getAlarm(IdPara idPara) {
        authService.checkAuth(idPara);
        authService.checkHasAlarmThrowExpWhenNotHave(idPara, idPara.getId());
        AlarmDO alarmDO=  alarmHandler.getAlarmById(idPara.getId());
        AlarmOut result=new AlarmOut();
        BeanUtils.copyProperties(alarmDO,result);
        result.setAlarmCycle(JsonUtils.parseArray(alarmDO.getAlarmCycle(), String.class));
        result.setMetricsId(alarmHandler.getMetricsIdByAlarmId(idPara.getId()));
        List<AlarmRuleDO> rules=alarmHandler.listRuleByAlarmIds(Arrays.asList(alarmDO.getId()));
        result.setAlarmRules(Instance.ofNullable(rules).stream().map(e->{
            AlarmRuleOut ruleOut=new AlarmRuleOut();
            BeanUtils.copyProperties(e,ruleOut);
            String alarmRuleContentStr = e.getAlarmRuleContent();
            List<AlarmRuleSingleContentOutput> alarmRuleSingleContentOutputList = JsonUtils.parseArray(alarmRuleContentStr, AlarmRuleSingleContentOutput.class);
            List<AlarmRuleSingleContentOutput> tagRules = Lists.newArrayList();
            List<AlarmRuleSingleContentOutput> fieldRules = Lists.newArrayList();
            List<AlarmRuleSingleContentOutput> functionRules = Lists.newArrayList();
            for (AlarmRuleSingleContentOutput alarmRuleSingleContentOutput : alarmRuleSingleContentOutputList) {
                int type = alarmRuleSingleContentOutput.getType();
                if (type == CompareType.TAG_COMPARE.getValue()) {
                    tagRules.add(alarmRuleSingleContentOutput);
                }
                if (type == CompareType.CHAIN_RATIO.getValue() || type == CompareType.THRESHOLD.getValue()) {
                    fieldRules.add(alarmRuleSingleContentOutput);
                }
                if (type == CompareType.FUNC_COMPARE.getValue()) {
                    functionRules.add(alarmRuleSingleContentOutput);
                }
            }
            AlarmRuleContentOutput alarmRuleContentOutput = new AlarmRuleContentOutput();
            alarmRuleContentOutput.setTagRules(tagRules);
            alarmRuleContentOutput.setFieldRules(fieldRules);
            alarmRuleContentOutput.setFunctionRules(functionRules);
            ruleOut.setAlarmRuleContent(alarmRuleContentOutput);
            return  ruleOut;
        }).collect(Collectors.toList()));
        String imChannelId = alarmHandler.getImChannelIdByAlarmId(idPara.getId());
        if (imChannelId != null) {
            result.setInstanceMessagingEnable(true);
            result.setInstanceMessagingChannelId(imChannelId);
        } else {
            result.setInstanceMessagingEnable(false);
        }
        return ResponseObject.success(result);
    }

    public ResponseObject getFieldAlarmRelationsByMetricsId(MetricsIdPara metricsIdPara) {
        try{
            authService.checkAuth(metricsIdPara);
            if (!authService.hasSuchMetrics(metricsIdPara, metricsIdPara.getMetricsId())) {
                return ResponseObject.hasNoSuchMetrics();
            }
            String metricsId = metricsIdPara.getMetricsId();
            FieldAlarmRelationOut out = alarmHandler.getFieldAlarmRelationsByMetricsId(metricsId, MAX_ALARM_COUNT_LIMIT);

            return ResponseObject.success(out);
        } catch (Exception e) {
            logger.error(ExceptionUtils.getTrace(e));
            throw new SiteException(InnerError.getCode(), InnerError.getErrMsg());
        }
    }

    private MetricsAlarmsRelationOut getMetricsAlarmsRelationByMetricsId(String metricsId){
        List<AlarmDO> alarmDOList = alarmHandler.listAlarmsByMetricsId(metricsId);
        List<SimpleAlarm> simpleAlarmList = new ArrayList<>();
        alarmDOList.forEach(e -> {
            SimpleAlarm simpleAlarm = new SimpleAlarm();
            simpleAlarm.setId(e.getId());
            simpleAlarm.setName(e.getName());
            simpleAlarmList.add(simpleAlarm);
        });
        MetricsAlarmsRelationOut out = new MetricsAlarmsRelationOut();
        out.setMetricsId(metricsId);
        out.setAlarms(simpleAlarmList);
        return out;
    }

    public ResponseObject getAlarmsByMetricsId(MetricsIdPara metricsIdPara){
        long startTime = System.currentTimeMillis();
        logger.info("[getAlarmsByMetricsId] metricsIdPara = {}", JsonUtils.toJsonStringIgnoreExp(metricsIdPara));
        authService.checkAuth(metricsIdPara);
        if (!authService.hasSuchMetrics(metricsIdPara, metricsIdPara.getMetricsId())) {
            return ResponseObject.hasNoSuchMetrics();
        }

        MetricsAlarmsRelationOut out = getMetricsAlarmsRelationByMetricsId(metricsIdPara.getMetricsId());
        logger.info("[getAlarmsByMetricsId] metricsIdPara = {}, response = {}", JsonUtils.toJsonStringIgnoreExp(metricsIdPara), JsonUtils.toJsonStringIgnoreExp(out));
        long endTime = System.currentTimeMillis();
        logger.info("[getAlarmsByMetricsId] cost {} ms", endTime-startTime);
        return ResponseObject.success(out);
    }



    public Optional<AlarmDefinition> findByNameAndTenantId(String name, String tenantId) {
        return alarmDefinitionHandler.findByNameAndTenantId(name, tenantId);
    }

    public ResponseObject getRelatedAlarmSilenceDetail(IdPara idPara) {
        authService.checkAuth(idPara);
        AlarmSilenceDO alarmSilenceDO = alarmSilenceHandler.findById(idPara.getId());
        AlarmRelatedSilenceOutput silenceOutput = new AlarmRelatedSilenceOutput();
        buildDilenceOutput(alarmSilenceDO, silenceOutput);
        return ResponseObject.success(silenceOutput);
    }

    public ResponseObject getRelatedAlarmSilence(AlarmIdPara alarmIdPara) {
        authService.checkAuth(alarmIdPara);
        String alarmId = alarmIdPara.getAlarmId();
        String userId = alarmIdPara.getUserId();
        Optional<AlarmDefinition> alarmDefinitionOptional = alarmDefinitionDao.findById(alarmId);
        Assert.isTrue(alarmDefinitionOptional.isPresent(), "alarm is not existed");
        AlarmDefinition alarmDefinition = alarmDefinitionOptional.get();
        Assert.isTrue(alarmDefinition.getTenantId().equals(alarmIdPara.getTenantId()), "alarm is not belong to the service");
        String serviceId = alarmDefinition.getTenantId();
        TenantDO tenantDO = tenantHandler.getTenantFromCacheFirst(serviceId);
        Assert.isTrue(tenantDO != null, "the service is not existed");
        Assert.isTrue(authService.canOperate(userId, serviceId), "user can not operate this service");
        String serviceName = tenantDO.getName();
        String alarmName = alarmDefinition.getName();
        String silenceName = serviceName + "_" + alarmName + "_" + alarmId + "_" + ALARM_SILENCE;
        AlarmSilenceDO alarmSilenceDO = alarmSilenceHandler.findByName(silenceName);
        AlarmRelatedSilenceOutput silenceOutput = new AlarmRelatedSilenceOutput();
        buildDilenceOutput(alarmSilenceDO, silenceOutput);
        return ResponseObject.success(silenceOutput);
    }

    public void buildDilenceOutput(AlarmSilenceDO alarmSilenceDO, AlarmRelatedSilenceOutput silenceOutput) {
        if (alarmSilenceDO == null) {
            silenceOutput.setExist(false);
            return;
        }
        BeanUtils.copyProperties(alarmSilenceDO, silenceOutput);
        silenceOutput.setExist(true);

        List<AlarmGroupTagItemOutput> groupTagItemList = new ArrayList<>();
        List<AlarmGroupServiceItemDO> serviceItemDOList = alarmGroupServiceItemHandler.findByAlarmGroupIdList(Lists.newArrayList(alarmSilenceDO.getAlarmGroupId()));
        if (!CollectionUtils.isEmpty(serviceItemDOList)) {
            List<AlarmGroupTagItemDO> groupTagItemDOList = alarmGroupTagItemHandler.findByAlarmGroupServiceItemIdList(
                    serviceItemDOList.stream().map(AlarmGroupServiceItemDO::getId).collect(Collectors.toList()));
            for (AlarmGroupTagItemDO alarmGroupTagItemDO : groupTagItemDOList) {
                AlarmGroupTagItemOutput alarmGroupTagItemOutput = new AlarmGroupTagItemOutput();
                BeanUtils.copyProperties(alarmGroupTagItemDO, alarmGroupTagItemOutput);
                groupTagItemList.add(alarmGroupTagItemOutput);
            }
        }
        silenceOutput.setGroupTagItemList(groupTagItemList);
    }

    private AlarmDefinition getAlarmDefinition(String alarmId, String tenantId) {
        return alarmDefinitionDao.findById(alarmId)
                .filter(ad -> ad.getTenantId().equals(tenantId))
                .orElseThrow(() -> new IllegalArgumentException("Alarm does not exist or does not belong to the service."));
    }

    private TenantDO getTenant(String serviceId) {
        TenantDO tenantDO = tenantHandler.getTenantFromCacheFirst(serviceId);
        if (tenantDO == null) {
            throw new IllegalArgumentException("The service does not exist.");
        }
        return tenantDO;
    }

    private String generateSilenceName(String serviceName, String alarmName, String alarmId) {
        return String.format("%s_%s_%s_%s", serviceName, alarmName, alarmId, ALARM_SILENCE);
    }

    private String generateGroupName(String serviceName, String alarmName, String alarmId) {
        return String.format("%s_%s_%s_%s", serviceName, alarmName, alarmId, ALARM_GROUP);
    }

    @Transactional
    public void deleteAlarmSilenceAndGroup(String silenceName, String groupName) {
        Optional<AlarmSilenceDO> alarmSilenceOptional = Optional.ofNullable(alarmSilenceHandler.findByName(silenceName));
        alarmSilenceOptional.ifPresent(alarmSilenceDO -> {
            alarmSilenceHandler.deleteById(alarmSilenceDO.getId());
            deleteAlarmGroup(groupName);
        });
    }

    @Transactional
    public void deleteAlarmGroup(String groupName) {
        AlarmGroupDO alarmGroupDO = alarmGroupHandler.findByName(groupName);
        if (alarmGroupDO != null) {
            List<AlarmGroupServiceItemDO> serviceItemDOList = alarmGroupServiceItemHandler.findByAlarmGroupIdList(
                    Collections.singletonList(alarmGroupDO.getId())
            );

            alarmGroupHandler.deleteById(alarmGroupDO.getId());
            alarmGroupServiceItemHandler.deleteByAlarmGroupId(alarmGroupDO.getId());
            serviceItemDOList.forEach(serviceItemDO -> {
                alarmGroupAlarmItemHandler.deleteByAlarmGroupServiceItemId(serviceItemDO.getId());
                alarmGroupTagItemHandler.deleteByAlarmGroupServiceItemId(serviceItemDO.getId());
            });
        }
    }

    public void delRelatedAlarmSilenceByIds(List<String> ids, String userId, String tenantId) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        ids.forEach(id -> delRelatedAlarmSilence(createAlarmIdPara(id, userId, tenantId)));
    }

    public AlarmIdPara createAlarmIdPara(String alarmId, String userId, String tenantId) {
        AlarmIdPara alarmIdPara = new AlarmIdPara();
        alarmIdPara.setAlarmId(alarmId);
        alarmIdPara.setUserId(userId);
        alarmIdPara.setTenantId(tenantId);
        return alarmIdPara;
    }

    @Transactional
    public ResponseObject delRelatedAlarmSilence(AlarmIdPara alarmIdPara) {
        authService.checkAuth(alarmIdPara);
        String alarmId = alarmIdPara.getAlarmId();
        String userId = alarmIdPara.getUserId();

        AlarmDefinition alarmDefinition = getAlarmDefinition(alarmId, alarmIdPara.getTenantId());
        String serviceId = alarmDefinition.getTenantId();
        Assert.isTrue(authService.canOperate(userId, serviceId), "user can not operate this service");
        TenantDO tenantDO = getTenant(serviceId);
        String silenceName = generateSilenceName(tenantDO.getName(), alarmDefinition.getName(), alarmId);
        String groupName = generateGroupName(tenantDO.getName(), alarmDefinition.getName(), alarmId);

        deleteAlarmSilenceAndGroup(silenceName, groupName);
        return ResponseObject.success(null);
    }

    @Transactional
    public ResponseObject addRelatedAlarmSilence(AlarmRelatedSilenceInput alarmRelatedSilenceInput)  {
        authService.checkAuth(alarmRelatedSilenceInput);
        alarmSilenceUtilService.checkAddAlarmRelatedSilenceParameter(alarmRelatedSilenceInput);
        String alarmId = alarmRelatedSilenceInput.getAlarmId();
        String serviceId = alarmRelatedSilenceInput.getTenantId();
        String userId = alarmRelatedSilenceInput.getUserId();
        String userName = userHandler.getNameById(userId);

        TenantDO tenantDO = tenantHandler.getTenantFromCacheFirst(serviceId);
        Assert.isTrue(tenantDO != null, "the service is not existed");

        Optional<AlarmDefinition> alarmDefinitionOptional = alarmDefinitionDao.findById(alarmId);
        Assert.isTrue(alarmDefinitionOptional.isPresent(), "alarm is not existed");

        String serviceName = tenantDO.getName();
        String alarmName = alarmDefinitionOptional.get().getName();

        AlarmGroupDO alarmGroupDO = new AlarmGroupDO();
        String alarmGroupId = IdUtils.generateId();
        alarmGroupDO.setId(alarmGroupId);
        String alarmGroupName = serviceName + "_" + alarmName + "_" + alarmId + "_" + ALARM_GROUP;

        alarmGroupDO.setName(alarmGroupName);
        alarmGroupDO.setCreator(userName);
        alarmGroupDO.setEditor(userName);
        alarmGroupDO.setVisibility(0);

        AlarmGroupServiceItemDO serviceItemDO = new AlarmGroupServiceItemDO();
        String serviceItemId = IdUtils.generateId();
        serviceItemDO.setId(serviceItemId);
        serviceItemDO.setServiceId(serviceId);
        serviceItemDO.setAlarmGroupId(alarmGroupId);

        AlarmGroupAlarmItemDO alarmItemDO = new AlarmGroupAlarmItemDO();
        String alarmItemId = IdUtils.generateId();
        alarmItemDO.setId(alarmItemId);
        alarmItemDO.setAlarmGroupServiceItemId(serviceItemId);
        alarmItemDO.setAlarmId(alarmId);

        List<AlarmGroupTagItemDO> alarmGroupTagItemDOS = new ArrayList<>();
        if (!CollectionUtils.isEmpty(alarmRelatedSilenceInput.getGroupTagItemList())) {
            alarmRelatedSilenceInput.getGroupTagItemList().forEach(tag -> {
                AlarmGroupTagItemDO alarmGroupTagItemDO = new AlarmGroupTagItemDO();
                BeanUtils.copyProperties(tag, alarmGroupTagItemDO);
                alarmGroupTagItemDO.setId(IdUtils.generateId());
                alarmGroupTagItemDO.setAlarmGroupServiceItemId(serviceItemId);
                alarmGroupTagItemDOS.add(alarmGroupTagItemDO);
            });
        }

        AlarmSilenceDO alarmSilenceDO = new AlarmSilenceDO();
        BeanUtils.copyProperties(alarmRelatedSilenceInput, alarmSilenceDO);
        String alarmSilenceId = IdUtils.generateId();
        String alarmSilenceName = serviceName + "_" + alarmName + "_" + alarmId + "_" + ALARM_SILENCE;
        alarmSilenceDO.setId(alarmSilenceId);
        alarmSilenceDO.setName(alarmSilenceName);
        alarmSilenceDO.setAlarmGroupId(alarmGroupId);
        alarmSilenceDO.setType(AlarmSilenceTypeEnum.ONE_TIME.getValue());
        alarmSilenceDO.setCreator(userName);
        alarmSilenceDO.setEditor(userName);
        alarmSilenceDO.setVisibility(0);

        AlarmSilenceStatusEnum statusEnum = null;
        try {
            statusEnum = alarmSilenceUtilService.calcAlarmSilenceStatus(alarmSilenceDO, null, new Date());
        }catch (Exception e){
            logger.error("calcAlarmSilenceStatus {} failed",alarmSilenceDO.getName(),e);
            org.springframework.util.Assert.isTrue(false,"calcAlarmSilenceStatus failed");
        }

        alarmSilenceDO.setStatus(statusEnum.getValue());

        alarmGroupHandler.insert(alarmGroupDO);
        alarmGroupServiceItemHandler.insert(serviceItemDO);
        alarmGroupAlarmItemHandler.batchInsert(Lists.newArrayList(alarmItemDO));
        alarmGroupTagItemHandler.batchInsert(alarmGroupTagItemDOS);
        alarmSilenceHandler.insert(alarmSilenceDO);
        return ResponseObject.success(null);
    }

    @Transactional
    public ResponseObject updateRelatedAlarmSilence(AlarmRelatedSilenceInput alarmRelatedSilenceInput)  {
        authService.checkAuth(alarmRelatedSilenceInput);
        alarmSilenceUtilService.checkUpdateAlarmRelatedSilenceParameter(alarmRelatedSilenceInput);
        String alarmId = alarmRelatedSilenceInput.getAlarmId();
        String serviceId = alarmRelatedSilenceInput.getTenantId();
        String userId = alarmRelatedSilenceInput.getUserId();
        String userName = userHandler.getNameById(userId);
        TenantDO tenantDO = tenantHandler.getTenantFromCacheFirst(serviceId);
        Assert.isTrue(tenantDO != null, "the service is not existed");

        Optional<AlarmDefinition> alarmDefinitionOptional = alarmDefinitionDao.findById(alarmId);
        Assert.isTrue(alarmDefinitionOptional.isPresent(), "alarm is not existed");

        String serviceName = tenantDO.getName();
        String alarmName = alarmDefinitionOptional.get().getName();
        String alarmSilenceName = serviceName + "_" + alarmName + "_" + alarmId + "_" + ALARM_SILENCE;
        AlarmSilenceDO alarmSilenceDO = alarmSilenceHandler.findByName(alarmSilenceName);
        BeanUtils.copyProperties(alarmRelatedSilenceInput, alarmSilenceDO);
        alarmSilenceDO.setCreator(userName);
        alarmSilenceDO.setEditor(userName);

        AlarmSilenceStatusEnum statusEnum = null;
        try {
            statusEnum = alarmSilenceUtilService.calcAlarmSilenceStatus(alarmSilenceDO, null, new Date());
        }catch (Exception e){
            logger.error("calcAlarmSilenceStatus {} failed",alarmSilenceDO.getName(),e);
            org.springframework.util.Assert.isTrue(false,"calcAlarmSilenceStatus failed");
        }

        alarmSilenceDO.setStatus(statusEnum.getValue());
        alarmSilenceHandler.update(alarmSilenceDO);

        String alarmGroupId = alarmSilenceDO.getAlarmGroupId();
        AlarmGroupOutput alarmGroupOutput = alarmGroupService.getAlarmGroupById(alarmGroupId, userId);
        Optional<AlarmGroupServiceItemOutput> alarmGroupServiceItemOutputOptional = alarmGroupOutput.getServiceItemList().stream().findFirst();

        List<AlarmGroupTagItemDO> addAlarmGroupTagItemDOList = new ArrayList<>();
        List<AlarmGroupTagItemDO> updateAlarmGroupTagItemDOList = new ArrayList<>();
        List<AlarmGroupTagItemInput> newAlarmGroupTagItemInputList = alarmRelatedSilenceInput.getGroupTagItemList();
        List<AlarmGroupTagItemOutput> oldAlarmGroupTagItemOutputList = alarmGroupServiceItemOutputOptional
                .map(AlarmGroupServiceItemOutput::getGroupTagItemList)
                .orElse(Collections.emptyList());
        if(CollectionUtils.isEmpty(oldAlarmGroupTagItemOutputList) && CollectionUtils.isEmpty(newAlarmGroupTagItemInputList)) {
            log.info("Instant Silence not add tag condition, alarmRelatedSilenceInput ={}, alarmName = {}", JsonUtils.toJsonStringIgnoreExp(alarmRelatedSilenceInput), alarmName);
            return ResponseObject.success(null);
        }
        Set<String> newTagItemIdList;
        if (!CollectionUtils.isEmpty(newAlarmGroupTagItemInputList)) {
            newAlarmGroupTagItemInputList.forEach(t -> {
                if(StringUtils.isEmpty(t.getId())){
                    AlarmGroupTagItemDO alarmGroupTagItemDO = new AlarmGroupTagItemDO();
                    BeanUtils.copyProperties(t, alarmGroupTagItemDO);
                    alarmGroupTagItemDO.setId(IdUtils.generateId());
                    alarmGroupTagItemDO.setAlarmGroupServiceItemId(alarmGroupServiceItemOutputOptional.get().getId());
                    addAlarmGroupTagItemDOList.add(alarmGroupTagItemDO);
                } else {
                    AlarmGroupTagItemDO alarmGroupTagItemDO = new AlarmGroupTagItemDO();
                    BeanUtils.copyProperties(t, alarmGroupTagItemDO);
                    alarmGroupTagItemDO.setAlarmGroupServiceItemId(alarmGroupServiceItemOutputOptional.get().getId());
                    updateAlarmGroupTagItemDOList.add(alarmGroupTagItemDO);
                }
            });
            newTagItemIdList = newAlarmGroupTagItemInputList.stream().filter(t -> StringUtils.isNotBlank(t.getId())).map(AlarmGroupTagItemInput::getId).collect(Collectors.toSet());
        } else {
            newTagItemIdList = new HashSet<>();
        }
        List<String> needDeleteTagItemIdList = oldAlarmGroupTagItemOutputList.stream().filter(t -> !newTagItemIdList.contains(t.getId())).map(AlarmGroupTagItemOutput::getId).toList();

        List<String> deleteAlarmGroupTagItemIdList = new ArrayList<>(needDeleteTagItemIdList);
        alarmGroupTagItemHandler.batchInsert(addAlarmGroupTagItemDOList);
        alarmGroupTagItemHandler.batchUpdate(updateAlarmGroupTagItemDOList);
        alarmGroupTagItemHandler.batchDelete(deleteAlarmGroupTagItemIdList);
        return ResponseObject.success(null);
    }
}
