package us.zoom.cube.site.biz;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import us.zoom.cube.lib.common.TaskTypeEnum;
import us.zoom.cube.site.core.SysParaHandler;
import us.zoom.cube.site.lib.IdPara;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.common.ValueText;
import us.zoom.cube.site.lib.dto.SysParaDTO;
import us.zoom.cube.site.lib.input.SysParaInput;
import us.zoom.cube.site.lib.output.PageResult;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.cube.site.lib.query.PublicSysParaQuery;
import us.zoom.cube.site.lib.query.SysParaQuery;
import us.zoom.infra.dao.model.CubeServerDO;
import us.zoom.infra.dao.model.SysParaDO;
import us.zoom.infra.dao.service.CubeServerDAO;
import us.zoom.infra.model.alarm.AlarmAAPara;
import us.zoom.infra.thread.NamedThreadFactory;
import us.zoom.infra.utils.Instance;
import us.zoom.infra.utils.IpUtils;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SysParaService {

    @Value("${cube-site.server.env:}")
    private String serverEnv;

    @Autowired
    private AuthService authService;

    @Autowired
    private SysParaHandler sysParaHandler;

    @Autowired
    private CubeServerDAO cubeServerDAO;


    private static final String PUBLIC = "public";

    private String sendAllToCubeType = "sendAllToCube";
    private String notifyIntervalInMinutesType="notifyIntervalInMinutes";

    private String serviceSendToSelfType = "serviceSendToSelf";

    private String managerServiceType="managerService";

    private String isDelOverdueServerForAllServiceType = "delOverdueServerForAllService";

    private String serverOverdueTimeType= "serverOverdueTime";

    private String delOverdueServerForDifferentServiceType ="delOverdueServerForDifService";

    private String switchAppNameToTenantNameType = "switchAppNameToTenantName";

    private String heartCheckInternalInMinsType = "heartCheckInternalInMins";

    private String siteClickhouseType = "siteClickhouse";

    private String siteInfluxdbType = "siteInfluxdb";

    private String siteSwitchType = "siteSwitch";

    private String siteClickhouseIgnoreSyncKey = "siteClickhouseIgnore";

    private String siteUseInfluxdbEnvKey = "cube.site.use.influxdb.env";

    private static final String SITE_CLICKHOUSE_TAG_TIME_SPAN_KEY = "siteClickhouseTagTimeSpan";

    private static final String SITE_CLICKHOUSE_TAG_TIME_SPAN_V2_KEY = "siteClickhouseTagTimeSpanV2";

    private static final String SITE_CLICKHOUSE_TAG_TIME_SPAN_V3_KEY = "siteClickhouseTagTimeSpanV3";

    private static final String SITE_CLICKHOUSE_TAG_LIMIT_KEY = "siteClickhouseTagLimit";

    private String CMDBConfigLoadType = "CMDBConfigLoad";

    private static final String defaultManagerServiceName = "manager_service";

    private static final String influx_remain_case_sensitive_database_names = "influx_remain_case_sensitive_database_names";

    private static final String alarmClickhouseSwitchType ="alarmClickhouseSwitch";

    private static final String hubForwardMapType = "hubForwardMap";

    private static  final Integer defaultOverdueServerDay  = 31 ;

    private static  final AtomicInteger overdueServerDayValue  = new AtomicInteger(defaultOverdueServerDay);


    private static  final Integer defaultNotifyIntervalInMinutesValue  = 120 ;
    private static final AtomicInteger notifyIntervalInMinutesValue= new AtomicInteger(defaultNotifyIntervalInMinutesValue);


    private static final AtomicBoolean isSendAllToCube = new AtomicBoolean(true);

    private static final AtomicBoolean delOverdueServerForAllServiceValue = new AtomicBoolean(false);

    public static final AtomicReference<String> managerServiceRef = new AtomicReference<>(defaultManagerServiceName);

    // Map<serviceName,boolean>
    private static final AtomicReference<Map<String,Boolean >> serviceSendToSelfMap = new AtomicReference<>(new HashMap<>(0));

    //Map<tenantName,serverOverdueDay>
    private static final AtomicReference<Map<String,Integer >> delOverdueServerForDifferentServiceMap = new AtomicReference<>(new HashMap<>(0));

    // Map<appName,tenantName>
    private static final AtomicReference<Map<String,String>> switchAppNameToTenantNameMap = new AtomicReference<>(new HashMap<>(0));

    private static  final Integer defaultHeartCheckInternalInMinsValue  = 120 ;
    private static final AtomicInteger heartCheckInternalInMinsValue= new AtomicInteger(defaultHeartCheckInternalInMinsValue);

    private String clickhouseSyncIgnoreListString="";

    private static final AtomicReference<Map<String, Set<String>>> siteSwitchRef = new AtomicReference<>();

    private AtomicReference<Map<String,SysParaDO>> siteClickhouseParas=new AtomicReference<>(new HashMap<>());

    private static final String TIME_LIMIT = "switchAndTimeLimit";

    private static final String ASYNCMQTOPICENUM = "mlarxAsyncmqTopicEnum";

    public static final String ZOOM_NODE_SWITCH = "zoomNodeSwitch";

    private static final String CUBE_THRESHOLDS= "cubeThresholds";

    private static final String ALARM_BOARD= "alarmBoard";

    public static final String SETTING_TAG= "settingTag";

    public static final String CUBE_TEMPLATE= "cubeTemplate";

    private static final AtomicReference<Map<String, String>> asyncmqTopicEnumCache = new AtomicReference<>();

    private static final AtomicReference<Map<String, String>> zoomNodeSwitchCache = new AtomicReference<>();

    private static final AtomicReference<Set<String>> siteInfluxdbEnvCache = new AtomicReference<>();


    private static final AtomicReference<Map<String, String>> timeLimitCache = new AtomicReference<>();

    private static final AtomicReference<Map<String, String>> thresHoldsCache = new AtomicReference<>();

    private static final AtomicReference<Map<String, String>> cubeTemplateCache = new AtomicReference<>();

    private AtomicReference<Map<String,Integer>>clickhouseLimits=new AtomicReference<>(new HashMap<>());

    private static final AtomicReference<Map<String, String>> alarmBoardCache = new AtomicReference<>();

    private static final AtomicReference<Map<String, String>> alarmGroupCache = new AtomicReference<>();

    private final static Integer DEAFULT_SQL_LIMIT=100000;

    private final static long DEFAULT_CK_TAG_QUERY_TIME_SPAN = 86400;

    private final static int DEFAULT_CK_TAG_QUERY_LIMIT = 10000;

    private static final String CUBE_PREPLAN_TYPE = "cubePreplan";

    private static final String EDITABLE_SYSTEM_PARAM = "editableSystemParam";

    private static final String alarmAaType = "alarmAaType";

    /**
     * Clickhouse related parameters
     */
    private static final AtomicReference<Map<String,String>>  alarmClickhouseSwitchRef = new AtomicReference<>(new HashMap<>());

    private static final AtomicBoolean isCMDBConfigLoad = new AtomicBoolean(false);

    private static final  AtomicReference<Map<String,String>> hubForwardMapRef = new AtomicReference<>(new HashMap<>(0));

    private static final AtomicReference<AlarmAAPara> alarmAAParaRef = new AtomicReference(new AlarmAAPara());

    @PostConstruct
    public void init(){
        Executors.newScheduledThreadPool(1,new NamedThreadFactory("sysparas  scheduler ")).scheduleAtFixedRate(new Runnable() {
            @Override
            public void run() {
                load();
            }
        }, 1,1, TimeUnit.MINUTES);
    }

    private void load() {
        try{
            log.info("begin load sysparas for site");
            Long begin = System.currentTimeMillis();
            String env=null;
            if (StringUtils.isNotBlank(serverEnv)) {
                env = serverEnv;
            } else {
                List<CubeServerDO> cubeServerDOS = cubeServerDAO.listByIpAndType(IpUtils.getLocalIP(), TaskTypeEnum.site.name());
                if (org.springframework.util.CollectionUtils.isEmpty(cubeServerDOS)) {
                    log.warn("Current server has not been registered.");
                } else {
                    env = cubeServerDOS.get(0).getEnv();
                }
            }
            List<SysParaDO> sysParaDOS = sysParaHandler.listByTypes(Arrays.asList(sendAllToCubeType, serviceSendToSelfType, alarmAaType,
                    isDelOverdueServerForAllServiceType,serverOverdueTimeType,delOverdueServerForDifferentServiceType,managerServiceType,
                    notifyIntervalInMinutesType,switchAppNameToTenantNameType,heartCheckInternalInMinsType,siteClickhouseType,siteClickhouseType+env,
                    siteSwitchType, alarmClickhouseSwitchType, TIME_LIMIT,ASYNCMQTOPICENUM,ZOOM_NODE_SWITCH,CUBE_THRESHOLDS,hubForwardMapType,CMDBConfigLoadType,siteInfluxdbType,ALARM_BOARD,CUBE_TEMPLATE));
            List<SysParaDO>  sendAllToCubeParas = new ArrayList<>();
            List<SysParaDO>  serviceSendToSelfParas = new ArrayList<>();
            List<SysParaDO>  delOverdueServerForAllServiceParas = new ArrayList<>();
            List<SysParaDO>  serverOverdueTimeParas = new ArrayList<>();
            List<SysParaDO>  delOverdueServerForDifferentServiceParas = new ArrayList<>();
            List<SysParaDO> managerServiceParas = new ArrayList<>();
            List<SysParaDO> notifyIntervalInMinutesParas = new ArrayList<>();
            List<SysParaDO> switchAppNameToTenantNameParas = new ArrayList<>();
            List<SysParaDO> heartCheckInternalInMinsParas = new ArrayList<>();
            List<SysParaDO> siteClickhouseParas = new ArrayList<>();
            List<SysParaDO> siteSwitchParas = new ArrayList<>();
            List<SysParaDO> alarmClickhouseParasParas =new ArrayList<>();
            List<SysParaDO> CMDBConfigLoadParas = new ArrayList<>();
            List<SysParaDO> switchAndTimeLimitParas =new ArrayList<>();
            List<SysParaDO> asyncmqTopicEnumParas =new ArrayList<>();
            List<SysParaDO> zoomNodeSwitchParas = new ArrayList<>();
            List<SysParaDO> cubeThresholdsParas = new ArrayList<>();
            List<SysParaDO> hubForwardMapParas =new ArrayList<>();
            List<SysParaDO> siteInfluxdbParas = new ArrayList<>();
            List<SysParaDO> alarmBoardParas = new ArrayList<>();
            List<SysParaDO> cubeTemplate = new ArrayList<>();
            List<SysParaDO> alarmAaParas = new ArrayList<>();
            split(alarmAaParas, sysParaDOS, sendAllToCubeParas, serviceSendToSelfParas, delOverdueServerForAllServiceParas, serverOverdueTimeParas,
                    delOverdueServerForDifferentServiceParas,managerServiceParas,notifyIntervalInMinutesParas,switchAppNameToTenantNameParas,
                    heartCheckInternalInMinsParas,siteClickhouseParas,siteSwitchParas, alarmClickhouseParasParas, switchAndTimeLimitParas,
                    asyncmqTopicEnumParas, zoomNodeSwitchParas, cubeThresholdsParas,hubForwardMapParas, siteInfluxdbParas, CMDBConfigLoadParas, alarmBoardParas,cubeTemplate);
            setSendToCube(sendAllToCubeParas);
            setServiceSendToSelf(serviceSendToSelfParas);
            setDelOverdueServerForAllServiceParas(delOverdueServerForAllServiceParas);
            setServerOverdueDay(serverOverdueTimeParas);
            setOverdueServerForDifferentService(delOverdueServerForDifferentServiceParas);
            setManagerService(managerServiceParas);
            setNotifyIntervalInMinutes(notifyIntervalInMinutesParas);
            setSwitchAppNameToTenantName(switchAppNameToTenantNameParas);
            setHeartCheckInternalInMins(heartCheckInternalInMinsParas);
            for(SysParaDO d:sysParaDOS){
                if(StringUtils.equals(d.getType(),siteClickhouseType+env)){
                    siteClickhouseParas.add(d);
                }
            }
            setSiteIgnoreClickhouseSyncList(siteClickhouseParas);
            setSiteSwitchCache(siteSwitchParas);

            setAlarmClickhouseSwitch(alarmClickhouseParasParas);
            setCMDBConfigLoad(CMDBConfigLoadParas);
            setLoadTimeLimit(switchAndTimeLimitParas);
            setAsyncmqTopicEnum(asyncmqTopicEnumParas);
            setZoomNodeSwitchCache(zoomNodeSwitchParas);
            setCubeThresHoldsCache(cubeThresholdsParas);
            setHubForwardMap(hubForwardMapParas);
            setSiteUseInfluxdbEnvCache(siteInfluxdbParas);
            setCubeTemplateCache(cubeTemplate);
            setAlarmBoardCache(alarmBoardParas);
            this.siteClickhouseParas.set(siteClickhouseParas.stream().collect(Collectors.toMap(u->u.getParaKey(), Function.identity(),(a,b)->b)));
            setClickhouseLimit();
            setAlarmAA(alarmAaParas);
            log.info("load sysparas is over ,cost is {}",System.currentTimeMillis() - begin);
        }catch (Exception e){
            log.error("load syspara is error ",e);
        }

    }

    private void setAlarmAA(List<SysParaDO> sysParaDOS) {
        if (CollectionUtils.isEmpty(sysParaDOS)) {
            return;
        }

        if (StringUtils.isBlank(sysParaDOS.get(0).getValue())) {
            return;
        }

        AlarmAAPara alarmAAPara = JsonUtils.toObject(sysParaDOS.get(0).getValue(), AlarmAAPara.class);

        if (alarmAAPara != null) {
            alarmAAParaRef.set(alarmAAPara);
        }

    }

    public Set<String>getInfluxRemainCaseSensitive() {
        Set<String> ret = new HashSet<>();
        try {
            return siteSwitchRef.get().get(influx_remain_case_sensitive_database_names);
        } catch (Exception e) {
        }
        return ret;
    }

    private static final String CLICKHOUSE_QUERY_ALLOW_TENANTS="cube.alarm.clickhouse.query.tenants.allow";
    private static final String CLICKHOUSE_QUERY_DENY_TENANTS="cube.alarm.clickhouse.query.tenants.deny";
    private static final String CLICKHOUSE_QUERY_FORCE_ENABLE_TENANTS="cube.site.clickhouse.tenants.query.force";
    private static final String CLICKHOUSE_QUERY_FORCE_DISABLE_TENANTS="cube.site.clickhouse.tenants.query.force";

    private static final String CLICKHOUSE_QUERY_ALLOW_NO_SELECT="cube.site.clickhouse.allow.no.select";

    private static final String CLICKHOUSE_DBA_LIST="cube.site.clickhouse.dba.list";

    private static final String CLICKHOUSE_QUERY_ALLOW_CROSS_DB_WITHOUT_PERMISSION="cube.site.clickhouse.allow.cross.db.without.permission";

    private static final String CLICKHOUSE_CN_OUTBOUND_FIREWALL="cube.site.clickhouse.cn.outbound.firewall";
    private static final String CLICKHOUSE_QUERY_ENV_ROUTE="cube.site.clickhouse.query.env.route";

    private static final String CLICKHOUSE_ONLY_RAW_SQL="cube.site.clickhouse.only.raw.sql";
    private static final String CLICKHOUSE_SYS_FORCE_ENV="cube.site.clickhouse.sys.force.query.env";
    private static final String CLICKHOUSE_SELECT_LIMIT="cube.site.clickhouse.select.limit";
    private static final String CLICKHOUSE_SELECT_SERVICE_LIMIT="cube.site.clickhouse.select.service.limit";
    private static final String CLICKHOUSE_ON_INFRA_PAGE="cube.site.use.clickhouse.infra.page";
    private static final String CLICKHOUSE_FILL_NULL_LENIENT_TIME="cube.site.clickhouse.fill.null.lenient.time";
    private static final String CLICKHOUSE_EMPTY_TABLE_DROP_LIMIT="cube.site.clickhouse.empty.table.drop.limit";

    private static final String CLICKHOUSE_SYNC_IGNORE_CLUSTER="cube.site.clickhouse.sync.ignore.cluster";
    private static final String CLICKHOUSE_SYNC_NEW_CLUSTER="cube.site.clickhouse.sync.new.cluster";

    private static final String CLICKHOUSE_CLUSTER_SYNC_RELATION = "clickhouse.cluster.sync.relation";

    private static final String CLICKHOUSE_ALL ="all_tenants";

    private static final String CLICKHOUSE_ENHANCED_VARIABLE_REPLACEMENT="cube.site.clickhouse.enhanced.variable.replacement";

    public void setClickhouseLimit(){
        SysParaDO selectLimit = siteClickhouseParas.get().get(CLICKHOUSE_SELECT_SERVICE_LIMIT);
        if(selectLimit!=null){
            try{
                Map<String,Integer>limits=new HashMap<>();
                Arrays.stream(selectLimit.getValue().split(";"))
                        .forEach(u-> limits.put(u.split(":")[0],Integer.valueOf(u.split(":")[1])));
                clickhouseLimits.set(limits);
            }catch (Exception e){
            }
        }
    }

    public Integer getSelectSerivceLimit(String service){
        Integer l = clickhouseLimits.get().get(service);
        if(l==null){
            return getSelectLimit();
        }
        return l;
    }

    public int getTableToDropLimit(){
        SysParaDO dropLimit = siteClickhouseParas.get().get(CLICKHOUSE_EMPTY_TABLE_DROP_LIMIT);
        if(dropLimit!=null){
            try{
                return Integer.parseInt(dropLimit.getValue());
            }catch (Exception e){
            }
        }
        return 0;
    }
    public long getFillNullLenientTime(){
        SysParaDO selectLimit = siteClickhouseParas.get().get(CLICKHOUSE_FILL_NULL_LENIENT_TIME);
        if(selectLimit!=null){
            try{
                return Long.parseLong(selectLimit.getValue());
            }catch (Exception e){
            }
        }
        return 120;
    }

    public Integer getSelectLimit(){
        SysParaDO selectLimit = siteClickhouseParas.get().get(CLICKHOUSE_SELECT_LIMIT);
        if(selectLimit!=null){
            try{
                return Integer.valueOf(selectLimit.getValue());
            }catch (Exception e){
            }
        }
        return DEAFULT_SQL_LIMIT;
    }

    public String getSystemForceClickhouseEnv(){
        SysParaDO sp = siteClickhouseParas.get().get(CLICKHOUSE_SYS_FORCE_ENV);
        if(sp==null){
            return null;
        }
        return sp.getValue();
    }

    public String getClickhouseEnvRoute(String serviceName){
        SysParaDO sp = siteClickhouseParas.get().get(CLICKHOUSE_QUERY_ENV_ROUTE);
        if(sp==null){
            return null;
        }
        try{
            Map<String,String> map = JsonUtils.toObject(sp.getValue(), Map.class);
            return map.get(serviceName);
        }catch (Exception e){
            return null;
        }
    }


    public String getUseClickHouseInfraPage(){
        SysParaDO sp = siteClickhouseParas.get().get(CLICKHOUSE_ON_INFRA_PAGE);
        if(sp==null){
            return "null";
        }
        return sp.getValue();
    }

    public boolean onlyRawSql(){
        SysParaDO onlyRaw = siteClickhouseParas.get().get(CLICKHOUSE_ONLY_RAW_SQL);
        if(onlyRaw!=null){
            return "true".equals(onlyRaw.getValue());
        }
        return false;
    }

    public boolean enhancedVariableReplacement(){
        SysParaDO onlyRaw = siteClickhouseParas.get().get(CLICKHOUSE_ENHANCED_VARIABLE_REPLACEMENT);
        if(onlyRaw!=null){
            return "true".equals(onlyRaw.getValue());
        }
        return false;
    }

    public boolean allowNoSelect(String user){
        try{
            SysParaDO sysParaDO = siteClickhouseParas.get().get(CLICKHOUSE_DBA_LIST);
            Set<String> allows = new HashSet<>(Arrays.asList(sysParaDO.getValue().split(",")));
            return allows.contains(user);
        }catch (Exception e){
            return false;
        }
    }

    public Set<String> getSyncIgnoreCluster(){
        SysParaDO sysParaDO = siteClickhouseParas.get().get(CLICKHOUSE_SYNC_IGNORE_CLUSTER);
        if(sysParaDO==null || StringUtils.isEmpty(sysParaDO.getValue())){
            return new HashSet<>();
        }
        return new HashSet<>(Arrays.asList(sysParaDO.getValue().split(",")));
    }

    public Set<String> getSyncNewCluster(){
        SysParaDO sysParaDO = siteClickhouseParas.get().get(CLICKHOUSE_SYNC_NEW_CLUSTER);
        if(sysParaDO==null || StringUtils.isEmpty(sysParaDO.getValue())){
            return new HashSet<>();
        }
        return new HashSet<>(Arrays.asList(sysParaDO.getValue().split(",")));
    }

    public Map<String, String> getClickhouseClusterSyncRelation(){
        SysParaDO sysParaDO = siteClickhouseParas.get().get(CLICKHOUSE_CLUSTER_SYNC_RELATION);
        if(sysParaDO==null || StringUtils.isEmpty(sysParaDO.getValue())){
            return new HashMap<>();
        }
        return JsonUtils.toObject(sysParaDO.getValue(), Map.class);
    }

    public Set<String>getCNOutboundFirewall(){
        SysParaDO sysParaDO = siteClickhouseParas.get().get(CLICKHOUSE_CN_OUTBOUND_FIREWALL);
        if(sysParaDO==null || StringUtils.isEmpty(sysParaDO.getValue())){
            return new HashSet<>();
        }
        return new HashSet<>(Arrays.asList(sysParaDO.getValue().split(",")));
    }

    public boolean allowCrossDbWithoutCheckPermission(){
        SysParaDO sysParaDO = siteClickhouseParas.get().get(CLICKHOUSE_QUERY_ALLOW_CROSS_DB_WITHOUT_PERMISSION);
        if(sysParaDO==null){
            return false;
        }
        return StringUtils.equals(sysParaDO.getValue(),"true");
    }


    public boolean isForceToClickhouseQuery(String tenantName){
        SysParaDO enable = siteClickhouseParas.get().get(CLICKHOUSE_QUERY_FORCE_ENABLE_TENANTS);
        SysParaDO disable = siteClickhouseParas.get().get(CLICKHOUSE_QUERY_FORCE_DISABLE_TENANTS);
        if(enable==null || StringUtils.isEmpty(enable.getValue())){
            return false;
        }
        Set<String> allows = new HashSet<>(Arrays.asList(enable.getValue().split(",")));

        if(allows.contains(CLICKHOUSE_ALL)){
            if(disable==null || StringUtils.isEmpty(disable.getValue())){
                return true;
            }
            Set<String> denies = new HashSet<>(Arrays.asList(disable.getValue().split(",")));
            return !denies.contains(tenantName);
        }
        return allows.contains(tenantName);
    }

    public boolean isClickhouseTenantQueryEnable(String tenantName){
        Map<String, String> clickhouseSwitch = alarmClickhouseSwitchRef.get();
        if(clickhouseSwitch==null){
            return false;
        }
        String allowString = clickhouseSwitch.get(CLICKHOUSE_QUERY_ALLOW_TENANTS);
        if(StringUtils.isEmpty(allowString)){
            return false;
        }
        String denyString = clickhouseSwitch.get(CLICKHOUSE_QUERY_DENY_TENANTS);
        Set<String> allows = new HashSet<>(Arrays.asList(allowString.split(",")));
        if(allows.contains(CLICKHOUSE_ALL)){
            if(StringUtils.isEmpty(denyString)){
                return true;
            }
            Set<String> denies = new HashSet<>(Arrays.asList(denyString.split(",")));
            return !denies.contains(tenantName);
        }
        return allows.contains(tenantName);
    }

    private void setAlarmClickhouseSwitch(List<SysParaDO> alarmClickhouseParasParas) {
        Map<String,String> results = new HashMap<>();
        if (CollectionUtils.isNotEmpty(alarmClickhouseParasParas)){
            for (SysParaDO sysParaDO : alarmClickhouseParasParas){
                try {
                    results.put(sysParaDO.getParaKey(),sysParaDO.getValue());
                }catch (Exception e){
                    log.error("setAlarmClickhouseSwitch error,para is {}",JsonUtils.toJsonStringIgnoreExp(sysParaDO),e);
                }
            }
        }
        alarmClickhouseSwitchRef.set(results);
    }

    private void setHubForwardMap(List<SysParaDO> hubForwardMapParas) {
        Map<String,String> results = new HashMap<>();
        if (CollectionUtils.isNotEmpty(hubForwardMapParas)){
            for (SysParaDO sysParaDO : hubForwardMapParas){
                try {
                    results.put(sysParaDO.getParaKey(),sysParaDO.getValue());
                }catch (Exception e){
                    log.error("setHubForwardMap error,para is {}",JsonUtils.toJsonStringIgnoreExp(sysParaDO),e);
                }
            }
        }
        hubForwardMapRef.set(results);
    }


    private void setSwitchAppNameToTenantName(List<SysParaDO> switchAppNameToTenantNameParas) {
        Map<String,String> results = new HashMap<>();
        if (CollectionUtils.isNotEmpty(switchAppNameToTenantNameParas)){
            for (SysParaDO sysParaDO : switchAppNameToTenantNameParas){
                try {
                    results.put(sysParaDO.getParaKey(),sysParaDO.getValue());
                }catch (Exception e){
                    log.error("setSwitchAppNameToTenantName error,para is {}",JsonUtils.toJsonStringIgnoreExp(sysParaDO),e);
                }
            }
        }
        switchAppNameToTenantNameMap.set(results);
    }

    private void setNotifyIntervalInMinutes(List<SysParaDO> notifyIntervalInMinutesParas) {
        if(CollectionUtils.isEmpty(notifyIntervalInMinutesParas)){
            notifyIntervalInMinutesValue.set(defaultNotifyIntervalInMinutesValue);
            return;
        }

        try{
            String value = notifyIntervalInMinutesParas.get(0).getValue();
            notifyIntervalInMinutesValue.set(Integer.valueOf(value));
        }catch (Exception e){
            log.error("setManagerService error,para is {} ",JsonUtils.toJsonStringIgnoreExp(notifyIntervalInMinutesParas),e);
        }
    }

    private void setSiteIgnoreClickhouseSyncList(List<SysParaDO> ps){
        if(ps.isEmpty()){
            clickhouseSyncIgnoreListString="";
            return;
        }
        for(SysParaDO p:ps){
            if(siteClickhouseIgnoreSyncKey.equals(p.getParaKey())){
                clickhouseSyncIgnoreListString=p.getValue();
            }
        }
    }

    private void setSiteUseInfluxdbEnvCache(List<SysParaDO> ps){
        for(SysParaDO p:ps){
            if(siteUseInfluxdbEnvKey.equals(p.getParaKey())){
                siteInfluxdbEnvCache.set(Sets.newHashSet(p.getValue().split(",")));
            }
        }
    }

    public Set<String> getSiteUseInfluxdbEnvCache(){
        if(siteInfluxdbEnvCache.get() != null){
            return siteInfluxdbEnvCache.get();
        } else {
            return new HashSet<>();
        }
    }

    private void setSiteSwitchCache(List<SysParaDO> ps){
        Map<String, Set<String>> siteSwitchCache = new HashMap<>();
        for(SysParaDO sysParaDO:ps){
            Set<String> metrics = siteSwitchCache.computeIfAbsent(sysParaDO.getParaKey(), f -> new HashSet<>());
            metrics.addAll(Arrays.asList(sysParaDO.getValue().split(",")));
        }
        siteSwitchRef.set(siteSwitchCache);
    }

    private void setHeartCheckInternalInMins(List<SysParaDO> heartCheckInternalInMinsParas) {
        if(CollectionUtils.isEmpty(heartCheckInternalInMinsParas)){
            heartCheckInternalInMinsValue.set(defaultHeartCheckInternalInMinsValue);
            return;
        }

        try{
            String value = heartCheckInternalInMinsParas.get(0).getValue();
            heartCheckInternalInMinsValue.set(Integer.valueOf(value));
        }catch (Exception e){
            log.error("setHeartCheckInternalInMins error,para is {} ",JsonUtils.toJsonStringIgnoreExp(heartCheckInternalInMinsParas),e);
        }
    }


    private void setManagerService(List<SysParaDO> managerServiceParas) {
        if(CollectionUtils.isEmpty(managerServiceParas)){
            managerServiceRef.set(defaultManagerServiceName);
            return;
        }

        try{
            String managerServiceName = managerServiceParas.get(0).getValue();
            managerServiceRef.set(managerServiceName);
        }catch (Exception e){
            log.error("setManagerService error,para is {} ",JsonUtils.toJsonStringIgnoreExp(managerServiceParas),e);
        }

    }

    private void setOverdueServerForDifferentService(List<SysParaDO> delOverdueServerForDifferentServiceParas) {
        Map<String,Integer> results = new HashMap<>();
        if(CollectionUtils.isNotEmpty(delOverdueServerForDifferentServiceParas)){
            for(SysParaDO sysParaDO:delOverdueServerForDifferentServiceParas){
                try {
                    results.put(StringUtils.lowerCase(sysParaDO.getParaKey()), Integer.valueOf(sysParaDO.getValue()));
                }catch (Exception e){
                    log.error("setOverdueServerForDifferentService error,para is {} ", JsonUtils.toJsonStringIgnoreExp(sysParaDO),e);
                }
            }
        }
        delOverdueServerForDifferentServiceMap.set(results);
    }

    private void setServerOverdueDay(List<SysParaDO> serverOverdueTimeParas) {
        try{
            if(CollectionUtils.isEmpty(serverOverdueTimeParas)){
                overdueServerDayValue.set(defaultOverdueServerDay);
                return;
            }
            Integer overdueDay = Integer.valueOf(serverOverdueTimeParas.get(0).getValue());
            if(null == overdueDay){
                overdueServerDayValue.set(defaultOverdueServerDay);
            }
        }catch (Exception e){
            log.error("setServerOverdueDay error ,para is {}",JsonUtils.toJsonStringIgnoreExp(serverOverdueTimeParas),e);
        }
    }

    private void setDelOverdueServerForAllServiceParas(List<SysParaDO> delOverdueServerForAllServiceParas) {
        try{
            if(CollectionUtils.isEmpty(delOverdueServerForAllServiceParas)){
                delOverdueServerForAllServiceValue.set(false);
                return;
            }
            boolean isDelOverdueServerForAllService = false;
            isDelOverdueServerForAllService = StringUtils.equals( delOverdueServerForAllServiceParas.get(0).getValue(),"true");
            delOverdueServerForAllServiceValue.set(isDelOverdueServerForAllService);
        }catch (Exception e){
            log.error("setDelOverdueServerForAllServiceParas error ",e);
        }
    }

    private void split(List<SysParaDO> alarmAaParas, List<SysParaDO> sysParaDOS, List<SysParaDO> sendAllToCubeParas, List<SysParaDO> serviceSendToSelfParas,
                       List<SysParaDO> delOverdueServerForAllServiceParas, List<SysParaDO> serverOverdueTimeParas,
                       List<SysParaDO> delOverdueServerForDifferentServiceParas, List<SysParaDO> managerServiceParas,
                       List<SysParaDO> notifyIntervalInMinutesParas,List<SysParaDO> switchAppNameToTenantNameParas,
                       List<SysParaDO> heartCheckInternalInMinsParas, List<SysParaDO> siteClickhouseParas, List<SysParaDO> siteSwitchParas,
                       List<SysParaDO> alarmClickhouseParasParas, List<SysParaDO> switchAndTimeLimitParas, List<SysParaDO> asyncmqTopicEnumParas,
                       List<SysParaDO> zoomNodeSwitchParas, List<SysParaDO> cubeThresholdsParas,List<SysParaDO> hubForwardMapParas,List<SysParaDO> siteInfluxdbParas,List<SysParaDO> CMDBConfigLoadParas, List<SysParaDO> alarmBoardParas,List<SysParaDO> cubeTemplateParas) {
        if(CollectionUtils.isEmpty(sysParaDOS)){
            return;
        }

        for(SysParaDO sysParaDO:sysParaDOS){
            if (StringUtils.equals(sysParaDO.getType(), alarmAaType)) {
                alarmAaParas.add(sysParaDO);
            } else if (StringUtils.equals(sysParaDO.getType(), sendAllToCubeType)) {
                sendAllToCubeParas.add(sysParaDO);
            }else if(StringUtils.equals(sysParaDO.getType(),serviceSendToSelfType)){
                serviceSendToSelfParas.add(sysParaDO);
            }else if(StringUtils.equals(sysParaDO.getType(),isDelOverdueServerForAllServiceType)){
                delOverdueServerForAllServiceParas.add(sysParaDO);
            }else if(StringUtils.equals(sysParaDO.getType(),serverOverdueTimeType)){
                serverOverdueTimeParas.add(sysParaDO);
            }else if(StringUtils.equals(sysParaDO.getType(),delOverdueServerForDifferentServiceType)){
                delOverdueServerForDifferentServiceParas.add(sysParaDO);
            }else if(StringUtils.equals(sysParaDO.getType(),managerServiceType)){
                managerServiceParas.add(sysParaDO);
            }else if(StringUtils.equals(sysParaDO.getType(),notifyIntervalInMinutesType)){
                notifyIntervalInMinutesParas.add(sysParaDO);
            }else if(StringUtils.equals(sysParaDO.getType(),switchAppNameToTenantNameType)){
                switchAppNameToTenantNameParas.add(sysParaDO);
            }else if(StringUtils.equals(sysParaDO.getType(),heartCheckInternalInMinsType)){
                heartCheckInternalInMinsParas.add(sysParaDO);
            }else if(StringUtils.equals(sysParaDO.getType(),siteClickhouseType)){
                siteClickhouseParas.add(sysParaDO);
            }else if(StringUtils.equals(sysParaDO.getType(),siteSwitchType)){
                siteSwitchParas.add(sysParaDO);
            }else if(StringUtils.equals(sysParaDO.getType(),alarmClickhouseSwitchType)){
                alarmClickhouseParasParas.add(sysParaDO);
            }else if (StringUtils.equals(sysParaDO.getType(),CMDBConfigLoadType)){
                CMDBConfigLoadParas.add(sysParaDO);
             } else if (StringUtils.equals(sysParaDO.getType(),TIME_LIMIT)) {
                switchAndTimeLimitParas.add(sysParaDO);
            } else if (StringUtils.equals(sysParaDO.getType(),ASYNCMQTOPICENUM)) {
                asyncmqTopicEnumParas.add(sysParaDO);
            } else if (StringUtils.equals(sysParaDO.getType(), ZOOM_NODE_SWITCH)) {
                zoomNodeSwitchParas.add(sysParaDO);
            } else if(StringUtils.equals(sysParaDO.getType(), CUBE_THRESHOLDS)) {
                cubeThresholdsParas.add(sysParaDO);
            }else if (StringUtils.equals(sysParaDO.getType(),hubForwardMapType)){
                hubForwardMapParas.add(sysParaDO);
            }else if(StringUtils.equals(sysParaDO.getType(),siteInfluxdbType)){
                siteInfluxdbParas.add(sysParaDO);
            } else if(StringUtils.equals(sysParaDO.getType(), ALARM_BOARD)) {
                alarmBoardParas.add(sysParaDO);
            }else if(StringUtils.equals(sysParaDO.getType(),CUBE_TEMPLATE)) {
                cubeTemplateParas.add(sysParaDO);
            }
        }
    }

    private void setServiceSendToSelf(List<SysParaDO> serviceSendToSelfParas) {
        try{
            Map<String,Boolean> serviceSendToSelf = new HashMap<>();
            if(CollectionUtils.isNotEmpty(serviceSendToSelfParas)) {

                for(SysParaDO sysParaDO: serviceSendToSelfParas){
                    if (StringUtils.isBlank(sysParaDO.getParaKey())) {
                        continue;
                    }
                    String serviceName = StringUtils.lowerCase(sysParaDO.getParaKey());
                    serviceSendToSelf.put(serviceName,"true".equalsIgnoreCase(StringUtils.trim(sysParaDO.getValue())));
                }
            }
            serviceSendToSelfMap.set(serviceSendToSelf);
        }catch (Exception e){
            log.error("load setServiceSendToSelf error ",e);
        }

    }

    private void setSendToCube(List<SysParaDO> sendAllToCubeParas) {
        try{
            if(CollectionUtils.isNotEmpty(sendAllToCubeParas)){
                isSendAllToCube.set(sendAllToCubeParas.get(0)!=null && "true".equals(sendAllToCubeParas.get(0).getValue()));
            }
        }catch (Exception e){
            log.error("load setSendToCube error ",e);

        }
    }


    private void setCMDBConfigLoad(List<SysParaDO> cmdbConfigLoadParas) {
        try{
            if(CollectionUtils.isNotEmpty(cmdbConfigLoadParas)){
                isCMDBConfigLoad.set(cmdbConfigLoadParas.get(0)!=null && "true".equals(cmdbConfigLoadParas.get(0).getValue()));
            }
        }catch (Exception e){
            log.error("load setSendToCube error ",e);

        }
    }


    public ResponseObject searchSysPara(PageQuery<SysParaQuery> pageQuery) {
        if (StringUtils.isBlank(pageQuery.getQueryPara().getType()) || !pageQuery.getQueryPara().getType().equals("dcMap")){
            authService.checkAuth(pageQuery);
        }
        List<SysParaDO> sysParaDOS=sysParaHandler.searchSysPara(pageQuery);
        Integer totalCount=sysParaHandler.getSysParaCount(pageQuery);
        return ResponseObject.success(new PageResult(totalCount,sysParaDOS));
    }

    public ResponseObject listByType(SysParaQuery sysParaQuery) {
        List<SysParaDO> sysParaDOS=sysParaHandler.listByType(sysParaQuery);
        return ResponseObject.success(sysParaDOS);
    }

    public SysParaDO getByType(String paraKey, String paraType) {
        List<SysParaDO> sysParaDOS = sysParaHandler.listByTypeAndParaKey(paraType, paraKey);
        return sysParaDOS.stream().findFirst().orElse(null);
    }

    public ResponseObject listPublic(PublicSysParaQuery sysParaQuery) {

        List<SysParaDO> sysParaDOS;
        if (CollectionUtils.isEmpty(sysParaQuery.getParaKeyList())) {
            sysParaDOS = sysParaHandler.listByTypes(List.of(PUBLIC));
        } else {
            sysParaDOS = sysParaHandler.listByTypeAndParaKeyList(PUBLIC, sysParaQuery.getParaKeyList());
        }

        List<SysParaDTO> ret = Instance.ofNullable(sysParaDOS).stream().map(x -> {
            SysParaDTO dto = new SysParaDTO();
            BeanUtils.copyProperties(x, dto);
            return dto;
        }).collect(Collectors.toList());

        return ResponseObject.success(ret);
    }

    public ResponseObject listByTypeInLabel(SysParaQuery sysParaQuery) {
        List<SysParaDO> sysParaDOS=sysParaHandler.listByType(sysParaQuery);
        return ResponseObject.success(Instance.ofNullable(sysParaDOS).stream().map(item->new ValueText(item.getParaKey(),item.getValue())).collect(Collectors.toList()));
    }


    public ResponseObject add(SysParaInput sysParaInput) {
        authService.checkAuth(sysParaInput);
        sysParaHandler.add(sysParaInput);
        return ResponseObject.success(true);
    }

    public ResponseObject edit(SysParaInput sysParaInput) {
        authService.checkAuth(sysParaInput);
        sysParaHandler.edit(sysParaInput);
        return ResponseObject.success(true);
    }

    public ResponseObject outEdit(SysParaInput sysParaInput) {

        authService.checkAuthFromAuthInterceptorIgnoreTenant();
        boolean canEdit = isEditableByOut(sysParaInput.getType(), sysParaInput.getParaKey());
        if (canEdit) {
            sysParaHandler.edit(sysParaInput);
            return ResponseObject.success(true);
        }
        return ResponseObject.success(false);
    }

    public boolean isEditableByOut(String type, String paramKey) {
        if (StringUtils.isBlank(type) || StringUtils.isBlank(paramKey)) {
            return false;
        }
        //disable update self
        if (CUBE_PREPLAN_TYPE.equals(type) && EDITABLE_SYSTEM_PARAM.equals(paramKey)) {
            return false;
        }
        List<SysParaDO> sysParaDOList = sysParaHandler.listByTypeAndParaKey(CUBE_PREPLAN_TYPE, EDITABLE_SYSTEM_PARAM);
        if (CollectionUtils.isNotEmpty(sysParaDOList)) {
            String value = sysParaDOList.get(0).getValue();
            if (StringUtils.isNotBlank(value)) {
                Map<String, String> editMap = JsonUtils.toObject(value, Map.class);
                return Optional.ofNullable(editMap.get(type)).map(paramKey::equals).orElse(false);
            }
        }
        return false;
    }

    public ResponseObject delete(IdPara idPara) {
        authService.checkAuth(idPara);
        sysParaHandler.delete(idPara.getId());
        return ResponseObject.success(true);
    }


    public ResponseObject getById(IdPara idPara) {
        authService.checkAuth(idPara);
        SysParaDO sysParaDO=  sysParaHandler.getById(idPara.getId());
        return ResponseObject.success(sysParaDO);
    }



    public boolean isSendToSelf(String tenantName) {
      Boolean result=  serviceSendToSelfMap.get().get(tenantName);
      return result != null && result;
    }

    public boolean isSendToCube() {
        return isSendAllToCube.get();
    }

    public boolean isCMDBConfigLoader() {
        return isCMDBConfigLoad.get();
    }

    public boolean isDelOverdueServerForAllService() {
        return  delOverdueServerForAllServiceValue.get();
    }

    public Integer getServerOverdueDay() {
        return overdueServerDayValue.get();
    }

    public Map<String,Integer> getDelOverdueServerForDifferentServiceMap() {
        return  delOverdueServerForDifferentServiceMap.get();
    }

    public String getManagerService() {
        return managerServiceRef.get();
    }

    public Integer getNotifyIntervalInMinutes() {
      return  notifyIntervalInMinutesValue.get();
    }
    public Map<String,String> getSwitchAppNameToTenantName(){
        return switchAppNameToTenantNameMap.get();
    }

    public String getHubForwardMap(){
        return hubForwardMapRef.get().get(hubForwardMapType);
    }

    public Integer getHeartCheckInternalInMins() {
        return  heartCheckInternalInMinsValue.get();
    }

    public String getClickhouseSyncIgnoreListString() {
        return clickhouseSyncIgnoreListString;
    }

    public long getClickhouseTagTimeSpan(){
        try{
            SysParaDO timespan = siteClickhouseParas.get().get(SITE_CLICKHOUSE_TAG_TIME_SPAN_KEY);
            return Long.parseLong(timespan.getValue());
        }catch (Exception e){
            return 3600*3;
        }
    }

    public long getClickhouseTagTimeSpanV2() {
        try {
            SysParaDO sysPara = siteClickhouseParas.get().get(SITE_CLICKHOUSE_TAG_TIME_SPAN_V2_KEY);
            return Long.parseLong(sysPara.getValue());
        } catch (Exception e) {
            log.error("getClickhouseTagTimeSpanV2 error", e);
        }
        return DEFAULT_CK_TAG_QUERY_TIME_SPAN;
    }

    public long getClickhouseTagTimeSpanV3() {
        try {
            SysParaDO sysPara = siteClickhouseParas.get().get(SITE_CLICKHOUSE_TAG_TIME_SPAN_V3_KEY);
            return Long.parseLong(sysPara.getValue());
        } catch (Exception e) {
            log.error("getClickhouseTagTimeSpanV2 error", e);
        }
        return DEFAULT_CK_TAG_QUERY_TIME_SPAN;
    }

    public int getClickhouseTagLimit() {
        try {
            SysParaDO sysPara = siteClickhouseParas.get().get(SITE_CLICKHOUSE_TAG_LIMIT_KEY);
            return Integer.parseInt(sysPara.getValue());
        } catch (Exception e) {
            log.error("getClickhouseTagLimit error", e);
        }
        return DEFAULT_CK_TAG_QUERY_LIMIT;
    }

    public Map getSwitchFilterDataAndTimeLimitAndRetrySendIM(String key) {
        boolean timeLimit = Objects.isNull(timeLimitCache.get()) || timeLimitCache.get().isEmpty() || org.springframework.util.StringUtils.isEmpty(timeLimitCache.get().get(key));
        if (timeLimit) {
            return Maps.newHashMap();
        }
        return JsonUtils.toObject((timeLimitCache.get().get(key)), Map.class);
    }

    private void setLoadTimeLimit(List<SysParaDO> sysParaDOS) {
        try {
            if (CollectionUtils.isEmpty(sysParaDOS)) {
                return;
            }
            Map<String, String> cache = new HashMap<>(sysParaDOS.size());
            for (SysParaDO sysParaDO : sysParaDOS) {
                try {
                    cache.put(sysParaDO.getParaKey(), sysParaDO.getValue());
                } catch (Exception e) {
                    log.error("loadTimeLimit error ,data is {}", JsonUtils.toJsonString(sysParaDO), e);
                }
            }
            timeLimitCache.set(cache);
        } catch (Exception e) {
            log.error("load sys para error", e);
        }
    }

    public String getAsyncmqTopicEnum(String key) {
        boolean exist = Objects.isNull(asyncmqTopicEnumCache.get()) || asyncmqTopicEnumCache.get().isEmpty() || org.springframework.util.StringUtils.isEmpty(asyncmqTopicEnumCache.get().get(key));
        if (exist) {
            return "";
        }
        return asyncmqTopicEnumCache.get().get(key);
    }

    public String getZoomNodeSwitch(String key) {
        boolean hasValue = Objects.isNull(zoomNodeSwitchCache.get()) || zoomNodeSwitchCache.get().isEmpty() || org.springframework.util.StringUtils.isEmpty(zoomNodeSwitchCache.get().get(key));
        if (hasValue) {
            return "";
        }
        return zoomNodeSwitchCache.get().get(key);
    }

    private void setAsyncmqTopicEnum(List<SysParaDO> sysParaDOS) {
        try {
            if (CollectionUtils.isEmpty(sysParaDOS)) {
                return;
            }
            Map<String, String> cache = new HashMap<>(sysParaDOS.size());
            for (SysParaDO sysParaDO : sysParaDOS) {
                try {
                    cache.put(sysParaDO.getParaKey(), sysParaDO.getValue());
                } catch (Exception e) {
                    log.error("loadAsyncmqTopicEnum error ,data is {}", JsonUtils.toJsonString(sysParaDO), e);
                }
            }
            asyncmqTopicEnumCache.set(cache);
        } catch (Exception e) {
            log.error("load sys para error", e);
        }
    }

    private void setAlarmBoardCache(List<SysParaDO> sysParaDOS) {
        try {
            if (CollectionUtils.isEmpty(sysParaDOS)) {
                return;
            }
            Map<String, String> cache = new HashMap<>(sysParaDOS.size());
            for (SysParaDO sysParaDO : sysParaDOS) {
                try {
                    cache.put(sysParaDO.getParaKey(), sysParaDO.getValue());
                } catch (Exception e) {
                    log.error("load alarmBoard cache error ,data is {}", JsonUtils.toJsonString(sysParaDO), e);
                }
            }
            alarmBoardCache.set(cache);
        } catch (Exception e) {
            log.error("load sys para error", e);
        }
    }

    public Map getAlarmBoardCache(String key) {
        boolean nonExistCache = Objects.isNull(alarmBoardCache.get()) || alarmBoardCache.get().isEmpty() || org.springframework.util.StringUtils.isEmpty(alarmBoardCache.get().get(key));
        if (nonExistCache) {
            return Maps.newHashMap();
        }
        return JsonUtils.toObject((alarmBoardCache.get().get(key)), Map.class);
    }

    private void setZoomNodeSwitchCache(List<SysParaDO> sysParaDOS) {
        try {
            if (CollectionUtils.isEmpty(sysParaDOS)) {
                return;
            }
            Map<String, String> cache = new HashMap<>(sysParaDOS.size());
            for (SysParaDO sysParaDO : sysParaDOS) {
                try {
                    cache.put(sysParaDO.getParaKey(), sysParaDO.getValue());
                } catch (Exception e) {
                    log.error("loadZoomNodeSwitchCache error ,data is {}", JsonUtils.toJsonString(sysParaDO), e);
                }
            }
            zoomNodeSwitchCache.set(cache);
        } catch (Exception e) {
            log.error("load sys para error", e);
        }
    }

    private void setCubeThresHoldsCache(List<SysParaDO> sysParaDOS) {
        try {
            if (CollectionUtils.isEmpty(sysParaDOS)) {
                return;
            }
            Map<String, String> cache = new HashMap<>(sysParaDOS.size());
            for (SysParaDO sysParaDO : sysParaDOS) {
                try {
                    cache.put(sysParaDO.getParaKey(), sysParaDO.getValue());
                } catch (Exception e) {
                    log.error("loadZoomNodeSwitchCache error ,data is {}", JsonUtils.toJsonString(sysParaDO), e);
                }
            }
            thresHoldsCache.set(cache);
        } catch (Exception e) {
            log.error("load sys para error", e);
        }
    }

    public Map getCubeThresHoldsCache(String key) {
        boolean timeLimit = Objects.isNull(thresHoldsCache.get()) || thresHoldsCache.get().isEmpty() || org.springframework.util.StringUtils.isEmpty(thresHoldsCache.get().get(key));
        if (timeLimit) {
            return Maps.newHashMap();
        }
        return JsonUtils.toObject((thresHoldsCache.get().get(key)), Map.class);
    }

    private void setCubeTemplateCache(List<SysParaDO> sysParaDOS) {
        try {
            if (CollectionUtils.isEmpty(sysParaDOS)) {
                return;
            }
            Map<String, String> cache = new HashMap<>(sysParaDOS.size());
            for (SysParaDO sysParaDO : sysParaDOS) {
                try {
                    cache.put(sysParaDO.getParaKey(), sysParaDO.getValue());
                } catch (Exception e) {
                    log.error("load cubeTemplate cache error ,data is {}", JsonUtils.toJsonString(sysParaDO), e);
                }
            }
            cubeTemplateCache.set(cache);
        } catch (Exception e) {
            log.error("load sys para error", e);
        }
    }

    public String getCubeTemplateCache(String key) {
        boolean IgnoreNPE = Objects.isNull(cubeTemplateCache.get()) || cubeTemplateCache.get().isEmpty() || org.springframework.util.StringUtils.isEmpty(cubeTemplateCache.get().get(key));
        if (IgnoreNPE) {
            return "";
        }
        return cubeTemplateCache.get().get(key).trim();
    }

    public AlarmAAPara getAlarmAAPara() {
        return alarmAAParaRef.get();
    }
}
