package us.zoom.cube.site.core;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.cube.lib.trace.TraceAgentConfig;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.site.api.intercept.AuthInterceptor;
import us.zoom.cube.site.lib.input.TraceAgentConfigInput;
import us.zoom.cube.site.lib.output.PageResult;
import us.zoom.cube.site.lib.output.trace.TraceAgentConfigOutput;
import us.zoom.infra.dao.model.TenantDO;
import us.zoom.infra.dao.model.TraceAgentConfigDO;
import us.zoom.infra.dao.service.TraceAgentConfigDAO;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @authoer: eason.jia
 * @createDate: 2024/8/14
 * @description:
 */
@Slf4j
@Component
public class TraceAgentHandler {

    @Autowired
    private TraceAgentConfigDAO traceAgentConfigDAO;

    @Autowired
    private TenantHandler tenantHandler;

    private static final Map<String, List<String>> DEFAULT_CLUSTER_REGION_MAPPING = new HashMap<>(){{
       put("global", Lists.newArrayList("global"));
    }};


    public PageResult<TraceAgentConfigOutput> queryTraceAgentConfig(TraceAgentConfigInput input, int page, int pageSize) {
        int countByParam = traceAgentConfigDAO.getCountByParam(null, input.getServiceId(),
                input.getCluster(), input.getRegion(), null);
        List<TraceAgentConfigDO> configDOs = traceAgentConfigDAO.queryByParam(null, input.getServiceId(),
                input.getCluster(), input.getRegion(), null, (page - 1) * pageSize, pageSize);
        List<TraceAgentConfigOutput> results = configDOs.stream().map(configDO -> {
            TraceAgentConfigOutput output = JsonUtils.toObjectByTypeRef(configDO.getConfig(), new TypeReference<>() {
            });
            TenantDO tenantById = tenantHandler.getTenantById(configDO.getServiceId());
            if (tenantById != null) {
                output.setServiceName(tenantById.getName());
            }
            output.setGmtCreate(configDO.getGmtCreate());
            output.setGmtModify(configDO.getGmtModify());
            output.setLastModifiedUser(configDO.getLastModifiedUser());
            output.setCreator(configDO.getCreator());
            output.setCluster(configDO.getCluster());
            output.setRegion(configDO.getRegion());
            output.setServiceId(configDO.getServiceId());
            output.setId(configDO.getId());
            return output;
        }).toList();
        return new PageResult<>(countByParam, results);

    }

    public String addTraceAgentConfig(TraceAgentConfigInput input) {
        TraceAgentConfigDO traceConfigDO = buildTraceAgentConfigDO(input);
        traceAgentConfigDAO.add(traceConfigDO);

        return traceConfigDO.getId();

    }

    public Boolean modifyAgentConfig(TraceAgentConfigInput input) {
        TraceAgentConfigDO traceConfigDO = new TraceAgentConfigDO();
        traceConfigDO.setLastModifiedUser(AuthInterceptor.getUserName());
        traceConfigDO.setId(input.getId());
        buildAndSetTraceAgentConfig(input, traceConfigDO);

        traceAgentConfigDAO.update(traceConfigDO);
        return true;
    }

    public Boolean deleteAgentConfig(String id) {

        traceAgentConfigDAO.deleteById(id);
        return true;
    }


    public Map<String, List<String>> getClusterRegionMapping(String tenantId) {
        List<TraceAgentConfigDO> traceAgentConfigDOS = traceAgentConfigDAO.queryByServiceId(tenantId);
        if (CollectionUtils.isEmpty(traceAgentConfigDOS)) {
            return DEFAULT_CLUSTER_REGION_MAPPING;
        }
        Map<String, List<String>> mapping = traceAgentConfigDOS.stream().collect(Collectors.groupingBy(TraceAgentConfigDO::getCluster,
                Collectors.mapping(TraceAgentConfigDO::getRegion, Collectors.toList())));
        mapping.putAll(DEFAULT_CLUSTER_REGION_MAPPING);
        return mapping;
    }

    private TraceAgentConfigDO buildTraceAgentConfigDO(TraceAgentConfigInput input) {
        TraceAgentConfigDO traceConfigDO = new TraceAgentConfigDO();
        traceConfigDO.setId(UUID.randomUUID().toString());
        traceConfigDO.setServiceId(input.getServiceId());
        traceConfigDO.setCluster(input.getCluster());
        traceConfigDO.setRegion(input.getRegion());
        traceConfigDO.setLastModifiedUser(AuthInterceptor.getUserName());
        traceConfigDO.setCreator(AuthInterceptor.getUserName());
        buildAndSetTraceAgentConfig(input, traceConfigDO);
        return traceConfigDO;
    }

    private void buildAndSetTraceAgentConfig(TraceAgentConfigInput input, TraceAgentConfigDO traceAgentConfigDO) {
        TraceAgentConfig traceAgentConfig = new TraceAgentConfig();
        traceAgentConfig.setRecordKeys(Optional.ofNullable(input.getRecordKeys()).orElse(Sets.newHashSet()));
        traceAgentConfig.setPropagableKeys(Optional.ofNullable(input.getPropagableKeys()).orElse(Sets.newHashSet()));
        traceAgentConfig.setSkipErrorLogStackFrameMapping(input.getSkipErrorLogStackFrameMapping());
        traceAgentConfig.setEnableMybatisEnhancement(input.getEnableMybatisEnhancement());
        traceAgentConfig.setMybatisEnhancementAsClient(input.getMybatisEnhancementAsClient());
        traceAgentConfig.setReportByHttpProtobufEnabled(input.getReportByHttpProtobufEnabled());
        traceAgentConfig.setMaxExportBatchSize(input.getMaxExportBatchSize());
        traceAgentConfig.setRules(input.getRules());
        traceAgentConfig.setSampler(TraceAgentConfig.SamplerType.fromString(input.getSampler()));
        traceAgentConfig.setSampleRatio(input.getSampleRatio());
        traceAgentConfig.setZoomPropagatorEnable(input.getZoomPropagatorEnable());
        traceAgentConfig.setCaptureErrorSpanEnabled(input.getCaptureErrorSpanEnabled());
        if (Boolean.TRUE.equals(traceAgentConfig.getCaptureErrorSpanEnabled())) {
            traceAgentConfig.setRecordDroppedSpanEnabled(true);
        }
        traceAgentConfig.setCaptureErrorLogEnabled(input.getCaptureErrorLogEnabled());

        traceAgentConfigDO.setConfig(JsonUtils.toJsonString(traceAgentConfig));

    }


}
