package us.zoom.cube.site.biz.clickhouse;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Event listener for ClickHouse table events
 * <AUTHOR>
 * @date 2025/4/3 13:25
 */
@Component
@Slf4j
public class ChTableEventListener {

    @Autowired
    private ClickhouseCron chTableSync;

    ExecutorService executorService = Executors.newFixedThreadPool(4);

    @EventListener
    public void handleChTableEvent(ChTableEvent<?> event) {
        executorService.execute(() -> {
            try {
                // Get metric ID safely
                String metricId = event.getMetricId();
                if (metricId == null) {
                    log.warn("Received ChTableEvent with non-String source data: {}", event.getSourceData());
                    return;
                }

                // Handle different event types
                switch (event.getSourceType()) {
                    case ChTableEvent.METRIC_AGG_CREATE_UPDATE_TYPE -> {
                        chTableSync.addAggBatchMetric(metricId);
                    }
                    case ChTableEvent.METRIC_CREATE_UPDATE_TYPE -> {
                        chTableSync.addMetrics(metricId);
                    }
                    default -> log.warn("Unknown event type: {} for metricId: {}", event.getSourceType(), metricId);
                }
            } catch (Exception e) {
                log.error("Error processing ChTableEvent for sourceType: {}, sourceData: {}", 
                         event.getSourceType(), event.getSourceData(), e);
            }
        });
    }
}
