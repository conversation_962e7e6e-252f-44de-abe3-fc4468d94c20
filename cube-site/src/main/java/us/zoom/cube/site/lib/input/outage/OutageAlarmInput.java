package us.zoom.cube.site.lib.input.outage;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class OutageAlarmInput {
    private String outageId;
    private String alarmDefinitionId;
    private String alarmRecordId;
    private String name;
    private String level;
    private Long triggerTime;
    private Long dateTime;
    private String tags;
    private String serviceId;
    private String serviceName;
}
