package us.zoom.cube.site.biz;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.zoom.op.monitor.domain.alarm.Channel;
import com.zoom.op.monitor.domain.alarm.ChannelParameter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.common.CubeConstants;
import us.zoom.cube.lib.common.RoleTypeEnum;
import us.zoom.cube.site.external.metadata.enums.ServiceLevelEnum;
import us.zoom.cube.site.external.metadata.enums.ServiceStatusEnum;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.site.api.intercept.AuthInterceptor;
import us.zoom.cube.site.biz.alarm.ChannelService;
import us.zoom.cube.site.biz.clickhouse.ClickhouseClusterService;
import us.zoom.cube.site.biz.syspara.AuthParaService;
import us.zoom.cube.site.biz.syspara.clickhouse.ClickhouseParaService;
import us.zoom.cube.site.core.*;
import us.zoom.cube.site.core.auth.TenantUserRelaHandler;
import us.zoom.cube.site.core.auth.UserRoleHandler;
import us.zoom.cube.site.core.usergroup.UserGroupHandler;
import us.zoom.cube.site.infra.enums.WebCodeEnum;
import us.zoom.cube.site.infra.enums.trace.FilterOperator;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.*;
import us.zoom.cube.site.lib.common.ValueText;
import us.zoom.cube.site.external.metadata.model.MetadataQueryRequest;
import us.zoom.cube.site.external.metadata.model.MetadataResponse;
import us.zoom.cube.site.lib.dto.UserTenantRoleDTO;
import us.zoom.cube.site.lib.dto.UserTenantRoleIncludeUserGroupDTO;
import us.zoom.cube.site.lib.input.*;
import us.zoom.cube.site.lib.input.clickhouse.ClickhouseTenantRelationInput;
import us.zoom.cube.site.lib.output.PageResult;
import us.zoom.cube.site.lib.output.tenant.TenantOut;
import us.zoom.cube.site.lib.query.*;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.service.UserGroupDAO;
import us.zoom.infra.dao.service.UserGroupTenantRelaDAO;
import us.zoom.infra.enums.TenantStatusEnum;
import us.zoom.infra.influx.service.InfluxService;
import us.zoom.infra.model.IdAndName;
import us.zoom.infra.model.usergroup.TenantRoleIncludeUserGroupDTO;
import us.zoom.infra.model.usergroup.UserGroupUserTO;
import us.zoom.infra.thread.ThreadLocalStore;
import us.zoom.infra.utils.CommonSplitConstants;
import us.zoom.infra.utils.Instance;
import us.zoom.cube.site.lib.metadata.MetaDataComponent;

import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.*;

@Component
@Slf4j
public class TenantService {

    public static final int DEFAULT_PAGE_SIZE = 200;

    @Autowired
    private TenantHandler tenantHandler;
    @Autowired
    private DataFlowHandler dataFlowHandler;
    @Autowired
    private AuthService authService;
    @Autowired
    private UserHandler userHandler;
    @Autowired
    private ChannelHandler channelHandler;
    @Autowired
    private ChannelService channelService;
    @Autowired
    private InfluxService influxService;
    @Autowired
    private DataParserHandler dataParserHandler;
    @Autowired
    private UserService userService;
    @Autowired
    private AsyncQueueGroupHandler asyncQueueGroupHandler;
    @Autowired
    private AsyncQueueHandler asyncQueueHandler;
    @Autowired
    private CubeServerHandler cubeServerHandler;

    @Autowired
    private SysParaService sysParaService;

    @Autowired
    private DataParserSourceHandler dataParserSourceHandler;

    @Autowired
    private UserRoleHandler userRoleHandler;

    @Autowired
    private TenantUserRelaHandler tenantUserRelaHandler;

    @Autowired
    private UserGroupDAO userGroupDAO;

    @Autowired
    private ClickhouseClusterService clickhouseClusterService;

    @Autowired
    private ClickhouseParaService clickhouseParaService;

    @Autowired
    private UserGroupTenantRelaDAO userGroupTenantRelaDAO;

    public static final String USER_ROLE= "userRole";
    @Autowired
    private UserGroupHandler userGroupHandler;

    @Autowired
    private AuthParaService authParaService;

    @Autowired
    private MetaDataApiService metaDataApiService;

    public List<TenantDO> myTenantNames(String userId) {
        List<TenantDO> tenantDOS = tenantHandler.myTenants(userId);

        boolean hasAuth = isRoleMaintainerOrSystemMaintainer();
        List<String> protectedServices = authParaService.getProtectedServices();
        if (!hasAuth && !CollectionUtils.isEmpty(protectedServices)) {
            tenantDOS = Instance.ofNullable(tenantDOS).stream().filter(x -> !protectedServices.contains(x.getName())).toList();
        }
        return tenantDOS;
    }

    public List<TenantOut> listAll(BasePara basePara) {
        authService.checkAuthIgnoreTenant(basePara);
//        authService.mustAdmin(basePara.getUserId());
        List<TenantOut> tenantOutList = Lists.newArrayList();
        List<TenantDO> tenantDOList = tenantHandler.listAll();
        Instance.ofNullable(tenantDOList).forEach(tenantDO -> {
            TenantOut tenantOut = new TenantOut();
            tenantOut.setId(tenantDO.getId());
            tenantOut.setName(tenantDO.getName());
            tenantOutList.add(tenantOut);
        });
        return tenantOutList;
    }

    public void addTenant(TenantDO tenantDO){
         tenantHandler.addTenant(tenantDO);
    }


    public TenantDO getTenantByName(String name){
        return tenantHandler.getTenantByName(name);
    }

    public ResponseObject getTenantByIdPara(IdPara idPara){
//        authService.mustAdmin(idPara.getUserId());
        authService.checkAuthIgnoreTenant(idPara);
        org.springframework.util.Assert.notNull(idPara.getId(), "id is null!");
        return ResponseObject.success(tenantHandler.getTenantById(idPara.getId()));
    }

    public TenantDO getTenantById(String id){
        return tenantHandler.getTenantById(id);
    }

//    public boolean assignUser(String tenantId,List<String> userIds){
//        return tenantHandler.assignUser(tenantId,userIds);
//    }

    public ResponseObject getAllTenantInLabel(BasePara basePara) {
       List<TenantDO> tenants= tenantHandler.listAll();
       return ResponseObject.success(Instance.ofNullable(tenants).stream().map(item->new ValueText(item.getId(),item.getName())).collect(Collectors.toList()));
    }


    public ResponseObject listMyTenantLabels(BasePara basePara) {
        List<TenantDO> tenants= tenantHandler.myTenants(basePara.getUserId());
        return ResponseObject.success(Instance.ofNullable(tenants).stream().map(item->new ValueText(item.getId(),item.getName())).collect(Collectors.toList()));
    }

    public ResponseObject getTenantByUserId(UserTenantQuery userTenantQuery) {
        if (userTenantQuery.getUserId() == null) {
            userTenantQuery.setUserId(ThreadLocalStore.getUserInfoLocal());
        }
        List<TenantUserRelaDO> tenantUserRelationDOs = tenantHandler.findTenantUserRelationByUseId(userTenantQuery.getUserId());
        List<TenantDO> tenantDOS = tenantHandler.listAll();
        Map<String, String> tenantAllMap = tenantDOS.stream().collect(Collectors.toMap(TenantDO::getId, TenantDO::getName));

        Map<String, List<TenantInfoDO>> ret = new HashMap<>();
        if (!CollectionUtils.isEmpty(tenantUserRelationDOs)) {
            for (TenantUserRelaDO tenantUserRelaDO : tenantUserRelationDOs) {
                List<TenantInfoDO> tenantInfoDOS = ret.get(tenantUserRelaDO.getRole());
                if (tenantInfoDOS == null) {
                    tenantInfoDOS = new ArrayList<>();
                }
                tenantInfoDOS.add(new TenantInfoDO(tenantUserRelaDO.getTenantId(), tenantAllMap.get(tenantUserRelaDO.getTenantId())));

                ret.put(tenantUserRelaDO.getRole(), tenantInfoDOS);
            }

        }
        UserTenantRoleDTO dto = new UserTenantRoleDTO();
        dto.setTenantRoleMap(ret);

        if (authService.canCrossAndOperate(userTenantQuery.getUserId())){
            dto.setAllTenant(tenantDOS.stream().map(x -> {
                TenantInfoDO t = new TenantInfoDO(x.getId(), x.getName());
                return t;
            }).collect(Collectors.toList()));
            dto.setAllRole(userRoleHandler.getAllRoles());
        }

        return ResponseObject.success(dto);
    }

    public ResponseObject searchTenantUser(PageQuery<NameQuery> pageQuery) {
        authService.checkAuthIgnoreTenant(pageQuery);
        UserDO editorUser = userHandler.getUserById(pageQuery.getUserId());
        Assert.notNull(editorUser, "editor user not exist!!");
        //only tenant-id, user-id are filled
        //1.get all tenant-user in this tenant
        List<TenantUserRelaDO> relas = tenantHandler.findRelaByUserName(pageQuery.getQueryPara().getName(), pageQuery.getTenantId(), pageQuery.getPageIndex(), pageQuery.getPageSize());

        if (CollectionUtils.isEmpty(relas)) {
            return ResponseObject.success(pageQuery.getOperId(), new PageResult(0, Lists.newArrayList()));
        }

        //2.get all user rela in this tenant, to get latest and oldest
        List<TenantUserRelaDO> relaByUserId = tenantUserRelaHandler.getTenantUserRelaByUserIdsAndTenantId(
                relas.stream().map(x -> x.getUserId()).collect(Collectors.toList()), pageQuery.getTenantId());

        if (CollectionUtils.isEmpty(relaByUserId)) {
            return ResponseObject.success(pageQuery.getOperId(), new PageResult(0, Lists.newArrayList()));
        }

        //3.group by user
        Map<String, List<TenantUserRelaDO>> tenantIdRelaMap = relaByUserId.stream().collect(Collectors.groupingBy(TenantUserRelaDO::getUserId));
        List<TenantUserRelaDTO> relasRet = relas.stream().map(x -> {
            TenantUserRelaDTO dto = new TenantUserRelaDTO();
            dto.setTenantId(x.getTenantId());
            dto.setUserId(x.getUserId());
            Map<String, Set<String>> userTenantRoles = tenantUserRelaHandler.getUserTenantRoles(x.getUserId());
            dto.setRole(userTenantRoles.get(x.getTenantId()));
            List<TenantUserRelaDO> tenantUserRelaDOList = tenantIdRelaMap.get(x.getUserId());
            Optional<TenantUserRelaDO> first = tenantUserRelaDOList.stream().sorted(Comparator.comparing(TenantUserRelaDO::getCreateTime)).findFirst();
            dto.setCreateTime(first.get().getCreateTime());
            List<TenantUserRelaDO> allRela = tenantUserRelaDOList.stream().sorted((Comparator.comparing(TenantUserRelaDO::getModifyTime))).collect(Collectors.toList());
            dto.setModifyTime(allRela.get(allRela.size() - 1).getModifyTime());
            UserInCache userFromCache = userHandler.getUserFromCache(x.getUserId());
            dto.setUserName(userFromCache == null ? null : userFromCache.getName());
            return dto;
        }).collect(Collectors.toList());

        int total = tenantHandler.getRelaCountByUserName(pageQuery.getQueryPara().getName(), pageQuery.getTenantId());
        return ResponseObject.success(pageQuery.getOperId(), new PageResult(total, relasRet));

    }

    public ResponseObject getTenantUserByUserIdAndTenantId(String userId, String tenantId) {

        TenantUserRelaDTO dto = new TenantUserRelaDTO();
        dto.setTenantId(tenantId);
        dto.setUserId(userId);
        UserInCache userFromCache = userHandler.getUserFromCache(userId);
        dto.setUserName(userFromCache == null ? null : userFromCache.getName());
        List<TenantUserRelaDO> tenantUserRelaByUserIdsAndTenantId = tenantUserRelaHandler.getTenantUserRelaByUserIdsAndTenantId(Lists.newArrayList(userId), tenantId);
        Set<String> roles = tenantUserRelaByUserIdsAndTenantId.stream().map(x -> x.getRole()).collect(toSet());
        dto.setRole(roles);

        return ResponseObject.success(dto);
    }

    public ResponseObject addTenantUser(TenantUserRelationInput relaInput) {
        authService.checkAuthIgnoreTenant(relaInput);
        relaInput.checkWhenAdd();
        if (!checkAuthForRole(relaInput)){
            throw new AuthException(WebCodeEnum.PermitError);
        }

        List<TenantUserRelaDO> relationDOList = tenantHandler.getRelationsByUserId(relaInput.getTargetUserId(), relaInput.getTenantId());
        if (!CollectionUtils.isEmpty(relationDOList)) {
            return ResponseObject.fail("user already exists in this tenant");
        }
        Set<String> toAddRoles = relaInput.getRole().stream().collect(toSet());

        List<TenantUserRelaDO> toAddRelationDO = new ArrayList<>();
        for (String role : toAddRoles) {
            TenantUserRelaDO relationDO = new TenantUserRelaDO();
            relationDO.setId(IdUtils.generateId());
            relationDO.setTenantId(relaInput.getTenantId());
            relationDO.setUserId(relaInput.getTargetUserId());
            relationDO.setRole(role);
            toAddRelationDO.add(relationDO);
        }

        tenantHandler.addTenantAll(toAddRelationDO);
        return ResponseObject.success(true);
    }

    @Transactional(rollbackFor = Exception.class)
    public ResponseObject editTenantUser(TenantUserRelationInput relaInput) {
        authService.checkAuthIgnoreTenant(relaInput);
        relaInput.checkWhenEdit();

        if (!checkAuthForRole(relaInput)){
            throw new AuthException(WebCodeEnum.PermitError);
        }

        tenantHandler.delTenantUserRelationByTenantIdAndUserId(relaInput.getTenantId(), relaInput.getTargetUserId());

        List<TenantUserRelaDO> toAddRelationDO = new ArrayList<>();
        for (String role : relaInput.getRole()) {
            TenantUserRelaDO relationDO = new TenantUserRelaDO();
            relationDO.setId(IdUtils.generateId());
            relationDO.setTenantId(relaInput.getTenantId());
            relationDO.setUserId(relaInput.getTargetUserId());
            relationDO.setRole(role);
            toAddRelationDO.add(relationDO);
        }

        tenantHandler.addTenantAll(toAddRelationDO);
        return ResponseObject.success(true);
    }

    private boolean checkAuthForRole(TenantUserRelationInput userInput) {
        if (authService.canCrossAndOperate(AuthInterceptor.getUserId())) {
            return true;
        }
        //operator should have same role in tenant
        for (String role : userInput.getRole()) {
            if (!authService.hasSuchRole(AuthInterceptor.getUserId(), AuthInterceptor.getTenantId(), role)) {
                return false;
            }
        }
        return true;
    }

    public ResponseObject searchTenant(PageQuery<NameQuery> pageQuery) {
        authService.checkAuthIgnoreTenant(pageQuery);
        List<TenantDO> relaList = tenantHandler.findTenantByName(pageQuery.getQueryPara().getId(), pageQuery.getQueryPara().getName(),
                pageQuery.getQueryPara().getDataFlowId(), pageQuery.getPageIndex(), pageQuery.getPageSize());
        int total = tenantHandler.getTenantCountByName(pageQuery.getQueryPara().getId(), pageQuery.getQueryPara().getName(),
                pageQuery.getQueryPara().getDataFlowId());
        return ResponseObject.success(pageQuery.getOperId(), new PageResult(total, relaList));
    }


    private static final Logger LOG= LoggerFactory.getLogger(TenantService.class.getName());

    public ResponseObject addTenant(TenantInput tenantInput) {
        tenantInput.check();
        Assert.notNull(tenantInput.getName(), "name is blank");
        LOG.info("add tenant userId ="+tenantInput.getUserId());
//        authService.mustAdmin(tenantInput.getUserId());
        authService.checkAuthIgnoreTenant(tenantInput);

        Integer level = tenantInput.getServiceLevel();
        if (level != null && ServiceLevelEnum.fromLevel(level) == null) {
            throw new SiteException(WebCodeEnum.ServiceLevelIllegal);
        }

        String inputServiceKey = tenantInput.getServiceKey();
        if (StringUtils.isNotBlank(inputServiceKey)) {
            TenantDO existingTenant = tenantHandler.getTenantByServiceKey(inputServiceKey);
            if (existingTenant != null) {
                if (TenantStatusEnum.NORMAL.getStatus().equals(existingTenant.getStatus())) {
                    String message = "The service key is already linked to another active Cube service '" + existingTenant.getName() + "'";
                    return ResponseObject.failWithCode(null, message, WebCodeEnum.ServiceKeyAlreadyExisted.getCode());
                } else {
                    tenantHandler.clearServiceKey(existingTenant.getId());
                }
            }
        }

        TenantDO tenantDO=new TenantDO();
        tenantDO.setCreateTime(new Date());
        tenantDO.setModifyTime(new Date());
        tenantDO.setName(tenantInput.getName());
        tenantDO.setId(IdUtils.generateId());
        tenantDO.setType(tenantInput.getType());
        tenantDO.setDataFlowId(tenantInput.getDataFlowId());
        tenantDO.setServiceKey(inputServiceKey);
        tenantDO.setServiceLevel(level);
        if (StringUtils.isNotEmpty(tenantInput.getGroup())) {
            String dataFlowId = dataFlowHandler.getDataFlowByGroupAndStatus(tenantInput.getGroup());
            if (StringUtils.isNotEmpty(dataFlowId)) {
                tenantDO.setDataFlowId(dataFlowId);
            }
            createNwsUserGroup(tenantInput.getGroup(), tenantDO.getId());
        }
        TenantDO existTenant = tenantHandler.getTenantByName(tenantInput.getName());
        if (existTenant != null) {
            String existId = existTenant.getId();
            String existKey = existTenant.getServiceKey();
            String inputKey = tenantInput.getServiceKey();
            boolean isNormal = TenantStatusEnum.NORMAL.getStatus().equals(existTenant.getStatus());

            if (!isNormal) {
                if (inputKey == null) {
                    // activate tenant only
                    tenantHandler.batchUpdateTenantStatus(List.of(existId), TenantStatusEnum.NORMAL);
                    return ResponseObject.success(existId);
                }
                // update serviceKey and level if existing serviceKey is empty
                if (existKey == null) {
                    tenantHandler.updateServiceKey(inputKey, level, existId);
                }
                // active tenant
                tenantHandler.batchUpdateTenantStatus(List.of(existId), TenantStatusEnum.NORMAL);
                return ResponseObject.success(existId);
            }

            // normal and with existing name
            if (inputKey == null || existKey != null) {
                throw new SiteException(WebCodeEnum.TenantAlreadyExisted);
            }
            // existKey is empty and inputKey has value, update serviceKey and level (align)
            tenantHandler.updateServiceKey(inputKey, level, existId);
            return ResponseObject.success(existId);
        }
        addTenant(tenantDO);

//        addAllAuthToAdminUser(tenantDO);

        handlerExtendInfo(tenantDO, tenantInput);

        connectToClickHouseCluster(tenantDO);

        return ResponseObject.success(tenantDO.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    public ResponseObject updateServiceKey(TenantInput updateInput) {
        ResponseObject checkResult = checkServiceKeyConflict(updateInput);
        if (checkResult != null) {
            return checkResult;
        }

        return executeUpdateServiceKey(updateInput);
    }

    @Transactional(rollbackFor = Exception.class)
    public ResponseObject confirmUpdateServiceKey(TenantInput updateInput) {
        ResponseObject checkResult = checkServiceKeyConflict(updateInput);
        if (checkResult != null && checkResult.getOperCode().equals(WebCodeEnum.ConfirmRequired.getCode())) {
            // user confirmed，clear occupied service_key
            TenantDO existingTenant = tenantHandler.getTenantByServiceKey(updateInput.getServiceKey());
            if (existingTenant != null && !existingTenant.getId().equals(updateInput.getTenantId())) {
                log.info("Clearing service key '{}' from tenant '{}' before updating tenant '{}'",
                        updateInput.getServiceKey(), existingTenant.getName(), updateInput.getTenantId());
                tenantHandler.clearServiceKey(existingTenant.getId());
            }
        } else if (checkResult != null) {
            return checkResult;
        }

        return executeUpdateServiceKey(updateInput);
    }

    /**
     * check service key duplicate
     * @param updateInput
     * @return return a confirmation message if conflicted, return null if no conflict
     */
    private ResponseObject checkServiceKeyConflict(TenantInput updateInput) {
        if (ServiceLevelEnum.fromLevel(updateInput.getServiceLevel()) == null) {
            throw new SiteException(WebCodeEnum.ServiceLevelIllegal);
        }

        authService.checkAuth(updateInput);
        TenantDO targetTenant = tenantHandler.getTenantById(updateInput.getTenantId());
        if (targetTenant == null) {
            return ResponseObject.failWithWebCode(WebCodeEnum.HasNoSuchTenant);
        }
        if (!authService.hasSuchTenant(updateInput.getUserId(), targetTenant.getId())) {
            return ResponseObject.failWithWebCode(WebCodeEnum.HasNoSuchTenant);
        }

        // check if the input service_key has already been used by another tenant
        TenantDO existingTenant = tenantHandler.getTenantByServiceKey(updateInput.getServiceKey());
        if (existingTenant != null && !existingTenant.getId().equals(updateInput.getTenantId())) {
            if (!authService.hasSuchTenant(updateInput.getUserId(), existingTenant.getId())) {
                log.info("service key '{}' is already used by another tenant that user '{}' do not have permission to access", updateInput.getServiceKey(), updateInput.getUserId());
                return ResponseObject.failWithCode(null,
                        "This service from Release is already linked to another Cube service '" + existingTenant.getName() + "' that you don't have permission to access",
                        WebCodeEnum.PermitError.getCode());
            }

            String confirmMessage = String.format("This service from Release is already linked to another Cube service '%s'. Are you sure you want to rebind it to this Cube service?", existingTenant.getName());
            return ResponseObject.failWithCode(null, confirmMessage, WebCodeEnum.ConfirmRequired.getCode());
        }

        return null; //no conflict
    }

    private ResponseObject executeUpdateServiceKey(TenantInput updateInput) {
        try {
            tenantHandler.updateServiceKey(updateInput.getServiceKey(), updateInput.getServiceLevel(), updateInput.getTenantId());

            TenantDO updatedTenant = tenantHandler.getTenantById(updateInput.getTenantId());
            return ResponseObject.success(updatedTenant);
        } catch (Exception e) {
            log.error("Failed to update tenant service key and level for tenant {}: {}",
                    updateInput.getTenantId(), e.getMessage(), e);
            return ResponseObject.failWithCode(null, "Failed to update service key and level: " + e.getMessage(),
                    WebCodeEnum.InnerError.getCode());
        }
    }

    public void connectToClickHouseCluster(TenantDO tenantDO) {
        Set<String> preferCluster = clickhouseParaService.getPreferCluster();
        List<ClickhouseClusterDO> clickhouseClusterDOS = clickhouseClusterService.listClusters();
        Set<String> clusterSet = clickhouseClusterDOS.stream().map(ClickhouseClusterDO::getId).collect(toSet());
        if(preferCluster.size()>0){
            for(String c:preferCluster){
                if(!clusterSet.contains(c)){
                    continue;
                }
                ClickhouseTenantRelationInput ctr=new ClickhouseTenantRelationInput();
                ctr.setRelatedTenantId(tenantDO.getId());
                ctr.setClickhouseClusterId(c);
                clickhouseClusterService.addTenantRelation(ctr);
            }
        }
    }

    public void createNwsUserGroup(String group, String destTeaantId) {
        UserGroupDO userGroupDO = userGroupDAO.getByName(group);
        if (null == userGroupDO || StringUtils.isEmpty(userGroupDO.getId())) {
            return;
        }
        UserGroupTenantRelaDO relaDO = new UserGroupTenantRelaDO();
        relaDO.setGroupId(userGroupDO.getId());
        relaDO.setTenantId(destTeaantId);
        relaDO.setId(IdUtils.generateId());
        relaDO.setCreateTime(new Date());
        relaDO.setModifyTime(new Date());
        userGroupTenantRelaDAO.add(relaDO);
    }

    private void addAllAuthToAdminUser(TenantDO tenantDO) {
        List<UserDO> userDOList=userHandler.findByRole(RoleTypeEnum.admin.name());
        List<TenantUserRelaDO> TenantUserRelationDOList = new ArrayList<>();
        for (UserDO userDO : userDOList) {
            TenantUserRelaDO relationDO = new TenantUserRelaDO();
            relationDO.setId(IdUtils.generateId());
            relationDO.setUserId(userDO.getId());
            relationDO.setRole(userDO.getRole());
            relationDO.setTenantId(tenantDO.getId());
            TenantUserRelationDOList.add(relationDO);
        }
        tenantHandler.addTenantAll(TenantUserRelationDOList);
    }

    public ResponseObject delTenant(DelTenantInput delTenantInput) {
        delTenantInput.check();
        LOG.info("del tenant userId =" + delTenantInput.getUserId());
//        authService.mustAdmin(delTenantInput.getUserId());
        authService.checkAuthIgnoreTenant(delTenantInput);

        // todo need del db in influxDB ?
        //  InfluxDBUtil.createDB(tenantInput.getName().toLowerCase());
        TenantDO existTenant = tenantHandler.getTenantByName(delTenantInput.getName());
        org.springframework.util.Assert.isTrue(null != existTenant, "the tenant is not existed, tenantName is " + delTenantInput.getName());

        List<DataParserSourceDO> dataParserSourceDOList;
        List<DataParserDO> dataParserDOList;
        List<String> inputQueueIdList = Lists.newArrayList();
        List<String> inputQueueGroupIdList = Lists.newArrayList();
        if (1 == delTenantInput.getType()) {
            dataParserSourceDOList = dataParserSourceHandler.getDataParserByTenantId(existTenant.getId());
            if (!CollectionUtils.isEmpty(dataParserSourceDOList)) {
                org.springframework.util.Assert.isTrue(false, "can not delete tenant having dataParser, tenantId is " + existTenant.getId());
            }
        } else {
            dataParserDOList = dataParserHandler.listDataParserByTenantId(existTenant.getId());
            if (!CollectionUtils.isEmpty(dataParserDOList)) {
                org.springframework.util.Assert.isTrue(false, "can not delete tenant having dataParser, tenandId is " + existTenant.getId());
            }
            inputQueueGroupIdList.addAll(asyncQueueGroupHandler.listAllForTenant(existTenant.getId()).stream().map(e -> e.getId()).collect(Collectors.toList()));
            inputQueueGroupIdList.forEach(e -> {
                inputQueueIdList.addAll(asyncQueueHandler.listRawByAqGroupId(e).stream().map(t -> t.getId()).collect(Collectors.toList()));
            });
        }

        List<String> tenantIdList = Lists.newArrayList(existTenant.getId());
        List<String> userIdList = Lists.newArrayList();
        List<String> channelIdList = Lists.newArrayList();

        if (delTenantInput.getDelDefaultUser()) {
            String normalUserName = delTenantInput.getName() + CommonSplitConstants.SPLIT + RoleTypeEnum.normal.name();
            UserDO normalUserDO = userHandler.getUserByName(normalUserName);
            if (normalUserDO != null) {
                userIdList.add(normalUserDO.getId());
            }
            String adminUserName = delTenantInput.getName() + CommonSplitConstants.SPLIT + RoleTypeEnum.admin.name();
            UserDO adminUserDO = userHandler.getUserByName(adminUserName);
            if (adminUserDO != null) {
                userIdList.add(adminUserDO.getId());
            }
        }
        if (delTenantInput.getDelDefaultChannel()) {
            Optional<Channel> channelOptional = channelHandler.findByNameAndTenantId(delTenantInput.getDefaultChannelName(), existTenant.getId());
            if (channelOptional.isPresent()) {
                channelIdList.add(channelOptional.get().getId());
            }
        }
        tenantHandler.delTenant(tenantIdList, userIdList, channelIdList, inputQueueIdList, inputQueueGroupIdList);
        return ResponseObject.success(existTenant.getId());
    }


    public ResponseObject existTenant(ExistTenantInput existTenantInput) {
        existTenantInput.check();
        authService.checkAuthIgnoreTenant(existTenantInput);
//        authService.mustAdmin(existTenantInput.getUserId());
        TenantDO existTenant = tenantHandler.getTenantByName(existTenantInput.getName());
        Boolean exist = existTenant != null;
        return ResponseObject.success(exist);
    }

    private void handlerExtendInfo(TenantDO tenantDO, TenantInput tenantInput) {
        if (tenantInput.getCreateDefaultUser()) {
            String[] roles = sysParaService.getCubeTemplateCache(USER_ROLE).split(",");
            String normalUserName = tenantInput.getName() + CommonSplitConstants.SPLIT + RoleTypeEnum.normal.name();
            UserInput normalUserInput = new UserInput();
            normalUserInput.setName(normalUserName);
            RoleTenantDO nomalRoleTenantDO = new RoleTenantDO();
            nomalRoleTenantDO.setRole(roles[0]);
            nomalRoleTenantDO.setTenantList(Collections.singletonList(tenantDO.getId()));
            normalUserInput.setRoleTenantList(Collections.singletonList(nomalRoleTenantDO));
            userService.addUser(normalUserInput);

            String adminUserName = tenantInput.getName() + CommonSplitConstants.SPLIT + RoleTypeEnum.admin.name();
            UserInput tenantAdminUserInput = new UserInput();
            tenantAdminUserInput.setName(adminUserName);
            RoleTenantDO adminRoleTenantDO = new RoleTenantDO();
            adminRoleTenantDO.setRole(roles[1]);
            adminRoleTenantDO.setTenantList(Collections.singletonList(tenantDO.getId()));
            tenantAdminUserInput.setRoleTenantList(Collections.singletonList(adminRoleTenantDO));
            userService.addUser(tenantAdminUserInput);
        }
        if (tenantInput.getCreateDefaultChannel()) {
            Channel channel = new Channel();
            channel.setName(tenantInput.getDefaultChannelName());
            channel.setTenantId(tenantDO.getId());
            channel.setUserId(tenantInput.getUserId());
            List<ChannelParameter> channelParameterList = new ArrayList<>();
            if ("kafka".equals(tenantInput.getDefaultChannelType())) {
                channel.setEngineName("Kafka");
                ChannelParameter channelParameter0 = new ChannelParameter();
                channelParameter0.setIsSecret(false);
                channelParameter0.setName("clusterId");
                channelParameter0.setValue("${clusterId}");
                ChannelParameter channelParameter1 = new ChannelParameter();
                channelParameter1.setIsSecret(false);
                channelParameter1.setName("fixTenant");
                channelParameter1.setValue(tenantInput.getSource());
                ChannelParameter channelParameter2 = new ChannelParameter();
                channelParameter2.setIsSecret(false);
                channelParameter2.setName("fixEventType");
                channelParameter2.setValue(tenantInput.getSource());
                ChannelParameter channelParameter3 = new ChannelParameter();
                channelParameter3.setIsSecret(false);
                channelParameter3.setName("topic");
                channelParameter3.setValue(tenantInput.getDefaultChannelTopic());
                channelParameterList.add(channelParameter0);
                channelParameterList.add(channelParameter1);
                channelParameterList.add(channelParameter2);
                channelParameterList.add(channelParameter3);
            } else if ("asyncmq".equals(tenantInput.getDefaultChannelType())) {
                channel.setEngineName("AsyncMQ");
                ChannelParameter channelParameter0 = new ChannelParameter();
                channelParameter0.setIsSecret(false);
                channelParameter0.setName("AsyncMQTopic");
                channelParameter0.setValue(tenantInput.getDefaultAsyncmqTopic());
                channelParameterList.add(channelParameter0);
                if (StringUtils.isNotEmpty(tenantInput.getClusterId())) {
                    ChannelParameter channelParameter1 = new ChannelParameter();
                    channelParameter1.setIsSecret(false);
                    channelParameter1.setName("clusterId");
                    channelParameter1.setValue(tenantInput.getClusterId());
                    channelParameterList.add(channelParameter1);
                }
            }
            channel.setParameters(channelParameterList);
            channel.setCreateTime(new Date());
            channel.setModifyTime(new Date());
            channelService.save(channel);
        }
    }

    public ResponseObject editTenant(TenantInput tenantInput) {
//        authService.mustAdmin(tenantInput.getId());
        authService.checkAuthIgnoreTenant(tenantInput);
        return ResponseObject.success(true);
    }

    public ResponseObject listMyDb(MetricQuery basePara) {
        long startTime = System.currentTimeMillis();
        log.info("listMyDb basePara = {}", JsonUtils.toJsonStringIgnoreExp(basePara));
        List<TenantDO> tenants= tenantHandler.myTenants(basePara.getUserId());
        ResponseObject responseObject = ResponseObject.success(Instance.ofNullable(tenants).stream().map(
                item->basePara.useClickhosue()? item.getName():item.getName().toLowerCase()).collect(Collectors.toList()));
        long endTime = System.currentTimeMillis();
        log.info("listMyDb cost = {} ms", endTime - startTime);
        return responseObject;
    }

    public ResponseObject assignDataFlowIdForIds(AssignTenantDataFlowInput assignTenantDataFlowInput) {
//        authService.mustAdmin(assignTenantDataFlowInput.getUserId());
        authService.checkAuth(assignTenantDataFlowInput);
        org.springframework.util.Assert.notNull(assignTenantDataFlowInput.getIds(), "ids is null ");
        if (StringUtils.isNotBlank(assignTenantDataFlowInput.getDataFlowId())){
            org.springframework.util.Assert.notNull(dataFlowHandler.getDataFlowById(assignTenantDataFlowInput.getDataFlowId()), "DataFlow not exist, id:" + assignTenantDataFlowInput.getDataFlowId());
        }else {
            assignTenantDataFlowInput.setDataFlowId("");
        }
        tenantHandler.assignDataFlowIdForIds(assignTenantDataFlowInput.getIds(), assignTenantDataFlowInput.getDataFlowId());
        return ResponseObject.success(true);
    }

    public PageInfo<String> serviceNameByPage(int pageIndex, int pageSize) {
        PageHelper.startPage(pageIndex, pageSize);
        return new PageInfo<>(tenantHandler.getNames());
    }

    public PageInfo<ServiceMetricsNameDO> serviceNamesByMetrics(MetricsPageInput metricsPageInput) {
        PageHelper.startPage(metricsPageInput.getPageIndex(), metricsPageInput.getPageSize());
        return new PageInfo<>(tenantHandler.listNamesByMetric(metricsPageInput.getMetricsName()));
    }

    public ResponseObject queryRoleCanAssignTenantUser() {
        Map<String, Set<String>> userTenantRoles = tenantUserRelaHandler.getUserTenantRoles(AuthInterceptor.getUserId());
        Set<String> roles = userTenantRoles.get(AuthInterceptor.getTenantId());
        if (null == roles) {
            roles = Sets.newHashSet();
        }
        if (authService.canCrossAndOperate(AuthInterceptor.getUserId())) {
            roles.add(RoleTypeEnum.admin_role.name());
        }
        return ResponseObject.success(roles);
    }

    public ResponseObject getTenantByUser(OutUserTenantQuery outUserTenantQuery) {
        outUserTenantQuery.check();
        UserInCache userFromCache = userHandler.getUserFromCacheByName(outUserTenantQuery.getUserName());

        Assert.isTrue(userFromCache != null, "user not exists:" + outUserTenantQuery.getUserName());

        UserTenantQuery userTenantQuery = new UserTenantQuery();
        userTenantQuery.setUserId(userFromCache.getId());
        return getTenantByUserId(userTenantQuery);
    }

    public ResponseObject deleteTenant(IdListPara idListPara) {
        if (!isRoleMaintainerOrSystemMaintainer()){
            throw new AuthException(WebCodeEnum.PermitError);
        }

        idListPara.check();

        tenantHandler.batchDeleteTenant(idListPara);

        return ResponseObject.success(null);
    }

    public ResponseObject queryTenantInfo(String tenantName) {
        authService.checkAuthFromAuthInterceptorIgnoreTenant();
        return ResponseObject.success(tenantHandler.getTenantByName(tenantName));
    }
    public ResponseObject queryUserRole(QueryUserRoleInput queryUserRoleInput) {

        //authService.checkAuth(queryTenantUserInput);

        return ResponseObject.success(tenantHandler.queryUserRole(queryUserRoleInput));
    }

    public ResponseObject searchTenantUserByRole(PageQuery<QueryTenantUserInput> queryTenantUserInput) {

        //authService.checkAuth(queryTenantUserInput);

        QueryTenantUserInput queryPara = queryTenantUserInput.getQueryPara();
        queryTenantUserInput.checkQueryParaNotNull();

        queryPara.check();

        return ResponseObject.success(tenantHandler.searchTenantUserByRole(queryTenantUserInput));
    }

    public ResponseObject queryUserRoleAndUserGroup(IdPara idPara) {
        //authService.checkAuth(idPara);

        if (null == idPara.getId()) {
            idPara.setId(AuthInterceptor.getUserId());
        }

        List<TenantUserRelaDO> tenantUserRelationDOs = tenantHandler.findTenantUserRelationByUseId(idPara.getId());
        // <Role, Set<TenantId>>
        HashMap<String, Set<String>> roleTenantListMap = Instance.ofNullable(tenantUserRelationDOs).stream()
                .collect(Collectors.groupingBy(TenantUserRelaDO::getRole, HashMap::new, mapping(TenantUserRelaDO::getTenantId, toSet())));


        //fill common role
        UserTenantRoleIncludeUserGroupDTO dto = new UserTenantRoleIncludeUserGroupDTO();
        // <Role, List<TenantRoleIncludeUserGroupDTO>>
        Map<String, List<TenantRoleIncludeUserGroupDTO>> tenantRoleMap = dto.getTenantRoleMap();
        fillCommon(roleTenantListMap, tenantRoleMap);
        fillUserGroups(idPara, tenantRoleMap);
        mergeCrossService(dto);

        return ResponseObject.success(dto);
    }

    public ResponseObject searchTenantByRoleUser(QueryTenantUserInput queryTenantUserInput) {
        authService.checkAuthIgnoreTenant(queryTenantUserInput);
        queryTenantUserInput.check();
        return ResponseObject.success(tenantHandler.searchTenantByRoleUser(queryTenantUserInput));
    }

    private void mergeCrossService(UserTenantRoleIncludeUserGroupDTO dto) {
        for (Map.Entry<String, List<TenantRoleIncludeUserGroupDTO>> entry : dto.getTenantRoleMap().entrySet()) {
            String role = entry.getKey();
            UserRoleDO roleByName = userRoleHandler.getRoleByName(role);
            if (null == roleByName || !roleByName.getCrossService()) {
                continue;
            }
            boolean fromCommon = false;
            Set fromUserGroup = new HashSet();
            for (TenantRoleIncludeUserGroupDTO roleDto : entry.getValue()) {
                fromCommon = fromCommon || roleDto.isFromCommon();
                fromUserGroup.addAll(roleDto.getFromUserGroup());
            }
            TenantRoleIncludeUserGroupDTO tenantRoleDTO = new TenantRoleIncludeUserGroupDTO();
            tenantRoleDTO.setName(CubeConstants.ALL_SERVICES);
            tenantRoleDTO.setFromCommon(fromCommon);
            tenantRoleDTO.setFromUserGroup(fromUserGroup);

            dto.getTenantRoleMap().put(role, Lists.newArrayList(tenantRoleDTO));
        }
    }

    private void fillCommon(HashMap<String, Set<String>> roleTenantListMap, Map<String, List<TenantRoleIncludeUserGroupDTO>> tenantRoleMap) {
        roleTenantListMap.entrySet().stream().forEach(entry -> {
            Instance.ofNullable(entry.getValue()).stream().forEach(tenantId -> {
                TenantRoleIncludeUserGroupDTO roleIncludeUserGroupDTO = new TenantRoleIncludeUserGroupDTO();
                roleIncludeUserGroupDTO.setId(tenantId);
                TenantDO tenant = tenantHandler.getTenantFromCacheFirst(tenantId);
                roleIncludeUserGroupDTO.setName(null != tenant ? tenant.getName() : null);
                roleIncludeUserGroupDTO.setFromCommon(true);

                tenantRoleMap.merge(entry.getKey(), Lists.newArrayList(roleIncludeUserGroupDTO), (oldValue, newValue) -> {
                    List<TenantRoleIncludeUserGroupDTO> ret = Lists.newArrayList();
                    ret.addAll(oldValue);
                    ret.addAll(newValue);

                    return ret;
                });
            });
        });
    }

    private void fillUserGroups(IdPara idPara, Map<String, List<TenantRoleIncludeUserGroupDTO>> tenantRoleMap) {
        //fill role from user group
        List<UserGroupUserTO> items = userGroupHandler.findTenantUserByUserId(idPara.getId());
        //<role, Map<tenantId, Set<userGroupIds>>>
        Map<String, Map<String, Set<String>>> roleTenantUserGroupMap = items.stream()
                .collect(groupingBy(UserGroupUserTO::getRole, groupingBy(UserGroupUserTO::getTenantId, mapping(UserGroupUserTO::getId, toSet()))));

        Instance.ofNullable(roleTenantUserGroupMap.entrySet()).stream().forEach((entry) -> {
            String role = entry.getKey();
            List<TenantRoleIncludeUserGroupDTO> tenantRoleIncludeUserGroupDTOS = tenantRoleMap.get(role);
            //<tenantId, userGroupIds>
            Map<String, Set<String>> tenantUserGroupMap = entry.getValue();

            tenantUserGroupMap.entrySet().forEach(tenantUserEntry -> {
                String tenantId =tenantUserEntry.getKey();
                Set<String> userGroupIDs = tenantUserEntry.getValue();
                //create new role
                if (!CollectionUtils.isEmpty(tenantRoleIncludeUserGroupDTOS)) {
                    findAdnSetUserGroup(tenantRoleIncludeUserGroupDTOS, tenantId, userGroupIDs);

                    //add to existing role
                } else {
                    TenantRoleIncludeUserGroupDTO tempDto = createTenantRoleIncludeUserGroupDTO(tenantId, userGroupIDs);
                    tenantRoleMap.merge(role, Lists.newArrayList(tempDto), (oldValue, newValue) -> {
                        oldValue.addAll(newValue);

                        return oldValue;
                    });
                }
            });


        });
    }

    private void findAdnSetUserGroup(List<TenantRoleIncludeUserGroupDTO> tenantRoleIncludeUserGroupDTOS, String tenantId, Set<String> userGroupIDs) {
        Iterator<TenantRoleIncludeUserGroupDTO> iterator = tenantRoleIncludeUserGroupDTOS.iterator();
        boolean found = false;
        while (iterator.hasNext()) {
            TenantRoleIncludeUserGroupDTO next = iterator.next();
            if (next.getId().equals(tenantId)) {
                if (!CollectionUtils.isEmpty(userGroupIDs)) {
                    Set<IdAndName> userGroupIan = userGroupIDs.stream().map(id -> {
                        UserGroupDO userGroup = userGroupHandler.getUserGroup(id);
                        IdAndName ian = new IdAndName(id, userGroup != null ? userGroup.getName() : null);
                        return ian;
                    }).collect(toSet());
                    next.getFromUserGroup().addAll(userGroupIan);
                }
                found = true;
            }

        }
        if (!found) {
            tenantRoleIncludeUserGroupDTOS.add(createTenantRoleIncludeUserGroupDTO(tenantId, userGroupIDs));
        }
    }

    private @NotNull TenantRoleIncludeUserGroupDTO createTenantRoleIncludeUserGroupDTO(String tenantId, Set<String> userGroupIds) {
        TenantRoleIncludeUserGroupDTO tempDto = new TenantRoleIncludeUserGroupDTO();
        tempDto.setId(tenantId);
        TenantDO tenantById = tenantHandler.getTenantById(tenantId);
        tempDto.setName(tenantById != null ? tenantById.getName() : null);
        Set<IdAndName> userGroupIan = userGroupIds.stream().map(id -> {
            UserGroupDO userGroup = userGroupHandler.getUserGroup(id);
            IdAndName ian = new IdAndName(id, userGroup != null ? userGroup.getName() : null);
            return ian;
        }).collect(toSet());

        tempDto.setFromUserGroup(userGroupIan);
        return tempDto;
    }

    private boolean isRoleMaintainerOrSystemMaintainer() {
        return authService.hasSuchRole(AuthInterceptor.getUserId(), null, RoleTypeEnum.roleMaintainer.name())
                || authService.hasSuchRole(AuthInterceptor.getUserId(), null, RoleTypeEnum.systemMaintainer.name());
    }

    public List<MetaDataComponent> queryMetadataWithConditions(ServiceLevelEnum typeName, Map<String, String> additionalConditions) {
        return queryMetadataWithConditions(typeName, additionalConditions, Arrays.asList(MetaDataComponent.FIELD_KEY, MetaDataComponent.FIELD_NAME));
    }

    public List<MetaDataComponent> queryMetadataWithConditions(ServiceLevelEnum typeName, Map<String, String> additionalConditions, List<String> fields) {
        List<MetadataQueryRequest.Condition> conditions = new ArrayList<>();
        conditions.add(createFieldCondition(MetaDataComponent.FIELD_STATUS, ServiceStatusEnum.ACTIVE.getValue(), FilterOperator.EQUAL.name()));

        if (additionalConditions != null) {
            additionalConditions.forEach((field, value) -> {
                if (StringUtils.isNotEmpty(value)) {
                    conditions.add(createFieldCondition(field, value, FilterOperator.EQUAL.name()));
                }
            });
        }

        return queryMetadata(typeName, conditions, fields);
    }

    private List<MetaDataComponent> queryMetadata(ServiceLevelEnum typeName, List<MetadataQueryRequest.Condition> conditions, List<String> fields) {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put(MetaDataComponent.FIELD_TYPE_NAME, typeName.name().toLowerCase());
        requestBody.put(MetaDataComponent.FIELD_FIELDS, fields);
        requestBody.put(MetaDataComponent.FIELD_PAGE_NUMBER, MetaDataComponent.DEFAULT_PAGE_NUMBER);
        requestBody.put(MetaDataComponent.FIELD_PAGE_SIZE, MetaDataComponent.DEFAULT_PAGE_SIZE);
        requestBody.put(MetaDataComponent.FIELD_CONDITIONS, conditions);

        try {
            Map<String, String> headers = metaDataApiService.createHeaderParams();
            String response = metaDataApiService.invokeMetaDataApi(headers, requestBody);
            if (response == null) {
                log.error("Failed to get response from metadata service for type: {}", typeName);
                return Collections.emptyList();
            }

            MetadataResponse metadataResponse = JsonUtils.toObject(response, MetadataResponse.class);
            if (!metadataResponse.isSuccess()) {
                log.error("Metadata service returned error for type {}: errorCode={}, errorMessage={}",
                        typeName, metadataResponse.getErrorCode(), metadataResponse.getErrorMessage());
                return Collections.emptyList();
            }

            if (metadataResponse.getData() == null || metadataResponse.getData().getList() == null) {
                log.warn("No data returned from metadata service for type: {}", typeName);
                return Collections.emptyList();
            }

            return metadataResponse.getData().getList().stream()
                    .map(item -> {
                        MetaDataComponent field = new MetaDataComponent();
                        field.setKey(item.getAttributes().get(MetaDataComponent.FIELD_KEY));
                        field.setName(item.getAttributes().get(MetaDataComponent.FIELD_NAME));
                        field.setParentKey(item.getAttributes().get(MetaDataComponent.FIELD_PARENT_KEY));
                        field.setParentName(item.getAttributes().get(MetaDataComponent.FIELD_PARENT_NAME));
                        return field;
                    })
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Failed to query metadata for type {}: {}", typeName, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    public MetaDataComponent queryHierarchyNames(String serviceKey, Integer serviceLevel) {
        if (StringUtils.isBlank(serviceKey) || serviceLevel == null) {
            return null;
        }

        if (ServiceLevelEnum.fromLevel(serviceLevel) == null) {
            throw new SiteException(WebCodeEnum.ServiceLevelIllegal);
        }

        try {
            if (serviceLevel == ServiceLevelEnum.SERVICE_GROUP.getLevel()) {
                return queryServiceGroupHierarchy(serviceKey);
            } else if (serviceLevel == ServiceLevelEnum.APPLICATION.getLevel()) {
                return queryApplicationHierarchy(serviceKey);
            } else {
                return null;
            }
        } catch (Exception e) {
            log.error("Failed to query metadata hierarchy for serviceKey: {}, serviceLevel: {}", serviceKey, serviceLevel, e);
            return null;
        }
    }

    private MetaDataComponent queryServiceGroupHierarchy(String serviceKey) {
        List<MetaDataComponent> results = queryMetadataWithConditions(ServiceLevelEnum.SERVICE_GROUP, Map.of(MetaDataComponent.FIELD_KEY, serviceKey),
                Arrays.asList(MetaDataComponent.FIELD_KEY, MetaDataComponent.FIELD_NAME, MetaDataComponent.FIELD_PARENT_KEY, MetaDataComponent.FIELD_PARENT_NAME));
        
        if (results.isEmpty()) {
            log.warn("No service group found for service key: {}", serviceKey);
            return null;
        }
        
        MetaDataComponent serviceGroup = results.get(0);
        MetaDataComponent result = new MetaDataComponent();
        result.setServiceGroupKey(serviceGroup.getKey());
        result.setServiceGroupName(serviceGroup.getName());
        result.setApplicationKey(null);
        result.setApplicationName(null);
        
        setProductInfo(result, serviceGroup.getParentKey(), serviceGroup.getParentName());
        
        return result;
    }

    private MetaDataComponent queryApplicationHierarchy(String serviceKey) {
        List<MetaDataComponent> appResults = queryMetadataWithConditions(ServiceLevelEnum.APPLICATION, Map.of(MetaDataComponent.FIELD_KEY, serviceKey),
                Arrays.asList(MetaDataComponent.FIELD_KEY, MetaDataComponent.FIELD_NAME, MetaDataComponent.FIELD_PARENT_KEY, MetaDataComponent.FIELD_PARENT_NAME));
        
        if (appResults.isEmpty()) {
            log.warn("No application found for service key: {}", serviceKey);
            return null;
        }
        
        MetaDataComponent application = appResults.get(0);
        String applicationName = application.getName();
        String parentKey = application.getParentKey();
        String serviceGroupName = application.getParentName();
        
        MetaDataComponent result = new MetaDataComponent();
        result.setApplicationKey(application.getKey());
        result.setApplicationName(applicationName);
        result.setServiceGroupKey(parentKey);
        result.setServiceGroupName(serviceGroupName);
        
        if (StringUtils.isBlank(parentKey)) {
            log.warn("Application {} has no parent key", serviceKey);
            result.setProductKey(null);
            result.setProductName(null);
            return result;
        }

        List<MetaDataComponent> serviceGroupResults = queryMetadataWithConditions(ServiceLevelEnum.SERVICE_GROUP, Map.of(MetaDataComponent.FIELD_KEY, parentKey),
                Arrays.asList(MetaDataComponent.FIELD_KEY, MetaDataComponent.FIELD_NAME, MetaDataComponent.FIELD_PARENT_KEY, MetaDataComponent.FIELD_PARENT_NAME));
        
        if (serviceGroupResults.isEmpty()) {
            log.warn("No service group found for key: {}", parentKey);
            result.setProductKey(null);
            result.setProductName(null);
            return result;
        }
        
        MetaDataComponent serviceGroup = serviceGroupResults.get(0);
        setProductInfo(result, serviceGroup.getParentKey(), serviceGroup.getParentName());
        
        return result;
    }

    private MetadataQueryRequest.Condition createFieldCondition(String field, String value, String operator) {
        MetadataQueryRequest.Condition condition = new MetadataQueryRequest.Condition();
        condition.setField(field);
        condition.setOperateType(operator);
        condition.setValue(value);
        return condition;
    }

    // If parentName is empty, try to query Product by parentKey
    private void setProductInfo(MetaDataComponent result, String parentKey, String parentName) {
        log.info("productName empty, query Product by parentKey: {}", parentKey);
        if (StringUtils.isBlank(parentName) && StringUtils.isNotBlank(parentKey)) {
            List<MetaDataComponent> productResults = queryMetadataWithConditions(ServiceLevelEnum.PRODUCT,
                    Map.of(MetaDataComponent.FIELD_KEY, parentKey), Arrays.asList(MetaDataComponent.FIELD_KEY, MetaDataComponent.FIELD_NAME));

            if (!productResults.isEmpty()) {
                MetaDataComponent product = productResults.get(0);
                result.setProductKey(product.getKey());
                result.setProductName(product.getName());
            } else {
                result.setProductKey(parentKey);
                result.setProductName(null);
            }
        } else {
            result.setProductKey(parentKey);
            result.setProductName(parentName);
        }
    }

}
