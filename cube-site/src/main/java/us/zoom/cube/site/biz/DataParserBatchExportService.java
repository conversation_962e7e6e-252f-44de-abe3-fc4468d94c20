package us.zoom.cube.site.biz;

import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import com.zoom.op.monitor.dao.alarm.AlarmDefinitionDao;
import com.zoom.op.monitor.domain.alarm.AlarmDefinition;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import us.zoom.cube.site.biz.ai.AiCheckService;
import us.zoom.cube.site.biz.alarm.AlarmDefinitionServiceImpl;
import us.zoom.cube.site.biz.syspara.AlarmParaService;
import us.zoom.cube.site.infra.utils.JacksonUtils;
import us.zoom.infra.dao.model.MetricsDO;
import us.zoom.infra.dao.service.MetricsDAO;
import us.zoom.infra.utils.CommonSplitConstants;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static us.zoom.cube.site.infra.constants.BiportConstant.*;

/**
 * @description:
 * @author: <EMAIL>
 * @date: 2024-07-16 17:55
 **/
@Service
@Slf4j
@RequiredArgsConstructor
public class DataParserBatchExportService extends DataParserExportService {
    private final AlarmDefinitionDao alarmDefinitionDao;

    private final MetricsDAO metricsDAO;

    private final AlarmParaService alarmParaService;

    private final AlarmDefinitionServiceImpl alarmDefinitionService;

    public static final String ALARM_EXPORT_COUNT = "cube.site.alaram.export.limit";
    public final static Long DEFAULT_ALARM_EXPORT_LIMIT = 30L;

    public Long getBatchLimit() {
        return alarmParaService.getParamLongValue(ALARM_EXPORT_COUNT, DEFAULT_ALARM_EXPORT_LIMIT);
    }

    @SneakyThrows
    public String exportAlarmWithAll(String expectedServiceId, List<String> alarmList) {
        Assert.isTrue(!CollectionUtils.isEmpty(alarmList), "Alarm List  can't be null when exporting Alarm");
        Long limit = getBatchLimit();
        Assert.isTrue(alarmList.size() <= limit, "A maximum of " + limit + " records can be batch operated");
        List<AlarmDefinition> alarmDefinitionList = alarmDefinitionDao.findAllById(alarmList);
        Assert.isTrue(!CollectionUtils.isEmpty(alarmDefinitionList), String.format("Alarm does not exist[%s], please check it", StringUtils.join(alarmList, CommonSplitConstants.COMMA_SPLIT)));
        List<String> alarmDefinitionIdList = alarmDefinitionList.stream().map(e -> e.getId()).collect(Collectors.toList());
        Collection subtract = CollectionUtils.subtract(alarmList, alarmDefinitionIdList);
        Assert.isTrue(CollectionUtils.isEmpty(subtract), String.format("Alarm does not exist[%s], please check it", StringUtils.join(subtract, CommonSplitConstants.COMMA_SPLIT)));
        Map<String, String> alarmToMetricsToMap = alarmDefinitionList.stream().collect(Collectors.toMap(AlarmDefinition::getId, AlarmDefinition::getMetricId));
        List<MetricsDO> metricsDOList = metricsDAO.listMetricsByMetricsIds(alarmToMetricsToMap.values().stream().distinct().toList());
        Assert.isTrue(!CollectionUtils.isEmpty(metricsDOList), String.format("Can't find the metrics by alarm id:[%s]", StringUtils.join(alarmList, CommonSplitConstants.COMMA_SPLIT)));

        List<String> merticIdList = metricsDOList.stream().filter(f -> !StringUtils.equals(expectedServiceId, f.getTenantId())).map(e -> e.getId()).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(merticIdList)) {
            List<String> notExistMetricsList = alarmToMetricsToMap.entrySet().stream()
                    .filter(entry -> merticIdList.contains(entry.getValue()))
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());
            Assert.isTrue(false, String.format("Can't find the metrics by alarm id:[%s]", StringUtils.join(notExistMetricsList, CommonSplitConstants.COMMA_SPLIT)));
        }

        alarmDefinitionList.forEach(alarmDefintion -> alarmDefinitionService.supplyAlarmDefinitionOutputInfo(alarmDefintion, true));

        //alarmDefinitionList=aiCheckService.checkAndReplaceAiAlarm(alarmDefinitionList);
        ObjectNode jsonNode = JacksonUtils.getObjectMapper().createObjectNode();
        ArrayNode alarmsArrayNodes = JacksonUtils.getObjectMapper().valueToTree(alarmDefinitionList);

        Map<String, String> metricsMap = metricsDOList.stream().collect(Collectors.toMap(MetricsDO::getId, MetricsDO::getMetricsName));
        alarmsArrayNodes.forEach(alarmNode -> {
            //add related info
            String alarmId = alarmNode.get(KEY_METRIC_ID).textValue();
            ((ObjectNode) alarmNode).put(RELATED_DATA_METRICS_NAME, metricsMap.get(alarmId));
        });
        jsonNode.putPOJO(KEY_ALARMS, alarmsArrayNodes);
        return super.filterUselessField(jsonNode);
    }


    @SneakyThrows
    @Override
    protected String filterUselessField(ObjectNode parserObjectNode) {
        String json = JacksonUtils.getObjectMapper().writeValueAsString(parserObjectNode);
        DocumentContext context = JsonPath.using(conf).parse(json);
        //all
        context.delete("$..id");
        context.delete("$..tenantId");
        context.delete("$..gmtCreate");
        context.delete("$..gmtModify");
        context.delete("$..createTime");
        context.delete("$..modifyTime");
        context.delete("$..collectorId");
        context.delete("$..derivedMetric.metricId");
        context.delete("$..derivedMetric.units[*].derivedMetricId");

        //alarm
        context.delete("$.insertAlarms[*]..userId");
        context.delete("$.insertAlarms[*].subAlarmId");
        context.delete("$.insertAlarms[*].metricId");
        context.delete("$.insertAlarms[*].notifications[*].channel.parameters");
        context.delete("$.insertAlarms[*].alarmExtensionRelations[*].relationId");

        //alarm
        context.delete("$.updateAlarms[*]..userId");
        context.delete("$.updateAlarms[*].subAlarmId");
        context.delete("$.updateAlarms[*].metricId");
        context.delete("$.updateAlarms[*].notifications[*].channel.parameters");
        context.delete("$.updateAlarms[*].alarmExtensionRelations[*].relationId");

        return context.jsonString();
    }
}
