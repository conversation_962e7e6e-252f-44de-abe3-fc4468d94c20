package us.zoom.cube.site.biz;

import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.zoom.op.monitor.domain.alarm.AlarmDefinition;
import com.zoom.op.monitor.domain.alarm.AlarmRule;
import com.zoom.op.monitor.domain.alarm.ConditionType;
import com.zoom.op.monitor.domain.alarm.Notification;
import com.zoom.op.monitor.domain.alarm.RuleCondition;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import us.zoom.cube.lib.config.hub.enums.FieldTypeEnum;
import us.zoom.cube.lib.config.hub.enums.SplitFieldTypeEnum;
import us.zoom.cube.lib.config.hub.processor.ExpressionEntryCfg;
import us.zoom.cube.lib.config.hub.processor.ExpressionSchema;
import us.zoom.cube.lib.config.hub.processor.SplitEntryCfg;
import us.zoom.cube.lib.config.hub.processor.SplitSchema;
import us.zoom.cube.lib.hub.DataParserRawDataTypeEnum;
import us.zoom.cube.lib.hub.ProcessorTypeEnum;
import us.zoom.cube.lib.utils.expression.CustomAviatorUtils;
import us.zoom.cube.site.biz.syspara.ServiceMonitorParaService;
import us.zoom.cube.site.core.AlarmDefinitionHandler;
import us.zoom.cube.site.infra.enums.WebCodeEnum;
import us.zoom.cube.site.infra.utils.JacksonUtils;
import us.zoom.cube.site.lib.MetricDefMetricsExportInfo;
import us.zoom.cube.site.lib.PipelineMetricExportInfo;
import us.zoom.cube.site.lib.SiteException;
import us.zoom.cube.site.lib.input.dataparser.DataField;
import us.zoom.cube.site.lib.output.dataparser.BaseProcessorOut;
import us.zoom.cube.site.lib.output.dataparser.DataParserPipelineOut;
import us.zoom.cube.site.lib.output.dataparser.DataParserV2Out;
import us.zoom.cube.site.lib.output.dataparser.ExpressionProcessorOut;
import us.zoom.cube.site.lib.output.dataparser.SplitProcessorOut;
import us.zoom.infra.dao.model.DataTypeDO;
import us.zoom.infra.dao.model.MetadataArrayItemDO;
import us.zoom.infra.dao.model.MetadataDO;
import us.zoom.infra.dao.model.MetadataItemDO;
import us.zoom.infra.dao.model.MetricDefinitionDO;
import us.zoom.infra.dao.model.MetricFieldDO;
import us.zoom.infra.dao.model.MetricTagDO;
import us.zoom.infra.dao.service.DataTypeDAO;
import us.zoom.infra.dao.service.MetaDataArrayItemDAO;
import us.zoom.infra.dao.service.MetaDataItemDAO;
import us.zoom.infra.dao.service.MetadataDAO;
import us.zoom.infra.dao.service.MetricDefinitionDAO;
import us.zoom.infra.dao.service.MetricFieldDAO;
import us.zoom.infra.dao.service.MetricTagDAO;
import us.zoom.infra.enums.PipelineUseStatusEnum;
import us.zoom.infra.utils.RegexConstants;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.stream.Collectors;

import static us.zoom.cube.site.infra.constants.BiportConstant.*;

/**
 * @author: Starls Ding
 * @date: 2022/12/20 13:23
 * @desc:
 */
@Slf4j
@Service
public class ServiceMonitorExportService {


    @Autowired
    private DataTypeDAO dataTypeDAO;
    @Autowired
    private MetricDefinitionDAO definitionDao;
    @Autowired
    private MetricFieldDAO metricFieldDAO;
    @Autowired
    private MetricTagDAO metricTagDAO;
    @Autowired
    private MetadataDAO metadataDAO;
    @Autowired
    private DataParserExportService dataParserExportService;
    @Autowired
    private ServiceMonitorParaService serviceMonitorParaService;
    @Autowired
    private MetaDataItemDAO metaDataItemDAO;
    @Autowired
    private MetaDataArrayItemDAO metadataArrayItemDao;
    @Autowired
    private AlarmDefinitionHandler alarmDefinitionHandler;
    @Autowired
    private ServiceMonitorMetricDefExport monitorMetricDefExport;

    public static Set<String> CUBE_FIELD_NUMBER_TYPES =  Sets.newHashSet("Short","Int","Long","Float","Double");
    public static Set<String> CUBE_FIELD_BOOLEAN_TYPES =  Sets.newHashSet("Boolean","String");

    public static String CUBE_FIELD_ARRAY_TYPE =  "Array";

    public static String FIELD_PREFIX="metrics.";



    public String exportDataParserWithAll(String serviceName) throws Exception {
        final ConcurrentHashMap<String, String> pipelineNameMap = new ConcurrentHashMap<>();
        final ConcurrentHashMap<String, Map<String, PipelineMetricExportInfo>> pipelineFieldMap = new ConcurrentHashMap<>();

        ObjectNode jsonNode = JacksonUtils.getObjectMapper().createObjectNode();
        if (StringUtils.isBlank(serviceName)) {
            throw new SiteException(WebCodeEnum.ServiceMonitorServiceNameEmpty);
        }

        long totalStart = System.currentTimeMillis();

        // DataParser
        long startDataParser = System.currentTimeMillis();
        ObjectNode dataParserNode = exportDataParser(serviceName);
        jsonNode.putPOJO(KEY_DATA_PARSER, dataParserNode);
        long endDataParser = System.currentTimeMillis();
        log.info("exportDataParser: {} s", String.format("%.3f", (endDataParser - startDataParser) / 1000.0));


        // Pipeline
        long startPipeline = System.currentTimeMillis();
        ArrayNode pipelineArrayNodes = exportPipeline(serviceName, pipelineNameMap, pipelineFieldMap);
        jsonNode.putPOJO(KEY_PIPELINE, pipelineArrayNodes);
        long endPipeline = System.currentTimeMillis();
        log.info("exportPipeline:   {} s", String.format("%.3f", (endPipeline - startPipeline) / 1000.0));


        // Metrics
        long startMetrics = System.currentTimeMillis();
        MetricDefMetricsExportInfo metricsExportInfo = exportMetrics(serviceName, pipelineNameMap, pipelineFieldMap);
        ArrayNode metricsArrayNodes = metricsExportInfo.getMetricsArrayNodes();
        jsonNode.putPOJO(KEY_METRICS, metricsArrayNodes);
        long endMetrics = System.currentTimeMillis();
        log.info("exportMetrics:    {} s", String.format("%.3f", (endMetrics - startMetrics) / 1000.0));


        // Alarms
        long startAlarms = System.currentTimeMillis();
        ArrayNode alarmsArrayNodes = exportAlarms(metricsExportInfo);
        jsonNode.putPOJO(KEY_ALARMS, alarmsArrayNodes);
        long endAlarms = System.currentTimeMillis();
        log.info("exportAlarms:     {} s", String.format("%.3f", (endAlarms - startAlarms) / 1000.0));


        long totalEnd = System.currentTimeMillis();
        log.info("Total:            {} s", String.format("%.3f", (totalEnd - totalStart) / 1000.0));

        return dataParserExportService.filterUselessField(jsonNode);
    }

    private ArrayNode exportAlarms(MetricDefMetricsExportInfo metricsExportInfo) {
        Map<String, String> metricIdAndNewName = metricsExportInfo.getMetricIdAndNewName();
        Map<String, Map<String, String>> metricTagMapping = metricsExportInfo.getMetricTagMapping();
        Map<String, Map<String, String>> metricFieldMapping = metricsExportInfo.getMetricFieldMapping();

        ArrayNode alarmsArray = JacksonUtils.getObjectMapper().createArrayNode();

        metricIdAndNewName.forEach((metricId, displayName) -> {
            Map<String, String> tagMap = metricTagMapping.getOrDefault(metricId, Collections.emptyMap());
            Map<String, String> fieldMap = metricFieldMapping.getOrDefault(metricId, Collections.emptyMap());

            Map<String, String> renameMap = new HashMap<>(tagMap);
            renameMap.putAll(fieldMap);

            List<AlarmDefinition> definitions = Optional.ofNullable(alarmDefinitionHandler.findByMetricId(metricId)).orElse(Collections.emptyList());

            definitions.forEach(def -> {
                if (def.getNotifications() != null) {
                    def.getNotifications().forEach(notification -> renameNotificationContent(notification, renameMap));
                }
                if (def.getRules() != null) {
                    def.getRules().forEach(rule -> renameAlarmRule(rule, tagMap, fieldMap, metricId));
                }
            });

            if (CollectionUtils.isNotEmpty(definitions)) {
                ArrayNode nodeArray = JacksonUtils.getObjectMapper().valueToTree(definitions);
                nodeArray.forEach(node -> {
                    if (node instanceof ObjectNode) {
                        ((ObjectNode) node).put(RELATED_DATA_METRICS_NAME, displayName);
                    }
                });
                alarmsArray.addAll(nodeArray);
            }
        });

        return alarmsArray;
    }

    private void renameNotificationContent(Notification notification, Map<String, String> renameMap) {
        String content = notification.getContent();
        if (StringUtils.isBlank(content) || !content.contains("${")) {
            return;
        }

        Matcher matcher = RegexConstants.PLACEHOLDER_EXPRESSION_PATTERN.matcher(content);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            String placeholder = matcher.group(1);
            String renamed = renameMap.get(placeholder);
            if (renamed != null) {
                matcher.appendReplacement(sb, Matcher.quoteReplacement("${" + renamed + "}"));
            } else {
                matcher.appendReplacement(sb, Matcher.quoteReplacement(matcher.group(0)));
            }
        }
        matcher.appendTail(sb);
        notification.setContent(sb.toString());
    }

    private void renameAlarmRule(AlarmRule rule, Map<String, String> tagMap, Map<String, String> fieldMap, String metricId) {
        List<RuleCondition> updated = Optional.ofNullable(rule.getConditions())
                .orElse(Collections.emptyList())
                .stream()
                .map(cond -> renameCondition(cond, tagMap, fieldMap, metricId))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        rule.setConditions(updated);
    }

    private RuleCondition renameCondition(RuleCondition cond, Map<String, String> tagMap, Map<String, String> fieldMap, String metricId) {
        String name = cond.getName();
        if (ConditionType.TAG.equals(cond.getConditionType())) {
            String newName = tagMap.get(name);
            if (tagMap.containsKey(name)) {
                cond.setName(newName);
            } else {
                log.warn("Tag no mapping, metricsId={}, oldTagName={}, newTagName={}", metricId, name, newName);
                return null;
            }
        } else if (ConditionType.FIELD.equals(cond.getConditionType())) {
            String newName = fieldMap.get(name);
            if (fieldMap.containsKey(name)) {
                cond.setName(newName);
            } else {
                log.warn("Field no mapping, metricsId={}, oldFieldName={}, newFieldName={}", metricId, name, newName);
            }
        } else if (ConditionType.EXPRESSION.equals(cond.getConditionType()) && StringUtils.isNotBlank(cond.getExpression())) {
            String expr = cond.getExpression();
            List<String> variableNames = CustomAviatorUtils.compile(expr, false).getVariableFullNames();
            for (String var : variableNames) {
                String newVar = fieldMap.get(var);
                if (newVar == null) {
                    newVar = tagMap.get(var);
                }
                if (newVar != null) {
                    expr = expr.replace(var, newVar);
                }
            }
            cond.setExpression(expr);
        }

        return cond;
    }

    /**
     *
     * @param pipelineNameMap
     * @param pipelineFieldMap
     * @return
     */
    private MetricDefMetricsExportInfo exportMetrics(String serviceName, Map<String, String> pipelineNameMap, Map<String, Map<String, PipelineMetricExportInfo>> pipelineFieldMap) throws Exception {
        return monitorMetricDefExport.exportMetrics(serviceName, pipelineNameMap, pipelineFieldMap);
    }

    private ArrayNode exportPipeline(String serviceName,Map<String, String> pipelineNameMap , Map<String, Map<String, PipelineMetricExportInfo>> pipelineFieldMap) {
        if(StringUtils.isBlank(serviceName)){
            return JacksonUtils.getObjectMapper().createArrayNode();
        }

        //Query  data_type according to topic
        List<DataTypeDO> dataTypeDOS = dataTypeDAO.findIdsByTopic(serviceName);
        if (CollectionUtils.isEmpty(dataTypeDOS)) {
            throw new SiteException(WebCodeEnum.ServiceMonitorDataTypeNotExist);
        }
        Map<Long, DataTypeDO> dataTypeDOMap = dataTypeDOS.stream().collect(Collectors.toMap(DataTypeDO::getId, Function.identity()));
        List<Long> dataTypeIds = dataTypeDOS.stream().map(DataTypeDO::getId).distinct().collect(Collectors.toList());
        //Query  metadata according to data_type_ids
        List<MetadataDO> metadataDOS = metadataDAO.findByDataTypeIds(dataTypeIds).stream().filter(metadataDO -> metadataDO.getEnable().equals(true)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(metadataDOS)) {
            throw new SiteException(WebCodeEnum.ServiceMonitorMetaDataNotExist);
        }
        List<Long> metadataIds = metadataDOS.stream().map(MetadataDO::getId).distinct().collect(Collectors.toList());
        List<MetadataItemDO> metadataItemDOS = metaDataItemDAO.findByItemIds(metadataIds);
        List<Long> metadataItemIds = metadataItemDOS.stream().map(MetadataItemDO::getId).distinct().collect(Collectors.toList());
        Map<Long, List<MetadataItemDO>> metadataItemMap = metadataItemDOS.stream()
                .collect(Collectors.groupingBy(MetadataItemDO::getMetadataId));

        List<MetadataArrayItemDO> metadataArrayItemDOS = metadataArrayItemDao.findByItemIds(metadataItemIds);
        Map<Long, List<MetadataArrayItemDO>> metadataArrayItemNameMap = metadataArrayItemDOS.stream()
                .collect(Collectors.groupingBy(MetadataArrayItemDO::getMetadataItemId));
        List<MetricDefinitionDO> metricDefinitions = definitionDao.findByDataTypeId(dataTypeIds);
        List<Long> definitionIds = metricDefinitions.stream().map(MetricDefinitionDO::getId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(definitionIds)){
            throw new SiteException(WebCodeEnum.ServiceMonitorMetricDefinitionNotExist);
        }
        List<MetricFieldDO> metricFieldDOS = metricFieldDAO.findByDefinitionIds(definitionIds);
        List<MetricTagDO> metricTagDOS = metricTagDAO.findByDefinitionIds(definitionIds);
        Map<Long, List<MetricFieldDO>> fieldMap = metricFieldDOS.stream()
                .collect(Collectors.groupingBy(MetricFieldDO::getMetricDefinitionId));
        Map<Long, List<MetricTagDO>> tagMap = metricTagDOS.stream()
                .collect(Collectors.groupingBy(MetricTagDO::getMetricDefinitionId));

        Map<Long, List<MetricDefinitionDO>> metricDefinitionMap = metricDefinitions.stream().collect(Collectors.groupingBy(MetricDefinitionDO::getDataTypeId));

        List<DataParserPipelineOut> pipelines = Lists.newArrayList();
        //Map<oldFieldName, Field{name,type}
        Map<String, PipelineMetricExportInfo> metricExportInfoMap = Maps.newHashMap();
        //2. handle metadata
        for (int i = 0; i < metadataDOS.size(); i++){
            MetadataDO metadata = metadataDOS.get(i);
            List<DataField> fields = Lists.newArrayList();
            metadata.setDataTypeDO(dataTypeDOMap.get(metadata.getDataTypeId()));
            metadata.setMetadataItems(metadataItemMap.get(metadata.getId()));
            //1. pipeline ：When pipeline name contains special characters, the created pipeline name is automatically replaced with underscores.
            DataParserPipelineOut pipeline = new DataParserPipelineOut();
            pipeline.setName(serviceName+"_"+metadata.getDataTypeDO().getEventType().replaceAll("[^a-zA-Z0-9_]", "_"));
            pipeline.setUseStatus(PipelineUseStatusEnum.use.getValue());
            pipeline.setOrder(i);
            pipeline.setFilterRule("dataType==\'" + metadata.getDataTypeDO().getEventType() + "\'");
            List<BaseProcessorOut> processores = Lists.newArrayList();
            //2.1 Split processor
            SplitProcessorOut splitProcessorOut = buildSplitProcessor(metadata, fields, metadataArrayItemNameMap, metricExportInfoMap);
            if (splitProcessorOut.getSchema() != null && StringUtils.isNotEmpty(splitProcessorOut.getSchema().getSeparator()) && CollectionUtils.isNotEmpty(splitProcessorOut.getSchema().getEntries())) {
                processores.add(splitProcessorOut);
            }
            //2.2 add fields

            //3. handle metric_def
            Long dataTypeId = metadata.getDataTypeId();
            List<MetricDefinitionDO> metricDefinitionDOS = metricDefinitionMap.get(dataTypeId);
            List<ExpressionEntryCfg> entries = Lists.newArrayList();
            if(CollectionUtils.isNotEmpty(metricDefinitionDOS)){
                for (MetricDefinitionDO metricDefinition : metricDefinitionDOS) {
                    List<MetricTagDO> metricTags = tagMap.get(metricDefinition.getId());
                    List<MetricFieldDO> metricFields = fieldMap.get(metricDefinition.getId());
                    Map<String, MetricFieldDO> metricFieldMap = metricFields.stream()
                            .collect(Collectors.toMap(
                                    MetricFieldDO::getName,
                                    Function.identity(),
                                    (existing, duplicate) -> existing
                            ));

                    if(CollectionUtils.isNotEmpty(metricFields)){
                        for (MetricFieldDO metricField : metricFields) {
                            //If the value is not directly taken from metrics, it is treated as an expression
                            if (!(FIELD_PREFIX + metricField.getName()).equals(metricField.getExpression())) {
                                //field with expression
                                metricExportInfoMap.put(metricField.getName(),PipelineMetricExportInfo.builder().fieldName(converterFieldName(metricField.getName())).fieldType(metricField.getMetricsFieldType().name()).build());
                                entries.add(ExpressionEntryCfg.builder().name(converterFieldName(metricField.getName())).expression(replaceWithSuffix(metricField.getExpression())).type(FieldTypeEnum.fromValue(metricField.getMetricsFieldType().getValue())).build());
                                fields.add(DataField.builder().fieldName(converterFieldName(metricField.getName())).fieldType(metricField.getMetricsFieldType().name()).hasPii(0).fieldSchema(StringUtils.EMPTY).build());
                            }else {
                                //normal field
                                metricExportInfoMap.put(metricField.getName(),PipelineMetricExportInfo.builder().fieldName(converterFieldName(metricField.getName())).fieldType(metricField.getMetricsFieldType().name()).build());
                                entries.add(ExpressionEntryCfg.builder().name(converterFieldName(metricField.getName())).expression(metricField.getName()).type(FieldTypeEnum.fromValue(metricField.getMetricsFieldType().getValue())).build());
                                fields.add(DataField.builder().fieldName(converterFieldName(metricField.getName())).fieldType(metricField.getMetricsFieldType().name()).hasPii(0).fieldSchema(StringUtils.EMPTY).build());
                            }
                        }
                    }
                    if(CollectionUtils.isNotEmpty(metricTags)){
                        for (MetricTagDO metricTag : metricTags) {
                            //If the value is not directly taken from metrics, it is treated as an expression &&  name not repeat from field
                            if (!(FIELD_PREFIX + metricTag.getName()).equals(metricTag.getExpression()) && Objects.isNull(metricFieldMap.get(metricTag.getName()))) {
                                metricExportInfoMap.put(metricTag.getName(),PipelineMetricExportInfo.builder().fieldName(converterFieldName(metricTag.getName())).fieldType(FieldTypeEnum.string.name()).build());
                                entries.add(ExpressionEntryCfg.builder().name(converterFieldName(metricTag.getName())).expression(replaceWithSuffix(metricTag.getExpression())).type(FieldTypeEnum.string).build());
                                fields.add(DataField.builder().fieldName(converterFieldName(metricTag.getName())).fieldType(FieldTypeEnum.string.name()).hasPii(0).fieldSchema(StringUtils.EMPTY).build());
                            }
                        }
                    }
                }
            }

            Map<String, DataField> dataFieldMap = fields.stream().filter(f -> f.getFieldName() != null).collect(Collectors.toMap(DataField::getFieldName, Function.identity(), (f1, f2) -> f1));

            // First construct a collection of all fieldName + type in dataField for matching
            Set<String> validFieldTypeKeys = dataFieldMap.entrySet().stream()
                    .map(entry -> entry.getKey() + "#" + entry.getValue().getFieldType()) // fieldName#type
                    .collect(Collectors.toSet());

            // De-duplicate entries: Use Set to determine the uniqueness of name + type, and keep those combinations that exist in dataFieldMap
            List<ExpressionEntryCfg> filteredEntries = entries.stream()
                    .filter(e -> e.getName() != null && e.getType() != null)
                    .filter(e -> validFieldTypeKeys.contains(e.getName() + "#" + e.getType()))
                    .collect(Collectors.collectingAndThen(
                            Collectors.toMap(
                                    e -> e.getName() + "#" + e.getType(), // key is used for deduplication
                                    Function.identity(),
                                    (e1, e2) -> e1 // In case of conflict, keep the first
                            ),
                            map -> new ArrayList<>(map.values())
                    ));
            // metadata-> processor : 1:1
            if(CollectionUtils.isNotEmpty(filteredEntries)){
                ExpressionProcessorOut expressionProcessor = new ExpressionProcessorOut();
                expressionProcessor.setSchema(ExpressionSchema.builder().entries(filteredEntries).build());
                expressionProcessor.setName(ProcessorTypeEnum.ExpressionProcessorCfg.getShortCode());
                expressionProcessor.setType(ProcessorTypeEnum.ExpressionProcessorCfg.getShortCode());
                expressionProcessor.setOrder(1);
                processores.add(expressionProcessor);
            }
            fields = new ArrayList<>(dataFieldMap.values());
            if(CollectionUtils.isNotEmpty(fields)){
                pipelineNameMap.put(metadata.getDataTypeDO().getEventType(),pipeline.getName());
                pipeline.setFields(fields);
                pipeline.setProcessores(processores);
                pipelines.add(pipeline);
                pipelineFieldMap.put(pipeline.getName(),metricExportInfoMap);
            }
        }

        return JacksonUtils.getObjectMapper().valueToTree(pipelines);
    }

    private static String converterFieldName(String  name) {
        return name.replaceAll("[^a-zA-Z0-9_]", "_").toLowerCase();
    }


    public static String replaceWithSuffix(String input) {
        String[] markers = {"@Cache@", "@Redis@"};
        for (String marker : markers) {
            int index = input.indexOf(marker);
            if (index != -1) {
                String suffix = input.substring(index + marker.length());
                return suffix.replace("'", "");
            }
        }
        // If there is no matching marker, return the original string
        return input.replace("metrics.", "");
    }

    @NotNull
    private static SplitProcessorOut buildSplitProcessor(MetadataDO metadata,List<DataField> fields,Map<Long, List<MetadataArrayItemDO>> metadataArrayItemNameMap,Map<String, PipelineMetricExportInfo> metricExportInfoMap) {
        SplitProcessorOut splitProcessor = new SplitProcessorOut();
        splitProcessor.setOrder(0);
        splitProcessor.setName(ProcessorTypeEnum.SplitProcessorCfg.getShortCode());
        splitProcessor.setType(ProcessorTypeEnum.SplitProcessorCfg.getShortCode());
        splitProcessor.setSourceField("message");
        SplitSchema splitSchema = new SplitSchema();
        splitSchema.setSeparator(metadata.getSeparator());
        if(CollectionUtils.isEmpty(metadata.getMetadataItems())){
            return splitProcessor;
        }
        splitProcessor.setSchema(buildSchema(metadata.getMetadataItems(),splitSchema,fields,metadataArrayItemNameMap,metricExportInfoMap));
        return splitProcessor;
    }

    private static SplitSchema buildSchema(List<MetadataItemDO> metadataItems, SplitSchema splitSchema, List<DataField> fields, Map<Long, List<MetadataArrayItemDO>> metadataArrayItemNameMap,Map<String, PipelineMetricExportInfo> metricExportInfoMap) {
        List<SplitEntryCfg> entries = Lists.newArrayList();
        for(MetadataItemDO item : metadataItems){
            SplitEntryCfg splitEntryCfg = SplitEntryCfg.builder().index(item.getIndex()).name(item.getName()).build();
            DataField field = new DataField();
            if(CUBE_FIELD_NUMBER_TYPES.contains(item.getType())){
                splitEntryCfg.setType(SplitFieldTypeEnum.number);
                field.setFieldType(FieldTypeEnum.number.name());
                metricExportInfoMap.put(item.getName(),PipelineMetricExportInfo.builder().fieldName(converterFieldName(item.getName())).fieldType(field.getFieldType()).build());
                field.setFieldName(converterFieldName(item.getName()));
                field.setHasPii(0);
                field.setFieldSchema(StringUtils.EMPTY);
//                fields.add(field);
                entries.add(splitEntryCfg);
            }else if(CUBE_FIELD_BOOLEAN_TYPES.contains(item.getType())){
                splitEntryCfg.setType(SplitFieldTypeEnum.string);
                field.setFieldType(FieldTypeEnum.string.name());
                metricExportInfoMap.put(item.getName(),PipelineMetricExportInfo.builder().fieldName(converterFieldName(item.getName())).fieldType(field.getFieldType()).build());
                field.setFieldName(converterFieldName(item.getName()));
                field.setHasPii(0);
                field.setFieldSchema(StringUtils.EMPTY);
//                fields.add(field);
                entries.add(splitEntryCfg);
            }else if(CUBE_FIELD_ARRAY_TYPE.equals(item.getType())){
                List<MetadataArrayItemDO> arrayItemNames = metadataArrayItemNameMap.get(item.getId());
                if(CollectionUtils.isNotEmpty(arrayItemNames)){
                    for(MetadataArrayItemDO arrayItem: arrayItemNames){
                        DataField dataField = new DataField();
                        if(CUBE_FIELD_NUMBER_TYPES.contains(arrayItem.getType())){
                            splitEntryCfg.setType(SplitFieldTypeEnum.number);
                            dataField.setFieldType(FieldTypeEnum.number.name());
                            metricExportInfoMap.put(arrayItem.getName(),PipelineMetricExportInfo.builder().fieldName(converterFieldName(arrayItem.getName())).fieldType(dataField.getFieldType()).build());
                            dataField.setFieldName(converterFieldName(arrayItem.getName()));
                            dataField.setHasPii(0);
                            dataField.setFieldSchema(StringUtils.EMPTY);
//                            fields.add(dataField);
                            entries.add(splitEntryCfg);
                        }else if(CUBE_FIELD_BOOLEAN_TYPES.contains(arrayItem.getType())){
                            splitEntryCfg.setType(SplitFieldTypeEnum.string);
                            dataField.setFieldType(FieldTypeEnum.string.name());
                            metricExportInfoMap.put(arrayItem.getName(),PipelineMetricExportInfo.builder().fieldName(converterFieldName(arrayItem.getName())).fieldType(dataField.getFieldType()).build());
                            dataField.setFieldName(converterFieldName(arrayItem.getName()));
                            dataField.setHasPii(0);
                            dataField.setFieldSchema(StringUtils.EMPTY);
//                            fields.add(dataField);
                            entries.add(splitEntryCfg);
                        }else{
                            //other type do not support
                        }
                    }
                }
            }else{
                // Section  List  do not support processor,but add fields
            }
        }

        //sort entries
        entries.sort(Comparator.comparingInt(SplitEntryCfg::getIndex));
        for (int i = 0; i < entries.size(); i++) {
            entries.get(i).setIndex(i);
        }
        splitSchema.setEntries(entries);
        return splitSchema;
    }


    private ObjectNode exportDataParser(String serviceName) {
        // Query groovy generic script
        String preProcessScript = serviceMonitorParaService.getServiceMonitorParam();
        //output
        DataParserV2Out dataParserV2Out = buildDataParserV2Out(serviceName,preProcessScript);
        return JacksonUtils.getObjectMapper().valueToTree(dataParserV2Out);
    }

    @NotNull
    private static DataParserV2Out buildDataParserV2Out(String serviceName,String preProcessScript) {
        DataParserV2Out dataParserV2Out = new DataParserV2Out();
        dataParserV2Out.setName(serviceName);
        dataParserV2Out.setRawDataParseRule(preProcessScript);
        dataParserV2Out.setRawDataType(DataParserRawDataTypeEnum.other.getCode());
        dataParserV2Out.setRawDataParseType("groovy");
        dataParserV2Out.setInvokeFunction("parseMessage");
        dataParserV2Out.setUseStatus(1);
        return dataParserV2Out;
    }



}
