package us.zoom.cube.site.lib.input.alarm.batch;

import com.zoom.op.monitor.domain.BaseEntity;
import com.zoom.op.monitor.domain.DerivedMetric;
import com.zoom.op.monitor.domain.TagInputDo;
import com.zoom.op.monitor.domain.alarm.AlarmConfigStatusEnum;
import com.zoom.op.monitor.domain.alarm.AlarmMatchModeEnum;
import com.zoom.op.monitor.domain.alarm.AlarmSourceEnum;
import com.zoom.op.monitor.domain.alarm.AlarmSourceTypeEnum;
import lombok.Data;
import us.zoom.cube.site.lib.output.tag.TagInfoOut;

import java.io.Serial;
import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: <EMAIL>
 * @date: 2024-07-31 14:21
 **/
@Data
public class AlarmDefinitionInput extends BaseEntity<String> {
    @Serial
    private static final long serialVersionUID = -4764532864274612092L;

    private String id;

    private String name;

    private String metricsName;

    private String metricId;

    private List<AlarmRuleInput> rules;

    private Integer timesInPeriod;

    private Integer periodInMinutes;

    private Boolean enabled = true;


    private List<NotificationInput> notifications;

    private List<AlarmExtensionRelationInput> alarmExtensionRelations = new ArrayList<>();


    private String description;

    private String subAlarmName;

    private String subAlarmId;

    private String creator;

    private String editor;

    private String levelsSendIncident;

    private Integer alarmMatchMode;

    private String groupTags;

    private AlarmConfigStatusEnum status = AlarmConfigStatusEnum.APPROVED;

    private AlarmSourceEnum source = AlarmSourceEnum.METRIC;

    private AlarmSourceTypeEnum sourceType = AlarmSourceTypeEnum.DEFAULT;

    private DerivedMetric derivedMetric;

    private List<TagInputDo> labelInfoList;

    public AlarmMatchModeEnum getAlarmMatchMode() {
        return (null == alarmMatchMode) ? AlarmMatchModeEnum.traversal : AlarmMatchModeEnum.getByMode(alarmMatchMode);
    }

}
