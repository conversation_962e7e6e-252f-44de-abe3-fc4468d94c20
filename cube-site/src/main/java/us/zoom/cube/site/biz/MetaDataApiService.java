package us.zoom.cube.site.biz;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.infra.dao.model.DistributedLockDO;
import us.zoom.infra.dao.service.DistributedLockDAO;
import us.zoom.infra.thread.ApiMonitorScheduler;
import us.zoom.infra.utils.*;
import us.zoom.cube.site.biz.syspara.AuthParaService;
import us.zoom.cube.site.core.MetaDataComponentHandler;
import us.zoom.cube.site.lib.metadata.MetaDataComponent;
import us.zoom.cube.site.lib.metadata.MetaDataComponentInput;
import us.zoom.cube.site.lib.metadata.MetaDataInput;
import us.zoom.infra.dao.model.MetaDataComponentDO;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * Author: charles.hu
 * Date: 2024/12/10
 * Description:
 */
@Component
@Slf4j
public class MetaDataApiService {

    @Autowired
    private MetaDataComponentHandler metaDataComponentHandler;

    @Autowired
    private AuthParaService authParaService;

    @Autowired
    private DistributedLockDAO distributedLockDAO;

    private static final String AUTHENTICATION_BEARER = "Bearer ";
    private static final String META_DATA_LOCK_TYPE = "meta_data_lock_type";
    private static final String META_DATA_CACHE_KEY = "cube";
    private static final String META_DATA_AUDIENCE = "metadata";
    private static final String META_DATA_COMPONENT = "component";
    private static final int LOCK_TIMEOUT = 1;
    private static final int MAX_PAGE_LIMIT = 50;
    private static final int PAGE_SIZE = 500;

    @PostConstruct
    public void initializeMetaDataSync() {
        ApiMonitorScheduler.getInstance().getScheduler()
                .scheduleAtFixedRate(this::syncDataFromMetaData, 5, 360, TimeUnit.MINUTES);
    }

    public void syncDataFromMetaData() {
        try {
            List<DistributedLockDO> locks = distributedLockDAO.listByType(META_DATA_LOCK_TYPE);
            if (CollectionUtils.isEmpty(locks)){
                return;
            }
            DistributedLockDO lock = locks.get(0);
            int lockResult = distributedLockDAO.lockWithType(
                    META_DATA_LOCK_TYPE, lock.getHandler(), IpUtils.getLocalIP(), LOCK_TIMEOUT);
            if (lockResult > 0) fetchAndSaveMetaDataList();
        } catch (Exception e) {
            log.error("Error syncing metadata", e);
        }
    }

    private void fetchAndSaveMetaDataList() {
        long getFetchTime = System.currentTimeMillis();
        Map<String, String> headerParams = createHeaderParams();
        int pageNumber = 1;
        List<MetaDataComponentInput> allDataList = new ArrayList<>();
        boolean hasMoreData = true;
        while (hasMoreData && pageNumber < MAX_PAGE_LIMIT) {
            Map<String, Object> requestBody = createRequestBody(pageNumber, PAGE_SIZE);
            String response = invokeMetaDataApi(headerParams, requestBody);
            if (response == null) {
                log.error("Failed to fetch metadata for page {}", pageNumber);
                break;
            }
            MetaDataInput metaDataInput = JsonUtils.toObject(response, MetaDataInput.class);
            if (metaDataInput == null || metaDataInput.getData() == null) {
                log.warn("No data returned for page {}", pageNumber);
                break;
            }
            List<MetaDataComponentInput> newData = metaDataInput.getData().getList();
            if (CollectionUtils.isEmpty(newData)) break;
            allDataList.addAll(newData);
            hasMoreData = pageNumber < metaDataInput.getData().getTotalPage();
            pageNumber++;
        }
        if (pageNumber >= MAX_PAGE_LIMIT) {
            log.warn("Reached the maximum page limit of {}", MAX_PAGE_LIMIT);
        }
        log.info("fetch api {} components, cost time: {}", allDataList.size(), (System.currentTimeMillis() - getFetchTime));
        processBatchOperations(allDataList);
    }

    private void processBatchOperations(List<MetaDataComponentInput> allDataList) {
        long startTime = System.currentTimeMillis();
        if (CollectionUtils.isEmpty(allDataList)) return;
        long getExistTime = System.currentTimeMillis();
        Map<String, MetaDataComponentDO> existingData = metaDataComponentHandler.getAllMetaDataComponents().stream()
                .collect(Collectors.toMap(MetaDataComponentDO::getDataKey, Function.identity()));
        log.info("ExistingData {} components, cost time: {}", existingData.size(), (System.currentTimeMillis() - getExistTime));
        List<MetaDataComponentDO> newDOs = convertToDOList(allDataList);
        List<MetaDataComponentDO> toInsert = new ArrayList<>(), toUpdate = new ArrayList<>(), toDelete = new ArrayList<>();
        Set<String> newKeys = new HashSet<>();
        for (MetaDataComponentDO newDO : newDOs) {
            newKeys.add(newDO.getDataKey());
            MetaDataComponentDO existingDO = existingData.get(newDO.getDataKey());
            if (existingDO == null) {
                toInsert.add(newDO);
            } else if (!isDataEqual(newDO, existingDO)) {
                toUpdate.add(newDO);
            }
        }
        existingData.values().stream()
                .filter(existingDO -> !newKeys.contains(existingDO.getDataKey()))
                .forEach(toDelete::add);
        log.info("ProcessBatchOperations toInsert size {} , toUpdate size {} , toDelete size {}", toInsert.size(), toUpdate.size(), toDelete.size());
        if (!toInsert.isEmpty()) {
            long insertTime = System.currentTimeMillis();
            metaDataComponentHandler.batchAddComponent(toInsert);
            log.info("Inserted {} new components, cost time: {}", toInsert.size(), (System.currentTimeMillis() - insertTime));
        }
        if (!toUpdate.isEmpty()) {
            long updateTime = System.currentTimeMillis();
            metaDataComponentHandler.batchUpdateComponent(toUpdate);
            log.info("Updated {} existing components, cost time: {}", toUpdate.size(), (System.currentTimeMillis() - updateTime));
        }
        if (!toDelete.isEmpty()) {
            long deleteTime = System.currentTimeMillis();
            metaDataComponentHandler.batchDeleteComponent(toDelete);
            log.info("Deleted {} components, cost time: {}", toDelete.size(), (System.currentTimeMillis() - deleteTime));
        }
        long endTime = System.currentTimeMillis();
        log.info("Processed {} metadata components, cost time: {}", allDataList.size(), endTime-startTime);
    }

    public Map<String, String> createHeaderParams() {
        String token = JWTUtils.generateToken(META_DATA_CACHE_KEY, META_DATA_AUDIENCE, 3600L, null, null);
        return Collections.singletonMap("Authorization", AUTHENTICATION_BEARER + token);
    }

    private Map<String, Object> createRequestBody(int pageNumber, int pageSize) {
        Map<String, Object> body = new HashMap<>();
        body.put("typeName", META_DATA_COMPONENT);
        body.put("pageNumber", pageNumber);
        body.put("pageSize", pageSize);
        return body;
    }

    public String invokeMetaDataApi(Map<String, String> headers, Map<String, Object> body) {
        try {
            AtomicInteger status = new AtomicInteger();
            return HttpUtils.post(
                    Collections.singletonList(authParaService.getMetaDataUrl()),
                    Collections.emptyMap(), headers, JsonUtils.toJsonString(body), status, null, 1);
        } catch (Exception e) {
            log.error("Error invoking metadata API", e);
            return null;
        }
    }

    private List<MetaDataComponentDO> convertToDOList(List<MetaDataComponentInput> inputs) {
        return inputs.stream().map(this::convertToDO).collect(Collectors.toList());
    }

    private MetaDataComponentDO convertToDO(MetaDataComponentInput input) {
        MetaDataComponent attributes = input.getAttributes();
        MetaDataComponentDO metaDataDO = new MetaDataComponentDO();
        BeanUtils.copyProperties(attributes, metaDataDO);
        metaDataDO.setId(IdUtils.generateId());
        metaDataDO.setDataKey(attributes.getKey());
        metaDataDO.setOperationOwner(convertToCommaSeparated(attributes.getOperationOwner()));
        metaDataDO.setSecurityOwner(convertToCommaSeparated(attributes.getSecurityOwner()));
        metaDataDO.setSecurityChampion(convertToCommaSeparated(attributes.getSecurityChampion()));
        return metaDataDO;
    }

    private String convertToCommaSeparated(List<String> list) {
        return CollectionUtils.isEmpty(list) ? null : String.join(CommonSplitConstants.COMMA_SPLIT, list);
    }

    private boolean isDataEqual(MetaDataComponentDO newDO, MetaDataComponentDO existingDO) {
        return Objects.equals(newDO, existingDO);
    }
}

