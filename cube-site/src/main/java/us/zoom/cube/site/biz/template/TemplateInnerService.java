package us.zoom.cube.site.biz.template;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.zoom.op.monitor.dao.alarm.ChannelDao;
import com.zoom.op.monitor.domain.alarm.*;
import jakarta.annotation.PostConstruct;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.async.mq.openapi.client.api.OpenApi;
import us.zoom.async.mq.openapi.model.base.Result;
import us.zoom.async.mq.openapi.model.params.topic.TopicCreateParam;
import us.zoom.async.mq.openapi.model.params.topic.TopicPrivilegesParam;
import us.zoom.async.mq.openapi.model.result.topic.BatchTopicResult;
import us.zoom.async.mq.openapi.model.result.topic.TopicFailedResult;
import us.zoom.async.mq.openapi.model.result.topic.TopicPrivilegesResult;
import us.zoom.async.mq.openapi.model.result.topic.TopicResult;
import us.zoom.cube.lib.common.AsyncMqQueueSourceTypeEnum;
import us.zoom.cube.lib.common.AsyncMqQueueStatusEnum;
import us.zoom.cube.lib.common.QueuePurposeTypeEnum;
import us.zoom.cube.lib.common.SwitchEnum;
import us.zoom.cube.lib.hub.DataParserRawDataTypeEnum;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.site.api.intercept.AuthInterceptor;
import us.zoom.cube.site.biz.*;
import us.zoom.cube.site.biz.syspara.CubeTemplateParaService;
import us.zoom.cube.site.core.*;
import us.zoom.cube.site.core.auth.TenantUserRelaHandler;
import us.zoom.cube.site.infra.AsyncMqOpenApiClientWrapper;
import us.zoom.cube.site.infra.utils.AsyncMqUtils;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.BusinessException;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.DashTemplateInput;
import us.zoom.cube.site.lib.input.template.*;
import us.zoom.cube.site.lib.input.template.model.BuildDataParserModel;
import us.zoom.cube.site.lib.input.template.model.TemplateRevokeModel;
import us.zoom.cube.site.lib.output.template.ListBindServiceItemOutput;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.service.*;
import us.zoom.infra.enums.AggregationOperatorEnum;
import us.zoom.infra.enums.MetricsTypeEnum;
import us.zoom.infra.enums.MonitorLogTypeEnum;
import us.zoom.infra.enums.TemplateItemTypeEnum;
import us.zoom.infra.model.LogResult;
import us.zoom.infra.thread.CubeThreadPool;
import us.zoom.infra.utils.AsyncUtils;
import us.zoom.infra.utils.CommonSplitConstants;
import us.zoom.infra.utils.DateUtils;
import us.zoom.infra.utils.Instance;
import us.zoom.mq.common.entity.AdvancedFeature;

import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static us.zoom.cube.site.biz.InputQueueApprovalRecommendService.*;
import static us.zoom.cube.site.biz.alarm.ChannelServiceImpl.ADD;
import static us.zoom.cube.site.biz.template.TemplateConstant.*;
import static us.zoom.cube.site.core.DashTemplateZcpHandler.UNDERLINE;
import static us.zoom.cube.site.core.config.AsyncmqAdminInstance.EXIST_TOPIC;
import static us.zoom.cube.site.core.config.AsyncmqAdminInstance.NEW_TOPIC;
import static us.zoom.cube.site.core.tag.ResourceTypeConstant.RESOURCE_AlARM_TYPE;
import static us.zoom.cube.site.core.tag.ResourceTypeConstant.RESOURCE_METRIC_TYPE;
import static us.zoom.cube.site.infra.enums.CubeTemplatePhaseEnum.*;
import static us.zoom.infra.utils.CommonSplitConstants.COMMA_SPLIT;
import static us.zoom.infra.utils.CommonSplitConstants.METRICS_FIELD_SPLIT;
import us.zoom.cube.lib.integrations.MetricsFieldTypeEnum;

@Service
@Slf4j
public class TemplateInnerService {

    @Autowired
    private TemplateHandlerV2 templateHandlerV2;

    @Autowired
    private TemplateItemHandler templateItemHandler;

    @Autowired
    private TenantHandler tenantHandler;

    @Autowired
    private DataParserSourceHandler dataParserSourceHandler;

    @Autowired
    private DataParserPipelineHandler dataParserPipelineHandler;

    @Autowired
    private TemplateHandler templateHandler;

    @Autowired
    private CollectorHandler collectorHandler;

    @Autowired
    private MetricsHandler metricsHandler;

    @Autowired
    private MetricsAggregationHandler metricsAggregationHandler;

    @Autowired
    private TemplateService templateService;

    @Autowired
    private MetricsAggregationRuleHandler metricsAggregationRuleHandler;

    @Autowired
    private AggregationCustomFieldRuleHandler aggregationCustomFieldRuleHandler;

    @Autowired
    private AlarmDefinitionHandler alarmDefinitionHandler;

    private static ThreadPoolExecutor executor;

    @Autowired
    private DataParserHandler dataParserHandler;

    @Autowired
    private SysParaService sysParaService;

    @Autowired
    private TemplateServiceRelationDAO templateServiceRelationDAO;

    @Autowired
    private CubeTemplateItemServiceRelaDAO cubeTemplateItemServiceRelaDAO;

    @Autowired
    private DashDAO dashDAO;

    @Autowired
    private DashHandler dashHandler;

    @Autowired
    private TemplateInnerHandlerService templateInnerHandlerService;

    @Autowired
    private DataFlowDataParserRelationDAO dataFlowDataParserRelationDAO;

    @Autowired
    private DataFlowHandler dataFlowHandler;

    @Autowired
    private KafkaQueueHandler kafkaQueueHandler;

    @Autowired
    private DataParserSourceDAO dataParserSourceDAO;

    @Autowired
    private DataParserTimestampProcessorHandler dataParserTimestampProcessorHandler;

    @Autowired
    private DataParserIpProcessorHandler dataParserIpProcessorHandler;

    @Autowired
    private TenantUserRelaHandler tenantUserRelaHandler;

    @Autowired
    private UserHandler userHandler;

    @Autowired
    private InputQueueApprovalRecommendService inputQueueApprovalRecommendService;

    @Autowired
    private AsyncMqQueueHandler asyncMqQueueHandler;

    @Autowired
    private AuthService authService;

    @Autowired
    private TemplateServiceRelationHandler templateServiceRelationHandler;

    @Autowired
    private TemplateGroupRelationHandler templateGroupRelationHandler;

    @Autowired
    private TemplateGroupHandler templateGroupHandler;

    @Autowired
    private ZcpService zcpService;

    @Autowired
    private AsyncmqClusterHandler asyncmqClusterHandler;

    @Autowired
    private DataParserDeleteService dataParserDeleteService;

    @Autowired
    private ChannelDao channelDao;

    @Autowired
    private CubeTemplateParaService cubeTemplateParaService;

    @Autowired
    private ResourceTagDAO resourceTagDAO;

    @Autowired
    private DataParserGroovyProcessorHandler dataParserGroovyProcessorHandler;

    @Autowired
    private DataParserRemapperProcessorHandler dataParserRemapperProcessorHandler;

    @Autowired
    private DataParserFilterProcessorHandler dataParserFilterProcessorHandler;

    private static ThreadPoolExecutor singleExecutor;

    @PostConstruct
    public void init() {
        executor = new ThreadPoolExecutor(3, 6, 2, TimeUnit.MINUTES, new LinkedBlockingQueue(1000));
        singleExecutor = new ThreadPoolExecutor(3, 6, 2, TimeUnit.MINUTES, new LinkedBlockingQueue(1000));
    }

    private final static Logger monitorLog = LoggerFactory.getLogger("Monitor");

    public static List<String> channelList = new ArrayList<>();

    {
        channelList.add("cube_template_P1");
        channelList.add("cube_template_P0");
        channelList.add("cube_template_P2");
    }

    public Map<String, Object> bindAndUpdateTemplate(InnerTemplateBindDTO innerTemplateBindDTO) {
        innerTemplateBindDTO.check();
        InnerTemplateBindInput input = new InnerTemplateBindInput();
        BeanUtils.copyProperties(innerTemplateBindDTO, input);
        input.setDcs(innerTemplateBindDTO.getDcs());
        return bindAndUpdateTemplate(input, INNER).get(0);
    }

    public List<Map<String, Object>> bindAndUpdateTemplate(InnerTemplateBindInput input, String type) {
        input.check();
        List<Map<String, Object>> result = new ArrayList<>();
        Map<String, Object> metricsMap = new HashMap<>();
        long start = System.currentTimeMillis();

        metricsMap.put(PHASE, requestStart);
        metricsMap.put(SERVICE_NAME, input.getServiceName());
        metricsMap.put(ACTION_TYPE, type);
        metricsMap.put(TEMPLATE_NAME, input.getTemplateName());
        monitorLog.info(LogResult.getLogResult(MonitorLogTypeEnum.cubeTemplateStat.name(), JsonUtils.toJsonString(input), metricsMap));

        final String userId = StringUtils.isEmpty(AuthInterceptor.getUserId()) ? input.getUserId() : AuthInterceptor.getUserId();
        TenantDO destTenantDO = tenantHandler.getTenantByName(input.getServiceName());
        Assert.isTrue(null != destTenantDO, "This service does not exist in the cube");

        try {
            Set<TemplateDO> templateDOS = new HashSet<>();
            String typeTemplate = sysParaService.getCubeTemplateCache(ZCP_TYPE_NAME);
            List<TypeTemplateMapping> allTypeTemplateMappings = JsonUtils.toObjectByTypeRef(typeTemplate, new TypeReference<List<TypeTemplateMapping>>() {
            });
            Map<String, List<String>> allTypeTemplateMappingMaps = new HashMap<>();
            allTypeTemplateMappings.forEach(e -> {
                allTypeTemplateMappingMaps.put(e.getType(), e.getTemplateList());
            });

            if (input.getWithTopic()) {
                input.getTypeList().retainAll(allTypeTemplateMappings.stream().map(TypeTemplateMapping::getType).collect(Collectors.toList()));
                input.getTypeList().forEach(i -> {
                    List<String> templateList = allTypeTemplateMappingMaps.get(i);
                    templateList.forEach(t -> {
                        TemplateDO template = templateHandlerV2.getByName(t);
                        templateDOS.add(template);
                    });
                });
            } else {
                TemplateDO templateDO = templateHandlerV2.getByName(input.getTemplateName());
                templateDOS.add(templateDO);
            }

            for (TemplateDO templateDO : templateDOS) {
                Map<String, Object> value = new HashMap<>();
                value.put(TEMPLATE, templateDO.getName());
                value.put(SERVICE_NAME, destTenantDO.getName());
                try {
                    bindByTemplateDO(templateDO, destTenantDO, userId, input, type, value, metricsMap);
                    value.put(SUCCESS, true);
                } catch (Exception e) {
                    value.put(SUCCESS, false);
                    value.put(MESSAGE, "bind template error, service name: " + input.getServiceName() + ", error reason: " + subStringError(e.getMessage()));
                    log.error("newBindTemplateError: , serviceName = {}, tempalteName = {}, userId = {}", destTenantDO.getName(), templateDO.getName(), userId, e);
                }
                result.add(value);
            }

            metricsMap.put(PHASE, requestEnd);
            metricsMap.put(COST, System.currentTimeMillis() - start);
            monitorLog.info(LogResult.getLogResult(MonitorLogTypeEnum.cubeTemplateStat.name(), JsonUtils.toJsonString(input), metricsMap));
            return result;
        } catch (Exception e) {
            metricsMap.put(PHASE, requestErrorEnd);
            metricsMap.put(COST, System.currentTimeMillis() - start);
            monitorLog.info(LogResult.getErrorLogResult(MonitorLogTypeEnum.cubeTemplateStat.name(), e, metricsMap, JsonUtils.toJsonString(input)));
            log.error("bindTemplateError: ", e);
            throw new BusinessException("bind template error, service name: " + input.getServiceName() + ", error reason: " + subStringError(e.getMessage()));
        }
    }

    public String subStringError(String message) {
        if (StringUtils.isEmpty(message)) {
            return " inner error, please contact Cube Support at #Ask Cube monitor & ZDCAgent channel for help, thanks";
        } else {
            return message.length() > 300 ? message.substring(0, 250) : message + ", please contact Cube Support if necessary, thanks";
        }
    }

    private void updateDcs(InnerTemplateBindInput input) {
        Set<String> dcs = Optional.ofNullable(input.getDcs()).orElseGet(HashSet::new);
        dcs.add(cubeTemplateParaService.getDefaultDc());
        input.setDcs(dcs);
    }

    public void bindByTemplateDO(TemplateDO templateDO,
                                 TenantDO destTenantDO,
                                 String userId,
                                 InnerTemplateBindInput input,
                                 String type,
                                 Map<String, Object> result,
                                 Map<String, Object> metricsMap) throws Exception {
        Assert.isTrue(null != templateDO, "template not exist");
        TenantDO sourceTenantDO = tenantHandler.getTenantByName(templateDO.getServiceName());
        Assert.isTrue(null != sourceTenantDO, "This service does not exist in the cube");
        TemplateServiceRelationDO addTemplateServiceRelationDO = new TemplateServiceRelationDO();
        TemplateServiceRelationDO updateTemplateServiceRelationDO = new TemplateServiceRelationDO();
        List<TemplateItemDO> templateItemDOS = templateItemHandler.listByTemplateId(templateDO.getId());
        if (CollectionUtils.isEmpty(templateItemDOS)) {
            throw new BusinessException("template item not exist");
        }
        updateDcs(input);

        boolean bind = true;
        TemplateServiceRelationDO templateServiceRelationDO = templateServiceRelationDAO.listByServiceIdAndTemplateId(destTenantDO.getId(), templateDO.getId());
        if (Objects.isNull(templateServiceRelationDO)) {
            addTemplateServiceRelationDO.setId(IdUtils.generateId());
            addTemplateServiceRelationDO.setTemplateId(templateDO.getId());
            addTemplateServiceRelationDO.setServiceId(destTenantDO.getId());
            addTemplateServiceRelationDO.setModifyTime(new Date());
            addTemplateServiceRelationDO.setModifyTime(new Date());
            result.put(TYPE, BIND);
        } else {
            BeanUtils.copyProperties(templateServiceRelationDO, updateTemplateServiceRelationDO);
            updateTemplateServiceRelationDO.setModifyTime(new Date());
            result.put(TYPE, UPDATE);
            bind = false;
        }

        final String tenantId = destTenantDO.getId();
        Optional<Channel> destChannel = channelDao.findByNameAndTenantIdLimit1(CHANNEL_NAME, tenantId);
        UserDO userDO = userHandler.getUserById(userId);
        Channel addDestChannel = templateInnerHandlerService.createChannel(destChannel, destTenantDO.getId(), userId, userDO);

        Map<String, Object> topic = Maps.newHashMap();
        Map<String, Set<String>> zcpTopicMapping = new HashMap<>();
        Map<String, String> zcpPrefixTopic = new HashMap<>();
        List<InnerTemplateBindInput.TopicMapping> zcpTopicMappingList = new ArrayList<>();
        if ("zcp".equals(type)) {
            zcpTopicMappingList = input.getTopicMappingList().stream().distinct().collect(Collectors.toList());
            zcpTopicMapping = zcpTopicMappingList.stream().collect(Collectors.toMap(InnerTemplateBindInput.TopicMapping::getTemplateTopic, InnerTemplateBindInput.TopicMapping::getTopicList));
            zcpPrefixTopic = zcpTopicMappingList.stream().collect(Collectors.toMap(InnerTemplateBindInput.TopicMapping::getTemplateTopic, InnerTemplateBindInput.TopicMapping::getPrefixTopic));
        }
        final List<InnerTemplateBindInput.TopicMapping> finalZcpTopicMappingList = zcpTopicMappingList;

        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
            try {
                futureCreateTopic(metricsMap, topic, finalZcpTopicMappingList, type, input, templateItemDOS, result);
            } catch (Throwable throwable) {
                log.error("async process failed", throwable);
            }
        }, singleExecutor);
        future.join();
        log.info("create topic success, input = {}, result = {}", JsonUtils.toJsonStringIgnoreExp(input), JsonUtils.toJsonString(result));

        // dataParser and pipeline
        List<DataParserSourceDO> addFlowDataParserSourceDOList = Lists.newArrayList();
        List<DataParserSourceDO> updateFlowDataParserSourceDOList = Lists.newArrayList();
        List<DataParserPipelineDO> addFlowPipelineDOList = Lists.newArrayList();
        List<DataParserPipelineDO> updateFlowPipelineDOList = Lists.newArrayList();
        List<DataParserDO> addDataParserDOList = Lists.newArrayList();
        List<DataParserDO> updateDataParserDOList = Lists.newArrayList();
        List<AsyncMqQueueDO> asyncMqQueueDOS = Lists.newArrayList();

        // processor
        List<DataParserRemapperProcessorDO> addFlowDataParserRemapperProcessorDOList = Lists.newArrayList();
        List<DataParserFilterProcessorDO> addFlowDataParserFilterProcessorDOList = Lists.newArrayList();
        List<DataParserGroovyProcessorDO> addFlowDataParserGroovyProcessorDOList = Lists.newArrayList();
        List<DataParserRemapperProcessorDO> addDataParserRemapperProcessorDOList = Lists.newArrayList();
        List<DataParserFilterProcessorDO> addDataParserFilterProcessorDOList = Lists.newArrayList();
        List<DataParserGroovyProcessorDO> addDataParserGroovyProcessorDOList = Lists.newArrayList();
        List<DataParserTimestampProcessorDO> addDataParserTimestampProcessorDOList = Lists.newArrayList();
        List<DataParserIpProcessorDO> addDataParserIpProcessorDOList = Lists.newArrayList();

        // metricsAggregation metricsAggregationRule aggregationFunction aggregationHistogram
        List<AggregationHistogramRangeItemDO> addAggregationHistogramRangeItemDOList = Lists.newArrayList();
        List<AggregationPercentileItemDO> addAggregationPercentileItemDOList = Lists.newArrayList();
        List<AggregationCustomFieldRuleDO> addAggregationCustomFieldRuleDOList = Lists.newArrayList();
        List<MetricsAggregationDO> addMetricsAggregationDOList = Lists.newArrayList();
        List<MetricsAggregationRuleDO> addMetricsAggregationRuleDOList = Lists.newArrayList();
        List<AggregationFunctionItemDO> addAggregationFunctionItemDOList = Lists.newArrayList();

        List<DataParserPipelineDO> addPipelineDOList = Lists.newArrayList();
        List<DataParserPipelineDO> updatePipelineDOList = Lists.newArrayList();

        // collector and collectorField and metrics
        List<CollectorDO> addCollectorDOList = Lists.newArrayList();
        List<CollectorFieldDO> addCollectorFieldDOList = Lists.newArrayList();
        List<MetricsDO> addMetricsDOList = Lists.newArrayList();
        List<MetricsDO> updateMetricsDOList = Lists.newArrayList();

        // collectorMetrics and metricsField
        List<CollectorMetricsDO> addCollectorMetricsDOList = Lists.newArrayList();
        List<MetricsFieldDO> addMetricsFieldDOList = Lists.newArrayList();
        List<AlarmDefinition> addAlarmDefinitionList = Lists.newArrayList();
        List<ResourceTagDo> addResourceTagDoList = Lists.newArrayList();
        List<DashTemplateInput> addDashTemplateInputList = Lists.newArrayList();
        List<String> updateDashTemplateInputList = Lists.newArrayList();

        List<DataFlowDataParserRelationDO> addDataFlowDataParserRelationDOS = Lists.newArrayList();
        List<CubeTemplateItemServiceRelaDO> addcubeTemplateItemServiceRelaDOList = Lists.newArrayList();
        List<CubeTemplateItemServiceRelaDO> updatecubeTemplateItemServiceRelaDOList = Lists.newArrayList();
        List<AsyncMqQueueDO> asyncMqQueueDOList = Lists.newArrayList();
        List<KafkaQueueDO> addKafkaQueueDOList = Lists.newArrayList();

        List<CubeTemplateItemServiceRelaDO> cubeTemplateItemServiceRelaDOS = cubeTemplateItemServiceRelaDAO.queryByServiceId(destTenantDO.getId(), templateDO.getId());
        Map<String, String> cubeTemplateItemServiceRelaDOHashMap = cubeTemplateItemServiceRelaDOS.
                stream().collect(Collectors.toMap(
                        CubeTemplateItemServiceRelaDO::getSourceTemplateItemId,
                        CubeTemplateItemServiceRelaDO::getDestTemplateItemId,
                        (oldValue, newValue) -> newValue
                ));

        List<CubeTemplateItemServiceRelaDO> dashboardCubeTemplateItemServiceRelaDOS = cubeTemplateItemServiceRelaDAO.queryByServiceIdAndType(destTenantDO.getId(), templateDO.getId(), 3);
        Map<String, String> dashboardCubeTemplateItemServiceRelaDOHashMap = dashboardCubeTemplateItemServiceRelaDOS.stream()
                .collect(Collectors.toMap(
                        CubeTemplateItemServiceRelaDO::getSourceTemplateItemId,
                        CubeTemplateItemServiceRelaDO::getDashTemplateRelaId,
                        (oldValue, newValue) -> newValue
                ));

        long start = System.currentTimeMillis();
        metricsMap.put(PHASE, createAllDTOStart);
        metricsMap.put(COST, System.currentTimeMillis() - start);
        monitorLog.info(LogResult.getLogResult(MonitorLogTypeEnum.cubeTemplateStat.name(), JsonUtils.toJsonString(input), metricsMap));
        for (TemplateItemDO item : templateItemDOS) {
            if (TemplateItemTypeEnum.DASHBOARD.getCode().equals(item.getType())) {
                buildDashboard(
                        addDashTemplateInputList,
                        updateDashTemplateInputList,
                        item,
                        destTenantDO,
                        userId,
                        bind,
                        addcubeTemplateItemServiceRelaDOList,
                        updatecubeTemplateItemServiceRelaDOList,
                        dashboardCubeTemplateItemServiceRelaDOHashMap,
                        templateDO.getId(),
                        input,
                        templateDO);
            } else {
                BuildDataParserModel buildDataParserModel = BuildDataParserModel.builder()
                        .userId(userId)
                        .destChannel(addDestChannel)
                        .destTenantDO(destTenantDO)
                        .item(item)
                        .addFlowDataParserSourceDOList(addFlowDataParserSourceDOList)
                        .updateFlowDataParserSourceDOList(updateFlowDataParserSourceDOList)
                        .addFlowPipelineDOList(addFlowPipelineDOList)
                        .updateFlowPipelineDOList(updateFlowPipelineDOList)
                        .addFlowDataParserRemapperProcessorDOList(addFlowDataParserRemapperProcessorDOList)
                        .addFlowDataParserFilterProcessorDOList(addFlowDataParserFilterProcessorDOList)
                        .addFlowDataParserGroovyProcessorDOList(addFlowDataParserGroovyProcessorDOList)
                        .addCollectorDOList(addCollectorDOList)
                        .addCollectorFieldDOList(addCollectorFieldDOList)
                        .addMetricsDOList(addMetricsDOList)
                        .updateMetricsDOList(updateMetricsDOList)
                        .addCollectorMetricsDOList(addCollectorMetricsDOList)
                        .addMetricsFieldDOList(addMetricsFieldDOList)
                        .addMetricsAggregationDOList(addMetricsAggregationDOList)
                        .addMetricsAggregationRuleDOList(addMetricsAggregationRuleDOList)
                        .addAggregationFunctionItemDOList(addAggregationFunctionItemDOList)
                        .addAggregationHistogramRangeItemDOList(addAggregationHistogramRangeItemDOList)
                        .addAggregationPercentileItemDOList(addAggregationPercentileItemDOList)
                        .addAggregationCustomFieldRuleDOList(addAggregationCustomFieldRuleDOList)
                        .addAlarmDefinitionList(addAlarmDefinitionList)
                        .addDataParserDOList(addDataParserDOList)
                        .updateDataParserDOList(updateDataParserDOList)
                        .addPipelineDOList(addPipelineDOList)
                        .updatePipelineDOList(updatePipelineDOList)
                        .addDataParserRemapperProcessorDOList(addDataParserRemapperProcessorDOList)
                        .addDataParserFilterProcessorDOList(addDataParserFilterProcessorDOList)
                        .addDataParserGroovyProcessorDOList(addDataParserGroovyProcessorDOList)
                        .bind(bind)
                        .addcubeTemplateItemServiceRelaDOList(addcubeTemplateItemServiceRelaDOList)
                        .updatecubeTemplateItemServiceRelaDOList(updatecubeTemplateItemServiceRelaDOList)
                        .cubeTemplateItemServiceRelaDOHashMap(cubeTemplateItemServiceRelaDOHashMap)
                        .templateDO(templateDO)
                        .addDataFlowDataParserRelationDOS(addDataFlowDataParserRelationDOS)
                        .sourceTenantDO(sourceTenantDO)
                        .asyncMqQueueDOList(asyncMqQueueDOList)
                        .result(result)
                        .addKafkaQueueDOList(addKafkaQueueDOList)
                        .addDataParserTimestampProcessorDOList(addDataParserTimestampProcessorDOList)
                        .requestType(type)
                        .zcpTopicMapping(zcpTopicMapping)
                        .zcpPrefixTopic(zcpPrefixTopic)
                        .addDataParserIpProcessorDOList(addDataParserIpProcessorDOList)
                        .userDO(userDO)
                        .input(input)
                        .addAsyncMqQueueDOS(asyncMqQueueDOS)
                        .addResourceTagDoList(addResourceTagDoList)
                        .build();

                Integer dataParserType = item.getType();
                if (TemplateItemTypeEnum.DATAPARSER.getCode().equals(dataParserType) || TemplateItemTypeEnum.NEWDATAPARSER.getCode().equals(dataParserType)) {
                    String dataParserName = TemplateItemTypeEnum.DATAPARSER.getName();
                    if (TemplateItemTypeEnum.NEWDATAPARSER.getCode().equals(dataParserType)) {
                        dataParserName = TemplateItemTypeEnum.NEWDATAPARSER.getName();
                    }
                    buildDataParser(buildDataParserModel, dataParserName);
                }
            }
        }
        metricsMap.put(PHASE, createAllDTOEnd);
        metricsMap.put(COST, System.currentTimeMillis() - start);
        monitorLog.info(LogResult.getLogResult(MonitorLogTypeEnum.cubeTemplateStat.name(), JsonUtils.toJsonString(input), metricsMap));

        templateInnerHandlerService.applyInDB(destTenantDO,
                addFlowDataParserSourceDOList,
                updateFlowDataParserSourceDOList,
                addFlowPipelineDOList,
                updateFlowPipelineDOList,
                addFlowDataParserRemapperProcessorDOList,
                addFlowDataParserFilterProcessorDOList,
                addFlowDataParserGroovyProcessorDOList,
                addCollectorDOList,
                addCollectorFieldDOList,
                addMetricsDOList,
                updateMetricsDOList,
                addCollectorMetricsDOList,
                addMetricsFieldDOList,
                addMetricsAggregationDOList,
                addMetricsAggregationRuleDOList,
                addAggregationFunctionItemDOList,
                addAggregationHistogramRangeItemDOList,
                addAggregationPercentileItemDOList,
                addAggregationCustomFieldRuleDOList,
                addAlarmDefinitionList,
                addDataParserDOList,
                updateDataParserDOList,
                addPipelineDOList,
                updatePipelineDOList,
                addDataParserRemapperProcessorDOList,
                addDataParserFilterProcessorDOList,
                addDataParserGroovyProcessorDOList,
                addDashTemplateInputList,
                updateDashTemplateInputList,
                addcubeTemplateItemServiceRelaDOList,
                updatecubeTemplateItemServiceRelaDOList,
                input.getApplyInDB(),
                addTemplateServiceRelationDO,
                updateTemplateServiceRelationDO,
                bind,
                addDataFlowDataParserRelationDOS,
                asyncMqQueueDOList,
                addKafkaQueueDOList,
                addDataParserTimestampProcessorDOList,
                addDataParserIpProcessorDOList,
                result,
                type,
                templateDO,
                metricsMap,
                input,
                asyncMqQueueDOS,
                addResourceTagDoList);
    }

    public Set<String> listTopicByTemplate(String templateTopic,
                                           String serviceName,
                                           Set<String> dcs) {
        Set<String> result = new HashSet<>();
        if (StringUtils.isNotBlank(templateTopic)) {
            templateTopic = templateTopic.replace(CUBE_APP_NAME, serviceName);
            for (String dc : dcs) {
                String topic = "";
                topic = templateTopic.replace(R_DC, dc);
                result.add(topic);
            }
        }
        return result;
    }

    public void createTopic(Map<String, Object> topic,
                            List<InnerTemplateBindInput.TopicMapping> topicMappingList) {
        if (!CollectionUtils.isEmpty(topicMappingList)) {
            topicMappingList.forEach(t -> {
                Future<Map<String, Object>> future = null;
                Callable<Map<String, Object>> topics = () -> asyncCreateTopic(t.getTopicList());
                future = executor.submit(topics);
                try {
                    topic.putAll(future.get());
                } catch (Exception e) {
                    log.error("create topic error", e);
                }
            });
        }
    }

    public void futureCreateTopic(Map<String, Object> metricsMap,
                                  Map<String, Object> topic,
                                  List<InnerTemplateBindInput.TopicMapping> finalZcpTopicMappingList,
                                  String type,
                                  InnerTemplateBindInput input,
                                  List<TemplateItemDO> templateItemDOS,
                                  Map<String, Object> result) {
        long start = System.currentTimeMillis();
        try {
            metricsMap.put(PHASE, createTopicStart);
            metricsMap.put(COST, System.currentTimeMillis() - start);
            monitorLog.info(LogResult.getLogResult(MonitorLogTypeEnum.cubeTemplateStat.name(), JsonUtils.toJsonString(input), metricsMap));
            switch (type) {
                case ZCP:
                    createTopic(topic, finalZcpTopicMappingList);
                    break;
                case INNER:
                    List<InnerTemplateBindInput.TopicMapping> topicMappingList = new ArrayList<>();
                    List<String> templateTopic = new ArrayList<>();
                    templateItemDOS.forEach(templateItemDO -> {
                        switch (templateItemDO.getType()) {
                            case 1:
                                DataParserDO dataParserDO = dataParserHandler.getById(templateItemDO.getItemId());
                                if (null != dataParserDO && !StringUtils.isEmpty(dataParserDO.getTopicTemplate())) {
                                    Map<String, String> topicTemplateMap = JsonUtils.toObjectByTypeRef(dataParserDO.getTopicTemplate(), new TypeReference<Map<String, String>>() {
                                    });
                                    templateTopic.add(topicTemplateMap.get(CUBE));
                                }
                                break;
                            case 2:
                                DataParserSourceDO dataParserSourceDO = dataParserSourceHandler.getById(templateItemDO.getItemId());
                                Assert.isTrue(null != dataParserSourceDO, "This data parser does not exist in the cube");
                                String topicTemplate = dataParserSourceDAO.findByTenantIdAndDataparserId(dataParserSourceDO.getTenantId(), dataParserSourceDO.getId());
                                if (!StringUtils.isEmpty(topicTemplate)) {
                                    Map<String, String> topicTemplateMap = JsonUtils.toObjectByTypeRef(topicTemplate, new TypeReference<Map<String, String>>() {
                                    });
                                    templateTopic.add(topicTemplateMap.get(CUBE));
                                }
                                break;
                            default:
                                break;
                        }
                    });
                    if (!CollectionUtils.isEmpty(templateTopic)) {
                        templateTopic.forEach(t -> {
                            InnerTemplateBindInput.TopicMapping topicMapping = new InnerTemplateBindInput.TopicMapping();
                            topicMapping.setTemplateTopic(t);
                            topicMapping.setTopicList(listTopicByTemplate(t, input.getServiceName(), input.getDcs()));
                            topicMappingList.add(topicMapping);
                        });
                        createTopic(topic, topicMappingList);
                    }
                    break;
                default:
                    break;
            }
            metricsMap.put(PHASE, createTopicEnd);
            metricsMap.put(COST, System.currentTimeMillis() - start);
            monitorLog.info(LogResult.getLogResult(MonitorLogTypeEnum.cubeTemplateStat.name(), JsonUtils.toJsonString(input), metricsMap));
            result.put(TOPIC, topic);
        } catch (Throwable throwable) {
            log.error("async process failed due to on error : {}", throwable.getMessage(), throwable);
        }
    }

    public void buildDashboard(List<DashTemplateInput> addDashTemplateInputList,
                               List<String> updateDashTemplateInputList,
                               TemplateItemDO item,
                               TenantDO destTenantDO,
                               String userId,
                               boolean bind,
                               List<CubeTemplateItemServiceRelaDO> addcubeTemplateItemServiceRelaDOList,
                               List<CubeTemplateItemServiceRelaDO> updatecubeTemplateItemServiceRelaDOList,
                               Map<String, String> dashboardCubeTemplateItemServiceRelaDOHashMap,
                               String templateId,
                               InnerTemplateBindInput input,
                               TemplateDO templateDO) {
        List<String> ignoreTpls = Arrays.asList(sysParaService.getCubeTemplateCache(IGNORE_JUDGE_TPL).split(","));
        if (CollectionUtils.isEmpty(ignoreTpls) || !ignoreTpls.contains(templateDO.getName())) {
            if (input.getWithTopic() && EC2_INFRA_DASHBOARD.equals(item.getName())) {
                return;
            }
            if (!input.getWithTopic() && ZCP_INFRA_DASHBOARD.equals(item.getName())) {
                return;
            }
        }
        DashDO tplDashboardDO = dashDAO.getDashById(item.getItemId());
        Assert.isTrue(null != tplDashboardDO, "template dashboard not exist, tplDashboardId is " + item.getItemId());
        String dashTemplateRelaId = dashboardCubeTemplateItemServiceRelaDOHashMap.get(item.getItemId());
        CubeTemplateItemServiceRelaDO existRelaDO = null;
        String destTemplateItemId = null;
        if (bind) {
            addDashboarTemplateInput(item, destTenantDO.getId(), userId, tplDashboardDO.getId(), addDashTemplateInputList);
        } else {
            if (StringUtils.isEmpty(dashTemplateRelaId)) {
                addDashboarTemplateInput(item, destTenantDO.getId(), userId, tplDashboardDO.getId(), addDashTemplateInputList);
            } else {
                updateDashTemplateInputList.add(dashTemplateRelaId);
                DashTemplateRelaDO dashTemplateRelaDO = dashHandler.getTemplateDashRelaById(dashTemplateRelaId);
                if (Objects.nonNull(dashTemplateRelaDO)) {
                    existRelaDO = cubeTemplateItemServiceRelaDAO.queryUniqueRela(buildCubeTemplateItemServiceRelaDO(
                            dashTemplateRelaDO.getDashId(),
                            tplDashboardDO.getId(),
                            templateId,
                            destTenantDO.getId(),
                            3));
                    destTemplateItemId = dashTemplateRelaDO.getDashId();
                }
            }
        }
        handleRelaDO(existRelaDO, destTemplateItemId, tplDashboardDO.getId(), templateId, destTenantDO.getId(), 3, addcubeTemplateItemServiceRelaDOList, updatecubeTemplateItemServiceRelaDOList);
    }

    public void addDashboarTemplateInput(TemplateItemDO item, String tenantId, String userId, String sourceDashboardId, List<DashTemplateInput> addDashTemplateInputList) {
        DashTemplateInput dashTemplateInput = new DashTemplateInput();
        dashTemplateInput.setServiceId(tenantId);
        dashTemplateInput.setDashId(sourceDashboardId);
        dashTemplateInput.setTemplateId(item.getId());
        Set<String> zcpOwnerList = new HashSet<>();
        zcpOwnerList.add(userId);
        zcpOwnerList.addAll(tenantUserRelaHandler.getCanOperateUsers(tenantId));
        dashTemplateInput.setZcpOwnerList(new ArrayList<>(zcpOwnerList));
        addDashTemplateInputList.add(dashTemplateInput);
    }

    public void buildDataParser(BuildDataParserModel buildDataParserModel, String dataparserType) {
        if (TemplateItemTypeEnum.DATAPARSER.getName().equals(dataparserType)) {
            TemplateItemDO item = buildDataParserModel.getItem();
            DataParserDO tplDataParserDO = dataParserHandler.getById(item.getItemId());
            Assert.isTrue(null != tplDataParserDO, "template dataParser not exist, tplDataparserId is " + item.getItemId());

            DataParserDO destDataParserDO = copyDataParser(
                    tplDataParserDO.getName(),
                    buildDataParserModel.getDestTenantDO(),
                    tplDataParserDO,
                    buildDataParserModel.getAddDataParserDOList(),
                    buildDataParserModel.getUpdateDataParserDOList(),
                    buildDataParserModel.isBind(),
                    buildDataParserModel.getCubeTemplateItemServiceRelaDOHashMap(),
                    buildDataParserModel.getUserId(),
                    buildDataParserModel.getAsyncMqQueueDOList(),
                    buildDataParserModel.getResult(),
                    buildDataParserModel.getAddKafkaQueueDOList(),
                    buildDataParserModel.getRequestType(),
                    buildDataParserModel.getZcpTopicMapping(),
                    buildDataParserModel.getInput().getDcs());

            final String destTenantId = buildDataParserModel.getDestTenantDO().getId();
            final String sourceDataparserId = tplDataParserDO.getId();
            final String destDataparserId = destDataParserDO.getId();
            List<DataParserPipelineDO> sourcePipelines = dataParserPipelineHandler.listPipelinesByDataParserId(tplDataParserDO.getId());
            if (!CollectionUtils.isEmpty(sourcePipelines)) {
                for (int pipelineIndex = 0; pipelineIndex < sourcePipelines.size(); pipelineIndex++) {
                    DataParserPipelineDO sourcePipeline = sourcePipelines.get(pipelineIndex);
                    DataParserPipelineDO destPipelineDO = copyPipeline(sourcePipeline.getName(),
                            destDataParserDO.getTenantId(),
                            buildDataParserModel.getAddPipelineDOList(),
                            buildDataParserModel.getUpdatePipelineDOList(),
                            sourcePipeline,
                            destDataParserDO.getId(),
                            buildDataParserModel.isBind(),
                            buildDataParserModel.getDestTenantDO());

                    copyDataparserProcessor(
                            sourcePipeline, destPipelineDO.getId(),
                            buildDataParserModel.getAddDataParserRemapperProcessorDOList(),
                            buildDataParserModel.getAddDataParserFilterProcessorDOList(),
                            buildDataParserModel.getAddDataParserGroovyProcessorDOList(),
                            buildDataParserModel.getAddDataParserTimestampProcessorDOList(),
                            buildDataParserModel.getAddDataParserIpProcessorDOList());

                    String sourceCollectorId = sourcePipeline.getCollectorId();
                    if (StringUtils.isEmpty(sourceCollectorId)) {
                        continue;
                    }

                    CollectorDO destCollectorDO = copyCollector(sourcePipeline, destTenantId, destPipelineDO, buildDataParserModel.getAddCollectorDOList());
                    copyPubilcFlowAndDataparserValue(sourceCollectorId,
                            destCollectorDO,
                            buildDataParserModel.getAddCollectorFieldDOList(),
                            buildDataParserModel.getAddMetricsDOList(),
                            buildDataParserModel.getUpdateMetricsDOList(),
                            buildDataParserModel.getAddCollectorMetricsDOList(),
                            buildDataParserModel.getAddMetricsFieldDOList(),
                            buildDataParserModel.getAddMetricsAggregationDOList(),
                            buildDataParserModel.getAddMetricsAggregationRuleDOList(),
                            buildDataParserModel.getAddAggregationFunctionItemDOList(),
                            buildDataParserModel.getAddAggregationHistogramRangeItemDOList(),
                            buildDataParserModel.getAddAggregationPercentileItemDOList(),
                            buildDataParserModel.getAddAggregationCustomFieldRuleDOList(),
                            buildDataParserModel.getAddAlarmDefinitionList(),
                            buildDataParserModel.getAddResourceTagDoList(),
                            buildDataParserModel.getUserId(),
                            destTenantId,
                            buildDataParserModel.getDestChannel(),
                            buildDataParserModel.getUserDO(),
                            buildDataParserModel.isBind());
                }
            }

            TemplateDO templateDO = buildDataParserModel.getTemplateDO();
            CubeTemplateItemServiceRelaDO existRelaDO = cubeTemplateItemServiceRelaDAO.queryUniqueRela(buildCubeTemplateItemServiceRelaDO(
                    destDataparserId,
                    sourceDataparserId,
                    templateDO.getId(),
                    destTenantId,
                    1));
            handleRelaDO(existRelaDO,
                    destDataparserId,
                    sourceDataparserId,
                    templateDO.getId(),
                    destTenantId,
                    1,
                    buildDataParserModel.getAddcubeTemplateItemServiceRelaDOList(),
                    buildDataParserModel.getUpdatecubeTemplateItemServiceRelaDOList());
        } else {
            TemplateItemDO item = buildDataParserModel.getItem();
            DataParserSourceDO tplDataParserSourceDO = dataParserSourceHandler.getById(item.getItemId());
            Assert.isTrue(null != tplDataParserSourceDO, "template dataParser not exist, tplDataparserId is " + item.getItemId());

            DataParserSourceDO destDataParserSourceDO = copyDataParserSource(tplDataParserSourceDO.getName(),
                    buildDataParserModel.getDestTenantDO(),
                    tplDataParserSourceDO,
                    buildDataParserModel.getAddFlowDataParserSourceDOList(),
                    buildDataParserModel.getUpdateFlowDataParserSourceDOList(),
                    buildDataParserModel.isBind(),
                    buildDataParserModel.getCubeTemplateItemServiceRelaDOHashMap(),
                    buildDataParserModel.getUserId());

            TenantDO destTenantDO = buildDataParserModel.getDestTenantDO();
            final String destTenantId = destTenantDO.getId();
            final String sourceDataparserId = tplDataParserSourceDO.getId();
            final String destDataparserId = destDataParserSourceDO.getId();

            List<DataFlowDataParserRelationDO> dataFlowDataParserRelationDOS = dataFlowDataParserRelationDAO.selectByServiceAndDataparserId(tplDataParserSourceDO.getId(), tplDataParserSourceDO.getTenantId());
            Assert.isTrue(!CollectionUtils.isEmpty(dataFlowDataParserRelationDOS), "template dataParser not have data flow, tplDataparserId is " + item.getItemId());

            for (DataFlowDataParserRelationDO dataFlowDataParserRelationDO : dataFlowDataParserRelationDOS) {
                DataFlowDO updateDataFlowDO = dataFlowHandler.getDataFlowById(dataFlowDataParserRelationDO.getDataFlowId());
                Assert.isTrue(null != updateDataFlowDO, "template service data flow is null");

                InnerTemplateBindInput input = buildDataParserModel.getInput();
                handleDataflowDO(updateDataFlowDO,
                        destTenantDO.getName(),
                        buildDataParserModel.getRequestType(),
                        buildDataParserModel.getZcpTopicMapping(),
                        buildDataParserModel.getZcpPrefixTopic(),
                        input.getDcs(),
                        buildDataParserModel.getAddAsyncMqQueueDOS());
                handleDataFlowRelaDO(buildDataParserModel.getAddDataFlowDataParserRelationDOS(),
                        updateDataFlowDO.getId(),
                        destTenantDO.getId(),
                        destTenantDO.getName(),
                        destDataparserId,
                        buildDataParserModel.getTemplateDO());
            }

            List<DataParserPipelineDO> sourcePipelines = dataParserPipelineHandler.listPipelinesByDataParserId(tplDataParserSourceDO.getId());
            if (!CollectionUtils.isEmpty(sourcePipelines)) {
                for (int pipelineIndex = 0; pipelineIndex < sourcePipelines.size(); pipelineIndex++) {
                    DataParserPipelineDO sourcePipeline = sourcePipelines.get(pipelineIndex);
                    DataParserPipelineDO destPipelineDO = copyPipeline(sourcePipeline.getName(),
                            destTenantId,
                            buildDataParserModel.getAddFlowPipelineDOList(),
                            buildDataParserModel.getUpdateFlowPipelineDOList(),
                            sourcePipeline,
                            destDataParserSourceDO.getId(),
                            buildDataParserModel.isBind(),
                            buildDataParserModel.getDestTenantDO());

                    copyFlowProcessor(sourcePipeline,
                            destPipelineDO.getId(),
                            buildDataParserModel.getAddFlowDataParserFilterProcessorDOList(),
                            buildDataParserModel.getAddFlowDataParserGroovyProcessorDOList(),
                            buildDataParserModel.getAddFlowDataParserRemapperProcessorDOList(),
                            buildDataParserModel.getAddDataParserTimestampProcessorDOList(),
                            buildDataParserModel.getAddDataParserIpProcessorDOList());

                    String sourceCollectorId = sourcePipeline.getCollectorId();
                    if (StringUtils.isEmpty(sourceCollectorId)) {
                        continue;
                    }

                    CollectorDO destCollectorDO = copyCollector(sourcePipeline, destDataParserSourceDO.getTenantId(), destPipelineDO, buildDataParserModel.getAddCollectorDOList());
                    copyPubilcFlowAndDataparserValue(sourceCollectorId,
                            destCollectorDO,
                            buildDataParserModel.getAddCollectorFieldDOList(),
                            buildDataParserModel.getAddMetricsDOList(),
                            buildDataParserModel.getUpdateMetricsDOList(),
                            buildDataParserModel.getAddCollectorMetricsDOList(),
                            buildDataParserModel.getAddMetricsFieldDOList(),
                            buildDataParserModel.getAddMetricsAggregationDOList(),
                            buildDataParserModel.getAddMetricsAggregationRuleDOList(),
                            buildDataParserModel.getAddAggregationFunctionItemDOList(),
                            buildDataParserModel.getAddAggregationHistogramRangeItemDOList(),
                            buildDataParserModel.getAddAggregationPercentileItemDOList(),
                            buildDataParserModel.getAddAggregationCustomFieldRuleDOList(),
                            buildDataParserModel.getAddAlarmDefinitionList(),
                            buildDataParserModel.getAddResourceTagDoList(),
                            buildDataParserModel.getUserId(),
                            destTenantId,
                            buildDataParserModel.getDestChannel(),
                            buildDataParserModel.getUserDO(),
                            buildDataParserModel.isBind());
                }
            }

            TemplateDO templateDO = buildDataParserModel.getTemplateDO();
            CubeTemplateItemServiceRelaDO existRelaDO = cubeTemplateItemServiceRelaDAO.queryUniqueRela(buildCubeTemplateItemServiceRelaDO(
                    destDataparserId,
                    sourceDataparserId,
                    templateDO.getId(),
                    destTenantId,
                    2));
            handleRelaDO(existRelaDO,
                    destDataparserId,
                    sourceDataparserId,
                    templateDO.getId(),
                    destTenantId,
                    2,
                    buildDataParserModel.getAddcubeTemplateItemServiceRelaDOList(),
                    buildDataParserModel.getUpdatecubeTemplateItemServiceRelaDOList());
        }
    }

    static List<String> metricsList = new ArrayList<>();

    static {
        metricsList.add("cpu");
        metricsList.add("process");
        metricsList.add("memory");
        metricsList.add("io");
        metricsList.add("disk");
    }

    public static boolean matchPlaceholder(String str) {
        if (StringUtils.isBlank(str)) {
            return false;
        }
        String regex = "\\{.*?\\}";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(str);
        return matcher.find();
    }

    public void handleDataflowDO(DataFlowDO updateDataFlowDO,
                                 String serviceName,
                                 String requestType,
                                 Map<String, Set<String>> zcpTopicMapping,
                                 Map<String, String> zcpPrefixTopic,
                                 Set<String> requestDcs,
                                 List<AsyncMqQueueDO> asyncMqQueueDOS) {
        if (StringUtils.isEmpty(updateDataFlowDO.getTopicTemplate())) {
            return;
        }
        Map<String, String> topicTemplateMap = Maps.newHashMap();
        if (StringUtils.isNotBlank(updateDataFlowDO.getTopicTemplate())) {
            topicTemplateMap = JsonUtils.toObjectByTypeRef(updateDataFlowDO.getTopicTemplate(), new TypeReference<Map<String, String>>() {
            });
        }
        if (ZCP.equals(requestType)) {
            Assert.isTrue(!StringUtils.isEmpty(topicTemplateMap.get(ZCP)), "zcp topic template is null");
            Set<String> topicSet = zcpTopicMapping.get(topicTemplateMap.get(ZCP));
            if (CollectionUtils.isEmpty(topicSet)) {
                return;
            }
            buildAndAddAsyncMqQueueDO(topicSet, updateDataFlowDO.getId(), AsyncMqQueueSourceTypeEnum.DataFlow.getType(),
                    StringUtils.EMPTY, asyncMqQueueDOS);
        } else {
            Set<String> dcs = new HashSet<>();
            String topic = topicTemplateMap.get(CUBE);
            if (!matchPlaceholder(topic)) {
                topic = StringUtils.EMPTY;
            } else {
                topic = topic.replace(CUBE_APP_NAME, serviceName).replace(R_DC + UNDERLINE, "");
                dcs.addAll(Splitter.on(",").omitEmptyStrings().trimResults().splitToList(sysParaService.getCubeTemplateCache(DC)));
                if (!CollectionUtils.isEmpty(requestDcs)) {
                    dcs.addAll(requestDcs);
                }
            }

            List<String> topicNames = Lists.newArrayList();
            if (!topic.equals(StringUtils.EMPTY)) {
                for (String dc : dcs) {
                    topicNames.add(dc + "_" + topic);
                }
            }
            buildAndAddAsyncMqQueueDO(topicNames, updateDataFlowDO.getId(), AsyncMqQueueSourceTypeEnum.DataFlow.getType(),
                    StringUtils.EMPTY, asyncMqQueueDOS);
        }
    }

    public void buildAndAddAsyncMqQueueDO(Collection<String> topicNames,
                                          String sourceId,
                                          Integer sourceType,
                                          String tenantId,
                                          List<AsyncMqQueueDO> asyncMqQueueDOS) {
        if (CollectionUtils.isEmpty(topicNames)) {
            return;
        }

        Map<String, Set<String>> clusterTopics = topicNames.stream()
                .collect(Collectors.groupingBy(
                        topic -> {
                            if (topic.startsWith(topic_start_with_eu02)) return topic_start_with_eu02;
                            if (topic.startsWith(TOPIC_START_WITH_MIL)) return TOPIC_START_WITH_MIL;
                            return DEFAULT_TOPIC;
                        },
                        Collectors.toSet()
                ));

        AsyncMqOpenApiClientWrapper globalClient = asyncmqClusterHandler.getDefaultOpenApiClient();
        AsyncMqOpenApiClientWrapper eu02Client = clusterTopics.containsKey(topic_start_with_eu02)
                ? asyncmqClusterHandler.getOpenapiClientByClusterName(topic_start_with_eu02)
                : globalClient;
        AsyncMqOpenApiClientWrapper milClient = clusterTopics.containsKey(TOPIC_START_WITH_MIL)
                ? asyncmqClusterHandler.getOpenapiClientByClusterName(TOPIC_START_WITH_MIL)
                : globalClient;

        Map<String, TopicResult> topicResultMap = new HashMap<>();
        processTopics(clusterTopics.get(DEFAULT_TOPIC), globalClient, topicResultMap);
        processTopics(clusterTopics.get(topic_start_with_eu02), eu02Client, topicResultMap);
        processTopics(clusterTopics.get(TOPIC_START_WITH_MIL), milClient, topicResultMap);

        clusterTopics.values().stream()
                .flatMap(Set::stream)
                .forEach(topicName -> processTopic(
                        topicName, sourceId, tenantId, sourceType,
                        globalClient, eu02Client, milClient,
                        topicResultMap, asyncMqQueueDOS
                ));

        inputQueueApprovalRecommendService.setRecommendConfig(asyncMqQueueDOS);
        if (AsyncMqQueueSourceTypeEnum.DataFlow.getType().equals(sourceType)) {
            asyncMqQueueDOS.forEach(d -> d.setStatus(AsyncMqQueueStatusEnum.Approved.getStatus()));
        }
    }

    private void processTopics(Set<String> topics,
                               AsyncMqOpenApiClientWrapper client,
                               Map<String, TopicResult> resultMap) {
        if (CollectionUtils.isEmpty(topics)) {
            return;
        }
        Result<List<TopicResult>> result = client.getOpenApi().listTopicsByNames(new ArrayList<>(topics));
        if (result.isSuccess() && result.getData() != null) {
            result.getData().forEach(t -> resultMap.put(t.getName(), t));
        }
    }

    private void processTopic(String topicName,
                              String sourceId,
                              String tenantId,
                              Integer sourceType,
                              AsyncMqOpenApiClientWrapper globalClient,
                              AsyncMqOpenApiClientWrapper eu02Client,
                              AsyncMqOpenApiClientWrapper milClient,
                              Map<String, TopicResult> topicResultMap,
                              List<AsyncMqQueueDO> resultList) {
        TopicResult result = topicResultMap.get(topicName);
        if (result == null) {
            return;
        }

        AsyncMqOpenApiClientWrapper targetClient = globalClient;
        if (topicName.startsWith(topic_start_with_eu02)) {
            targetClient = eu02Client;
        } else if (topicName.startsWith(TOPIC_START_WITH_MIL)) {
            targetClient = milClient;
        }
        Set<String> existTopics = asyncMqQueueHandler.getNamesBySourceIdAndAsyncMqClusterId(
                sourceId, targetClient.getAsyncMqClusterId()
        );
        if (!existTopics.add(topicName)) {
            log.info("Topic already exists: {}", JsonUtils.toJsonStringIgnoreExp(result));
            return;
        }

        AsyncMqQueueDO queueDO = asyncMqQueueHandler.buildNewAsyncMqQueueDO(
                targetClient.getAsyncMqClusterId(),
                tenantId,
                sourceType,
                sourceId,
                topicName,
                result
        );
        resultList.add(queueDO);
    }

    public void handleDataFlowRelaDO(List<DataFlowDataParserRelationDO> addDataFlowDataParserRelationDOS,
                                     String dataFlowId,
                                     String tenantId,
                                     String tenantName,
                                     String destDataparserId,
                                     TemplateDO templateDO) {
        DataFlowDataParserRelationDO dataFlowDataParserRelationDO = dataFlowDataParserRelationDAO.selectUniqueRelaDO(dataFlowId, destDataparserId, tenantId);
        if (null == dataFlowDataParserRelationDO) {
            addDataFlowDataParserRelationDOS.add(createDataFlowRelation(dataFlowId, tenantId, tenantName, destDataparserId));
        }
        if (templateDO == null) {
            return;
        }
        String jvmTemplateValue = cubeTemplateParaService.getJvmTemplateValue();
        if (!StringUtils.equals(jvmTemplateValue, templateDO.getName())) {
            return;
        }

        String apmDataFlowValue = cubeTemplateParaService.getApmDataFlowValue();
        DataFlowDO dataFlowDO = dataFlowHandler.getDataFlowByName(apmDataFlowValue);
        Set<String> dataFlowIds = dataFlowDataParserRelationDAO.selectRelation(destDataparserId, tenantId)
                .stream()
                .map(DataFlowDataParserRelationDO::getDataFlowId)
                .collect(Collectors.toSet());

        if (!dataFlowIds.contains(dataFlowDO.getId())) {
            DataFlowDataParserRelationDO apmRelation = dataFlowDataParserRelationDAO.selectUniqueRelaDO(dataFlowDO.getId(), destDataparserId, tenantId);
            if (apmRelation == null) {
                addDataFlowDataParserRelationDOS.add(createDataFlowRelation(dataFlowDO.getId(), tenantId, tenantName, destDataparserId));
            }
        }
    }

    public DataFlowDataParserRelationDO createDataFlowRelation(String dataflowId, String tenantId, String tenantName, String dataParserId) {
        DataFlowDataParserRelationDO dataFlowDataParserRelationDO = new DataFlowDataParserRelationDO();
        dataFlowDataParserRelationDO.setId(IdUtils.generateId());
        dataFlowDataParserRelationDO.setDataFlowId(dataflowId);
        dataFlowDataParserRelationDO.setTenantId(tenantId);
        dataFlowDataParserRelationDO.setTenantName(tenantName);
        dataFlowDataParserRelationDO.setDataParserId(dataParserId);
        dataFlowDataParserRelationDO.setAddUserName(CHANNEL_NAME);
        dataFlowDataParserRelationDO.setAddUserId(CHANNEL_NAME);
        dataFlowDataParserRelationDO.setGmtCreate(DateUtils.format(new Date(), DateUtils.FORMART1));
        return dataFlowDataParserRelationDO;
    }

    public CubeTemplateItemServiceRelaDO buildCubeTemplateItemServiceRelaDO(String destTemplateItemId,
                                                                            String sourceTemplateItemId,
                                                                            String cubeTemplateId,
                                                                            String destTenantId,
                                                                            int type) {
        CubeTemplateItemServiceRelaDO cubeTemplateItemServiceRelaDO = new CubeTemplateItemServiceRelaDO();
        cubeTemplateItemServiceRelaDO.setCubeTemplateId(cubeTemplateId);
        cubeTemplateItemServiceRelaDO.setDestTemplateItemId(destTemplateItemId);
        cubeTemplateItemServiceRelaDO.setSourceTemplateItemId(sourceTemplateItemId);
        cubeTemplateItemServiceRelaDO.setType(type);
        cubeTemplateItemServiceRelaDO.setServiceId(destTenantId);
        return cubeTemplateItemServiceRelaDO;
    }

    public void handleRelaDO(CubeTemplateItemServiceRelaDO existRelaDO,
                             String destTemplateItemId,
                             String sourceTemplateItemId,
                             String cubeTemplateId,
                             String destTenantId,
                             int type,
                             List<CubeTemplateItemServiceRelaDO> addTplTenantRelaDOList,
                             List<CubeTemplateItemServiceRelaDO> updateTplTenantRelaDOList) {
        if (existRelaDO == null) {
            CubeTemplateItemServiceRelaDO tplTenantRelaDO = new CubeTemplateItemServiceRelaDO();
            tplTenantRelaDO.setId(IdUtils.generateId());
            tplTenantRelaDO.setCubeTemplateId(cubeTemplateId);
            tplTenantRelaDO.setDestTemplateItemId(destTemplateItemId);
            tplTenantRelaDO.setSourceTemplateItemId(sourceTemplateItemId);
            tplTenantRelaDO.setType(type);
            tplTenantRelaDO.setServiceId(destTenantId);
            tplTenantRelaDO.setCreateTime(new Date());
            tplTenantRelaDO.setModifyTime(new Date());
            addTplTenantRelaDOList.add(tplTenantRelaDO);
        } else {
            existRelaDO.setModifyTime(new Date());
            updateTplTenantRelaDOList.add(existRelaDO);

        }
    }

    public void copyPubilcFlowAndDataparserValue(String sourceCollectorId,
                                                 CollectorDO destCollectorDO,
                                                 List<CollectorFieldDO> addCollectorFieldDOList,
                                                 List<MetricsDO> addMetricsDOList,
                                                 List<MetricsDO> updateMetricsDOList,
                                                 List<CollectorMetricsDO> addCollectorMetricsDOList,
                                                 List<MetricsFieldDO> addMetricsFieldDOList,
                                                 List<MetricsAggregationDO> addMetricsAggregationDOList,
                                                 List<MetricsAggregationRuleDO> addMetricsAggregationRuleDOList,
                                                 List<AggregationFunctionItemDO> addAggregationFunctionItemDOList,
                                                 List<AggregationHistogramRangeItemDO> addAggregationHistogramRangeItemDOList,
                                                 List<AggregationPercentileItemDO> addAggregationPercentileItemDOList,
                                                 List<AggregationCustomFieldRuleDO> addAggregationCustomFieldRuleDOList,
                                                 List<AlarmDefinition> addAlarmDefinitionList,
                                                 List<ResourceTagDo> addResourceTagDoList,
                                                 String userId,
                                                 String destTenantId,
                                                 Channel destChannel,
                                                 UserDO userDO,
                                                 Boolean bind) {
        copyCollectorFields(sourceCollectorId, destCollectorDO.getId(), addCollectorFieldDOList);

        List<CollectorMetricsDO> sourceCollectorMetricsDOList = collectorHandler.listMetricsCollectorByCollectorIds(Arrays.asList(sourceCollectorId));
        if (!CollectionUtils.isEmpty(sourceCollectorMetricsDOList)) {
            sourceCollectorMetricsDOList.forEach(collectorMetricsDO -> {
                MetricsDO sourceMetricsDO = metricsHandler.getMetricsById(collectorMetricsDO.getMetricsId());
                MetricsDO destMetricsDO = copyMetrics(sourceMetricsDO,
                        destTenantId,
                        destCollectorDO,
                        addMetricsDOList,
                        addResourceTagDoList,
                        updateMetricsDOList,
                        addMetricsAggregationDOList,
                        addMetricsAggregationRuleDOList,
                        addAggregationFunctionItemDOList,
                        addAggregationHistogramRangeItemDOList,
                        addAggregationPercentileItemDOList,
                        addAggregationCustomFieldRuleDOList,
                        addMetricsFieldDOList,
                        addCollectorMetricsDOList,
                        userDO,
                        bind);

                List<AlarmDefinition> sourceAlarmDefinitionList = alarmDefinitionHandler.findByMetricId(collectorMetricsDO.getMetricsId());
                if (CollectionUtils.isEmpty(sourceAlarmDefinitionList)) {
                    return;
                }
                sourceAlarmDefinitionList.forEach(sourceAlarmDefinition -> {
                    copyAlarmDefinition(
                            userId,
                            sourceAlarmDefinition,
                            destMetricsDO.getId(),
                            destTenantId,
                            destChannel,
                            addAlarmDefinitionList,
                            userDO,
                            addResourceTagDoList);
                });
            });
        }
    }

    public void handlerDestNotification(AlarmDefinition sourceAlarmDefinition,
                                        AlarmDefinition destAlarmDefinition,
                                        String destMetricsId,
                                        String destTenantId, String userId,
                                        UserDO userDO) {
        BeanUtils.copyProperties(sourceAlarmDefinition, destAlarmDefinition);
        destAlarmDefinition.setId(null);
        destAlarmDefinition.setCreateTime(new Date());
        destAlarmDefinition.setMetricId(destMetricsId);
        destAlarmDefinition.setModifyTime(new Date());
        destAlarmDefinition.setTenantId(destTenantId);
        destAlarmDefinition.setUserId(userId);
        destAlarmDefinition.setCreator(userDO.getName());
        destAlarmDefinition.setEditor(userDO.getName());
    }

    private void handlerNotification(Notification notification, Notification destNotification, String destTenantId,
                                     String userId, String channelId) {
        BeanUtils.copyProperties(notification, destNotification);
        destNotification.setId(null);
        destNotification.setCreateTime(new Date());
        destNotification.setModifyTime(new Date());
        destNotification.setTenantId(destTenantId);
        destNotification.setUserId(userId);
        Channel channel = new Channel();
        channel.setId(channelId);
        destNotification.setChannel(channel);
    }

    private void handlerAlarmExtensionRelation(AlarmExtensionRelation alarmExtensionRelation, AlarmExtensionRelation destAlarmExtensionRelation) {
        BeanUtils.copyProperties(alarmExtensionRelation, destAlarmExtensionRelation);
        destAlarmExtensionRelation.setId(null);
        destAlarmExtensionRelation.setCreateTime(new Date());
        destAlarmExtensionRelation.setModifyTime(new Date());
    }

    private void copyAlarmDefinition(String userId,
                                     AlarmDefinition sourceAlarmDefinition,
                                     String destMetricsId,
                                     String destTenantId,
                                     Channel destChannel,
                                     List<AlarmDefinition> addAlarmDefinitionList,
                                     UserDO userDO,
                                     List<ResourceTagDo> addResourceTagDoList) {
        Optional<AlarmDefinition> alarmDefinitionOptional = alarmDefinitionHandler.findByNameAndTenantId(sourceAlarmDefinition.getName(), destTenantId);
        AlarmDefinition destAlarmDefinition = null;
        List<ResourceTagDo> sourceResourceTag = resourceTagDAO.findByResourceId(sourceAlarmDefinition.getId(), RESOURCE_AlARM_TYPE, sourceAlarmDefinition.getTenantId());
        if (!alarmDefinitionOptional.isPresent()) {
            destAlarmDefinition = new AlarmDefinition();
            handlerDestNotification(sourceAlarmDefinition, destAlarmDefinition, destMetricsId, destTenantId, userId, userDO);

            List<Notification> destNotificationList = new ArrayList<>();
            destAlarmDefinition.setNotifications(destNotificationList);
            List<Notification> sourceNotifications = Optional.ofNullable(sourceAlarmDefinition.getNotifications()).orElse(Collections.emptyList());
            if (!sourceNotifications.isEmpty()) {
                sourceNotifications.forEach(source -> {
                    Notification destNotification = new Notification();
                    handlerNotification(source, destNotification, destTenantId, userId, destChannel.getId());
                    destNotificationList.add(destNotification);
                });
            }

            if (!CollectionUtils.isEmpty(sourceAlarmDefinition.getAlarmExtensionRelations())) {
                List<AlarmExtensionRelation> alarmExtensionRelations = new ArrayList<>();
                sourceAlarmDefinition.getAlarmExtensionRelations().forEach(sourceRelation -> {
                    AlarmExtensionRelation alarmExtensionRelation = new AlarmExtensionRelation();
                    handlerAlarmExtensionRelation(sourceRelation, alarmExtensionRelation);
                    alarmExtensionRelations.add(alarmExtensionRelation);
                });
                destAlarmDefinition.setAlarmExtensionRelations(alarmExtensionRelations);
            }

            if (!CollectionUtils.isEmpty(sourceAlarmDefinition.getAlarmExtensionRelations())) {
                List<AlarmExtensionRelation> alarmExtensionRelations = new ArrayList<>();
                sourceAlarmDefinition.getAlarmExtensionRelations().forEach(sourceRelation -> {
                    AlarmExtensionRelation alarmExtensionRelation = new AlarmExtensionRelation();
                    templateService.handlerAlarmExtensionRelation(sourceRelation, alarmExtensionRelation);
                    alarmExtensionRelations.add(alarmExtensionRelation);
                });

                destAlarmDefinition.setAlarmExtensionRelations(alarmExtensionRelations);
            }

            List<AlarmRule> destAlarmRuleList = new ArrayList<>();
            templateService.handlerAlarmRule(destAlarmRuleList, destAlarmDefinition, sourceAlarmDefinition, addAlarmDefinitionList);
            copyLabelDO(sourceResourceTag, addResourceTagDoList, destTenantId, userDO, destAlarmDefinition.getId(), new HashSet<>(), destAlarmDefinition.getName());
        } else {
            destAlarmDefinition = alarmDefinitionOptional.get();

            List<ResourceTagDo> destResourceTag = resourceTagDAO.findByResourceId(destAlarmDefinition.getId(), RESOURCE_AlARM_TYPE, destAlarmDefinition.getTenantId());
            copyLabelDO(sourceResourceTag, addResourceTagDoList, destTenantId, userDO, destAlarmDefinition.getId(),
                    destResourceTag.stream()
                            .map(ResourceTagDo::getTagId)
                            .collect(Collectors.toSet()), destAlarmDefinition.getName());
        }
    }

    public void addCopyMetricsDO(MetricsDO destMetricsDO,
                                 MetricsDO sourceMetrics,
                                 List<MetricsDO> addMetricsDOList,
                                 String destTenantId,
                                 UserDO userDO) {
        BeanUtils.copyProperties(sourceMetrics, destMetricsDO);
        String DestMetricsId = IdUtils.generateId();
        destMetricsDO.setCreateTime(new Date());
        destMetricsDO.setModifyTime(new Date());
        destMetricsDO.setId(DestMetricsId);
        destMetricsDO.setTenantId(destTenantId);
        destMetricsDO.setCreator(userDO.getName());
        destMetricsDO.setEditor(userDO.getName());
        addMetricsDOList.add(destMetricsDO);
    }

    private boolean copyLabelDO(List<ResourceTagDo> sourceResourceTag,
                                List<ResourceTagDo> addResourceTagDoList,
                                String destTenantId,
                                UserDO userDO,
                                String commonId,
                                Set<String> existTagIdList,
                                String commonName) {
        if (CollectionUtils.isEmpty(sourceResourceTag)) {
            return false;
        }
        final Date currentDate = new Date();
        final String userId = userDO.getId();
        boolean hasNewElements = false;
        for (ResourceTagDo tag : sourceResourceTag) {
            if (!existTagIdList.contains(tag.getTagId())) {
                addResourceTagDoList.add(
                        createDestTag(tag, destTenantId, userId, commonId, currentDate, commonName)
                );
                hasNewElements = true;
            }
        }
        return hasNewElements;
    }

    private ResourceTagDo createDestTag(ResourceTagDo sourceTag,
                                        String tenantId,
                                        String userId,
                                        String commonId,
                                        Date currentDate,
                                        String commonName) {
        ResourceTagDo destTag = new ResourceTagDo();
        BeanUtils.copyProperties(sourceTag, destTag);
        destTag.setId(IdUtils.generateId());
        destTag.setTenantId(tenantId);
        destTag.setResource(commonId);
        destTag.setCreator(userId);
        destTag.setModifier(userId);
        destTag.setGmtCreate(currentDate);
        destTag.setGmtModify(currentDate);
        destTag.setCommonName(commonName);
        return destTag;
    }

    public MetricsAggregationDO copyMetricsAggregation(MetricsAggregationDO sourceMetricsAggregationDO, String destMetricsId, List<MetricsAggregationDO> addMetricsAggregationDOList) {
        MetricsAggregationDO destMetricsAggregationDO = new MetricsAggregationDO();
        BeanUtils.copyProperties(sourceMetricsAggregationDO, destMetricsAggregationDO);
        destMetricsAggregationDO.setId(IdUtils.generateId());
        destMetricsAggregationDO.setMetricsId(destMetricsId);
        destMetricsAggregationDO.setCreateTime(new Date());
        destMetricsAggregationDO.setModifyTime(new Date());
        addMetricsAggregationDOList.add(destMetricsAggregationDO);
        return destMetricsAggregationDO;
    }

    private MetricsDO copyMetrics(MetricsDO sourceMetrics,
                                  String destTenantId,
                                  CollectorDO destCollectorDO,
                                  List<MetricsDO> addMetricsDOList,
                                  List<ResourceTagDo> addResourceTagDoList,
                                  List<MetricsDO> updateMetricsDOList,
                                  List<MetricsAggregationDO> addMetricsAggregationDOList,
                                  List<MetricsAggregationRuleDO> addMetricsAggregationRuleDOList,
                                  List<AggregationFunctionItemDO> addAggregationFunctionItemDOList,
                                  List<AggregationHistogramRangeItemDO> addAggregationHistogramRangeItemDOList,
                                  List<AggregationPercentileItemDO> addAggregationPercentileItemDOList,
                                  List<AggregationCustomFieldRuleDO> addAggregationCustomFieldRuleDOList,
                                  List<MetricsFieldDO> addMetricsFieldDOList,
                                  List<CollectorMetricsDO> addCollectorMetricsDOList,
                                  UserDO userDO,
                                  Boolean bind) {
        String sourceMetricsId = sourceMetrics.getId();
        MetricsDO destMetricsDO = metricsHandler.findMetricsByNameAndTenantId(sourceMetrics.getMetricsName(), destTenantId);
        if (bind && null != destMetricsDO) {
            Assert.isTrue(false, "You have the same name metrics as the template, metrics name is " + destMetricsDO.getMetricsName());
        }
        List<ResourceTagDo> sourceResourceTag = resourceTagDAO.findByResourceId(sourceMetricsId, RESOURCE_METRIC_TYPE, sourceMetrics.getTenantId());

        if (null == destMetricsDO) {
            destMetricsDO = new MetricsDO();
            addCopyMetricsDO(destMetricsDO, sourceMetrics, addMetricsDOList, destTenantId, userDO);
            Map<String, String> metricsFieldIdSource2DestMap = new HashMap<>();
            List<MetricsFieldDO> sourceMetricsFieldDOList = metricsHandler.listFieldsByMetricsIds(Arrays.asList(sourceMetricsId));
            templateService.copyMetricsFields(sourceMetricsFieldDOList, destMetricsDO.getId(), addMetricsFieldDOList, metricsFieldIdSource2DestMap);
            copyLabelDO(sourceResourceTag, addResourceTagDoList, destTenantId, userDO, destMetricsDO.getId(), new HashSet<>(), destMetricsDO.getMetricsName());
            if (destMetricsDO.getType() == MetricsTypeEnum.AGGREGATION.getValue()) {
                MetricsAggregationDO sourceMetricsAggregationDO = metricsAggregationHandler.getByMetricsId(sourceMetricsId);
                if (sourceMetricsAggregationDO == null) {
                    Assert.isTrue(false, "sourceMetricsAggregationDO is missing! sourceMetricsId = " + sourceMetricsId);
                }
                MetricsAggregationDO destMetricsAggregationDO = copyMetricsAggregation(sourceMetricsAggregationDO, destMetricsDO.getId(), addMetricsAggregationDOList);
                List<MetricsAggregationRuleDO> sourceMetricsAggregationRuleDOList = metricsAggregationRuleHandler.listRulesByAggIds(Arrays.asList(sourceMetricsAggregationDO.getId()));
                Map<String, String> ruleIdSource2DestMap = new HashMap<>();
                templateService.copyMetricsAggregationRules(sourceMetricsAggregationRuleDOList, destMetricsAggregationDO.getId(), ruleIdSource2DestMap, addMetricsAggregationRuleDOList);
                List<String> sourceRuleIdList = Instance.ofNullable(sourceMetricsAggregationRuleDOList).stream().map(MetricsAggregationRuleDO::getId).collect(Collectors.toList());

                templateService.copyFunctionItems(sourceRuleIdList, sourceMetricsFieldDOList, metricsFieldIdSource2DestMap, ruleIdSource2DestMap, addAggregationFunctionItemDOList);
                templateService.copyHistogramRangeItems(sourceRuleIdList, sourceMetricsFieldDOList, metricsFieldIdSource2DestMap, ruleIdSource2DestMap, addAggregationHistogramRangeItemDOList);
                templateService.copyPercentileItems(sourceRuleIdList, sourceMetricsFieldDOList, metricsFieldIdSource2DestMap, ruleIdSource2DestMap, addAggregationPercentileItemDOList);
                templateService.copyCustomFieldRules(sourceMetricsAggregationDO.getId(), destMetricsAggregationDO.getId(), sourceMetricsFieldDOList, metricsFieldIdSource2DestMap, addAggregationCustomFieldRuleDOList);
            }
        } else {
            // Judge metrics tag
            String destMetricsTags = destMetricsDO.getTagNames();
            String sourceMetricsTags = sourceMetrics.getTagNames();
            Set<String> set1 = splitAndTrimToSet(destMetricsTags);
            Set<String> set2 = splitAndTrimToSet(sourceMetricsTags);
            boolean needUpdate = !set1.equals(set2);

            // Judge label
            List<ResourceTagDo> destResourceTag = resourceTagDAO.findByResourceId(destMetricsDO.getId(), RESOURCE_METRIC_TYPE, destTenantId);
            needUpdate = needUpdate || copyLabelDO(
                    sourceResourceTag, addResourceTagDoList,
                    destTenantId, userDO, destMetricsDO.getId(),
                    destResourceTag.stream().map(ResourceTagDo::getTagId).collect(Collectors.toSet()),
                    destMetricsDO.getMetricsName()
            );

            destMetricsDO.setModifyTime(new Date());
            destMetricsDO.setTenantId(destTenantId);
            destMetricsDO.setEditor(userDO.getName());
            
            List<String> destTagNames = destMetricsTags != null ?
                Arrays.stream(destMetricsTags.split(",")).collect(Collectors.toList()) : new ArrayList<>();
            List<String> sourceTagNames = sourceMetricsTags != null ? 
                Arrays.stream(sourceMetricsTags.split(",")).toList() : new ArrayList<>();
            destTagNames.addAll(sourceTagNames);
            destMetricsDO.setTagNames(destTagNames.stream().distinct().collect(Collectors.joining(",")));
            destMetricsDO.setDocumentLink(sourceMetrics.getDocumentLink());

            boolean hasNewElements = false;
            List<MetricsFieldDO> sourceMetricsFieldDOList = metricsHandler.listFieldsByMetricsIds(Collections.singletonList(sourceMetricsId));
            if (!CollectionUtils.isEmpty(sourceMetricsFieldDOList) && destMetricsDO.getType() == MetricsTypeEnum.ORIGINAL.getValue()) {
                Set<String> sourceMetricsName = sourceMetricsFieldDOList.stream().map(MetricsFieldDO::getFieldName).collect(Collectors.toSet());
                Map<String, MetricsFieldDO> sourceMetricsFieldMaps = sourceMetricsFieldDOList.stream().collect(Collectors.toMap(MetricsFieldDO::getFieldName, m -> m));
                String destMetricsFiledId = destMetricsDO.getId();

                List<MetricsFieldDO> destMetricsFieldDOList = metricsHandler.listFieldsByMetricsIds(Collections.singletonList(destMetricsFiledId));
                Set<String> destMetricsName = destMetricsFieldDOList.stream()
                        .map(MetricsFieldDO::getFieldName)
                        .map(String::toLowerCase)
                        .collect(Collectors.toSet());

                for (String fieldName : sourceMetricsName) {
                    if (!destMetricsName.contains(fieldName.toLowerCase())) {
                        hasNewElements = true;
                        MetricsFieldDO destMetricsFieldDO = new MetricsFieldDO();
                        BeanUtils.copyProperties(sourceMetricsFieldMaps.get(fieldName), destMetricsFieldDO);
                        destMetricsFieldDO.setMetricsId(destMetricsFiledId);
                        destMetricsFieldDO.setId(IdUtils.generateId());
                        destMetricsFieldDO.setModifyTime(new Date());
                        destMetricsFieldDO.setCreateTime(new Date());
                        addMetricsFieldDOList.add(destMetricsFieldDO);
                    }
                }
            } else {
                MetricsAggregationDO sourceMetricsAggregationDO = metricsAggregationHandler.getByMetricsId(sourceMetricsId);
                if (sourceMetricsAggregationDO != null) {
                    MetricsAggregationDO destMetricsAggregationDO = metricsAggregationHandler.getByMetricsId(destMetricsDO.getId());
                    if (destMetricsAggregationDO == null) {
                        destMetricsAggregationDO = new MetricsAggregationDO();
                        BeanUtils.copyProperties(sourceMetricsAggregationDO, destMetricsAggregationDO);
                        destMetricsAggregationDO.setId(IdUtils.generateId());
                        destMetricsAggregationDO.setMetricsId(destMetricsDO.getId());
                        destMetricsAggregationDO.setCreateTime(new Date());
                        destMetricsAggregationDO.setModifyTime(new Date());
                        addMetricsAggregationDOList.add(destMetricsAggregationDO);
                        hasNewElements = true;
                    }
                    
                    List<MetricsAggregationRuleDO> sourceMetricsAggregationRuleDOList = metricsAggregationRuleHandler.listRulesByAggIds(Collections.singletonList(sourceMetricsAggregationDO.getId()));
                    List<MetricsAggregationRuleDO> destMetricsAggregationRuleDOList = metricsAggregationRuleHandler.listRulesByAggIds(Collections.singletonList(destMetricsAggregationDO.getId()));
                    
                    Map<String, String> sourceRuleIdToDestRuleIdMap = new HashMap<>();
                    for (MetricsAggregationRuleDO sourceRule : sourceMetricsAggregationRuleDOList) {
                        MetricsAggregationRuleDO existingDestRule = destMetricsAggregationRuleDOList.stream()
                            .filter(destRule -> destRule.getAggField().equals(sourceRule.getAggField()) && 
                                                destRule.getAggRuleType().equals(sourceRule.getAggRuleType()))
                            .findFirst()
                            .orElse(null);
                        
                        if (existingDestRule == null) {
                            MetricsAggregationRuleDO destRule = new MetricsAggregationRuleDO();
                            BeanUtils.copyProperties(sourceRule, destRule);
                            destRule.setId(IdUtils.generateId());
                            destRule.setAggId(destMetricsAggregationDO.getId());
                            destRule.setCreateTime(new Date());
                            destRule.setModifyTime(new Date());
                            addMetricsAggregationRuleDOList.add(destRule);
                            sourceRuleIdToDestRuleIdMap.put(sourceRule.getId(), destRule.getId());
                            hasNewElements = true;
                        } else {
                            sourceRuleIdToDestRuleIdMap.put(sourceRule.getId(), existingDestRule.getId());
                        }
                    }

                    if (!sourceRuleIdToDestRuleIdMap.isEmpty()) {
                        List<String> allDestRuleIds = new ArrayList<>(sourceRuleIdToDestRuleIdMap.values());
                        List<AggregationFunctionItemDO> existingDestFunctionItems = metricsAggregationRuleHandler.listFunctionItemsByRuleIds(allDestRuleIds);
                        
                        List<String> sourceRuleIds = new ArrayList<>(sourceRuleIdToDestRuleIdMap.keySet());
                        List<AggregationFunctionItemDO> sourceFunctionItemDOList = metricsAggregationRuleHandler.listFunctionItemsByRuleIds(sourceRuleIds);
                        
                        for (AggregationFunctionItemDO sourceFunctionItem : sourceFunctionItemDOList) {
                            if (AggregationOperatorEnum.sample.name().equals(sourceFunctionItem.getAggType())) {
                                continue;
                            }

                            String destRuleId = sourceRuleIdToDestRuleIdMap.get(sourceFunctionItem.getMetricsAggregationRuleId());
                            if (destRuleId != null) {
                                boolean functionItemExists = existingDestFunctionItems.stream()
                                    .anyMatch(destItem -> destItem.getMetricsAggregationRuleId().equals(destRuleId) && 
                                                        destItem.getAggType().equals(sourceFunctionItem.getAggType()));
                                
                                if (!functionItemExists) {
                                    String expectedFieldName = null;
                                    MetricsAggregationRuleDO sourceRule = sourceMetricsAggregationRuleDOList.stream()
                                        .filter(rule -> rule.getId().equals(sourceFunctionItem.getMetricsAggregationRuleId()))
                                        .findFirst()
                                        .orElse(null);
                                    
                                    if (sourceRule != null) {
                                        String fieldPrefix = sourceRule.getFieldPrefix();
                                        expectedFieldName = fieldPrefix + CommonSplitConstants.METRICS_FIELD_SPLIT + sourceFunctionItem.getAggType();
                                    }

                                    MetricsFieldDO existingDestField = null;
                                    final String finalExpectedFieldName = expectedFieldName;
                                    if (expectedFieldName != null) {
                                        existingDestField = metricsHandler.listFieldsByMetricsIds(Collections.singletonList(destMetricsDO.getId()))
                                            .stream()
                                            .filter(field -> field.getFieldName().equals(finalExpectedFieldName))
                                            .findFirst()
                                            .orElse(null);
                                    }
                                    
                                    if (existingDestField == null) {
                                        MetricsFieldDO destMetricsField = new MetricsFieldDO();
                                        destMetricsField.setId(IdUtils.generateId());
                                        destMetricsField.setMetricsId(destMetricsDO.getId());
                                        destMetricsField.setFieldType(MetricsFieldTypeEnum.number.getValue());

                                        destMetricsField.setFieldName(expectedFieldName);
                                        destMetricsField.setCreateTime(new Date());
                                        destMetricsField.setModifyTime(new Date());
                                        addMetricsFieldDOList.add(destMetricsField);
                                        existingDestField = destMetricsField;
                                    }

                                    AggregationFunctionItemDO destFunctionItem = new AggregationFunctionItemDO();
                                    BeanUtils.copyProperties(sourceFunctionItem, destFunctionItem);
                                    destFunctionItem.setId(IdUtils.generateId());
                                    destFunctionItem.setMetricsAggregationRuleId(destRuleId);
                                    destFunctionItem.setTargetFieldId(existingDestField.getId());
                                    destFunctionItem.setCreateTime(new Date());
                                    destFunctionItem.setModifyTime(new Date());
                                    addAggregationFunctionItemDOList.add(destFunctionItem);
                                    hasNewElements = true;
                                }
                            }
                        }
                    }
                }
            }
            needUpdate = needUpdate || hasNewElements;
            if (needUpdate) {
                updateMetricsDOList.add(destMetricsDO);
            }
        }
        copyCollectorMetrics(destMetricsDO.getId(), destCollectorDO.getId(), addCollectorMetricsDOList);
        return destMetricsDO;
    }

    private Set<String> splitAndTrimToSet(String input) {
        return input == null ? Set.of() :
                Arrays.stream(input.split(COMMA_SPLIT))
                        .map(String::trim)
                        .filter(s -> !s.isEmpty())
                        .collect(Collectors.toSet());
    }

    public void copyCollectorMetrics(String destMetricsId, String destCollectorId, List<CollectorMetricsDO> addCollectorMetricsDOList) {
        CollectorMetricsDO destCollectorMetricsDO = collectorHandler.getCollectorIdMetricsIdAndCollectorId(destMetricsId, destCollectorId);
        if (null == destCollectorMetricsDO) {
            destCollectorMetricsDO = new CollectorMetricsDO();
            templateService.addCopyCollectorMetrics(destCollectorMetricsDO, destMetricsId, destCollectorId, addCollectorMetricsDOList);
        } else {
            log.info("already exist collectorMetrics, id = {}", destCollectorMetricsDO.getId());
        }
    }

    public void copyCollectorFields(String sourceCollectorId, String destCollectorId, List<CollectorFieldDO> addCollectorFieldDOList) {
        List<CollectorFieldDO> sourceFields = collectorHandler.listFieldByCollectorIds(Arrays.asList(sourceCollectorId));
        if (CollectionUtils.isEmpty(sourceFields)) {
            return;
        }

        List<CollectorFieldDO> destFields = collectorHandler.listFieldByCollectorIds(Arrays.asList(destCollectorId));
        Set<String> destFieldsName = Instance.ofNullable(destFields)
                .stream()
                .map(CollectorFieldDO::getSourceField)
                .map(String::toLowerCase)
                .collect(Collectors.toSet());

        for (CollectorFieldDO sourceField : sourceFields) {
            if (destFieldsName.contains(sourceField.getSourceField().toLowerCase())) {
                log.info("already exists collectorField, name = {}", sourceField.getSourceField());
                continue;
            }
            CollectorFieldDO destField = new CollectorFieldDO();
            templateHandler.addCopyCollectorFields(destField, sourceField, destCollectorId, addCollectorFieldDOList);
        }
    }

    public CollectorDO copyCollector(DataParserPipelineDO sourcePipeline, String tenantId, DataParserPipelineDO destPipeLine, List<CollectorDO> addCollectorDOList) {
        if (StringUtils.isEmpty(sourcePipeline.getCollectorId())) {
            return null;
        }

        CollectorDO sourceCollector = collectorHandler.getCollectorById(sourcePipeline.getCollectorId());
        if (null == sourceCollector) {
            return null;
        }

        String destCollectorId = destPipeLine.getCollectorId();
        CollectorDO destCollector;

        if (StringUtils.isEmpty(destCollectorId)) {
            destCollectorId = IdUtils.generateId();
            destCollector = new CollectorDO();
            templateHandler.addCopyCollector(destCollectorId, destCollector, sourceCollector, tenantId, addCollectorDOList, destPipeLine);
        } else {
            destCollector = collectorHandler.getCollectorById(destCollectorId);
        }
        return destCollector;
    }

    private void copyFlowProcessor(DataParserPipelineDO sourcePipeline, String destPipelineId,
                                   List<DataParserFilterProcessorDO> addFlowDataParserFilterProcessorDOList,
                                   List<DataParserGroovyProcessorDO> addFlowDataParserGroovyProcessorDOList,
                                   List<DataParserRemapperProcessorDO> addFlowDataParserRemapperProcessorDOList,
                                   List<DataParserTimestampProcessorDO> addDataParserTimestampProcessorDOList,
                                   List<DataParserIpProcessorDO> addDataParserIpProcessorDOList) {
        copyFilter(sourcePipeline, destPipelineId, addFlowDataParserFilterProcessorDOList);
        copyGroovy(sourcePipeline, destPipelineId, addFlowDataParserGroovyProcessorDOList);
        copyRemapper(sourcePipeline, destPipelineId, addFlowDataParserRemapperProcessorDOList);
        copyTimeStamp(sourcePipeline, destPipelineId, addDataParserTimestampProcessorDOList);
        copyIpProcessor(sourcePipeline, destPipelineId, addDataParserIpProcessorDOList);
    }

    private void copyDataparserProcessor(DataParserPipelineDO sourcePipeline, String destPipelineId,
                                         List<DataParserRemapperProcessorDO> addDataParserRemapperProcessorDOList,
                                         List<DataParserFilterProcessorDO> addDataParserFilterProcessorDOList,
                                         List<DataParserGroovyProcessorDO> addDataParserGroovyProcessorDOList,
                                         List<DataParserTimestampProcessorDO> addDataParserTimestampProcessorDOList,
                                         List<DataParserIpProcessorDO> addDataParserIpProcessorDOList) {
        copyFilter(sourcePipeline, destPipelineId, addDataParserFilterProcessorDOList);
        copyGroovy(sourcePipeline, destPipelineId, addDataParserGroovyProcessorDOList);
        copyRemapper(sourcePipeline, destPipelineId, addDataParserRemapperProcessorDOList);
        copyTimeStamp(sourcePipeline, destPipelineId, addDataParserTimestampProcessorDOList);
        copyIpProcessor(sourcePipeline, destPipelineId, addDataParserIpProcessorDOList);
    }

    public void copyFilter(DataParserPipelineDO sourcePipeline, String destPipelineId, List<DataParserFilterProcessorDO> addDataParserFilterProcessorDOList) {

        List<DataParserFilterProcessorDO> sourceFilterProcessorDOList = dataParserFilterProcessorHandler.listByPipelineIds(Arrays.asList(sourcePipeline.getId()));
        List<DataParserFilterProcessorDO> destFilterProcessorDOList = dataParserFilterProcessorHandler.listByPipelineIds(Arrays.asList(destPipelineId));
        if (CollectionUtils.isEmpty(sourceFilterProcessorDOList)) {
            return;
        }
        if (CollectionUtils.isEmpty(destFilterProcessorDOList)) {
            sourceFilterProcessorDOList.forEach(filterProcessor -> {
                DataParserFilterProcessorDO destProcessor = new DataParserFilterProcessorDO();
                BeanUtils.copyProperties(filterProcessor, destProcessor);
                destProcessor.setId(IdUtils.generateId());
                destProcessor.setDataParserPipelineId(destPipelineId);
                destProcessor.setGmtCreate(new Date());
                destProcessor.setGmtModify(new Date());
                addDataParserFilterProcessorDOList.add(destProcessor);
            });

        } else {
            // TODO how to update
        }
    }

    public void copyGroovy(DataParserPipelineDO sourcePipeline, String destPipelineId, List<DataParserGroovyProcessorDO> addDataParserGroovyProcessorDOList) {
        List<DataParserGroovyProcessorDO> sourceProcessorDOList = dataParserGroovyProcessorHandler.listByPipelineIds(Arrays.asList(sourcePipeline.getId()));
        List<DataParserGroovyProcessorDO> destParserProcessorDOList = dataParserGroovyProcessorHandler.listByPipelineIds(Arrays.asList(destPipelineId));
        if (CollectionUtils.isEmpty(sourceProcessorDOList)) {
            return;
        }
        if (CollectionUtils.isEmpty(destParserProcessorDOList)) {
            sourceProcessorDOList.forEach(remapperProcessor -> {
                DataParserGroovyProcessorDO destProcessor = new DataParserGroovyProcessorDO();
                BeanUtils.copyProperties(remapperProcessor, destProcessor);
                destProcessor.setId(IdUtils.generateId());
                destProcessor.setDataParserPipelineId(destPipelineId);
                destProcessor.setGmtCreate(new Date());
                destProcessor.setGmtModify(new Date());
                addDataParserGroovyProcessorDOList.add(destProcessor);
            });

        } else {
            // TODO how to update
        }
    }

    public void copyRemapper(DataParserPipelineDO sourcePipeline, String destPipelineId, List<DataParserRemapperProcessorDO> addDataParserRemapperProcessorDOList) {
        List<DataParserRemapperProcessorDO> sourceRemapperProcessorDOList = dataParserRemapperProcessorHandler.listByPipelineIds(Arrays.asList(sourcePipeline.getId()));
        List<DataParserRemapperProcessorDO> destParserRemapperProcessorDOList = dataParserRemapperProcessorHandler.listByPipelineIds(Arrays.asList(destPipelineId));
        if (CollectionUtils.isEmpty(sourceRemapperProcessorDOList)) {
            return;
        }
        if (CollectionUtils.isEmpty(destParserRemapperProcessorDOList)) {
            sourceRemapperProcessorDOList.forEach(remapperProcessor -> {
                DataParserRemapperProcessorDO destProcessor = new DataParserRemapperProcessorDO();
                BeanUtils.copyProperties(remapperProcessor, destProcessor);
                destProcessor.setId(IdUtils.generateId());
                destProcessor.setDataParserPipelineId(destPipelineId);
                destProcessor.setGmtCreate(new Date());
                destProcessor.setGmtModify(new Date());
                addDataParserRemapperProcessorDOList.add(destProcessor);
            });

        } else {
            // TODO how to update
        }
    }

    public void copyTimeStamp(DataParserPipelineDO sourcePipeline, String destPipelineId, List<DataParserTimestampProcessorDO> addDataParserTimestampProcessorDOList) {
        List<DataParserTimestampProcessorDO> sourceTimeStampProcessorDOList = dataParserTimestampProcessorHandler.listByPipelineIds(Arrays.asList(sourcePipeline.getId()));
        List<DataParserTimestampProcessorDO> destParserTimeStampProcessorDOList = dataParserTimestampProcessorHandler.listByPipelineIds(Arrays.asList(destPipelineId));
        if (CollectionUtils.isEmpty(sourceTimeStampProcessorDOList)) {
            return;
        }
        if (CollectionUtils.isEmpty(destParserTimeStampProcessorDOList)) {
            sourceTimeStampProcessorDOList.forEach(remapperProcessor -> {
                DataParserTimestampProcessorDO destProcessor = new DataParserTimestampProcessorDO();
                BeanUtils.copyProperties(remapperProcessor, destProcessor);
                destProcessor.setId(IdUtils.generateId());
                destProcessor.setDataParserPipelineId(destPipelineId);
                destProcessor.setGmtCreate(new Date());
                destProcessor.setGmtModify(new Date());
                addDataParserTimestampProcessorDOList.add(destProcessor);
            });
        }
    }

    public void copyIpProcessor(DataParserPipelineDO sourcePipeline, String destPipelineId, List<DataParserIpProcessorDO> addDataParserIpProcessorDOList) {
        List<DataParserIpProcessorDO> sourceIpProcessorDOList = dataParserIpProcessorHandler.listByPipelineIds(Arrays.asList(sourcePipeline.getId()));
        List<DataParserIpProcessorDO> destParserIpProcessorDOList = dataParserIpProcessorHandler.listByPipelineIds(Arrays.asList(destPipelineId));
        if (CollectionUtils.isEmpty(sourceIpProcessorDOList)) {
            return;
        }
        if (CollectionUtils.isEmpty(destParserIpProcessorDOList)) {
            sourceIpProcessorDOList.forEach(processorDO -> {
                DataParserIpProcessorDO destProcessor = new DataParserIpProcessorDO();
                BeanUtils.copyProperties(processorDO, destProcessor);
                destProcessor.setId(IdUtils.generateId());
                destProcessor.setDataParserPipelineId(destPipelineId);
                destProcessor.setGmtCreate(new Date());
                destProcessor.setGmtModify(new Date());
                addDataParserIpProcessorDOList.add(destProcessor);
            });
        }
    }

    private DataParserPipelineDO copyPipeline(String name, String destTenantId,
                                              List<DataParserPipelineDO> addFlowPipelineDOList,
                                              List<DataParserPipelineDO> updateFlowPipelineDOList,
                                              DataParserPipelineDO sourcePipeline,
                                              String id,
                                              boolean bind,
                                              TenantDO destTenantDO) {
        DataParserPipelineDO destPipeline = dataParserPipelineHandler.getByTenantIdAndName(destTenantId, name);
        if (bind && null != destPipeline) {
            Assert.isTrue(false, "You have the same name pipeline as the template, pipeline name: " + name);
        }
        if (null == destPipeline) {
            destPipeline = new DataParserPipelineDO();
            templateHandler.addCopyPipelineDO(destPipeline, sourcePipeline, id, destTenantId, addFlowPipelineDOList);
        } else {
            DataParserPipelineDO tmpDO = new DataParserPipelineDO();
            templateHandler.updateCopyPipelineDO(tmpDO, destPipeline, sourcePipeline, updateFlowPipelineDOList);
        }
        return destPipeline;
    }

    private DataParserSourceDO copyDataParserSource(String name,
                                                    TenantDO destTenantDO,
                                                    DataParserSourceDO dataParserSourceDO,
                                                    List<DataParserSourceDO> addFlowDataParserSourceDOList,
                                                    List<DataParserSourceDO> updateFlowDataParserSourceDOList,
                                                    boolean bind,
                                                    Map<String, String> cubeTemplateItemServiceRelaDOHashMap,
                                                    String userId) {
        String destDataparserId = cubeTemplateItemServiceRelaDOHashMap.get(dataParserSourceDO.getId());
        DataParserSourceDO destDataParserSourceDO = dataParserSourceHandler.getById(destDataparserId);
        if (Objects.isNull(destDataParserSourceDO)) {
            destDataParserSourceDO = new DataParserSourceDO();
            addCopyDataParserSource(destDataParserSourceDO,
                    dataParserSourceDO,
                    destTenantDO.getId(),
                    addFlowDataParserSourceDOList,
                    userId);
        } else {
            if (bind) {
                Assert.isTrue(false, "already exists dataparser but not created by template，please rename dataparser than try again, name = " + name);
            }
            DataParserSourceDO tmpDO = new DataParserSourceDO();
            updateCopyDataParserSource(tmpDO, destDataParserSourceDO, dataParserSourceDO, destTenantDO.getId(), updateFlowDataParserSourceDOList, userId);
        }
        return destDataParserSourceDO;
    }

    public void
    updateCopyDataParserSource(DataParserSourceDO tmpDO,
                               DataParserSourceDO destDataParserSourceDO,
                               DataParserSourceDO dataParserSourceDO,
                               String destTenantId,
                               List<DataParserSourceDO> updateDataParserSourceDOList,
                               String userId) {
        BeanUtils.copyProperties(destDataParserSourceDO, tmpDO);
        BeanUtils.copyProperties(dataParserSourceDO, destDataParserSourceDO);
        destDataParserSourceDO.setId(tmpDO.getId());
        destDataParserSourceDO.setName(tmpDO.getName());
        destDataParserSourceDO.setGmtCreate(tmpDO.getGmtCreate());
        destDataParserSourceDO.setSource(StringUtils.isEmpty(tmpDO.getSource()) ? dataParserSourceDO.getSource() : tmpDO.getSource());
        Set<String> paths = new HashSet<>();
        paths.addAll(StringUtils.isNotEmpty(dataParserSourceDO.getPaths()) ?
                JsonUtils.toObjectByTypeRef(dataParserSourceDO.getPaths(), new TypeReference<List<String>>() {
                }) : new ArrayList<>());
        paths.addAll(StringUtils.isNotEmpty(tmpDO.getPaths()) ?
                JsonUtils.toObjectByTypeRef(tmpDO.getPaths(), new TypeReference<List<String>>() {
                }) : new ArrayList<>());
        destDataParserSourceDO.setPaths(JsonUtils.toJsonString(paths));
        destDataParserSourceDO.setGmtModify(new Date());
        destDataParserSourceDO.setEditUserId(userId);
        destDataParserSourceDO.setTenantId(destTenantId);
        if (DataParserRawDataTypeEnum.other.name().equals(tmpDO.getRawDataFormat())) {
            destDataParserSourceDO.setRawDataFormat(tmpDO.getRawDataFormat());
            destDataParserSourceDO.setRawDataParseType(tmpDO.getRawDataParseType());
            destDataParserSourceDO.setInvokeFunction(tmpDO.getInvokeFunction());
            destDataParserSourceDO.setRawDataParseRule(tmpDO.getRawDataParseRule());
        }
        updateDataParserSourceDOList.add(destDataParserSourceDO);
    }

    public void addCopyDataParserSource(DataParserSourceDO destDataParserSourceDO,
                                        DataParserSourceDO dataParserSourceDO,
                                        String destTenantId,
                                        List<DataParserSourceDO> addDataParserSourceDOList,
                                        String userId) {
        BeanUtils.copyProperties(dataParserSourceDO, destDataParserSourceDO);
        destDataParserSourceDO.setId(IdUtils.generateId());
        destDataParserSourceDO.setGmtCreate(new Date());
        destDataParserSourceDO.setGmtModify(new Date());
        destDataParserSourceDO.setTenantId(destTenantId);
        destDataParserSourceDO.setCreateUserId(userId);
        destDataParserSourceDO.setEditUserId(userId);
        destDataParserSourceDO.setEditUserId(userId);
        destDataParserSourceDO.setCreateUserId(userId);
        addDataParserSourceDOList.add(destDataParserSourceDO);
    }

    public void handleAsyncmqQueueDO(List<AsyncMqQueueDO> asyncMqQueueDOList,
                                     String templateTopic,
                                     String serviceName,
                                     String dataparserId,
                                     Map<String, Object> result,
                                     String requestType,
                                     Map<String, Set<String>> zcpTopicMapping,
                                     String tenantId,
                                     Set<String> dcs) {
        Set<String> buildTopic = new HashSet<>();
        if (ZCP.equals(requestType)) {
            buildTopic = zcpTopicMapping.getOrDefault(templateTopic, new HashSet<>());
        } else {
            buildTopic = listTopicByTemplate(templateTopic, serviceName, dcs);
        }
        if (CollectionUtils.isEmpty(buildTopic)) {
            return;
        }
        buildTopic(buildTopic, dataparserId, result, asyncMqQueueDOList, tenantId);
    }

    public void buildTopic(Set<String> buildTopic,
                           String dataparserId,
                           Map<String, Object> result,
                           List<AsyncMqQueueDO> asyncMqQueueDOList,
                           String tenantId) {
        if (CollectionUtils.isEmpty(buildTopic)) {
            return;
        }
        List<String> failedTopicNames = Lists.newArrayList();
        List<List<String>> topicBatchList = AsyncMqUtils.splitIntoMultipleBatches(Lists.newArrayList(buildTopic));
        AsyncUtils.parallelFutureJoinWithoutResult(topicBatchList, topicList -> {
            buildAndAddAsyncMqQueueDO(topicList, dataparserId, AsyncMqQueueSourceTypeEnum.DataParser.getType(),
                    tenantId, asyncMqQueueDOList);
        }, (e, topicList) -> {
            log.error("build topic failed, topic list: {}", topicList, e);
        }, CubeThreadPool.getThreadPool());
        result.put("failedCreateTopic", JsonUtils.toJsonString(failedTopicNames));
    }

    private AsyncMqQueueDO bulidAsyncMqQueueDO(String topic,
                                               AtomicInteger order,
                                               String sourceId,
                                               String unitTagId,
                                               String tenantId,
                                               int sourceType,
                                               String userId) {
        AsyncMqQueueDO asyncMqQueueDO = new AsyncMqQueueDO();
        asyncMqQueueDO.setCreateTime(new Date());
        asyncMqQueueDO.setId(IdUtils.generateId());
        asyncMqQueueDO.setTopic(topic);
//        asyncMqQueueDO.setGroupId(topic + "_" + CHANNEL_NAME + "_group");
//        inputQueueApprovalRecommendService.setInputQueueConfig(topic, sourceId, asyncMqQueueDO);
        asyncMqQueueDO.setSourceId(sourceId);
        asyncMqQueueDO.setIsBroadcast(SwitchEnum.no.name());
        asyncMqQueueDO.setThreadCount(1);
        asyncMqQueueDO.setOrder(order.incrementAndGet());
        asyncMqQueueDO.setType(INPUT);
        asyncMqQueueDO.setOutputQueueCfg("");
        asyncMqQueueDO.setOuterId("");
        asyncMqQueueDO.setTenantId(tenantId);
        asyncMqQueueDO.setAsyncmqClusterId(sysParaService.getCubeTemplateCache(ASYNCMQ_CLUSTER));
        asyncMqQueueDO.setStatus(1);
        asyncMqQueueDO.setSourceType(1);
        asyncMqQueueDO.setEditor(userId);
        asyncMqQueueDO.setCreator(userId);
        asyncMqQueueDO.setSourceType(sourceType);
        return asyncMqQueueDO;
    }

    public DataParserDO copyDataParser(String name,
                                       TenantDO destTenantDO,
                                       DataParserDO dataParserDO,
                                       List<DataParserDO> addFlowDataParserSourceDOList,
                                       List<DataParserDO> updateFlowDataParserSourceDOList,
                                       boolean bind,
                                       Map<String, String> cubeTemplateItemServiceRelaDOHashMap,
                                       String userId,
                                       List<AsyncMqQueueDO> asyncMqQueueDOList,
                                       Map<String, Object> result,
                                       List<KafkaQueueDO> addKafkaQueueDOList,
                                       String requestType,
                                       Map<String, Set<String>> zcpTopicMapping,
                                       Set<String> dcs) {
        String destDataparserId = cubeTemplateItemServiceRelaDOHashMap.get(dataParserDO.getId());
        DataParserDO destDataParserDO = dataParserHandler.getById(destDataparserId);
        Map<String, String> topicTemplateMap = new HashMap<>();
        if (StringUtils.isNotBlank(dataParserDO.getTopicTemplate())) {
            topicTemplateMap = JsonUtils.toObjectByTypeRef(dataParserDO.getTopicTemplate(), new TypeReference<Map<String, String>>() {
            });
        }
        if (Objects.isNull(destDataParserDO)) {
            destDataParserDO = new DataParserDO();
            BeanUtils.copyProperties(dataParserDO, destDataParserDO);
            String dataparserId = IdUtils.generateId();
            destDataParserDO.setId(dataparserId);
            destDataParserDO.setGmtCreate(new Date());
            destDataParserDO.setGmtModify(new Date());
            destDataParserDO.setTenantId(destTenantDO.getId());
            destDataParserDO.setCreateUserId(userId);
            destDataParserDO.setEditUserId(userId);
            destDataParserDO.setUnitTagIds(dataParserDO.getUnitTagIds());
            destDataParserDO.setCreateUserId(userId);
            destDataParserDO.setEditUserId(userId);
            destDataParserDO.setRemark(CHANNEL_NAME);
            addFlowDataParserSourceDOList.add(destDataParserDO);
            String topicTemplate = "";
            if (ZCP.equals(requestType)) {
                topicTemplate = topicTemplateMap.get(ZCP);
            } else {
                topicTemplate = topicTemplateMap.get(CUBE);
            }
            handleAsyncmqQueueDO(asyncMqQueueDOList,
                    topicTemplate,
                    destTenantDO.getName(),
                    dataparserId,
                    result,
                    requestType,
                    zcpTopicMapping,
                    destTenantDO.getId(),
                    dcs);
        } else {
            if (bind) {
                Assert.isTrue(false, "already exists dataparser but not created by template，please rename dataparser than try again, name = " + name);
            }
            DataParserSourceDO tmpDO = new DataParserSourceDO();
            BeanUtils.copyProperties(destDataParserDO, tmpDO);
            BeanUtils.copyProperties(dataParserDO, destDataParserDO);
            destDataParserDO.setId(tmpDO.getId());
            destDataParserDO.setGmtCreate(tmpDO.getGmtCreate());
            destDataParserDO.setGmtModify(new Date());
            destDataParserDO.setTenantId(destTenantDO.getId());
            destDataParserDO.setEditUserId(userId);
            updateFlowDataParserSourceDOList.add(destDataParserDO);

            if (ZCP.equals(requestType)) {
                String topicTemplate = topicTemplateMap.get(ZCP);
                DataParserDO finalDestDataParserDO = destDataParserDO;
                Optional.ofNullable(topicTemplate)
                        .filter(StringUtils::isNotEmpty)
                        .ifPresent(template -> {
                            Set<String> buildTopic = new HashSet<>(Optional.ofNullable(zcpTopicMapping.get(template)).orElse(Collections.emptySet()));

                            List<AsyncMqQueueDO> asyncMqQueueDOS = asyncMqQueueHandler.searchTopicNames(finalDestDataParserDO.getId());
                            if (!CollectionUtils.isEmpty(asyncMqQueueDOS)) {
                                Set<String> asyncTopics = asyncMqQueueDOS.stream()
                                        .map(AsyncMqQueueDO::getTopic)
                                        .collect(Collectors.toSet());
                                buildTopic.removeAll(asyncTopics);
                            }
                            buildTopic(buildTopic, tmpDO.getId(), result, asyncMqQueueDOList, destTenantDO.getId());
                        });
            }
        }
        handleKafkaQueueDOList(addKafkaQueueDOList,
                dataParserDO,
                destDataParserDO);
        return destDataParserDO;
    }

    private void handleKafkaQueueDOList(List<KafkaQueueDO> addKafkaQueueDOList,
                                        DataParserDO dataParserDO,
                                        DataParserDO destDataParserDO) {
        Set<String> dataParserIds = new HashSet<>();
        dataParserIds.add(dataParserDO.getId());
        List<KafkaQueueDO> list = kafkaQueueHandler.getByDataParserIds(dataParserIds);
        list.stream().filter(l -> l.getType().equals(QueuePurposeTypeEnum.alarm_output.name()) || l.getType().equals(QueuePurposeTypeEnum.calc_output.name())).forEach(e -> {
            KafkaQueueDO kafkaQueueDO = new KafkaQueueDO();
            BeanUtils.copyProperties(e, kafkaQueueDO);
            kafkaQueueDO.setDataParserId(destDataParserDO.getId());
            kafkaQueueDO.setCreateTime(new Date());
            kafkaQueueDO.setId(IdUtils.generateId());
            addKafkaQueueDOList.add(kafkaQueueDO);
        });
    }

    public Map<String, Object> asyncCreateTopic(Set<String> topicSet) {
        Map<String, Object> results = new HashMap<>();
        AsyncMqOpenApiClientWrapper clientWrapper = asyncmqClusterHandler.getDefaultOpenApiClient();
        AsyncMqOpenApiClientWrapper eu02Client = asyncmqClusterHandler.getOpenapiClientByClusterName(topic_start_with_eu02);
        AsyncMqOpenApiClientWrapper milClient = asyncmqClusterHandler.getOpenapiClientByClusterName(TOPIC_START_WITH_MIL);

        Map<String, Set<String>> topicPartition = topicSet.stream()
                .collect(Collectors.groupingBy(this::getTopicGroup, Collectors.toSet()));

        requestAsyncmq(topicPartition.getOrDefault(DEFAULT_TOPIC, Collections.emptySet()), clientWrapper, results);
        requestAsyncmq(topicPartition.getOrDefault(topic_start_with_eu02, Collections.emptySet()), eu02Client, results);
        requestAsyncmq(topicPartition.getOrDefault(TOPIC_START_WITH_MIL, Collections.emptySet()), milClient, results);
        return results;
    }

    private String getTopicGroup(String topic) {
        if (topic.startsWith(topic_start_with_eu02)) return topic_start_with_eu02;
        if (topic.startsWith(TOPIC_START_WITH_MIL)) return TOPIC_START_WITH_MIL;
        return DEFAULT_TOPIC;
    }

    public void requestAsyncmq(Set<String> topicSet, AsyncMqOpenApiClientWrapper client, Map<String, Object> results) {
        if (CollectionUtils.isEmpty(topicSet)) {
            log.info("topicSet is null, skip create topic");
            return;
        }
        List<List<String>> topicBatchList = AsyncMqUtils.splitIntoMultipleBatches(Lists.newArrayList(topicSet));
        AsyncUtils.parallelFutureJoinWithoutResult(topicBatchList, topicList -> {
            OpenApi openapi = client.getOpenApi();
            Result<List<TopicResult>> restResult = openapi.listTopicsByNames(topicList);
            if (!restResult.isSuccess()) {
                log.error("request asyncmq list topic by name error, " + restResult.getMessage());
                throw new RuntimeException(restResult.getMessage());
            }
            restResult.getData().forEach(t -> {
                results.put(t.getName() + EXIST_TOPIC, us.zoom.cube.site.biz.template.TemplateConstant.SUCCESS);
            });
            List<TopicResult> topicResults = restResult.getData();
            Map<String, TopicResult> topicNameMap = topicResults.stream().collect(Collectors.toMap(TopicResult::getName, Function.identity()));
            List<TopicCreateParam> createParams = topicList.stream()
                    .filter(topicName -> !topicNameMap.containsKey(topicName))
                    .map(this::createTopicPara).toList();
            Result<BatchTopicResult> createRestResult = openapi.createBatchTopics(createParams);
            if (!createRestResult.isSuccess()) {
                log.error("request asyncmq create topic error, " + createRestResult.getMessage());
                throw new RuntimeException(createRestResult.getMessage());
            }
            List<TopicResult> successTopics = createRestResult.getData().getSuccessTopics();
            List<TopicFailedResult> failedTopics = createRestResult.getData().getFailedTopics();
            if (!CollectionUtils.isEmpty(failedTopics)) {
                failedTopics.forEach(failTopic -> {
                    TopicCreateParam topic = failTopic.getCreateParam();
                    results.put(topic.getName(), failTopic.getErrorMsg());
                });
            }

            if (!CollectionUtils.isEmpty(successTopics)) {
                successTopics.forEach(topicResult -> {
                    results.put(topicResult.getName() + NEW_TOPIC, SUCCESS);
                    Result<TopicPrivilegesResult> privilegeRestResult = openapi.getTopicPrivileges(topicResult.getName());
                    if (!privilegeRestResult.isSuccess()) {
                        throw new RuntimeException(privilegeRestResult.getMessage());
                    }
                    TopicPrivilegesResult topicPrivilegesResult = privilegeRestResult.getData();
                    if (!topicPrivilegesResult.getProduceAppNames().contains(APP_ZDCAGENT) || !topicPrivilegesResult.getConsumeAppNames().contains(APP_HUB)) {
                        TopicPrivilegesParam topicPrivilegesParam = new TopicPrivilegesParam();
                        topicPrivilegesParam.setTopicNames(Sets.newHashSet(topicResult.getName()));
                        topicPrivilegesParam.setProduceAppNames(Sets.newHashSet(APP_ZDCAGENT));
                        topicPrivilegesParam.setConsumeAppNames(Sets.newHashSet(APP_HUB));
                        Result<Void> setPrivilegeRestResult = openapi.setTopicsPrivileges(topicPrivilegesParam);
                        if (!setPrivilegeRestResult.isSuccess()) {
                            results.put(topicResult.getName(), setPrivilegeRestResult.getMessage());
                        }
                    }
                });
            }
        }, (e, topicList) -> {
            log.error("create topic failed", e);
        }, CubeThreadPool.getThreadPool());
    }


    public TopicCreateParam createTopicPara(String topic) {
        String dc = topic.split("_")[0];
        TopicCreateParam topicCreateParam = new TopicCreateParam();
        topicCreateParam.setName(topic);
        topicCreateParam.setOwner(OWNER);
        topicCreateParam.setDescription("created by cube monitor");
        topicCreateParam.setDcName(dc);
        topicCreateParam.setDataRetentionTime(3L);
        topicCreateParam.setDataRetentionTimeUnit(TimeUnit.DAYS);
        topicCreateParam.setClusterTag(cubeTemplateParaService.getClusterTag());
        topicCreateParam.setConsumerClientParamName(ZDCA_CONSUMER);
        topicCreateParam.setProducerClientParamName(ZDCA_PRODUCER);
        topicCreateParam.setDataRetentionTime(1L);
        return topicCreateParam;
    }

    private static void setAdvancedFeatureMap(Map<String, AdvancedFeature> advancedFeatureMap, String key, Object value) {
        if (value != null) {
            AdvancedFeature advancedFeature = advancedFeatureMap.getOrDefault(key, new AdvancedFeature());
            advancedFeature.setCurrentValue(String.valueOf(value));
        }
    }

    public Map<String, Object> revokeTemplate(InnerTemplateRevokeInput input, String type) {
        input.check();
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> metricsMap = new HashMap<>();
        long start = System.currentTimeMillis();
        try {
            metricsMap.put(PHASE, revokeRequestStart);
            metricsMap.put(SERVICE_NAME, input.getServiceName());
            metricsMap.put(ACTION_TYPE, type);
            metricsMap.put(TEMPLATE_NAME, input.getTemplateName());
            monitorLog.info(LogResult.getLogResult(MonitorLogTypeEnum.cubeTemplateStat.name(), JsonUtils.toJsonString(input), metricsMap));
            final String userId = AuthInterceptor.getUserId();
            log.info(userId);
            TenantDO destTenantDO = tenantHandler.getTenantByName(input.getServiceName());
            Assert.isTrue(null != destTenantDO, "This service does not exist in the cube");
            TemplateDO templateDO = templateHandlerV2.getByName(input.getTemplateName());
            Assert.isTrue(null != templateDO, "template not exist");
            List<TemplateItemDO> templateItemDOS = templateItemHandler.listByTemplateId(templateDO.getId());
            if (CollectionUtils.isEmpty(templateItemDOS)) {
                throw new BusinessException("template item not exist");
            }

            List<String> delDashboardList = Lists.newArrayList();
            List<String> delcubeTemplateItemServiceRelaDOList = Lists.newArrayList();
            List<String> delDataParserSourceDOList = Lists.newArrayList();
            List<String> delDataParserDOList = Lists.newArrayList();
            List<String> delPipelineDOList = Lists.newArrayList();
            List<String> delCollectorDOList = Lists.newArrayList();
            List<String> delCollectorFieldDOList = Lists.newArrayList();
            List<String> delMetricsDOList = Lists.newArrayList();
            List<String> delAlarmDefinitionList = Lists.newArrayList();
            Map<String, String> delAsyncqueues = new HashMap<>();

            final String templateId = templateDO.getId();
            // type,1 dataParser，2 new DataParser, 3 dashboard

            metricsMap.put(PHASE, revokeCreateAllDTOStart);
            metricsMap.put(COST, System.currentTimeMillis() - start);
            monitorLog.info(LogResult.getLogResult(MonitorLogTypeEnum.cubeTemplateStat.name(), JsonUtils.toJsonString(input), metricsMap));

            templateItemDOS.forEach(item -> {
                List<CubeTemplateItemServiceRelaDO> cubeTemplateItemServiceRelaDOS = cubeTemplateItemServiceRelaDAO.queryByServiceId(destTenantDO.getId(), templateId);

                Map<String, String> cubeTemplateItemServiceRelaDOHashMap = cubeTemplateItemServiceRelaDOS.stream()
                        .filter(i -> !Objects.equals(i.getType(), TemplateItemTypeEnum.DASHBOARD.getCode()))
                        .collect(Collectors.toMap(
                                CubeTemplateItemServiceRelaDO::getSourceTemplateItemId,
                                CubeTemplateItemServiceRelaDO::getDestTemplateItemId,
                                (oldValue, newValue) -> newValue));
                cubeTemplateItemServiceRelaDOS.forEach(e -> {
                    delcubeTemplateItemServiceRelaDOList.add(e.getId());
                });

                switch (item.getType()) {
                    case 1:
                        DataParserDO tplDataParserDO = dataParserHandler.getById(item.getItemId());
                        Assert.isTrue(null != tplDataParserDO, "template dataParser not exist, tplDataParser =" + item.getItemId());
                        String destDataparserId = cubeTemplateItemServiceRelaDOHashMap.get(tplDataParserDO.getId());
                        DataParserDO destDataParserDO = dataParserHandler.getById(destDataparserId);
                        if (Objects.nonNull(destDataParserDO)) {
                            delDataParserDOList.add(destDataParserDO.getId());
                        }

                        List<DataParserPipelineDO> destPipelines = dataParserPipelineHandler.listPipelinesByDataParserId(destDataParserDO.getId());
                        delPipelineDOList.addAll(destPipelines.stream().map(DataParserPipelineDO::getId).collect(Collectors.toList()));
                        templateService.buildCollectorAndMetricsAndAlarm(
                                delCollectorDOList,
                                delCollectorFieldDOList,
                                delMetricsDOList,
                                delAlarmDefinitionList,
                                destPipelines);

                        break;
                    case 2:
                        String dataparserSourceId = addDelSourceDataParserSourceDOList(item,
                                cubeTemplateItemServiceRelaDOHashMap,
                                delDataParserSourceDOList);

                        List<DataParserPipelineDO> destSourcePipelines = dataParserPipelineHandler.listPipelinesByDataParserId(dataparserSourceId);
                        delPipelineDOList.addAll(destSourcePipelines.stream().map(DataParserPipelineDO::getId).collect(Collectors.toList()));
                        templateService.buildCollectorAndMetricsAndAlarm(
                                delCollectorDOList,
                                delCollectorFieldDOList,
                                delMetricsDOList,
                                delAlarmDefinitionList,
                                destSourcePipelines);

                        DataParserSourceDO tplDataParserSourceDO = dataParserSourceHandler.getById(item.getItemId());
                        if (INNER.equals(type)) {
                            List<DataFlowDataParserRelationDO> dataFlowDataParserRelationDOS = dataFlowDataParserRelationDAO.
                                    selectByServiceAndDataparserId(tplDataParserSourceDO.getId(), tplDataParserSourceDO.getTenantId());
                            for (DataFlowDataParserRelationDO dataFlowDataParserRelationDO : dataFlowDataParserRelationDOS) {
                                DataFlowDO updateDataFlowDO = dataFlowHandler.getDataFlowById(dataFlowDataParserRelationDO.getDataFlowId());
                                Assert.isTrue(null != updateDataFlowDO, "template service data flow is null");
                                dataParserDeleteService.handleDelDataflowDO(updateDataFlowDO, destTenantDO.getName(), delAsyncqueues);
                            }
                        }
                        break;
                    case 3:
                        cubeTemplateItemServiceRelaDOS
                                .stream()
                                .filter(c -> c.getSourceTemplateItemId().equals(item.getItemId()))
                                .map(CubeTemplateItemServiceRelaDO::getDashTemplateRelaId)
                                .forEach(delDashboardList::add);
                        break;
                    default:
                        break;
                }
            });
            metricsMap.put(PHASE, revokeCreateAllDTOEnd);
            metricsMap.put(COST, System.currentTimeMillis() - start);
            monitorLog.info(LogResult.getLogResult(MonitorLogTypeEnum.cubeTemplateStat.name(), JsonUtils.toJsonString(input), metricsMap));

            TemplateRevokeModel revokeModel = TemplateRevokeModel.builder()
                    .delDataParserSourceDOList(delDataParserSourceDOList)
                    .delDataParserDOList(delDataParserDOList)
                    .delPipelineDOList(delPipelineDOList)
                    .delCollectorDOList(delCollectorDOList)
                    .delCollectorFieldDOList(delCollectorFieldDOList)
                    .delMetricsDOList(delMetricsDOList)
                    .delAlarmDefinitionList(delAlarmDefinitionList)
                    .delDashboardList(delDashboardList)
                    .delcubeTemplateItemServiceRelaDOList(delcubeTemplateItemServiceRelaDOList)
                    .applyInDB(input.getApplyInDB())
                    .serviceId(destTenantDO.getId())
                    .templateId(templateDO.getId())
                    .templateDO(templateDO)
                    .type(type)
                    .destTenantDO(destTenantDO)
                    .metricsMap(metricsMap)
                    .input(input)
                    .delAsyncqueues(delAsyncqueues)
                    .build();
            templateInnerHandlerService.revokeInDB(revokeModel);
        } catch (Exception e) {
            log.error("revokeTemplateError: serviceName: {}, tempalteName: {}, operator: {}", input.getServiceName(), input.getTemplateName(), input.getOperator(), e);
            metricsMap.put(PHASE, revokeRequestErrorEnd);
            metricsMap.put(COST, System.currentTimeMillis() - start);
            monitorLog.info(LogResult.getErrorLogResult(MonitorLogTypeEnum.cubeTemplateStat.name(), e, metricsMap, JsonUtils.toJsonString(input)));
            throw new BusinessException("revoke template error, service name: " + input.getServiceName() + ", error reason: " + subStringError(e.getMessage()));
        }
        result.put(input.getServiceName() + "#_" + input.getTemplateName(), SUCCESS);
        metricsMap.put(PHASE, revokeRequestEnd);
        metricsMap.put(COST, System.currentTimeMillis() - start);
        monitorLog.info(LogResult.getLogResult(MonitorLogTypeEnum.cubeTemplateStat.name(), JsonUtils.toJsonString(input), metricsMap));
        return result;
    }

    public String addDelSourceDataParserSourceDOList(TemplateItemDO item,
                                                     Map<String, String> cubeTemplateItemServiceRelaDOHashMap,
                                                     List<String> delSourceDataParserSourceDOList) {
        DataParserSourceDO tplDataParserSourceDO = dataParserSourceHandler.getById(item.getItemId());
        Assert.isTrue(null != tplDataParserSourceDO, "template dataParser not exist, tplTenantId is " + item.getItemId());

        String destDataparserId = cubeTemplateItemServiceRelaDOHashMap.get(tplDataParserSourceDO.getId());
        DataParserSourceDO destDataParserSourceDO = dataParserSourceHandler.getById(destDataparserId);
        Assert.isTrue(null != destDataParserSourceDO, "dest dataParser not exist,  destDataparserId is " + destDataparserId);
        delSourceDataParserSourceDOList.add(destDataParserSourceDO.getId());
        return destDataParserSourceDO.getId();
    }

    public Map<String, Object> revokeTemplate(InnerTemplateRevokeDTO dto) {
        dto.check();
        InnerTemplateRevokeInput input = new InnerTemplateRevokeInput();
        BeanUtils.copyProperties(dto, input);
        return revokeTemplate(input, INNER);
    }

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class TypeTemplateMapping {
        String type;
        List<String> templateList;
    }

    public ResponseObject listServiceByTemplateName(InnerTemplateBaseInput innerTemplateBaseInput) {
        innerTemplateBaseInput.check();
        authService.checkAuthFromAuthInterceptorIgnoreTenant();
        List<TemplateDO> templateDOS = new ArrayList<>();

        innerTemplateBaseInput.getTemplateNames().forEach(t -> {
            TemplateDO templateDO = templateHandlerV2.getByName(t);
            templateDOS.add(templateDO);
        });

        List<String> ids = templateDOS.stream().map(TemplateDO::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            return ResponseObject.success(null);
        }
        List<TemplateServiceRelationDO> templateServiceRelations = templateServiceRelationHandler.listByTemplateIds(ids);
        List<String> serverIds = templateServiceRelations.stream().map(TemplateServiceRelationDO::getServiceId).collect(Collectors.toList());
        List<TenantDO> tenants = tenantHandler.listByIds(serverIds);
        Map<String, TenantDO> tenantMap = tenants.stream().collect(Collectors.toMap(TenantDO::getId, Function.identity()));
        Map<String, TemplateDO> templateMap = templateDOS.stream().collect(Collectors.toMap(TemplateDO::getId, Function.identity()));
        List<TemplateGroupRelationDO> relations = templateGroupRelationHandler.listByTemplateId(ids);
        Map<String, List<TemplateGroupRelationDO>> groupRelationMap = relations.stream()
                .collect(Collectors.groupingBy(TemplateGroupRelationDO::getTemplateGroupId));
        List<TemplateGroupDO> groups = templateGroupHandler.listTemplateGroupList(relations.stream().map(TemplateGroupRelationDO::getTemplateGroupId).collect(Collectors.toList()));
        Map<String, List<TemplateServiceRelationDO>> templateServiceMap = templateServiceRelations.stream().collect(Collectors.groupingBy(TemplateServiceRelationDO::getTemplateId));
        List<ListBindServiceItemOutput> outputs = TemplateServiceV2.buildOutput(groups, groupRelationMap, templateMap, tenantMap, templateServiceMap);
        return ResponseObject.success(outputs);
    }

    public ResponseObject getTemplateName() {
        authService.checkAuthFromAuthInterceptorIgnoreTenant();
        return zcpService.getTypeName();
    }
}
