package us.zoom.cube.site.lib.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import us.zoom.cube.site.infra.enums.audit.AuditEventType;

import java.sql.Timestamp;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AuditLogEntity {
    private String tableName;
    private String resourceId;
    private String resourceType;
    private String trackingId;
    private String txId;
    private String requestURI;
    private String value;
    private String comment;
    private String operator;
    private Timestamp time;
}
