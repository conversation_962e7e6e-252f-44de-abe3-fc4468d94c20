package us.zoom.cube.site.biz;

import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.ReadContext;
import com.jayway.jsonpath.TypeRef;
import com.jayway.jsonpath.spi.json.JacksonJsonProvider;
import com.jayway.jsonpath.spi.mapper.JacksonMappingProvider;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import us.zoom.cube.site.biz.biport.AbstractCubeImportService;
import us.zoom.cube.site.core.DataParserPipelineHandler;
import us.zoom.cube.site.core.MetricsHandler;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.core.tag.ResourceTypeConstant;
import us.zoom.cube.site.core.tag.service.ResourceTagService;
import us.zoom.cube.site.core.tag.service.TagService;
import us.zoom.cube.site.infra.utils.JacksonUtils;
import us.zoom.cube.site.lib.biport.BiImportRelationInfo;
import us.zoom.cube.site.lib.input.MetricsInput;
import us.zoom.cube.site.lib.input.dataparser.StringJsonInput;
import us.zoom.cube.site.lib.input.tag.input.ResourceTagInfoInput;
import us.zoom.infra.dao.model.DataParserPipelineDO;
import us.zoom.infra.dao.model.MetricsDO;
import us.zoom.infra.thread.ThreadLocalStore;
import us.zoom.infra.utils.CommonSplitConstants;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static us.zoom.cube.site.infra.constants.BiportConstant.KEY_METRICS;

/**
 * @author: Starls Ding
 * @date: 2023/7/19 14:44
 * @desc:
 */
@Slf4j
@Service
public class BiMetricsImportService extends AbstractCubeImportService {

    @Autowired
    private TenantHandler tenantHandler;
    @Autowired
    private DataParserPipelineHandler dataParserPipelineHandler;
    @Autowired
    private MetricsHandler metricsHandler;
    @Autowired
    private MetricsService metricsService;
    @Autowired
    private BiAlarmsImportService biAlarmsImportService;
    @Autowired
    private ResourceTagService resourceTagService;
    @Autowired
    private TagService tagService;

    @Override
    public void setNextImportService() {
        this.nextImportService = biAlarmsImportService;
    }

    @Override
    protected BiImportRelationInfo doImport(StringJsonInput jsonInput, BiImportRelationInfo relaInfo) throws Exception {
        //duplicate from DataParserFullImportService
        ObjectNode jsonNodes = JacksonUtils.getObjectMapper().readValue(jsonInput.getJson(), ObjectNode.class);
        ArrayNode metricsArrayNode = jsonNodes.withArray(KEY_METRICS);

        JavaType metricsInputType = JacksonUtils.getObjectMapper().getTypeFactory().constructParametricType(List.class, MetricsInput.class);
        List<MetricsInput> metricsInputList = JacksonUtils.getObjectMapper().readValue(metricsArrayNode.toString(), metricsInputType);

        for (MetricsInput metricsInput : metricsInputList) {
            String metricsName = metricsInput.getMetricsName();
            if (!CollectionUtils.isEmpty(relaInfo.getDuplicatedMetrics())
                    && relaInfo.getDuplicatedMetrics().contains(metricsName)) {
                continue;
            }
            metricsInput.setCollectorId(relaInfo.getPipelineNameAndCollectorIdMap().get(
                    relaInfo.getMetricsNameAndPipelineNameMap().get(metricsName)
            ));
            MetricsDO metricsDO = metricsService.addMetricsBase(metricsInput);
            relaInfo.getMetricsNameAndIds().put(metricsName, metricsDO.getId());
            relaInfo.getImportedMetrics().add(metricsName);

            // import tag
            if(!CollectionUtils.isEmpty(metricsInput.getLabelInfoList())){
                ResourceTagInfoInput resourceTagInfoInput = new ResourceTagInfoInput();
                resourceTagService.deleteResourceTagByName(metricsDO.getId(), ResourceTypeConstant.RESOURCE_METRIC_TYPE);
                metricsInput.getLabelInfoList().forEach(labInfo->{
                    String tagId = tagService.createOrUpdateTagInfo(labInfo, ThreadLocalStore.getTenantInfoLocal(), ThreadLocalStore.getUserNameLocal());
                    if(tagId != null){
                        resourceTagInfoInput.setTagId(tagId);
                        resourceTagInfoInput.setResource(metricsDO.getId());
                        resourceTagInfoInput.setResourceType(ResourceTypeConstant.RESOURCE_METRIC_TYPE);
                        resourceTagService.addResourceTag(resourceTagInfoInput);
                    }
                });
            }else{
                resourceTagService.deleteResourceTagByName(metricsDO.getId(), ResourceTypeConstant.RESOURCE_METRIC_TYPE);
            }
        }
        return relaInfo;
    }

    @Override
    protected BiImportRelationInfo doPreCheck(StringJsonInput jsonInput, BiImportRelationInfo relaInfo, boolean checkDuringImport, boolean bySelf) throws Exception {
        final String serviceId = jsonInput.getTenantId();
        final boolean isIncremental = jsonInput.isIncremental();

        Configuration jsonPathConfig = Configuration.builder()
                .jsonProvider(new JacksonJsonProvider(JacksonUtils.getObjectMapper()))
                .mappingProvider(new JacksonMappingProvider(JacksonUtils.getObjectMapper()))
                .build();

        ReadContext jsonContext = JsonPath.using(jsonPathConfig).parse(jsonInput.getJson());

        int metricsConfigNum = (int) jsonContext.read("$.metrics.length()");

        //check if only one metric and doesn't exist
        List<String> jsonMetricsNames = jsonContext.read("$.metrics[*].metricsName");
        if (bySelf) {
            selfImportCheck(relaInfo, serviceId, jsonContext, jsonMetricsNames);
        }

        //Actually this is one part of structure validation
        TypeRef<List<BiMetricsImportService.MetricAndPipeline>> metricsPipelineTypeRef = new TypeRef<List<BiMetricsImportService.MetricAndPipeline>>() {
        };
        List<BiMetricsImportService.MetricAndPipeline> metricAndPipelineNames = jsonContext.read("$.metrics[*]", metricsPipelineTypeRef);
        metricAndPipelineNames.forEach(mp -> relaInfo.getMetricsNameAndPipelineNameMap().put(mp.getMetricsName(), mp.getPipelineName()));

        if (metricsConfigNum <= 0) {
            return relaInfo;
        }
        relaInfo.setHasMetrics(true);

        //check the if exist duplicate metrics
        checkDuplicatedMetrics(relaInfo, serviceId, isIncremental, jsonMetricsNames);

        //check related alarm structure for metrics
        checkRelatedAlarmStructure(jsonContext, jsonMetricsNames);

        return relaInfo;
    }

    private void checkDuplicatedMetrics(BiImportRelationInfo relaInfo, String serviceId, boolean isIncremental, List<String> jsonMetricsNames) {
        List<MetricsDO> metricsDOS = metricsHandler.getMetricsByTenant(serviceId);
        Map<String, String> metrincsNameAndIdMap = metricsDOS.stream().collect(Collectors.toMap(MetricsDO::getMetricsName, MetricsDO::getId));
        relaInfo.getMetricsNameAndIds().putAll(metrincsNameAndIdMap);
        Collection<String> existedMetrics = metrincsNameAndIdMap.keySet();

        if (CollectionUtils.isNotEmpty(existedMetrics)) {
            List<String> duplicatedMetrics = new ArrayList<>(jsonMetricsNames);
            boolean isAllDuplicated = !duplicatedMetrics.retainAll(existedMetrics);
            if (!isIncremental) {
                //should be no any duplicated metrics if do full-quantity import
                Assert.isTrue(CollectionUtils.isEmpty(duplicatedMetrics), String.format("Duplicated Metrics [%s], please check it", StringUtils.join(duplicatedMetrics, CommonSplitConstants.COMMA_SPLIT)));
            } else if (isAllDuplicated) {
                //skip all the metrics, in other words, no need to do import metrics
                relaInfo.setHasMetrics(false);
            }
            relaInfo.getDuplicatedMetrics().addAll(duplicatedMetrics);
            relaInfo.getNewlyAddMetrics().addAll(CollectionUtils.subtract(jsonMetricsNames, duplicatedMetrics));
        }
    }

    private void checkRelatedAlarmStructure(ReadContext jsonContext, List<String> jsonMetricsNames) {
        int alarmConfigNum = (int) jsonContext.read("$.alarms.length()");
        if (alarmConfigNum > 0) {
            //1. check if related metrics is this metrics
            List<String> alarmRelatedMetricsNames = jsonContext.read("$.alarms[*].metricsName");
            Assert.isTrue(alarmConfigNum == alarmRelatedMetricsNames.size(), "structure error : no metrics name config for alarm section");
            alarmRelatedMetricsNames.forEach(relatedMetrics -> Assert.isTrue(jsonMetricsNames.contains(relatedMetrics), String.format("Some alarm related metrics name [%s] is not match ", relatedMetrics)));
        }
    }

    private void selfImportCheck(BiImportRelationInfo relaInfo, String serviceId, ReadContext jsonContext, List<String> jsonMetricsNames) {
        Assert.isTrue(jsonMetricsNames.size() == 1, "No any or more than one metrics configuration in the JSON, please check it!");
        String metricsName = jsonMetricsNames.get(0);
        Assert.isNull(metricsHandler.findMetricsByNameAndTenantId(metricsName, serviceId), "Duplicate metrics name, please change it!");

        //check pipeline
        String pipelineName = jsonContext.read("$.metrics[0].pipelineName");
        DataParserPipelineDO pipelineDO = dataParserPipelineHandler.getByTenantIdAndName(serviceId, pipelineName);
        Assert.notNull(pipelineDO, String.format("Can't find the pipeline for this metrics:%s", metricsName));
        Assert.notNull(pipelineDO.getCollectorId(), String.format("No any field in this Pipelie:%s", pipelineName));

        relaInfo.getPipelineNameAndCollectorIdMap().put(pipelineName, pipelineDO.getCollectorId());
    }

    //need to use static for JsonPath and TypeRef
    @Data
    private static class MetricAndPipeline {
        private String metricsName;
        private String pipelineName;
    }


}
