package us.zoom.cube.site.lib.input.rca;

import lombok.Data;
import us.zoom.cube.site.lib.BasePara;

@Data
public class RcaCommentInput extends BasePara {
    private String id;
    private String rcaId;
    private String commentId;
    private String parentCommentId;
    private String content;
    private String mailContent;
    /**
     * If there are multiple, separate them with commas. If they are the same user, remove the duplicates.
     */
    private String mentionedUserIds;
    /**
     * @see us.zoom.cube.site.lib.outage.CommentTypeEnum
     */
    private String type;
    /**
     * @see us.zoom.cube.site.lib.outage.MentionModuleType
     */
    private String mentionModuleType;
}
