package us.zoom.cube.site.biz.syspara;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.infra.syspara.SysParaEventService;
import us.zoom.infra.thread.NamedThreadFactory;

import jakarta.annotation.PostConstruct;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class SysParaEventWrapper {


    @Autowired
    private SysParaEventService sysParaEventService;


    @PostConstruct
    public void init(){
        sysParaEventService.load();
        Executors.newScheduledThreadPool(1,new NamedThreadFactory("sysparas  scheduler ")).scheduleAtFixedRate(new Runnable() {
            @Override
            public void run() {
                sysParaEventService.load();
            }
        }, 1,1, TimeUnit.MINUTES);
    }

}
