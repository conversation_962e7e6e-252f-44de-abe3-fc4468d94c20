package us.zoom.cube.site.lib.outage;

/**
 * OutageTypeEnum
 */
public enum OutageTypeEnum {

    Functional("Functional"),
    Availability("Availability"),
    Performance("Performance"),
    OperationsRelease("Operations/Release"),
    Dependency("Dependency");

    private String code;

    OutageTypeEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }
}
