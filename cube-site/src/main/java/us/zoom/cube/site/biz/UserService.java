package us.zoom.cube.site.biz;

import com.google.common.collect.Maps;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.util.Pair;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.logout.CookieClearingLogoutHandler;
import org.springframework.security.web.authentication.logout.SecurityContextLogoutHandler;
import org.springframework.security.web.authentication.rememberme.AbstractRememberMeServices;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.site.api.intercept.AuthInterceptor;
import us.zoom.cube.site.biz.syspara.AuthParaService;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.core.UserHandler;
import us.zoom.cube.site.core.auth.TenantUserRelaHandler;
import us.zoom.cube.site.infra.enums.WebCodeEnum;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.AuthException;
import us.zoom.cube.site.lib.BasePara;
import us.zoom.cube.site.lib.IdListPara;
import us.zoom.cube.site.lib.IdPara;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.common.ValueText;
import us.zoom.cube.site.lib.input.SetTenantInput;
import us.zoom.cube.site.lib.input.UserInput;
import us.zoom.cube.site.lib.input.outage.UcsOrgUserInput;
import us.zoom.cube.site.lib.input.outage.UcsUserInput;
import us.zoom.cube.site.lib.output.LoginUser;
import us.zoom.cube.site.lib.output.PageResult;
import us.zoom.cube.site.lib.output.alarm.AlarmMentionQueryOut;
import us.zoom.cube.site.lib.output.ucs.UcsOrgOutput;
import us.zoom.cube.site.lib.output.ucs.UcsUserOutput;
import us.zoom.cube.site.lib.query.NameQuery;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.cube.site.lib.query.SearchUserQuery;
import us.zoom.infra.dao.model.RoleTenantDO;
import us.zoom.infra.dao.model.TenantDO;
import us.zoom.infra.dao.model.TenantUserRelaDO;
import us.zoom.infra.dao.model.UserDO;
import us.zoom.infra.dao.service.TenantDAO;
import us.zoom.infra.enums.UserStatusEnum;
import us.zoom.infra.utils.HttpUtils;
import us.zoom.infra.utils.Instance;
import us.zoom.infra.utils.JWTUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Component
@Slf4j
@SuppressWarnings({"rawtypes","unchecked"})
public class UserService {

    @Value("${logout.url}")
    private String logoutUrl;

    @Autowired
    private UserHandler userHandler;

    @Autowired
    private AuthService authService;

    @Autowired
    private TenantHandler tenantHandler;

    @Autowired
    private TenantUserRelaHandler tenantUserRelHandler;

    @Autowired
    private TenantDAO tenantDAO;

    @Autowired
    private AuthParaService authParaService;

    private static final String SPACE = " ";

    private static final String DOT = ".";

    private static final String EMAIL_SUFFIX = "@zoom.us";

    @Value("${outage-ucs.accountId:}")
    private String accountId;
    @Value("${outage-ucs.other.accountId:}")
    private String otherAccountId;
    @Value("${outage-ucs.endPoint:}")
    private String ucsEndPoint;
    @Value("${outage-ucsOrg.endPoint:}")
    private String ucsOrgEndPoint;
    private static final String AUTHENTICATION_BEARER = "Bearer ";


    @Autowired
    private TenantUserRelaHandler tenantUserRelaHandler;

    public ResponseObject logout(HttpServletResponse response, HttpServletRequest request) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if(auth!=null){
            CookieClearingLogoutHandler cookieClearingLogoutHandler = new CookieClearingLogoutHandler(AbstractRememberMeServices.SPRING_SECURITY_REMEMBER_ME_COOKIE_KEY);
            SecurityContextLogoutHandler securityContextLogoutHandler = new SecurityContextLogoutHandler();
            cookieClearingLogoutHandler.logout(request, response, auth);
            securityContextLogoutHandler.logout(request, response, auth);
        }
        Map<String,Object> dataMap = new HashMap<>();
        dataMap.put("logoutUrl",logoutUrl);
        return ResponseObject.success(dataMap);
    }

    public UserDO getUserByName(String userName) {
        return  userHandler.getUserByName(userName);
    }

    public ResponseObject getUserInfo(String userId) {
        UserDO userDO = userHandler.getUserById(userId);
        if (userDO == null) {
            return ResponseObject.requireToLogin();
        }
        LoginUser data = new LoginUser();
        data.setName(userDO.getName());
        data.setCurrentTenant(userDO.getCurrentTenant());
        data.setUserId(userId);

        List<String> roles = Instance.ofNullable(tenantUserRelHandler.getUserTenantRoles(userId))
                .values().stream().flatMap(x -> x.stream()).distinct().toList();
        data.setRoles(roles);
        return ResponseObject.success(null, data);
    }

    public ResponseObject setTenant(SetTenantInput setTenantInput) {
        return ResponseObject.success(userHandler.setTenant(setTenantInput.getUserId(), setTenantInput.getTenantId()));
    }

    public ResponseObject getUserById(IdPara userId) {
        UserDO userDO=userHandler.getUserById(userId.getId());
        userDO.setPasswd("");
        userDO.setPasswordNew("");

        setTenantRoleList(tenantUserRelaHandler.getUserTenantRolesFromDbAndCache(userId.getId(), false), userDO);

        return  ResponseObject.success(userDO);
    }

    public ResponseObject searchUser(PageQuery<SearchUserQuery> pageQuery) {
        if(!authService.hasSuchTenant(pageQuery.getUserId(),pageQuery.getTenantId())){
            return ResponseObject.hasNoSuchTenant();
        }
        List<UserDO> collectorResultList = userHandler.findByNameLike(pageQuery.getQueryPara().getName(),pageQuery.getPageIndex(),pageQuery.getPageSize());

        if (!pageQuery.getQueryPara().isIgnoreTenantList()) {
            List<String> userList = collectorResultList.stream().map(x -> x.getId()).collect(Collectors.toList());
            Map<String, Map<String, Set<String>>> userTenantRolesFromDB = tenantUserRelaHandler.getUserTenantRolesFromDbAndCache(userList, true);
            Instance.ofNullable(collectorResultList).forEach(user -> {
                user.setPasswd("");
                user.setPasswordNew("");

                setTenantRoleList(userTenantRolesFromDB.get(user.getId()), user);
            });
        }

        int total=userHandler.getCountByNameLike(pageQuery.getQueryPara().getName());
        return ResponseObject.success(pageQuery.getOperId(),new PageResult(total,collectorResultList));
    }

    public ResponseObject searchAlarmMentionUser(PageQuery<NameQuery> pageQuery) {

        if (Objects.isNull(pageQuery)) {
            return ResponseObject.fail("param is NULL");
        }

        String name = Optional.of(pageQuery).map(PageQuery::getQueryPara).map(NameQuery::getName).orElse(null);
        if (StringUtils.isBlank(name)) {
            name = "";
        }
        if (name.contains(SPACE)) {
            name = name.replaceAll(SPACE, DOT);
        }
        int pageIndex = pageQuery.getPageIndex();
        int pageSize = pageQuery.getPageSize();

        List<UserDO> collectorResultList = userHandler.searchAlarmMentionUser(name, pageIndex, pageSize);
        int total = userHandler.getCountByNameLike(pageQuery.getQueryPara().getName());
        List<AlarmMentionQueryOut> outputList = new ArrayList<>();
        if (CollectionUtils.isEmpty(collectorResultList)) {
            if (name.contains(EMAIL_SUFFIX)) {
                AlarmMentionQueryOut alarmMentionQueryOut = new AlarmMentionQueryOut();
                alarmMentionQueryOut.setEmail(name);
                alarmMentionQueryOut.setDisplayName(AlarmMentionQueryOut.convertToDisplayName(name));
                outputList.add(alarmMentionQueryOut);
                total = 1;
            }
        } else {
            outputList = collectorResultList.stream().map(AlarmMentionQueryOut::convertFromUserDO).collect(Collectors.toList());
        }

        return ResponseObject.success(pageQuery.getOperId(),new PageResult(total, outputList));
    }

    private void setTenantRoleList(Map<String, Set<String>> userTenantRolesFromDB, UserDO user) {
        Map<String, Set<String>> userTenantRoles = userTenantRolesFromDB;
        List<RoleTenantDO> roleTenantDOList = new ArrayList<>();

        List<Pair<String, String>> pairList = new ArrayList<>();
        Instance.ofNullable(userTenantRoles).forEach((x, y) -> {
            for (String role : y) {
                Pair<String, String> p = Pair.of(role, x);
                pairList.add(p);
            }
        });
        Map<String, List<Pair<String, String>>> collect = pairList.stream().collect(Collectors.groupingBy(Pair::getFirst));

        Instance.ofNullable(collect).forEach((x, y) -> {
            RoleTenantDO roleTenantDO = new RoleTenantDO();
            roleTenantDO.setRole(x);
            Set<String> tenantSet = y.stream().map(z -> z.getSecond()).collect(Collectors.toSet());
            roleTenantDO.setTenantList(Arrays.asList(tenantSet.toArray(new String[]{})));

            roleTenantDOList.add(roleTenantDO);
        });

        user.setRoleList(roleTenantDOList);
    }

    public ResponseObject addUser(UserInput userInput) {
      //  authService.checkAuthByGetAuthFromAuthInterceptor();
        userInput.checkWhenAdd();

        if (!checkAuthForRole(userInput)) {
            throw new AuthException(WebCodeEnum.PermitError);
        }

        UserDO userDO = userHandler.getUserByName(userInput.getName());
        if (null != userDO) {
            if (UserStatusEnum.NORMAL.getStatus().equals(userDO.getStatus())) {
                Assert.isTrue(userDO == null, "User already exists");
            } else {
                //activate user
                userHandler.updateStatus(List.of(userDO.getId()), UserStatusEnum.NORMAL);
                return ResponseObject.success(true);
            }
        }

        userDO = userHandler.addUser(userInput.getName(), "normal");
        userInput.setId(userDO.getId());
        editUserAuthority(userInput);
        return ResponseObject.success(true);
    }

    private boolean checkAuthForRole(UserInput userInput) {
        if (authService.canCrossAndOperate(AuthInterceptor.getUserId())) {
            return true;
        }
        //operator should have same role in tenant
        for (RoleTenantDO roleTenantDO : userInput.getRoleTenantList()) {
            for (String tenant : roleTenantDO.getTenantList()) {
                if (!authService.hasSuchRole(AuthInterceptor.getUserId(), tenant, roleTenantDO.getRole())) {
                    return false;
                }
            }
        }

        //check removed auth
        Map<String, Set<String>> userTenantRoles = tenantUserRelaHandler.getUserTenantRolesFromDbAndCache(userInput.getId(), false);

        //remove request role from existing role
        if (null != userTenantRoles) {
            for (RoleTenantDO roleTenantDO : userInput.getRoleTenantList()) {
                for (String tenant : roleTenantDO.getTenantList()) {
                    Set<String> tenantRoles = userTenantRoles.get(tenant);
                    tenantRoles.remove(roleTenantDO.getRole());
                }
            }
            for (Map.Entry<String, Set<String>> tenantRoleEntry : userTenantRoles.entrySet()) {
                for (String role : tenantRoleEntry.getValue()) {
                    if (!authService.hasSuchRole(AuthInterceptor.getUserId(), tenantRoleEntry.getKey(), role)) {
                        return false;
                    }
                }
            }
        }



        return true;
    }


    private void editUserAuthority(UserInput userInput) {

        //remove all Relation
        tenantHandler.delTenantUserRelationByUserId(userInput.getId());

        userInput.getRoleTenantList().forEach(x -> {
            //TODO Assert.isTrue(RoleTypeEnum.isRightValue(x.getRole()),"auth is wrong");
            UserDO userDO = new UserDO();

            //userHandler.setTenant(userInput.getId(), "");
            if (!CollectionUtils.isEmpty(x.getTenantList())) {
                List<TenantUserRelaDO> tenantUserRelationDOList = new ArrayList<>();
                for (String tenant : x.getTenantList()) {
                    // Underwriting treatment no tenants exits no relation
                    TenantDO tenantDO = tenantDAO.getTenantById(tenant);
                    if (Objects.nonNull(tenantDO)) {
                        TenantUserRelaDO relationDO = new TenantUserRelaDO();
                        relationDO.setId(IdUtils.generateId());
                        relationDO.setUserId(userInput.getId());
                        //                        relationDO.setUserName(x.getName());
                        relationDO.setRole(x.getRole());
                        relationDO.setTenantId(tenant);
                        tenantUserRelationDOList.add(relationDO);
                    }
                }
                if (!CollectionUtils.isEmpty(tenantUserRelationDOList)) {
                    tenantHandler.addTenantAll(tenantUserRelationDOList);
                    userHandler.setTenant(userInput.getId(), tenantUserRelationDOList.get(0).getTenantId());
                }
            }
            BeanUtils.copyProperties(x, userDO);
            userHandler.editUser(userDO, false);
        });

        tenantUserRelaHandler.reloadUserToCache(userInput.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    public ResponseObject editUser(UserInput userInput) {
        //authService.checkAuthByGetAuthFromAuthInterceptor();
        userInput.checkWhenEdit();
        if (!checkAuthForRole(userInput)){
            throw new AuthException(WebCodeEnum.PermitError);
        }

        editUserAuthority(userInput);
        return ResponseObject.success(true);
    }

    public ResponseObject delUser(IdListPara idPara) {
        if (!authService.canCrossAndOperate(AuthInterceptor.getUserId())){
            throw new AuthException(WebCodeEnum.PermitError);
        }
        idPara.check();
        userHandler.updateStatus(idPara.getIds(), UserStatusEnum.SUSPENDED);
        return ResponseObject.success(true);
    }

    public ResponseObject listAllUserLabels(BasePara basePara) {
        List<UserDO> users= userHandler.listAll();
        return ResponseObject.success(Instance.ofNullable(users).stream().map(item->new ValueText(item.getId(),item.getName())).collect(Collectors.toList()));
    }

    public ResponseObject queryUserInfo(String userName) {
        authService.checkAuthFromAuthInterceptorIgnoreTenant();
        return ResponseObject.success(userHandler.getUserByName(userName));
    }

    public ResponseObject<Object> getUser(UcsUserInput ucsUserInput) {
        Assert.isTrue(Objects.nonNull(ucsUserInput.getFetchOrgStructure()), "fetchOrgStructure can't be empty.");
        ucsUserInput.getMust().setAccountId(Collections.singletonList(accountId));
        Map<String, String> accountMap = Maps.newHashMap();
        accountMap.put(accountId,"dev");
        accountMap.put(otherAccountId,"deva");
        ucsUserInput.getMust().setAccountClusterMap(accountMap);
        Map<String, String> headerParams = new HashMap<>(2);
        headerParams.put("Authorization",AUTHENTICATION_BEARER+ JWTUtils.generateToken("ucs_api", "ucs", 3600L, null, null));
        headerParams.put("x-zm-account-id",accountId);
        String post = null;
        String body= JsonUtils.toJsonString(ucsUserInput);
        AtomicInteger status = new AtomicInteger();
        try {
            post = HttpUtils.post(List.of(ucsEndPoint), new HashMap<>(), headerParams, body, status, null, 1);
        } catch (Exception e) {
            log.error("invoke ucs request error,", e);
        }
        UcsUserOutput ucsUserOutput = JsonUtils.toObject(post,UcsUserOutput.class);
        if(CollectionUtils.isEmpty(ucsUserOutput.getData()) || ucsUserInput.getFetchOrgStructure().equals(false)){
            return ResponseObject.success(ucsUserOutput);
        }
        ucsUserOutput.getData().stream().peek(ucsUser -> {
            UcsOrgOutput userOrg = getUserOrg(ucsUser.getUserId());
            if(Objects.nonNull(userOrg) && Objects.nonNull(userOrg.getLeaders())){
                ucsUser.setLeaders(userOrg.getLeaders());
            }
        }).collect(Collectors.toList());

        return ResponseObject.success(ucsUserOutput);
    }


    public UcsOrgOutput getUserOrg(String userId){
        UcsOrgUserInput ucsUserInput = new UcsOrgUserInput();
        Map<String, String> headerParams = new HashMap<>(2);
        headerParams.put("Authorization", AUTHENTICATION_BEARER+JWTUtils.generateToken("ucs_org_api", "ucs", 3600L, null, null));
        headerParams.put("x-zm-account-id",accountId);
        String post = null;
        ucsUserInput.setRange(Arrays.asList("1", "2"));
        ucsUserInput.setAccountId(accountId);
        ucsUserInput.setUserId(userId);
        String body= JsonUtils.toJsonString(ucsUserInput);
        AtomicInteger status = new AtomicInteger();
        try {
            post = HttpUtils.post(List.of(ucsOrgEndPoint), new HashMap<>(), headerParams, body, status, null, 1);
        } catch (Exception e) {
            log.error("invoke ucs request error,", e);
        }
        return JsonUtils.toObject(post, UcsOrgOutput.class);
    }
}
