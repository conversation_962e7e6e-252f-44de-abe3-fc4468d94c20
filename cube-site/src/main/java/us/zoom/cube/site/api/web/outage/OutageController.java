package us.zoom.cube.site.api.web.outage;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import us.zoom.async.file.TokenClient;
import us.zoom.async.file.auth.AccessTokenRequest;
import us.zoom.async.file.exception.FSCException;
import us.zoom.cube.site.api.intercept.AuthInterceptor;
import us.zoom.cube.site.biz.outage.OutageService;
import us.zoom.cube.site.lib.IdPara;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.outage.FileSrvConfig;
import us.zoom.cube.site.lib.input.outage.OutageActionEditInput;
import us.zoom.cube.site.lib.input.outage.OutageAddInput;
import us.zoom.cube.site.lib.input.outage.OutageAlarmBatchInput;
import us.zoom.cube.site.lib.input.outage.OutageAlarmDeleteInput;
import us.zoom.cube.site.lib.input.outage.OutageCommentInput;
import us.zoom.cube.site.lib.input.outage.OutageDeleteInput;
import us.zoom.cube.site.lib.input.outage.OutageEditInput;
import us.zoom.cube.site.lib.input.outage.OutageEventDeleteInput;
import us.zoom.cube.site.lib.input.outage.OutageEventInput;
import us.zoom.cube.site.lib.input.outage.OutageStatusInput;
import us.zoom.cube.site.lib.input.outage.SmartOutageInput;
import us.zoom.cube.site.lib.input.outage.TokenInput;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.cube.site.lib.query.outage.OutageActionQuery;
import us.zoom.cube.site.lib.query.outage.OutageAlarmQuery;
import us.zoom.cube.site.lib.query.outage.OutageCommentQuery;
import us.zoom.cube.site.lib.query.outage.OutageEventQuery;
import us.zoom.cube.site.lib.query.outage.OutageQuery;

import java.net.URISyntaxException;

/**
 */
@RestController
@RequestMapping("/api/outage")
public class OutageController {

    @Autowired
    private OutageService outageService;

    @Value("${fileServer.endpoint:}")
    private String endpoint;
    @Value("${outage-image.channelId:}")
    private Integer channelId;
    @Value("${outage-image.channelName:}")
    private String channelName;



    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject searchOutageList(@RequestBody PageQuery<OutageQuery> pageQuery) {
        return  outageService.searchOutageList(pageQuery);
    }


    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject addOutage(@RequestBody OutageAddInput outageAddInput) {
        return  outageService.add(outageAddInput) ;
    }


    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject editOutageList(@RequestBody OutageEditInput outageEditInput) {
        return  outageService.edit(outageEditInput)  ;
    }


    @RequestMapping(value = "/detail", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject detailOutage(@RequestBody IdPara idPara) {
        return  outageService.getById(idPara);
    }


    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject deleteOutage(@RequestBody IdPara idPara) {
        return  outageService.delete(idPara)  ;
    }

    @RequestMapping(value = "/check", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject checkDuplicateOutage(@RequestBody OutageAddInput outageAddInput) {
        return  outageService.checkOutage(outageAddInput)  ;
    }



    @RequestMapping(value = "/updateStatus", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject updateStatus(@RequestBody OutageStatusInput outageStatusInput) {
        return  outageService.updateStatus(outageStatusInput);
    }


    @RequestMapping(value = "/comment", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject comment(@RequestBody OutageCommentInput outageStatusInput) {
        return  outageService.comment(outageStatusInput);
    }

    @RequestMapping(value = "/comment/edit", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject editComment(@RequestBody OutageCommentInput commentInput) {
        return  outageService.editComment(commentInput);
    }


    @RequestMapping(value = "/comment/list", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject commentList(@RequestBody OutageCommentQuery query) {
        return  outageService.searchOutageCommentList(query);
    }


    @RequestMapping(value = "/comment/delete", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject deleteComment(@RequestBody OutageDeleteInput outageDeleteInput) {
        return  outageService.deleteComment(outageDeleteInput);
    }


    @RequestMapping(value = "/event/add", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject eventAdd(@RequestBody OutageEventInput outageEventInput) {
        return outageService.eventAdd(outageEventInput);
    }


    @RequestMapping(value = "/event/edit", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject eventEdit(@RequestBody OutageEventInput outageEventInput) {
        return outageService.eventEdit(outageEventInput);
    }



    @RequestMapping(value = "/event/list", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject eventList(@RequestBody OutageEventQuery query) {
        return  outageService.searchOutageEventList(query);
    }


    @RequestMapping(value = "/event/delete", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject eventDelete(@RequestBody OutageEventDeleteInput outageEventDeleteInput) {
        return outageService.eventDelete(outageEventDeleteInput);
    }


    @RequestMapping(value = "/alarm/list", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject alarmList(@RequestBody PageQuery<OutageAlarmQuery> pageQuery) {
        return  outageService.searchOutageAlarmList(pageQuery)  ;
    }


    @RequestMapping(value = "/alarm/add", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject alarmAdd(@RequestBody OutageAlarmBatchInput alarmInput) {
        return outageService.alarmAdd(alarmInput);
    }


    @RequestMapping(value = "/alarm/delete", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject alarmDelete(@RequestBody OutageAlarmDeleteInput outageAlarmDeleteInput) {
        return outageService.alarmDelete(outageAlarmDeleteInput);
    }


    @RequestMapping(value = "/action/list", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject actionList(@RequestBody OutageActionQuery query) {
        return  outageService.searchOutageActionList(query)  ;
    }


    @RequestMapping(value = "/action/edit", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject editAction(@RequestBody OutageActionEditInput actionEditInput) {
        return  outageService.editAction(actionEditInput)  ;
    }


    @GetMapping(value = "/zfs/getMetaData")
    public ResponseObject<FileSrvConfig> getMetaData() {
        return ResponseObject.success(FileSrvConfig.builder().endPoint(endpoint).channel(channelName).channelId(channelId).userId(AuthInterceptor.getUserId()).build());
    }



    @PostMapping(value = "/zfs/getToken")
    public ResponseObject<String> getToken(@RequestBody TokenInput tokenInput) throws FSCException, URISyntaxException {
        TokenClient tokenClient = TokenClient.TokenClientBuilder.standardAsymmetric("cube")
                .withTimeout(5000, 5000)
                .build();
        String token = tokenClient.generateAccessToken(new AccessTokenRequest(3600), tokenInput.getHeaders(), tokenInput.getMethod(), tokenInput.getUri(), tokenInput.getBody());
        return ResponseObject.success(token);
    }



    @PostMapping(value = "/smart/add")
    public ResponseObject<String> smartAdd(@RequestBody SmartOutageInput input) {
        String userId = input.getUserId();
        Assert.isTrue(StringUtils.isNotBlank(input.getRcaWiki()), "rcaWikiUrl is blank");
        outageService.asyncSmartAddOutage(input,userId);
        return ResponseObject.success("The import task has been submitted. You will be notified by email once it is completed");
    }

}
