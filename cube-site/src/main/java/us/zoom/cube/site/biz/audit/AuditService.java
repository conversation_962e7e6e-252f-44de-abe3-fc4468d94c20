package us.zoom.cube.site.biz.audit;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.site.biz.MetricsService;
import us.zoom.cube.site.biz.SysParaService;
import us.zoom.cube.site.core.SubEnvironmentHandler;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.core.config.ClickhouseHandlerFactory;
import us.zoom.cube.site.infra.enums.audit.AuditEventType;
import us.zoom.cube.site.infra.utils.ConvertUtils;
import us.zoom.cube.site.lib.dto.AuditLogEntity;
import us.zoom.cube.site.lib.input.audit.AuditQuery;
import us.zoom.cube.site.lib.output.PageResult;
import us.zoom.cube.site.lib.output.audit.AuditHistoryItem;
import us.zoom.cube.site.lib.query.ClickhouseQuery;
import us.zoom.cube.site.lib.query.MetricQuery;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.infra.clickhouse.ClickhouseVariable;
import us.zoom.infra.clickhouse.QueryMonitor;
import us.zoom.infra.dao.model.SysParaDO;
import us.zoom.infra.dao.model.TenantDO;
import us.zoom.infra.thread.ThreadLocalStore;
import us.zoom.jsqlparser.JSQLParserException;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public abstract class AuditService {

    protected static final String AUDIT_PARAM_TYPE = "audit";

    @Autowired
    protected TenantHandler tenantHandler;

    @Autowired
    protected SubEnvironmentHandler subEnvironmentHandler;

    @Autowired
    protected SysParaService sysParaService;

    @Autowired
    protected MetricsService metricsService;

    @Autowired
    protected ClickhouseHandlerFactory clickhouseHandlerFactory;

    protected abstract String getSysParaKey();


    public PageResult<AuditHistoryItem> getAuditHistoryItems(PageQuery<AuditQuery> auditQuery) {
        BasicQueryInfo basicQueryInfo = buildQueryInfo(auditQuery);
        if (basicQueryInfo == null) {
            throw new RuntimeException("can't get audit query info");
        }
        AuditQuery queryPara = auditQuery.getQueryPara();
        List<Map<String, Object>> query = clickhouseHandlerFactory.get().query(basicQueryInfo.getTenantName(), basicQueryInfo.getSql(), basicQueryInfo.getRoutedEnv(), new QueryMonitor());
        List<Map<String, Object>> countQuery = clickhouseHandlerFactory.get().query(basicQueryInfo.getTenantName(), basicQueryInfo.getCountSql(), basicQueryInfo.getRoutedEnv(), new QueryMonitor());
        int totalSize = countQuery.stream().findFirst().flatMap(map -> map.values().stream().findFirst())
                .map(count -> Integer.parseInt(count.toString())).orElse(0);
        List<AuditHistoryItem> result = Lists.newArrayList();
        List<AuditLogEntity> auditLogEntities = query.stream().map(map -> {
            AuditLogEntity entity = ConvertUtils.beanToBean(map, AuditLogEntity.class);
            if (StringUtils.equals(entity.getResourceId(), queryPara.getResourceId())) {
                AuditHistoryItem auditHistoryItem = ConvertUtils.beanToBean(entity, AuditHistoryItem.class);
                auditHistoryItem.setUpdateTime(new Date(entity.getTime().getTime()));
                result.add(auditHistoryItem);
            }
            return entity;
        }).toList();
        PageResult<AuditHistoryItem> pageResult = doGetAuditHistoryItems(queryPara, auditLogEntities, result);
        pageResult.setTotal(totalSize);
        return pageResult;
    }

    protected abstract PageResult<AuditHistoryItem> doGetAuditHistoryItems(AuditQuery queryPara, List<AuditLogEntity> originResult, List<AuditHistoryItem> result);

    protected void buildSource(AuditQuery auditQuery, AuditHistoryItem item, List<AuditLogEntity> entities) {
        try {
            AuditDetailBuilder<?> auditDetailBuilder = auditQuery.getResourceType().newBuilder();
            entities.forEach(entity -> setInAuditDetailBuilder(entity, auditDetailBuilder));
            item.setSource(auditDetailBuilder.build(item.getResourceId()));
        } catch (Throwable e) {
            log.error("build source error", e);
            item.setQueryError(e.getMessage());
        }
    }

    protected abstract void setInAuditDetailBuilder(AuditLogEntity entity, AuditDetailBuilder<?> auditDetailBuilder);

    protected BasicQueryInfo buildQueryInfo(PageQuery<AuditQuery> auditQuery) {
        try {
            String tenantId = ThreadLocalStore.getTenantInfoLocal();
            TenantDO tenantDO = tenantHandler.getTenantJustFromCache(tenantId);
            SysParaDO sysParaDO = sysParaService.getByType(getSysParaKey(), AUDIT_PARAM_TYPE);
            QueryConfig queryConfig = JsonUtils.toObjectByTypeRef(sysParaDO.getValue(), new TypeReference<QueryConfig>() {
            });
            AuditQuery queryPara = auditQuery.getQueryPara();
            BasicQueryInfo basicQueryInfo = queryConfig.getDefaultConfig();
            if (queryConfig.getConfigMap() != null && queryConfig.getConfigMap().containsKey(queryPara.getResourceType().name())) {
                basicQueryInfo = queryConfig.getConfigMap().get(queryPara.getResourceType().name());
            }
            String sql = generateSql(basicQueryInfo.getSql(), auditQuery.getPageIndex(), auditQuery.getPageSize());
            String countSql = generateSql(basicQueryInfo.getCountSql(), null, null);
            ClickhouseQuery clickhouseQuery = new ClickhouseQuery();
            clickhouseQuery.setDatabase(basicQueryInfo.getTenantName());
            clickhouseQuery.setDbType("clickhouse");
            Map<String, Object> params = Maps.newHashMap();
            AuditEventType eventType = null;
            String resourceId = queryPara.getResourceId();
            if (queryPara.isDeleted()) {
                eventType = AuditEventType.DELETE;
            }
            params.put("serviceName", new ClickhouseVariable("serviceName", tenantDO.getName()));
            params.put("resourceType", new ClickhouseVariable("resourceType", queryPara.getResourceType()));
            params.put("resourceId", new ClickhouseVariable("resourceId", resourceId));
            params.put("eventType", new ClickhouseVariable("eventType", eventType));
            params.put("trackingId", new ClickhouseVariable("trackingId", queryPara.getTrackingId()));
            params.put("blackList", new ClickhouseVariable("blackList", "array", basicQueryInfo.getBlackList()));
            params.put("whiteList", new ClickhouseVariable("whiteList", "array", basicQueryInfo.getWhiteList()));
            params = JsonUtils.toObjectByTypeRef(JSON.toJSONString(params), new TypeReference<Map<String, Object>>() {
            });
            clickhouseQuery.setParams(params);
            sql = optimizeSql(sql, clickhouseQuery);
            countSql = optimizeSql(countSql, clickhouseQuery);
            String routedEnv = subEnvironmentHandler.getDataQueryEnv(basicQueryInfo.getTenantName());
            if (routedEnv == null) {
                routedEnv = sysParaService.getClickhouseEnvRoute(basicQueryInfo.getTenantName());
            }
            return new BasicQueryInfo(routedEnv, basicQueryInfo.getTenantName(), sql, countSql);
        } catch (Throwable e) {
            log.error("generate sql for audit query failed", e);
            // ignore
            return null;
        }
    }

    private String generateSql(String sql,
                               Integer pageIndex,
                               Integer pageSize) {
        if (pageSize != null && pageIndex != null) {
            sql = sql.replaceAll(":limit:", String.valueOf(pageSize))
                    .replaceAll(":offset:", String.valueOf((pageIndex - 1) * pageSize));
        }
        return sql;
    }

    private String optimizeSql(String sql, ClickhouseQuery clickhouseQuery) throws JSQLParserException {
        clickhouseQuery.setQuery(sql);
        MetricQuery metricQuery = metricsService.genSql(clickhouseQuery);
        return metricsService.tableNameEncoding(metricQuery.getQuery(), Collections.emptySet());
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    protected static class QueryConfig {
        private BasicQueryInfo defaultConfig;
        private Map<String, BasicQueryInfo> configMap;
    }


    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    protected static class BasicQueryInfo {
        private String routedEnv;
        private String tenantName;
        private String sql;
        private String countSql;
        private Set<String> blackList;
        private Set<String> whiteList;

        public BasicQueryInfo(String routedEnv, String tenantName, String sql, String countSql) {
            this.routedEnv = routedEnv;
            this.tenantName = tenantName;
            this.sql = sql;
            this.countSql = countSql;
        }
    }
}