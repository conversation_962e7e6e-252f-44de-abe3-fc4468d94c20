package us.zoom.cube.site.biz.audit.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.site.biz.audit.AuditDetailBuilder;
import us.zoom.cube.site.biz.audit.AuditDetailTable;
import us.zoom.cube.site.biz.audit.AuditService;
import us.zoom.cube.site.infra.utils.ConvertUtils;
import us.zoom.cube.site.lib.dto.AuditLogEntity;
import us.zoom.cube.site.lib.input.audit.AuditQuery;
import us.zoom.cube.site.lib.output.PageResult;
import us.zoom.cube.site.lib.output.audit.AuditHistoryItem;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class DbBinLogAuditService extends AuditService {

    private static final String QUERY_HISTORY_SQL_PARAM_KEY = "historySql";

    @Override
    protected PageResult<AuditHistoryItem> doGetAuditHistoryItems(AuditQuery queryPara, List<AuditLogEntity> originResult, List<AuditHistoryItem> result) {
        Map<String, AuditHistoryItem> historyItemMap = result.stream().collect(Collectors.toMap(AuditHistoryItem::getTxId, Function.identity()));
        Map<String, List<AuditLogEntity>> auditLogMap = originResult.stream().collect(Collectors.groupingBy(AuditLogEntity::getTxId));
        List<AuditHistoryItem> collect = auditLogMap.entrySet().stream().map(entry -> {
            List<AuditLogEntity> auditLogEntities = entry.getValue();
            AuditHistoryItem auditHistoryItem = historyItemMap.get(entry.getKey());
            buildSource(queryPara, auditHistoryItem, auditLogEntities);
            return auditHistoryItem;
        }).toList();
        return new PageResult<>(collect);
    }

    @Override
    protected String getSysParaKey() {
        return QUERY_HISTORY_SQL_PARAM_KEY;
    }

    @Override
    protected void setInAuditDetailBuilder(AuditLogEntity entity, AuditDetailBuilder<?> auditDetailBuilder) {
        AuditDetailTable table = AuditDetailTable.getByTableName(entity.getTableName());
        if (table == null) {
            log.error("can't find audit detail table for tableName: {}, raw data: {}", entity.getTableName(), entity.getValue());
            return;
        }
        Class<?> beanClass = table.getBeanClass();
        Map<String, Object> map = JsonUtils.toObjectByTypeRef(entity.getValue(), new TypeReference<Map<String, Object>>() {});
        auditDetailBuilder.buildContext(ConvertUtils.parseFromDbData(map, beanClass));
    }
}
