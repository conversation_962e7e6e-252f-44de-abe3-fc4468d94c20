package us.zoom.cube.site.lib.input.trace;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import us.zoom.cube.site.infra.enums.trace.*;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.List;

import static org.apache.commons.lang3.StringUtils.EMPTY;
import static us.zoom.cube.site.infra.enums.trace.DataSource.TRACES;
import static us.zoom.cube.site.infra.enums.trace.FunctionName.*;

/**
 * @author: eason.jia
 * @date: 2024/8/18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BuilderQuery {
    private String queryName;
    private long stepInterval;
    private String dataSource;
    private String aggregateOperator;
    private AttributeKey aggregateAttribute;
    private String temporality;
    private FilterSet filters;
    private List<AttributeKey> groupBy;
    private String expression;
    private boolean disabled;
    private List<Having> having;
    private String legend;
    private long limit;
    private long offset;
    private long pageSize;
    private List<OrderBy> orderBy;
    private String reduceTo;
    private List<AttributeKey> selectColumns;
    private String timeAggregation;
    private String spaceAggregation;
    private List<Function> functions;
    private long shiftBy;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AttributeKey {
        private String id;
        private String dataType;
        private String key;
        @JsonProperty("isColumn")
        private boolean isColumn;
        private String type;
        @JsonProperty("isJSON")
        private boolean isJSON;

        public boolean validate() {
            if (!AttributeKeyDataType.validate(dataType)) {
                return false;
            }
            if (isColumn && !AttributeKeyType.validate(type)) {
                return false;
            }
            if (StringUtils.isBlank(key)) {
                return false;
            }
            return true;
        }
    }

    @Data
    public static class Having {
        private String columnName;
        private String op;
        private Object value;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FilterSet {
        private List<FilterItem> items;
        private String op;

        public boolean validate() {
            if (!StringUtils.equalsAny(op, EMPTY, "AND", "OR")) {
                return false;
            }
            for (FilterItem item : items) {
                if (!item.getKey().validate()) {
                    return false;
                }
            }
            return true;
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FilterItem {
        private AttributeKey key;
        private Object value;
        private String op;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrderBy {
        private String columnName;
        private String order;

        // below are not json fields
        private String key;
        private String dataType;
        private String type;
        private boolean isColumn;
    }

    @Data
    public static class Function {
        private String name;
        private List<Object> args;
    }

    public String validate() {
        if (StringUtils.isBlank(queryName)) {
            return "query name is required";
        }

        // if expression is same as query name, it's a simple builder query and not a formula
        // formula involves more than one data source, aggregate operator, etc.
        if (StringUtils.equals(queryName, expression)) {
            if (!StringUtils.equals(dataSource, TRACES.getValue())) {
                return "data source is invalid: " + dataSource;
            }
            if (!AggregateOperator.validate(aggregateOperator)) {
                return "aggregate operator is invalid: " + aggregateOperator;
            }
            if (aggregateAttribute == null && AggregateOperator.requireAttribute(aggregateOperator)) {
                return "aggregate attribute is required, aggregateOperator: " + aggregateOperator;
            }
        }

        if (filters != null) {
            if (!filters.validate()) {
                return "filters are invalid";
            }
        }

        if (CollectionUtils.isNotEmpty(groupBy)) {
            for (AttributeKey groupBy : groupBy) {
                if (!groupBy.validate()) {
                    return "group by is invalid: " + JsonUtils.toJsonStringIgnoreExp(groupBy);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(having)) {
            for (Having having : having) {
                if (!HavingOperator.validate(having.getOp())) {
                    return "having operator is invalid: " + JsonUtils.toJsonStringIgnoreExp(having);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(selectColumns)) {
            for (AttributeKey selectColumn : selectColumns) {
                if (!selectColumn.validate()) {
                    return "select column is invalid: " + JsonUtils.toJsonStringIgnoreExp(selectColumn);
                }
            }
        }

        if (StringUtils.isBlank(expression)) {
            return "expression is required";
        }

        // actually, we are not prepared to support function
        if (CollectionUtils.isNotEmpty(functions)) {
            for (Function function : functions) {
                if (!FunctionName.validate(function.getName())) {
                    return "function name is invalid: " + JsonUtils.toJsonStringIgnoreExp(function);
                }
                if (StringUtils.equals(function.getName(), TIME_SHIFT.getName())) {
                    if (CollectionUtils.isEmpty(function.getArgs())) {
                        return "timeShiftBy param missing in query: " + JsonUtils.toJsonStringIgnoreExp(function);
                    }
                    Object arg = function.getArgs().get(0);
                    if (!(arg instanceof Double)) {
                        // if string, attempt to convert to float
                        try {
                            double timeShiftBy = Double.parseDouble((String) arg);
                            function.getArgs().set(0, timeShiftBy);
                        } catch (NumberFormatException e) {
                            return "timeShiftBy param should be a number: " + JsonUtils.toJsonStringIgnoreExp(function);
                        }
                    }
                } else if (StringUtils.equalsAny(function.getName(), EWMA3.getName(), EWMA5.getName(), EWMA7.getName())) {
                    if (CollectionUtils.isEmpty(function.getArgs())) {
                        return "alpha param missing in query: " + JsonUtils.toJsonStringIgnoreExp(function);
                    }
                    Object arg = function.getArgs().get(0);
                    double alpha;
                    if (arg instanceof Double) {
                        alpha = (Double) arg;
                    } else {
                        // if string, attempt to convert to float
                        try {
                            alpha = Double.parseDouble((String) arg);
                        } catch (NumberFormatException e) {
                            return "alpha param should be a float: " + JsonUtils.toJsonStringIgnoreExp(function);
                        }
                    }
                    if (alpha < 0 || alpha > 1) {
                        return "alpha param should be between 0 and 1, now is: " + alpha;
                    }
                } else if (StringUtils.equalsAny(function.getName(), CUT_OFF_MAX.getName(), CUT_OFF_MIN.getName(), CLAMP_MAX.getName(), CLAMP_MIN.getName())) {
                    if (CollectionUtils.isEmpty(function.getArgs())) {
                        return "threshold param missing in query: " + JsonUtils.toJsonStringIgnoreExp(function);
                    }
                    Object arg = function.getArgs().get(0);
                    if (!(arg instanceof Double)) {
                        // if string, attempt to convert to float
                        try {
                            double threshold = Double.parseDouble((String) arg);
                            function.getArgs().set(0, threshold);
                        } catch (NumberFormatException e) {
                            return "threshold param should be a float: " + JsonUtils.toJsonStringIgnoreExp(function);
                        }
                    }
                }
            }
        }
        return EMPTY;
    }
}