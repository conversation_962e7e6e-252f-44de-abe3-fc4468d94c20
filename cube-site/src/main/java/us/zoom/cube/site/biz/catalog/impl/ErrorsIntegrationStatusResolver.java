package us.zoom.cube.site.biz.catalog.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import jakarta.annotation.PostConstruct;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.site.biz.catalog.IntegrationResolverType;
import us.zoom.cube.site.biz.catalog.IntegrationStatusResolver;
import us.zoom.cube.site.core.MetricsHandler;
import us.zoom.cube.site.core.config.ClickhouseHandlerFactory;
import us.zoom.cube.site.lib.dto.catalog.ComponentQuery;
import us.zoom.cube.site.lib.output.catalog.IntegrationStatus;
import us.zoom.infra.clickhouse.ClickhouseSqlUtil;
import us.zoom.infra.syspara.SysParaEventService;
import us.zoom.infra.thread.ThreadLocalStore;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static us.zoom.cube.site.infra.constants.catalog.ServiceCatalogConstant.SERVICE_CATALOG;
import static us.zoom.cube.site.infra.constants.trace.TraceConstant.TRACE_DATABASE_NAME;

/**
 * Errors integration status resolver
 * <AUTHOR>
 */
@Service
public class ErrorsIntegrationStatusResolver implements IntegrationStatusResolver {

    private static final String LOG_ERROR_METRIC_NAME = "error_log_metrics_orig";
    private static final String TRACE_ERROR_CHECK_SQL = "select name from system.tables where database='cube_trace' and  name ='distributed_trace_error_index_%s'";

    private static final String LOG_ERROR_COMPONENT = "log_exceptions";

    private static final String TRACE_ERROR_COMPONENT = "trace_exceptions";

    private static final String ERROR_INTEGRATION_CONFIG = "error_integration_config";

    private ErrorIntegrationConfig config = new ErrorIntegrationConfig();

    @PostConstruct
    public void init() {
        SysParaEventService.registerListenerBySpecificSysPara(para -> {
            config = JsonUtils.toObjectByTypeRef(para.getValue(), new TypeReference<ErrorIntegrationConfig>() {});
        }, SERVICE_CATALOG, ERROR_INTEGRATION_CONFIG);
    }

    @Autowired
    private MetricsHandler metricsHandler;

    @Autowired
    private ClickhouseHandlerFactory clickhouseHandlerFactory;

    @Override
    public IntegrationResolverType getIntegrationStatusResolverType() {
        return IntegrationResolverType.errors;
    }

    @Override
    public IntegrationStatus getIntegrationStatus(ComponentQuery query) {
        boolean exist = metricsHandler.hasSameMetricsName(config.getLogErrorMetricName() != null ? config.getLogErrorMetricName() : LOG_ERROR_METRIC_NAME,
                Lists.newArrayList(ThreadLocalStore.getTenantInfoLocal()));
        List<Map<String, Object>> result = clickhouseHandlerFactory.get().query(config.getTraceCheckerDatabaseName() != null ? config.getTraceCheckerDatabaseName() : TRACE_DATABASE_NAME,
                String.format(config.getTraceCheckerSql() != null ? config.getTraceCheckerSql() : TRACE_ERROR_CHECK_SQL,
                        ClickhouseSqlUtil.encodeClickhouseName(ThreadLocalStore.getTenantNameLocal())));
        if (!exist && CollectionUtils.isEmpty(result)) {
            return getDisabledIntegrationStatus(query);
        }
        IntegrationStatus enabledIntegrationStatus = getEnabledIntegrationStatus(query);
        List<IntegrationStatus> subStatusList = Lists.newArrayList();
        Map<String, ComponentQuery> map = query.getSubComponents() == null ? Collections.emptyMap() :
                query.getSubComponents().stream().collect(Collectors.toMap(ComponentQuery::getComponent, Function.identity()));

        if (exist) {
            query.setComponent(map.get(LOG_ERROR_COMPONENT).getDisplayName());
            subStatusList.add(getEnabledIntegrationStatus(query));
        }
        if (CollectionUtils.isNotEmpty(result)) {
            query.setComponent(map.get(TRACE_ERROR_COMPONENT).getDisplayName());
            subStatusList.add(getEnabledIntegrationStatus(query));
        }
        enabledIntegrationStatus.setSubComponents(subStatusList);
        return enabledIntegrationStatus;
    }

    @Data
    private static class ErrorIntegrationConfig {
        private String logErrorMetricName;
        private String traceCheckerSql;
        private String traceCheckerDatabaseName;
    }
} 