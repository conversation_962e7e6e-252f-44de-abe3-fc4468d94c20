package us.zoom.cube.site.biz;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.site.biz.syspara.AuthParaService;
import us.zoom.cube.site.core.UcsUserHanlder;
import us.zoom.cube.site.lib.ucs.*;
import us.zoom.infra.dao.model.DistributedLockDO;
import us.zoom.infra.dao.model.UcsUserOrgInfoDO;
import us.zoom.infra.dao.service.DistributedLockDAO;
import us.zoom.infra.thread.ApiMonitorScheduler;
import us.zoom.infra.utils.HttpUtils;
import us.zoom.infra.utils.IdUtils;
import us.zoom.infra.utils.IpUtils;
import us.zoom.infra.utils.JWTUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Author: charles.hu
 * Date: 2024/12/12
 * Description:
 */
@Component
@Slf4j
public class UcsUserApiService {

    @Autowired
    private AuthParaService authParaService;

    @Autowired
    private DistributedLockDAO distributedLockDAO;

    @Autowired
    private UcsUserHanlder ucsUserHanlder;

    private static final String AUTHENTICATION_BEARER = "Bearer ";
    private static final String UCS_LOCK_TYPE = "ucs_lock_type";
    private static final String UCS_CACHE_KEY = "ucs_org_api";
    private static final String UCS_AUDIENCE = "ucs";
    private static final int LOCK_TIMEOUT = 1;

    @PostConstruct
    public void initializeUcsUserSync() {
        ApiMonitorScheduler.getInstance().getScheduler()
                .scheduleAtFixedRate(this::syncDataFromUcsUser, 5, 360, TimeUnit.MINUTES);
    }

    public void syncDataFromUcsUser() {
        try {
            List<DistributedLockDO> locks = distributedLockDAO.listByType(UCS_LOCK_TYPE);
            if (CollectionUtils.isEmpty(locks)) {
                log.info("No locks found for type: {}", UCS_LOCK_TYPE);
                return;
            }
            DistributedLockDO lock = locks.get(0);
            int lockResult = distributedLockDAO.lockWithType(
                    UCS_LOCK_TYPE, lock.getHandler(), IpUtils.getLocalIP(), LOCK_TIMEOUT);
            if (lockResult > 0) {
                log.info("Lock acquired, starting ucs user data sync.");
                fetchAndSaveUcsUserList();
            } else {
                log.warn("Unable to acquire lock for ucs user data sync.");
            }
        } catch (Exception e) {
            log.error("Error syncing ucs data", e);
        }
    }

    public void fetchAndSaveUcsUserList() {
        try {
            String requestBody = JsonUtils.toJsonString(createInitialRequestBody());
            String response = invokeUcsUserApi(createHeaderParams(), requestBody, authParaService.getUcsEndPoint());
            if (response != null) {
                UcsUserResponse ucsUserResponse = JsonUtils.toObject(response, UcsUserResponse.class);
                if (ucsUserResponse != null && ucsUserResponse.getUserInfo() != null) {
                    log.info("Fetched ucs for root user, starting subordinate processing...");
                    String requestRootBody = JsonUtils.toJsonString(createRequestBody(
                            ucsUserResponse.getUserInfo().get(0).getAccountId(), ucsUserResponse.getUserInfo().get(0).getUserId(), Arrays.asList("1", "2")));
                    String responseRoot = invokeUcsUserApi(createHeaderParams(), requestRootBody, authParaService.getUcsOrgEndPoint());
                    UcsUserOrgResponse ucsUserOrgResponse = JsonUtils.toObject(responseRoot, UcsUserOrgResponse.class);
                    List<UcsUserInfo> allUserInfos = new ArrayList<>();
                    allUserInfos.add(ucsUserOrgResponse.getUser());
                    collectUcsUserInfos(ucsUserOrgResponse, allUserInfos);
                    batchSaveOrUpdateUsers(allUserInfos);
                }
            } else {
                log.warn("Empty or null response from ucs API.");
            }
        } catch (Exception e) {
            log.error("Error fetching and saving ucs data", e);
        }
    }

    private void collectUcsUserInfos(UcsUserOrgResponse userInfo, List<UcsUserInfo> userList) {
        if (userInfo == null || CollectionUtils.isEmpty(userInfo.getSubordinates())) {
            return;
        }
        for (UcsUserInfo subordinate : userInfo.getSubordinates()) {
            subordinate.setLeader(userInfo.getUser().getUserId());
            userList.add(subordinate);
            String requestBody = JsonUtils.toJsonString(createRequestBody(
                    subordinate.getAccountId(), subordinate.getUserId(), Arrays.asList("1", "2")));
            String response = invokeUcsUserApi(createHeaderParams(), requestBody, authParaService.getUcsOrgEndPoint());
            if (response != null) {
                UcsUserOrgResponse nextUserInfo = JsonUtils.toObject(response, UcsUserOrgResponse.class);
                collectUcsUserInfos(nextUserInfo, userList);
            } else {
                log.warn("Null response while processing subordinate: {}", subordinate.getUserId());
            }
        }
    }

    private void batchSaveOrUpdateUsers(List<UcsUserInfo> userInfos) {
        if (CollectionUtils.isEmpty(userInfos)) return;
        long getExistTime = System.currentTimeMillis();
        List<UcsUserOrgInfoDO> existingRecords = Optional.ofNullable(ucsUserHanlder.getAllUcsUser())
                .orElse(Collections.emptyList());
        Map<String, UcsUserOrgInfoDO> existingMap = existingRecords.stream()
                .collect(Collectors.toMap(UcsUserOrgInfoDO::getUserId, Function.identity()));
        log.info("ExistingData {} ucs records, cost time: {}", existingRecords.size(), (System.currentTimeMillis() - getExistTime));
        List<UcsUserOrgInfoDO> recordsToAdd = new ArrayList<>();
        List<UcsUserOrgInfoDO> recordsToUpdate = new ArrayList<>();
        Set<String> existingUserIds = new HashSet<>(existingMap.keySet());
        for (UcsUserInfo info : userInfos) {
            UcsUserOrgInfoDO existingRecord = existingMap.get(info.getUserId());
            if (existingRecord == null) {
                UcsUserOrgInfoDO newRecord = convertToUcsUserOrgInfoDO(info);
                recordsToAdd.add(newRecord);
            } else {
                if (isRecordChanged(existingRecord, info)) {
                    updateExistingRecord(existingRecord, info);
                    recordsToUpdate.add(existingRecord);
                }
                existingUserIds.remove(info.getUserId());
            }
        }
        List<String> recordsToDelete = new ArrayList<>(existingUserIds);
        log.info("BatchSaveOrUpdateUsers recordsToAdd size {} , recordsToUpdate size {} , recordsToDelete size {}", recordsToAdd.size(), recordsToUpdate.size(), recordsToDelete.size());
        if (!recordsToAdd.isEmpty()) {
            ucsUserHanlder.batchAddUcsUser(recordsToAdd);
            log.info("Batch ucs added {} records.", recordsToAdd.size());
        }
        if (!recordsToUpdate.isEmpty()) {
            ucsUserHanlder.updateAddUcsUser(recordsToUpdate);
            log.info("Batch ucs updated {} records.", recordsToUpdate.size());
        }
        if (!recordsToDelete.isEmpty()) {
            ucsUserHanlder.deleteAddUcsUser(recordsToDelete);
            log.info("Batch ucs deleted {} records.", recordsToDelete.size());
        }
    }

    private UcsUserOrgInfoDO convertToUcsUserOrgInfoDO(UcsUserInfo info) {
        UcsUserOrgInfoDO ucsUserOrgInfoDO = new UcsUserOrgInfoDO();
        BeanUtils.copyProperties(info, ucsUserOrgInfoDO);
        ucsUserOrgInfoDO.setId(IdUtils.generateId());
        return ucsUserOrgInfoDO;
    }

    private boolean isRecordChanged(UcsUserOrgInfoDO existing, UcsUserInfo incoming) {
        return !Objects.equals(existing.getEmail(), incoming.getEmail()) ||
                !Objects.equals(existing.getDept(), incoming.getDept()) ||
                !Objects.equals(existing.getAccountId(), incoming.getAccountId()) ||
                !Objects.equals(existing.getJobTitle(), incoming.getJobTitle())||
                !Objects.equals(existing.getWorkPhone(), incoming.getWorkPhone())||
                !Objects.equals(existing.getLeader(), incoming.getLeader());
    }

    private void updateExistingRecord(UcsUserOrgInfoDO existing, UcsUserInfo incoming) {
        existing.setEmail(incoming.getEmail());
        existing.setDept(incoming.getDept());
        existing.setAccountId(incoming.getAccountId());
        existing.setLeader(incoming.getLeader());
        existing.setWorkPhone(incoming.getWorkPhone());
        existing.setJobTitle(incoming.getJobTitle());
    }

    private Map<String, String> createHeaderParams() {
        String token = JWTUtils.generateToken(UCS_CACHE_KEY, UCS_AUDIENCE, 3600L, null, null);
        return Collections.singletonMap("Authorization", AUTHENTICATION_BEARER + token);
    }

    private Map<String, Object> createInitialRequestBody() {
        Map<String, Object> body = new HashMap<>();
        body.put("emails", Collections.singletonList(authParaService.getUcsSearchKey()));
        return body;
    }

    private Map<String, Object> createRequestBody(String accountId, String userId, List<String> range) {
        Map<String, Object> body = new HashMap<>();
        body.put("accountId", accountId);
        body.put("userId", userId);
        body.put("range", range);
        return body;
    }

    private String invokeUcsUserApi(Map<String, String> headers, String body, String url) {
        try {
            AtomicInteger status = new AtomicInteger();
            return HttpUtils.post(Collections.singletonList(url), Collections.emptyMap(), headers, body, status, null, 1);
        } catch (Exception e) {
            log.error("Error invoking ucs API: {}", url, e);
            return null;
        }
    }
}

