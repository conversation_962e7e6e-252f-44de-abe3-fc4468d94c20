package us.zoom.cube.site.api.intercept;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletRequestWrapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.site.biz.audit.AuditSysParam;
import us.zoom.cube.site.biz.e2e.SpringBeanUtil;
import us.zoom.cube.site.biz.syspara.AuditLogParaService;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.lib.IdPara;
import us.zoom.cube.site.lib.query.ClickhouseQuery;
import us.zoom.infra.clickhouse.ClickhouseSqlUtil;
import us.zoom.infra.clickhouse.SqlPlaceholder;
import us.zoom.infra.dao.model.DashDO;
import us.zoom.infra.dao.service.DashDAO;
import us.zoom.infra.enums.MonitorTypeEnum;
import us.zoom.infra.thread.ThreadLocalStore;
import us.zoom.infra.utils.DateUtils;

import javax.annotation.Nullable;
import java.io.BufferedReader;
import java.lang.invoke.MethodHandle;
import java.lang.invoke.MethodHandles;
import java.lang.invoke.MethodType;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

import static us.zoom.cube.site.infra.constants.trace.TraceConstant.RESPONSE_DATA_ATTRIBUTE;
import static us.zoom.cube.site.infra.constants.trace.TraceConstant.RESPONSE_DATA_ATTRIBUTE_LIST;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 08/19/2022 13:35
 * @Description:
 */
@Slf4j
@Component
public class MonitoringRequestMetrics {
    @Autowired
    private AuditLogParaService auditLogParaService;
    public final static String REQUEST_TIME_KEY = "_cubeReqTime";

    private final static ObjectMapper objectMapper = new ObjectMapper();
    private final static Logger monitorLog = LoggerFactory.getLogger("Monitor");

    private static final Map<Class<?>, IdAndNameGetter> idAndNameGetterMap = Maps.newHashMap();

    public static final String CURRENT_USER = "currentUser";

    public static final String USER = "user";

    public static final String SYSTEM = "system";

    public static final String STR = "/api/";

    public static final String SOURCE_TYPE = "sourceType";

    public static final String CSMS_PATH = "csmsPath";

    private static final Map<String, TriConsumer<String, String, Object>> urlFunctionMap = new HashMap<>();
    private static final Set<String> Hub_Request_Uris = Set.of(
            "/cfg/hub/v8/getHubCfg",
            "/out/pipeline/discover",
            "/cfg/hub/getAsyncmqAccount",
            "/out/hub/getTopicInfoByUnit"
    );

    @Autowired
    private TenantHandler tenantHandler;

    @FunctionalInterface
    public interface TriConsumer<T, U, V> {
        void accept(T t, U u, V v);
    }


    static {
        urlFunctionMap.put("/api/dashboard/getDashAndCards", MonitoringRequestMetrics::extractDashMonitorData);
        urlFunctionMap.put("/api/metric/queryClickHouse", MonitoringRequestMetrics::extractQueryMonitorData);
    }

    public static final String ISSUER = "issuer";

    public void auditLog(HttpServletRequest request, HttpServletResponse response) {
        auditLog(request, response, false);
    }


    private WebHttpServletRequestWrapper getRequestWrapper(ServletRequest request) {
        if (request instanceof WebHttpServletRequestWrapper) {
            return (WebHttpServletRequestWrapper) request;
        }
        if (request instanceof ServletRequestWrapper) {
            return getRequestWrapper(((ServletRequestWrapper) request).getRequest());
        }
        log.info("getRequestWrapper : {}", request.getClass());
        return null;
    }

    public void auditLog(HttpServletRequest request, HttpServletResponse response, boolean isLimit) {
        try {
            List<Object> attributes = (List<Object>) request.getAttribute(RESPONSE_DATA_ATTRIBUTE_LIST);
            if (CollectionUtils.isNotEmpty(attributes)) {
                for (Object attribute : attributes) {
                    doAudit(request, response, attribute, false, true, isLimit);
                }
                request.removeAttribute(RESPONSE_DATA_ATTRIBUTE_LIST);
                return;
            }
            Object attribute = request.getAttribute(RESPONSE_DATA_ATTRIBUTE);
            if (attribute != null) {
                doAudit(request, response, attribute, false, false, isLimit);
                request.removeAttribute(RESPONSE_DATA_ATTRIBUTE);
                return;
            }
            String body = Optional.ofNullable(getRequestWrapper(request)).map(r -> r.getBody()).orElse(null);
            doAudit(request, response, body, true, false, isLimit);
        } catch (Exception e) {
            log.error("auditLog error: reason = {}", e.getMessage(), e);
        }
    }

    public void doAudit(HttpServletRequest request, HttpServletResponse response, Object body,
                        boolean fromResponseBody, boolean multipleChange, boolean isLimit) throws JsonProcessingException {
        String uri = request.getRequestURI();
        Map<String, Object> map = new HashMap<>();
        Object id = null;
        Object name = null;
        if (body != null) {
            if (!fromResponseBody) {
                IdAndNameGetter idAndNameGetter = idAndNameGetterMap.computeIfAbsent(body.getClass(), clazz -> {
                    MethodHandle getName = findAccessorOrNull(clazz, "getName", String.class);
                    MethodHandle getId = findAccessorOrNull(clazz, "getId", String.class);
                    return new IdAndNameGetter(getId, getName);
                });
                id = idAndNameGetter.getId(body);
                name = idAndNameGetter.getName(body);
                body = JsonUtils.toJsonString(body);
            } else if (body instanceof String) {
                JsonNode requestBody = objectMapper.readValue((String) body, JsonNode.class);
                id = requestBody.get("id");
                name = requestBody.get("name");
            }
        }
        if (id != null) {
            map.put("id", id);
        }
        if (name != null) {
            map.put("name", name);
        }
        if (isRecordBody(uri)) {
            map.put("body", body);
        }
        map.put("type", MonitorTypeEnum.auditLog.name());
        map.put("isLimit", isLimit);
        map.put("requestURI", uri);
        map.put("requestURL", request.getRequestURL());
        if (ThreadLocalStore.getTenantInfoLocal() != null) {
            String tenantName = tenantHandler.getTenantByIdFromCache(ThreadLocalStore.getTenantInfoLocal()).getName();
            Optional.ofNullable(urlFunctionMap.get(uri)).ifPresent(consumer -> consumer.accept(uri, tenantName, request));
            map.put("tenantName", tenantName);
        }
        map.put("method", request.getMethod());
        map.put("userId", AuthInterceptor.getUserId());
        if (Hub_Request_Uris.contains(uri)) {
            map.put("userName", "cube-hub");
        } else {
            map.put("userName", AuthInterceptor.getUserName());
        }
        map.put("tenantId", AuthInterceptor.getTenantId());
        map.put("respCode", response.getStatus());
        if (multipleChange) {
            map.put("parentTrackingId", request.getHeader("x-zm-trackingid"));
            map.put("trackingId", UUID.randomUUID().toString());
        } else {
            map.put("trackingId", request.getHeader("x-zm-trackingid"));
        }
        map.put("cost", System.currentTimeMillis() - (Long) request.getAttribute(REQUEST_TIME_KEY));
        map.put("ts", System.currentTimeMillis());
        map.put("showTime", DateUtils.format(new Date(), DateUtils.FORMART1));
        map.put(CURRENT_USER, request.getParameter(CURRENT_USER));
        map.put(SOURCE_TYPE, request.getParameter(SOURCE_TYPE));
        map.put("interfaceType", uri.contains(STR) ? USER : SYSTEM);
        map.put(CSMS_PATH, request.getAttribute(CSMS_PATH));
        map.put(ISSUER, request.getAttribute(ISSUER));
        monitorLog.info(JsonUtils.toJsonStringIgnoreExp(map));
    }

    public static void extractDashMonitorData(String uri, String tenantName, Object input) {
        Map<String, Object> map = new HashMap<>();
        map.put("type", MonitorTypeEnum.auditLogDetail.name());
        if (input instanceof HttpServletRequest) {
            String json = readJsonBody((HttpServletRequest) input);
            IdPara idPara = JsonUtils.toObject(json, IdPara.class);
            String id = idPara.getId();
            DashDAO dashDAO = SpringBeanUtil.getBean(DashDAO.class);
            DashDO dash = dashDAO.getDashById(id);
            String name = dash.getName();
            map.put("name", name);
            map.put("dashId", id);
            map.put("uri", uri);
            map.put("ts", System.currentTimeMillis());
            map.put("tenant", tenantName);
            map.put("userName", AuthInterceptor.getUserName());
        }
        monitorLog.info(JsonUtils.toJsonStringIgnoreExp(map));
    }

    public static void extractQueryMonitorData(String uri, String tenantName, Object input) {
        Map<String, Object> map = new HashMap<>();
        map.put("type", MonitorTypeEnum.auditLogDetail.name());
        if (input instanceof HttpServletRequest) {
            map.put("uri", uri);
            map.put("tenant", tenantName);
            map.put("userName", AuthInterceptor.getUserName());
            map.put("ts", System.currentTimeMillis());
            String json = readJsonBody((HttpServletRequest) input);
            ClickhouseQuery clickhouseQuery = JsonUtils.toObject(json, ClickhouseQuery.class);
            String query = clickhouseQuery.getQuery();
            SqlPlaceholder placeholder = new SqlPlaceholder();
            String sql = ClickhouseSqlUtil.doPlaceholder(query, placeholder);
            try {
                Set<String> tables = ClickhouseSqlUtil.extractTables(sql);
                for (String table : tables) {
                    map.put("service", table.split("\\.")[0].replace("\"", ""));
                    map.put("metric", table.split("\\.")[1].replace("\"", ""));
                    monitorLog.info(JsonUtils.toJsonStringIgnoreExp(map));
                }
            } catch (Exception e) {
                log.warn(e.getMessage());
            }
        }
    }

    public static String readJsonBody(HttpServletRequest request) {
        StringBuilder json = new StringBuilder();
        try (BufferedReader reader = request.getReader()) {
            String line;
            while ((line = reader.readLine()) != null) {
                json.append(line);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return json.toString();
    }

    protected boolean isRecordBody(String uri) {
        AuditSysParam auditSysParam = auditLogParaService.getAuditSysParam();
        if (auditSysParam == null) {
            return false;
        }
        if (auditSysParam.getGlobalSwitch()) {
            if (auditSysParam.getBlacklist().contains(uri)) {
                return false;
            }
            return true;
        } else {
            if (auditSysParam.getWhitelist().contains(uri)) {
                return true;
            }
            return false;
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class IdAndNameGetter {
        private MethodHandle idGetter;
        private MethodHandle nameGetter;

        public String getId(Object object) {
            if (idGetter == null) {
                return null;
            }
            try {
                return (String) idGetter.invoke(object);
            } catch (Throwable e) {
                return null;
            }
        }

        public String getName(Object object) {
            if (nameGetter == null) {
                return null;
            }
            try {
                return (String) nameGetter.invoke(object);
            } catch (Throwable e) {
                return null;
            }
        }
    }

    @Nullable
    private MethodHandle findAccessorOrNull(Class<?> clz, String methodName, Class<?> rtClazz) {
        try {
            return MethodHandles.publicLookup()
                    .findVirtual(clz, methodName, MethodType.methodType(rtClazz));
        } catch (Throwable t) {
            return null;
        }
    }
}
