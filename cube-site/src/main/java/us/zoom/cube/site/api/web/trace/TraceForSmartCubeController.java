package us.zoom.cube.site.api.web.trace;

import com.google.common.collect.Lists;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import us.zoom.cube.site.infra.enums.trace.*;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.trace.BuilderQuery;
import us.zoom.cube.site.lib.input.trace.CompositeQuery;
import us.zoom.cube.site.lib.input.trace.SimpleTraceQueryParam;
import us.zoom.cube.site.lib.input.trace.TraceQueryRangeParam;
import us.zoom.cube.site.lib.output.trace.queryrange.QueryRangeResponse;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * @author: eason.jia
 * @date: 2025/7/25
 */
@RestController
@RequestMapping("/api/trace")
public class TraceForSmartCubeController {

    private static final Logger logger = LoggerFactory.getLogger(TraceForSmartCubeController.class);

    @Autowired
    private TraceV3Controller traceV3Controller;

    @RequestMapping(value = "/simpleQuery", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject<QueryRangeResponse> simpleQuery(@RequestBody SimpleTraceQueryParam simpleTraceQueryParam) {
        TraceQueryRangeParam traceQueryRangeParam = convertParam(simpleTraceQueryParam);
        logger.info("simpleTraceQueryParam:{}, traceQueryRangeParam:{}", simpleTraceQueryParam, traceQueryRangeParam);
        return traceV3Controller.queryRange(traceQueryRangeParam);
    }

    private TraceQueryRangeParam convertParam(SimpleTraceQueryParam simpleTraceQueryParam) {
        return TraceQueryRangeParam.builder()
                .start(simpleTraceQueryParam.getStart())
                .end(simpleTraceQueryParam.getEnd())
                .compositeQuery(
                        CompositeQuery.builder()
                                .queryType(QueryType.BUILDER.getType())
                                .panelType(PanelType.LIST.getType())
                                .fillGaps(false)
                                .builderQueries(
                                        new HashMap<>() {{
                                            put("A", BuilderQuery.builder()
                                                    .dataSource(DataSource.TRACES.getValue())
                                                    .queryName("A")
                                                    .aggregateOperator(AggregateOperator.NO_OP.getOperator())
                                                    .aggregateAttribute(buildEmptyAttributeKey())
                                                    .functions(new ArrayList<>())
                                                    .filters(buildFilterSet(simpleTraceQueryParam))
                                                    .expression("A")
                                                    .disabled(false)
                                                    .stepInterval(60)
                                                    .having(new ArrayList<>())
                                                    .limit(simpleTraceQueryParam.getLimit() > 0 ? simpleTraceQueryParam.getLimit() : 10)
                                                    .orderBy(Lists.newArrayList(
                                                            BuilderQuery.OrderBy.builder()
                                                                    .columnName("durationNano")
                                                                    .order("desc")
                                                                    .build()))
                                                    .groupBy(new ArrayList<>())
                                                    .reduceTo(ReduceToOperator.AVG.getOperator())
                                                    .offset(0)
                                                    .selectColumns(Lists.newArrayList(
                                                            buildServiceAttributeKey(),
                                                            buildNameAttributeKey(),
                                                            buildDurationNanoAttributeKey())
                                                    )
                                                    .build());
                                        }})
                                .build())
                .dataSource(DataSource.TRACES.getValue())
                .variables(new HashMap<>())
                .queryColdData(false)
                .build();
    }

    private BuilderQuery.AttributeKey buildEmptyAttributeKey() {
        return BuilderQuery.AttributeKey.builder()
                .id("------false")
                .dataType("")
                .key("")
                .isColumn(false)
                .type("")
                .isJSON(false)
                .build();
    }

    private BuilderQuery.FilterSet buildFilterSet(SimpleTraceQueryParam simpleTraceQueryParam) {
        List<BuilderQuery.FilterItem> filterItemList = new ArrayList<>();
        if (StringUtils.isNotBlank(simpleTraceQueryParam.getServiceName())) {
            filterItemList.add(BuilderQuery.FilterItem.builder()
                    .key(buildServiceAttributeKey())
                    .op(FilterOperator.IN.getOperator())
                    .value(Lists.newArrayList(simpleTraceQueryParam.getServiceName()))
                    .build());
        }
        if (BooleanUtils.isTrue(simpleTraceQueryParam.isHasError())) {
            filterItemList.add(BuilderQuery.FilterItem.builder()
                    .key(buildhasErrorAttributeKey())
                    .op(FilterOperator.IN.getOperator())
                    .value(Lists.newArrayList(true))
                    .build());
        }
        if (StringUtils.isNotBlank(simpleTraceQueryParam.getCluster())) {
            filterItemList.add(BuilderQuery.FilterItem.builder()
                    .key(buildAppClusterAttributeKey())
                    .op(FilterOperator.IN.getOperator())
                    .value(Lists.newArrayList(simpleTraceQueryParam.getCluster().toLowerCase()))
                    .build());
        }
        if (StringUtils.isNotBlank(simpleTraceQueryParam.getPodName())) {
            filterItemList.add(BuilderQuery.FilterItem.builder()
                    .key(buildPodNameAttributeKey())
                    .op(FilterOperator.IN.getOperator())
                    .value(Lists.newArrayList(simpleTraceQueryParam.getPodName()))
                    .build());
        }
        if (StringUtils.isNotBlank(simpleTraceQueryParam.getDbSystem())) {
            filterItemList.add(BuilderQuery.FilterItem.builder()
                    .key(buildhDbSystemAttributeKey())
                    .op(FilterOperator.IN.getOperator())
                    .value(Lists.newArrayList(simpleTraceQueryParam.getDbSystem().toLowerCase()))
                    .build());
        }
        if (StringUtils.isNotBlank(simpleTraceQueryParam.getHttpPath())) {
            filterItemList.add(BuilderQuery.FilterItem.builder()
                    .key(buildhHttpRouteAttributeKey())
                    .op(FilterOperator.IN.getOperator())
                    .value(Lists.newArrayList(simpleTraceQueryParam.getHttpPath()))
                    .build());
        }
        return BuilderQuery.FilterSet.builder()
                .items(filterItemList)
                .op("AND")
                .build();
    }

    private BuilderQuery.AttributeKey buildServiceAttributeKey() {
        return BuilderQuery.AttributeKey.builder()
                .id("serviceName--string--tag--true")
                .dataType(AttributeKeyDataType.STRING.getDataType())
                .key("serviceName")
                .type("tag")
                .isColumn(true)
                .isJSON(false)
                .build();
    }

    private BuilderQuery.AttributeKey buildNameAttributeKey() {
        return BuilderQuery.AttributeKey.builder()
                .id("name--string--tag--true")
                .dataType(AttributeKeyDataType.STRING.getDataType())
                .key("name")
                .type("tag")
                .isColumn(true)
                .isJSON(false)
                .build();
    }

    private BuilderQuery.AttributeKey buildDurationNanoAttributeKey() {
        return BuilderQuery.AttributeKey.builder()
                .id("durationNano--float64--tag--true")
                .dataType(AttributeKeyDataType.FLOAT64.getDataType())
                .key("durationNano")
                .type("tag")
                .isColumn(true)
                .isJSON(false)
                .build();
    }

    private BuilderQuery.AttributeKey buildhasErrorAttributeKey() {
        return BuilderQuery.AttributeKey.builder()
                .id("hasError--bool--tag--true")
                .dataType(AttributeKeyDataType.BOOL.getDataType())
                .key("hasError")
                .type("tag")
                .isColumn(true)
                .isJSON(false)
                .build();
    }

    private BuilderQuery.AttributeKey buildAppClusterAttributeKey() {
        return BuilderQuery.AttributeKey.builder()
                .id("appcluster--string--tag--false")
                .dataType(AttributeKeyDataType.STRING.getDataType())
                .key("appcluster")
                .type("tag")
                .isColumn(false)
                .isJSON(false)
                .build();
    }

    private BuilderQuery.AttributeKey buildPodNameAttributeKey() {
        return BuilderQuery.AttributeKey.builder()
                .id("podName--string--tag--false")
                .dataType(AttributeKeyDataType.STRING.getDataType())
                .key("podName")
                .type("tag")
                .isColumn(false)
                .isJSON(false)
                .build();
    }

    private BuilderQuery.AttributeKey buildhDbSystemAttributeKey() {
        return BuilderQuery.AttributeKey.builder()
                .id("dbSystem--string--tag--true")
                .dataType(AttributeKeyDataType.STRING.getDataType())
                .key("dbSystem")
                .type("tag")
                .isColumn(true)
                .isJSON(false)
                .build();
    }

    private BuilderQuery.AttributeKey buildhHttpRouteAttributeKey() {
        return BuilderQuery.AttributeKey.builder()
                .id("httpRoute--string--tag--true")
                .dataType(AttributeKeyDataType.STRING.getDataType())
                .key("httpRoute")
                .type("tag")
                .isColumn(true)
                .isJSON(false)
                .build();
    }
}