package us.zoom.cube.site.api.filter;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import us.zoom.commons.jwt.service.JWTService;
import us.zoom.commons.jwt.service.TokenRegisterRequest;
import us.zoom.commons.jwt.service.TokenVerifiedRequest;
import us.zoom.commons.jwt.service.VerifyResult;
import us.zoom.commons.jwt.service.impl.DefaultJWTService;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.site.biz.syspara.CubeSiteParaService;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;

import static us.zoom.cube.site.api.intercept.MonitoringRequestMetrics.*;

@Slf4j
public class AuthFilter implements Filter {

    private static final Logger LOG = LoggerFactory.getLogger(AuthFilter.class);
    private static final AtomicLong LOG_INFO_INDEX = new AtomicLong();
    private FilterConfig filterConfigInit;

    private static final String AUTHENTICATION_BEARER = "Bearer ";

    public static final String AUTHENTICATION = "Authentication";

    public static final String AUTHORIZATION= "Authorization";
    
    public static final String USERNAME = "userName";

    public static final String[] PATTERN_URLS = {"/cfg/hub/getHubCfg"};

    public static final String CSMS_JWT_PUBLIC_KEY_PATH_LIST = "csms.jwt.public.key.path.list";

    private final JWTService jwtService = new DefaultJWTService();

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        this.filterConfigInit = filterConfig;

        // asymmetric jwt token
        List<String> csmsPublicKeyPathList = Optional.ofNullable(filterConfig.getInitParameter(CSMS_JWT_PUBLIC_KEY_PATH_LIST))
                .map(s -> JsonUtils.toObjectByTypeRef(s, new TypeReference<List<String>>() {
                }))
                // default public paths, if the list is empty, the application fails to start
                .orElse(Lists.newArrayList("prod/hub", "prod/probe"));
        log.info("AuthFilter CSMS path list: " + csmsPublicKeyPathList);

        // asymmetric jwt token
        for (String path : csmsPublicKeyPathList) {
            try {
                jwtService.register(TokenRegisterRequest.newOneOfAsymmetricVerifiedKey(List.of(path), "ES256"));
            } catch (Exception e) {
                LOG.error("token register failed, path:{}", path, e);
            }
        }

    }

    public void registerAsymmetricJwtCsmsPathList(List<String> csmsPublicKeyPathList) {
        if (CollectionUtils.isNotEmpty(csmsPublicKeyPathList)) {
            // asymmetric jwt token
            for (String path : csmsPublicKeyPathList) {
                try {
                    jwtService.register(TokenRegisterRequest.newOneOfAsymmetricVerifiedKey(List.of(path), "ES256"));
                } catch (Exception e) {
                    LOG.error("token register failed, path:{}", path, e);
                }
            }
        }
    }

    private void build401(HttpServletResponse response) throws IOException {
        response.setStatus(401);
        response.addHeader("X-Content-Type-Options", "nosniff");
        response.addHeader("Pragma", "no-cache, no-store, max-age=0, must-revalidate");
        response.addHeader("Expires", "nosniff");
        response.addHeader("Strict-Transport-Security", "max-age=31536000 ; includeSubDomains");
        response.addHeader("X-Frame-Options", "DENY");
        response.addHeader("X-XSS-Protection", "1; mode=block");
        response.getWriter().write("Forbidden");
        response.getWriter().flush();
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
//        //
//        if(!needAuth(servletRequest)){
//            doFilter(servletRequest, servletResponse, filterChain);
//            return;
//        }


        if (!(servletRequest instanceof HttpServletRequest)) {
            filterChain.doFilter(servletRequest, servletResponse);
            return;
        }

        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;
        String authenticationToken = getAuthenticationToken(request);
        if (isInvalidToken(authenticationToken)) {
            build401(response);
            return;
        }

        String token = authenticationToken.substring(AUTHENTICATION_BEARER.length(), authenticationToken.length());
        if (StringUtils.isEmpty(token)) {
            build401(response);
            return;
        }

        try {
            VerifyResult verifyResult = jwtService.verifyToken(TokenVerifiedRequest.newVerifyOne(token));
            CubeSiteParaService cubeSiteParaService = CubeSiteParaService.getInstance();
            if (cubeSiteParaService.enableCheck()) {
                String issuer = verifyResult.getIssuer();
                Map<String, Set<String>> issuerUsernameRela = cubeSiteParaService.getIssuerUsernameRela();
                String username = request.getParameter(USERNAME);
                if (issuerUsernameRela.containsKey(issuer)) {
                    Set<String> usernameList = issuerUsernameRela.get(issuer);
                    if (CollectionUtils.isNotEmpty(usernameList) && !usernameList.contains(username)) {
                        log.error("token validate failed, issuer:{}, users = {}, userName= {}, url = {}, csmsPath = {}", issuer,
                                JsonUtils.toJsonString(issuerUsernameRela), username, request.getRequestURL(), verifyResult.getCSMSPathOfSecret());
                        throw new RuntimeException("Access denied: Issuer: " + issuer + " should not use this username: " + username);
                    }
                }
            }

            String jsonString = new String(Base64.getDecoder().decode(verifyResult.getPayload()));
            Map<String, Object> payload = JsonUtils.toObject(jsonString, Map.class);
            request.setAttribute("userName", payload.get(CURRENT_USER));
            request.setAttribute(CSMS_PATH, verifyResult.getCSMSPathOfSecret());
            request.setAttribute(ISSUER, verifyResult.getIssuer());
        } catch (Exception e) {
            if (LOG_INFO_INDEX.incrementAndGet() % 1000 == 0) {
                LOG.error("token verify failed", e);
            }
            LOG_INFO_INDEX.compareAndSet(Long.MAX_VALUE, 0);
            build401(response);
            return;
        }
        //build session
        filterChain.doFilter(servletRequest, servletResponse);
    }

    private String getAuthenticationToken(HttpServletRequest request) {
        String token = request.getHeader(AUTHENTICATION);
        return StringUtils.isBlank(token) ? request.getHeader(AUTHORIZATION) : token;
    }

    private boolean isInvalidToken(String token) {
        return StringUtils.isEmpty(token) || token.length() < AUTHENTICATION_BEARER.length();
    }
}
