package us.zoom.cube.site.biz.syspara;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import us.zoom.cube.site.biz.audit.AuditSysParam;
import us.zoom.infra.dao.model.SysParaDO;
import us.zoom.infra.syspara.SysParaEventHandlerIntf;
import us.zoom.infra.syspara.SysParaEventService;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.Arrays;
import java.util.List;

/**
 * @author: <PERSON> zhen<PERSON>
 * @date: 2023/12/05 15:06
 * @desc:
 */
@Slf4j
@Component
public class AuditLogParaService implements SysParaEventHandlerIntf {
    private static final String AUDIT_LOG_TYPE = "auditLog";
    private static final String PRINT_BODY_KEY = "printBody";
    private AuditSysParam auditSysParam = new AuditSysParam();

    public AuditLogParaService() {
        SysParaEventService.registeSysparaListener(this, Arrays.asList(AUDIT_LOG_TYPE));
    }

    @Override
    public boolean onEventWhenHasData(List<SysParaDO> sysParas) {
        if (CollectionUtils.isEmpty(sysParas)) {
            return true;
        }
        for (SysParaDO sysParaDO : sysParas) {
            if (!StringUtils.equals(AUDIT_LOG_TYPE, sysParaDO.getType())) {
                continue;
            }
            try {
                if (StringUtils.equals(PRINT_BODY_KEY, sysParaDO.getParaKey())) {
                    this.auditSysParam = JsonUtils.toObjectByTypeRef(sysParas.get(0).getValue(), new TypeReference<AuditSysParam>() {
                    });
                    log.info("load auditLog sys param:{}", auditSysParam);
                }
            } catch (Exception e) {
                log.error("load auditLog sys param error", e);
            }
        }
        return true;
    }

    public AuditSysParam getAuditSysParam() {
        return auditSysParam;
    }

    @Override
    public boolean onEventWhenNoData() {
        return false;
    }
}
