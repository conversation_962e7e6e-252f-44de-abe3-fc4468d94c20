package us.zoom.cube.site.biz.outage;

import groovy.util.logging.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.biz.AuthService;
import us.zoom.cube.site.core.OutageHandler;
import us.zoom.cube.site.core.UserHandler;
import us.zoom.cube.site.lib.IdPara;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.outage.OutageActionEditInput;
import us.zoom.cube.site.lib.input.outage.OutageAddInput;
import us.zoom.cube.site.lib.input.outage.OutageAlarmBatchInput;
import us.zoom.cube.site.lib.input.outage.OutageAlarmDeleteInput;
import us.zoom.cube.site.lib.input.outage.OutageCommentInput;
import us.zoom.cube.site.lib.input.outage.OutageDeleteInput;
import us.zoom.cube.site.lib.input.outage.OutageEditInput;
import us.zoom.cube.site.lib.input.outage.OutageEventDeleteInput;
import us.zoom.cube.site.lib.input.outage.OutageEventInput;
import us.zoom.cube.site.lib.input.outage.OutageStatusInput;
import us.zoom.cube.site.lib.input.outage.SmartOutageInput;
import us.zoom.cube.site.lib.output.PageResult;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.cube.site.lib.query.outage.OutageActionQuery;
import us.zoom.cube.site.lib.query.outage.OutageAlarmQuery;
import us.zoom.cube.site.lib.query.outage.OutageCommentQuery;
import us.zoom.cube.site.lib.query.outage.OutageEventQuery;
import us.zoom.cube.site.lib.query.outage.OutageQuery;
import us.zoom.infra.dao.model.OutageActionDO;
import us.zoom.infra.dao.model.OutageAggDO;
import us.zoom.infra.dao.model.OutageAlarmDO;
import us.zoom.infra.dao.model.OutageCommentDO;
import us.zoom.infra.dao.model.OutageDO;
import us.zoom.infra.dao.model.OutageEventDO;
import us.zoom.infra.dao.service.OutageDAO;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class OutageService {
    @Autowired
    private AuthService authService;
    @Autowired
    private OutageHandler outageHandler;
    @Autowired
    private UserHandler userHandler;
    @Autowired
    private OutageDAO outageDAO;



    private static final Logger monitorLogger = LoggerFactory.getLogger("Monitor");



    public ResponseObject searchOutageList(PageQuery<OutageQuery> pageQuery) {
        authService.checkAuth(pageQuery);
        List<OutageDO> outageList = outageHandler.searchOutageList(pageQuery);
        Integer totalCount = outageHandler.getOutageParaCount(pageQuery);
        return ResponseObject.success(new PageResult(totalCount, outageList));
    }

    public ResponseObject add(OutageAddInput outageAddInput) {
        authService.checkAuth(outageAddInput);
        return ResponseObject.success(outageHandler.add(outageAddInput));
    }

    public ResponseObject edit(OutageEditInput outageEditInput) {
        authService.checkAuth(outageEditInput);
        outageHandler.edit(outageEditInput);
        return ResponseObject.success(true);
    }

    public ResponseObject getById(IdPara idPara) {
        authService.checkAuth(idPara);
        OutageDO outageDO=  outageHandler.getById(idPara.getId());
        return ResponseObject.success(outageDO);
    }

    public ResponseObject delete(IdPara idPara) {
        authService.checkAuth(idPara);
        outageHandler.delete(idPara.getId());
        return ResponseObject.success(true);
    }

    public ResponseObject updateStatus(OutageStatusInput outageStatusInput) {
        authService.checkAuth(outageStatusInput);
        outageHandler.updateStatus(outageStatusInput);
        return ResponseObject.success(true);
    }

    public ResponseObject comment(OutageCommentInput outageCommentInput) {
        authService.checkAuth(outageCommentInput);
        outageHandler.comment(outageCommentInput);
        return ResponseObject.success(true);
    }


    public ResponseObject searchOutageCommentList(OutageCommentQuery commentQuery) {
        authService.checkAuth(commentQuery);
        List<OutageCommentDO> outageCommentList = outageHandler.searchOutageCommentList(commentQuery);
        try {
            outageCommentList = buildCommentTree(outageCommentList);
        }catch (Exception e){
            monitorLogger.error("buildCommentTree error Message=",e);
        }
        return ResponseObject.success(outageCommentList);
    }

    public static List<OutageCommentDO> buildCommentTree(List<OutageCommentDO> comments) {
        if (comments == null || comments.isEmpty()) {
            return new ArrayList<>();
        }


        List<OutageCommentDO> commentTree = new ArrayList<>();
        for (OutageCommentDO comment : comments) {
            if (comment.getParentCommentId() == null || comment.getParentCommentId().isEmpty() || comment.getParentCommentId().equals("0")) {
                commentTree.add(comment); // Attach top-level comments directly to tree
            } else {
                addToParent(comment, comments); // Recursively add comments to their parent
            }
        }

        return commentTree; // Return the list of top-level comments
    }

    private static void addToParent(OutageCommentDO comment, List<OutageCommentDO> comments) {
        for (OutageCommentDO potentialParent : comments) {
            if(CollectionUtils.isEmpty(potentialParent.getCommentList())){
                potentialParent.setCommentList(Lists.newArrayList());
            }
            if (potentialParent.getCommentId().equals(comment.getParentCommentId())) {
                potentialParent.getCommentList().add(comment);
                return;
            }
        }
    }

    /**
     * deleteComment
     * @param outageDeleteInput
     * @return
     */
    public ResponseObject deleteComment(OutageDeleteInput outageDeleteInput) {
        authService.checkAuth(outageDeleteInput);
        outageHandler.deleteComment(outageDeleteInput.getOutageId(),outageDeleteInput.getCommentId());
        return ResponseObject.success(true);
    }

    public ResponseObject searchOutageEventList(OutageEventQuery query) {
        authService.checkAuth(query);
        List<OutageEventDO> outageCommentList = outageHandler.searchOutageEventList(query);
        return ResponseObject.success(outageCommentList);
    }

    public ResponseObject eventAdd(OutageEventInput outageAddInput) {
        authService.checkAuth(outageAddInput);
        return ResponseObject.success(outageHandler.eventAdd(outageAddInput));
    }

    public ResponseObject eventEdit(OutageEventInput outageAddInput) {
        authService.checkAuth(outageAddInput);
        outageHandler.eventEdit(outageAddInput);
        return ResponseObject.success(true);
    }

    public ResponseObject eventDelete(OutageEventDeleteInput outageEventDeleteInput) {
        authService.checkAuth(outageEventDeleteInput);
        outageHandler.eventDelete(outageEventDeleteInput);
        return ResponseObject.success(true);
    }


    public ResponseObject searchOutageAlarmList(PageQuery<OutageAlarmQuery> pageQuery) {
        authService.checkAuth(pageQuery);
        List<OutageAlarmDO> outageAlarmList = outageHandler.searchOutageAlarmList(pageQuery);
        Integer totalCount = outageHandler.searchOutageAlarmCount(pageQuery);
        return ResponseObject.success(new PageResult(totalCount, outageAlarmList));
    }

    public ResponseObject alarmAdd(OutageAlarmBatchInput alarmInput) {
        authService.checkAuth(alarmInput);
        return ResponseObject.success(outageHandler.alarmAdd(alarmInput));
    }

    public ResponseObject alarmDelete(OutageAlarmDeleteInput outageAlarmDeleteInput) {
        authService.checkAuth(outageAlarmDeleteInput);
        outageHandler.alarmDelete(outageAlarmDeleteInput);
        return ResponseObject.success(true);
    }

    public ResponseObject searchOutageActionList(OutageActionQuery query) {
        authService.checkAuth(query);
        List<OutageActionDO> outageActionList = outageHandler.searchOutageActionList(query);
        return ResponseObject.success(outageActionList);
    }

    public ResponseObject editAction(OutageActionEditInput actionEditInput) {
        authService.checkAuth(actionEditInput);
        outageHandler.editAction(actionEditInput);
        return ResponseObject.success(true);
    }

    /**
     *
     * @param outageAddInput
     * @return
     */
    public ResponseObject checkOutage(OutageAddInput outageAddInput) {
        Assert.notNull(outageAddInput, "outage input is null");
        Assert.isTrue(!StringUtils.isBlank(outageAddInput.getTitle()), "outage title is blank");
        OutageDO outageDO = outageHandler.searchOutageByTitle(outageAddInput.getTitle());
        if(outageDO != null){
            return ResponseObject.success(true);
        }
        return ResponseObject.success(false);
    }

    public ResponseObject editComment(OutageCommentInput commentInput) {
        authService.checkAuth(commentInput);
        outageHandler.editComment(commentInput);
        return ResponseObject.success(true);
    }

    @Async("customAsyncExecutor")
    public void asyncSmartAddOutage(SmartOutageInput input,String userId) {
        OutageAggDO outageAggDO = null;
        try {
            outageAggDO = outageHandler.smartAddOutage(input,userId);
            outageHandler.notice(outageAggDO.getOutageDO().getId(),outageAggDO.getUserName(),outageAggDO.getUserName(),"smartAddOutage add success",null,"","asyncSmartAddOutage");
        } catch (Exception e) {
            monitorLogger.error("smartAddOutage async import failed", e);
            outageHandler.notice(outageAggDO.getOutageDO().getId(),outageAggDO.getUserName(),outageAggDO.getUserName(),"smartAddOutage add fail,please check",null,"","asyncSmartAddOutage");
        }
    }




}
