package us.zoom.cube.site.biz;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.Option;
import com.zoom.op.monitor.dao.alarm.AlarmDefinitionDao;
import com.zoom.op.monitor.domain.TagInputDo;
import com.zoom.op.monitor.domain.alarm.AlarmDefinition;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.data.domain.Example;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.core.AlarmDefinitionHandler;
import us.zoom.cube.site.core.DataParserHandler;
import us.zoom.cube.site.core.DataParserPipelineHandler;
import us.zoom.cube.site.core.DataParserSourceHandler;
import us.zoom.cube.site.core.tag.ResourceTypeConstant;
import us.zoom.cube.site.core.tag.service.ResourceTagService;
import us.zoom.cube.site.infra.utils.JacksonUtils;
import us.zoom.cube.site.lib.IdPara;
import us.zoom.cube.site.lib.output.config.metrics.MetricsOut;
import us.zoom.cube.site.lib.output.dataparser.DataParserPipelineOut;
import us.zoom.cube.site.lib.output.dataparser.DataParserV2Out;
import us.zoom.cube.site.lib.output.dataparser.PipelineInfoOut;
import us.zoom.cube.site.lib.output.dataparsersource.DataParserSourceSearchOut;
import us.zoom.infra.dao.model.DataParserDO;
import us.zoom.infra.dao.model.DataParserPipelineDO;
import us.zoom.infra.dao.model.MetricsDO;
import us.zoom.infra.dao.service.CollectorMetricsDAO;
import us.zoom.infra.dao.service.MetricsDAO;
import us.zoom.infra.thread.NamedThreadFactory;

import jakarta.annotation.PostConstruct;
import us.zoom.infra.thread.ThreadLocalStore;

import java.util.*;
import java.util.concurrent.*;

import static us.zoom.cube.site.infra.constants.BiportConstant.*;

/**
 * @author: Starls Ding
 * @date: 2022/12/20 13:23
 * @desc:
 */
@Slf4j
@Service
@Primary
public class DataParserExportService {

    @Autowired
    private DataParserHandler dataParserHandler;
    @Autowired
    private DataParserSourceService dataParserSourceService;
    @Autowired
    private DataParserSourceHandler dataParserSourceHandler;
    @Autowired
    private DataParserServiceV2 dataParserServiceV2;
    @Autowired
    private DataParserPipelineHandler dataParserPipelineHandler;
    @Autowired
    private CollectorMetricsDAO collectorMetricsDAO;
    @Autowired
    private MetricsDAO metricsDAO;
    @Autowired
    private MetricsService metricsService;
    @Autowired
    private AlarmDefinitionHandler alarmDefinitionHandler;
    @Autowired
    private AlarmDefinitionDao alarmDefinitionDao;
    @Autowired
    private ResourceTagService resourceTagService;

    //
    //private ObjectMapper objectMapper;
    protected Configuration conf;
    private ExecutorService executorService;

    @PostConstruct
    public void init() {
        /*objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);*/
        conf = Configuration.defaultConfiguration().addOptions(Option.SUPPRESS_EXCEPTIONS);
        executorService = new ThreadPoolExecutor(20, 20, 2, TimeUnit.MINUTES, new ArrayBlockingQueue<>(500),
                new NamedThreadFactory("export"), new ThreadPoolExecutor.CallerRunsPolicy());
    }

    public String exportDataParserWithAll(String expectedServiceId, String parserId, boolean isNewDataParser) throws Exception {
        final Map<String, String> dataParserIdAndNames = new HashMap<>();
        final ConcurrentHashMap<String, String> pipelineIdAndNames = new ConcurrentHashMap<>();
        final ConcurrentHashMap<String, String> collectorAndPipelineIds = new ConcurrentHashMap<>();
        final ConcurrentHashMap<String, String> metricsIdAndNames = new ConcurrentHashMap<>();

        ObjectNode jsonNode = JacksonUtils.getObjectMapper().createObjectNode();
        // DataParser or NewDataParser
        String dataParserName = null;
        if (isNewDataParser) {
            ObjectNode dataParserSourceNode = exportNewDataParser(expectedServiceId, parserId, dataParserIdAndNames);
            dataParserName = dataParserSourceNode.get("name").asText();
            jsonNode.putPOJO(KEY_DATA_PARSER_SOURCE, dataParserSourceNode);
        } else {
            ObjectNode dataParserNode = exportDataParser(expectedServiceId, parserId, dataParserIdAndNames);
            dataParserName = dataParserNode.get("name").asText();
            jsonNode.putPOJO(KEY_DATA_PARSER, dataParserNode);
        }
        //Pipeline
        ArrayNode pipelineArrayNodes = exportPipeline(parserId, dataParserName, pipelineIdAndNames, collectorAndPipelineIds);
        jsonNode.putPOJO(KEY_PIPELINE, pipelineArrayNodes);
        //Metrics
        ArrayNode metricsArrayNodes = exportMetrics(collectorAndPipelineIds, pipelineIdAndNames, metricsIdAndNames);
        jsonNode.putPOJO(KEY_METRICS, metricsArrayNodes);
        //Alarms
        ArrayNode alarmsArrayNodes = exportAlarms(metricsIdAndNames);
        jsonNode.putPOJO(KEY_ALARMS, alarmsArrayNodes);
        String json = filterUselessField(jsonNode);
        return json;
    }

    public String exportPipelineWithAll(String expectedServiceId, String pipelineId) throws Exception {
        Assert.isTrue(StringUtils.isNotBlank(pipelineId), "Pipeline id can't be null when exporting Pipeline");
        DataParserPipelineDO pipelineDO = dataParserPipelineHandler.getPipelineById(pipelineId);
        Assert.notNull(pipelineDO, String.format("Can't find the pipeline by id:%s", pipelineId));
        Assert.isTrue(StringUtils.equals(expectedServiceId, pipelineDO.getTenantId()), "This pipeline is not match your service");
        DataParserPipelineOut pipeline = new DataParserPipelineOut();
        BeanUtils.copyProperties(pipelineDO, pipeline);

        final ConcurrentHashMap<String, String> pipelineIdAndNames = new ConcurrentHashMap<>();
        final ConcurrentHashMap<String, String> collectorAndPipelineIds = new ConcurrentHashMap<>();
        final ConcurrentHashMap<String, String> metricsIdAndNames = new ConcurrentHashMap<>();
        ObjectNode jsonNode = JacksonUtils.getObjectMapper().createObjectNode();
        //Pipeline
        ObjectNode pipelineNode = exportPipeline(pipeline, pipelineIdAndNames, collectorAndPipelineIds, null);
        ArrayNode pipelineArrayNodes = JacksonUtils.getObjectMapper().createArrayNode();
        pipelineArrayNodes.add(pipelineNode);
        jsonNode.putPOJO(KEY_PIPELINE, pipelineArrayNodes);
        //Metrics
        ArrayNode metricsArrayNodes = exportMetrics(collectorAndPipelineIds, pipelineIdAndNames, metricsIdAndNames);
        jsonNode.putPOJO(KEY_METRICS, metricsArrayNodes);
        //Alarms
        ArrayNode alarmsArrayNodes = exportAlarms(metricsIdAndNames);
        jsonNode.putPOJO(KEY_ALARMS, alarmsArrayNodes);
        String json = filterUselessField(jsonNode);
        return json;
    }

    public String exportMetricsWithAll(String expectedServiceId, String metricsId) throws Exception {
        Assert.isTrue(StringUtils.isNotBlank(metricsId), "Metrics id can't be null when exporting Metrics");

        //duplicated code
        MetricsOut metricsOut = metricsService.getMetricsById(metricsId);
        Assert.notNull(metricsOut, String.format("Can't find Metrics by Metrics id : %s", metricsId));
        Assert.isTrue(StringUtils.equals(expectedServiceId, metricsOut.getTenantId()), "This metrics is not match your service");

        //check if collector and pipeline exist
        String collectorId = collectorMetricsDAO.getCollectorIdByMetricsId(metricsId);
        Assert.isTrue(StringUtils.isNotBlank(collectorId), String.format("Can't find collecotr by Metrics id :%s", metricsId));

        DataParserPipelineDO pipelineDO = dataParserPipelineHandler.getPipelineByCollectorId(collectorId);
        Assert.notNull(pipelineDO, String.format("Can't find pipeline by Metrics id : %s", metricsId));

        ObjectNode jsonNode = JacksonUtils.getObjectMapper().createObjectNode();

        //convert and add related info
        ObjectNode metricsNode = JacksonUtils.getObjectMapper().valueToTree(metricsOut);
        metricsNode.put(RELATED_PIPELINE_NAME, pipelineDO.getName());
        List<ObjectNode> metricsObjectNodeList = Collections.synchronizedList(new ArrayList<>());
        metricsObjectNodeList.add(metricsNode);
        jsonNode.putPOJO(KEY_METRICS, metricsObjectNodeList);

        //export related Alarms
        ArrayNode alarmsArrayNodes = exportAlarms(new ConcurrentHashMap<>(Collections.singletonMap(metricsOut.getId(), metricsOut.getMetricsName())));
        jsonNode.putPOJO(KEY_ALARMS, alarmsArrayNodes);

        String json = filterUselessField(jsonNode);
        return json;
    }

    public String exportAlarmWithAll(String expectedServiceId, String alarmId) throws Exception {
        Assert.isTrue(StringUtils.isNotBlank(alarmId), "Alarm id can't be null when exporting Alarm");
        AlarmDefinition alarmDefQuery = new AlarmDefinition();
        alarmDefQuery.setId(alarmId);
        alarmDefQuery.setEnabled(null);
        AlarmDefinition alarmDefinition = alarmDefinitionDao.findOne(Example.of(alarmDefQuery)).orElseThrow(() -> new RuntimeException(String.format("Can't find the alarm by id:%s", alarmId)));
        MetricsDO metricsDO = metricsDAO.getMetricsById(alarmDefinition.getMetricId());
        Assert.notNull(metricsDO, String.format("Can't find the metrics by alarm id:%s", alarmId));
        Assert.isTrue(StringUtils.equals(expectedServiceId, metricsDO.getTenantId()), "This alarm is not match your service");
        ObjectNode jsonNode = JacksonUtils.getObjectMapper().createObjectNode();
        ArrayNode singleAlarmsArrayNodes = JacksonUtils.getObjectMapper().valueToTree(Collections.singletonList(alarmDefinition));
        singleAlarmsArrayNodes.forEach(alarmNode -> {
            //add related info
            ((ObjectNode) alarmNode).put(RELATED_DATA_METRICS_NAME, metricsDO.getMetricsName());
        });
        jsonNode.putPOJO(KEY_ALARMS, singleAlarmsArrayNodes);
        String json = filterUselessField(jsonNode);
        return json;
    }

    private ObjectNode exportNewDataParser(String expectedServiceId, String dataParserSourceId, Map<String, String> dataParserIdAndNames) {
        Assert.isTrue(StringUtils.isNotBlank(dataParserSourceId), String.format("New data parser id can't be null or empty!"));
        DataParserSourceSearchOut dpsOut = dataParserSourceService.getById(dataParserSourceId);
        Assert.notNull(dpsOut, String.format("Can't find the new data parser by id:%s", dataParserSourceId));
        Assert.isTrue(StringUtils.equals(expectedServiceId, dpsOut.getTenantId()), "This new data parser is not match your service");
        dataParserIdAndNames.put(dataParserSourceId, dpsOut.getName());
        ObjectNode dataParserSourceNode = JacksonUtils.getObjectMapper().valueToTree(dpsOut);
        return dataParserSourceNode;
    }


    private ObjectNode exportDataParser(String expectedServiceId, String dataParserId, Map<String, String> dataParserIdAndNames) {
        Assert.isTrue(StringUtils.isNotBlank(dataParserId), String.format("Data parser id can't be null or empty!"));
        DataParserDO dataParserDO = dataParserHandler.getById(dataParserId);
        Assert.notNull(dataParserDO, String.format("can't find the data parser by id:%s", dataParserId));
        Assert.isTrue(StringUtils.equals(expectedServiceId, dataParserDO.getTenantId()), "This data parser is not match your service");
        DataParserV2Out dataParserV2Out = new DataParserV2Out();
        dataParserV2Out.transfer(dataParserDO);
        dataParserIdAndNames.put(dataParserId, dataParserDO.getName());

        ObjectNode dataParserNode = JacksonUtils.getObjectMapper().valueToTree(dataParserV2Out);
        return dataParserNode;
    }

    private ArrayNode exportPipeline(String dataParserId, String dataParserName,
                                     ConcurrentHashMap<String, String> pipelineIdAndNames,
                                     ConcurrentHashMap<String, String> collectorAndPipelineIds) {

        List<DataParserPipelineOut> pipelineDatas = dataParserServiceV2.getPipelinesByDataParserIds(new IdPara(dataParserId));
        if (CollectionUtils.isEmpty(pipelineDatas)) {
            return JacksonUtils.getObjectMapper().createArrayNode();
        }
        List<ObjectNode> pipelineObjectNodeList = Collections.synchronizedList(new ArrayList<>(pipelineDatas.size()));

        List<CompletableFuture> futureList = new ArrayList<>();
        for (DataParserPipelineOut pipeline : pipelineDatas) {
            futureList.add(
                    CompletableFuture.supplyAsync(() -> exportPipeline(pipeline, pipelineIdAndNames, collectorAndPipelineIds, dataParserName), executorService)
                            .thenAcceptAsync(pipelineNode -> pipelineObjectNodeList.add(pipelineNode), executorService));
        }
        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[]{})).join();

        ArrayNode pipelineArrayNodes = JacksonUtils.getObjectMapper().valueToTree(pipelineObjectNodeList);
        return pipelineArrayNodes;
    }

    private ObjectNode exportPipeline(DataParserPipelineOut pipeline,
                                      ConcurrentHashMap<String, String> pipelineIdAndNames,
                                      ConcurrentHashMap<String, String> collectorAndPipelineIds,
                                      String dataParserName) {
        PipelineInfoOut pipelineExtendsInfo = dataParserServiceV2.getPipelineInfoByPipelineId(pipeline.getId());
        pipeline.setFields(pipelineExtendsInfo.getFields());
        pipeline.setProcessores(pipelineExtendsInfo.getProcessors());
        //convert and add related info
        ObjectNode pipelineNode = JacksonUtils.getObjectMapper().valueToTree(pipeline);
        if (StringUtils.isNotBlank(dataParserName)) {
            pipelineNode.put(RELATED_DATA_PARSER_NAME, dataParserName);
        }
        //
        pipelineIdAndNames.put(pipeline.getId(), pipeline.getName());
        if (StringUtils.isNotBlank(pipeline.getCollectorId())) {
            collectorAndPipelineIds.put(pipeline.getCollectorId(), pipeline.getId());
        }
        return pipelineNode;
    }


    private ArrayNode exportMetrics(ConcurrentHashMap<String, String> collectorAndPipelineIds,
                                    ConcurrentHashMap<String, String> pipelineIdAndNames,
                                    ConcurrentHashMap<String, String> metricsIdAndNames) {
        List<ObjectNode> metricsObjectNodeList = Collections.synchronizedList(new ArrayList<>());
        Enumeration<String> collectorIds = collectorAndPipelineIds.keys();

        List<CompletableFuture> futureList = new ArrayList<>();
        while (collectorIds.hasMoreElements()) {
            final String collectorId = collectorIds.nextElement();
            futureList.add(CompletableFuture.runAsync(() -> {
                List<MetricsDO> metricsDOList = metricsDAO.listMetricsByCollectorId(collectorId);
                if (!CollectionUtils.isEmpty(metricsDOList)) {
                    for (MetricsDO metricsDO : metricsDOList) {

                        //for Field & Aggregateion(rule)
                        MetricsOut metricsOut = metricsService.getMetricsById(metricsDO.getId());
                        //convert and add related info
                        ObjectNode metricsNode = JacksonUtils.getObjectMapper().valueToTree(metricsOut);
                        metricsNode.put(RELATED_PIPELINE_NAME, Optional.of(pipelineIdAndNames.get(collectorAndPipelineIds.get(collectorId))).orElseThrow(RuntimeException::new));
                        metricsObjectNodeList.add(metricsNode);
                        //
                        metricsIdAndNames.put(metricsOut.getId(), metricsDO.getMetricsName());
                    }
                }
            }, executorService));
        }
        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[]{})).join();
        ArrayNode metricsArrayNodes = JacksonUtils.getObjectMapper().valueToTree(metricsObjectNodeList);
        return metricsArrayNodes;
    }

    private ArrayNode exportAlarms(ConcurrentHashMap<String, String> metricsIdAndNames) {
        ArrayNode alarmsArrayNodes = JacksonUtils.getObjectMapper().createArrayNode();
        List<ArrayNode> alarmsArrayNodeList = Collections.synchronizedList(new ArrayList<>());

        List<CompletableFuture> futureList = new ArrayList<>();
        for (Map.Entry<String, String> metricsEntry : metricsIdAndNames.entrySet()) {
            futureList.add(CompletableFuture.runAsync(() -> {
                final String metricsId = metricsEntry.getKey();
                List<AlarmDefinition> alarmDefinitions = alarmDefinitionHandler.findByMetricId(metricsId);

                if (!CollectionUtils.isEmpty(alarmDefinitions)) {
                    alarmDefinitions.stream().forEach(alarmDefinition -> {
                        List<TagInputDo> tagInputDos = resourceTagService.listResourceTagOutDo(alarmDefinition.getTenantId(), alarmDefinition.getId(),
                                ResourceTypeConstant.RESOURCE_AlARM_TYPE);
                        alarmDefinition.setLabelInfoList(tagInputDos);
                    });
                    ArrayNode singleAlarmsArrayNodes = JacksonUtils.getObjectMapper().valueToTree(alarmDefinitions);
                    singleAlarmsArrayNodes.forEach(alarmNode -> {
                        //add related info
                        ((ObjectNode) alarmNode).put(RELATED_DATA_METRICS_NAME, metricsIdAndNames.get(metricsId));
                    });
                    alarmsArrayNodeList.add(singleAlarmsArrayNodes);
                }
            }, executorService));
        }
        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[]{})).join();
        for (ArrayNode alarmArrayNode : alarmsArrayNodeList) {
            alarmsArrayNodes.addAll(alarmArrayNode);
        }
        return alarmsArrayNodes;
    }


    protected String filterUselessField(ObjectNode parserObjectNode) throws JsonProcessingException {
        String json = JacksonUtils.getObjectMapper().writeValueAsString(parserObjectNode);
        DocumentContext context = JsonPath.using(conf).parse(json);
        //all
        context.delete("$..id");
        context.delete("$..tenantId");
        context.delete("$..gmtCreate");
        context.delete("$..gmtModify");
        context.delete("$..editor");
        context.delete("$..creator");
        context.delete("$..createTime");
        context.delete("$..modifyTime");
        context.delete("$..collectorId");

        //data parser source
        context.delete("$.dataParserSource.createUserId");
        context.delete("$.dataParserSource.editUserId");
        context.delete("$.dataParserSource.dataFlowId");
        context.delete("$.dataParserSource.serviceName");
        context.delete("$.dataParserSource.createUserName");
        context.delete("$.dataParserSource.editUserName");
        //pipeline
        context.delete("$.pipeline[*].dataParserId");
        context.delete("$.pipeline[*].parentId");
        context.delete("$.pipeline[*].userId");
        context.delete("$.pipeline[*].userName");
        context.delete("$.pipeline[*].authResourceUrl");
        //pipeline fields
        context.delete("$.pipeline[*].fields[*].canEdit");
        context.delete("$.pipeline[*].fields[*].sourceField");
        //pipeline processors
        context.delete("$.pipeline[*].processores[*].dataParserPipelineId");
        //metrics
        context.delete("$.metrics[*]..metricsId"); //include $.pipeline[*].metrics[*].alarms[*].metricsId
        context.delete("$.metrics[*]..aggId");
        //tagList
        context.delete("$.metrics[*].labelInfoList[*].tagTypeId");
        //alarm
        context.delete("$.alarms[*]..userId");
        context.delete("$.alarms[*].subAlarmId");
        context.delete("$.alarms[*].metricId");
        context.delete("$.alarms[*].notifications[*].channel.parameters");
        context.delete("$.alarms[*].notifications[*].channel.shareServiceId");
        context.delete("$.alarms[*].alarmExtensionRelations[*].relationId");
        context.delete("$.alarms[*].derivedMetric.metricId");
        context.delete("$.alarms[*].derivedMetric.units[*].derivedMetricId");
        //tagList
        context.delete("$.alarms[*].labelInfoList[*].tagTypeId");


        return context.jsonString();
    }


}
