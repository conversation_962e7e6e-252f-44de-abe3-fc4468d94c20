package us.zoom.cube.site.lib.input;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 * @create 2020/7/28 1:35 PM
 */

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AlarmRuleSingleContentInput {

    /**
     * {@link us.zoom.infra.model.alarm.CompareType}
     */
    int type;

    String tag;

    String field;

    /**
     * number, string
     */
    String fieldType;

    /**
     * For map type fields, this field stores the key of the map
     */
    String mapKey;

    /**
     * ==, >, >=, <=, crIncrease, crDecrease
     */
    String operator;

    /**
     * Comparing value with the field value using the operator
     */
    String cmpValue;

    /**
     * Used for defining expressions, e.g, customized functions
     */
    String expression;
}
