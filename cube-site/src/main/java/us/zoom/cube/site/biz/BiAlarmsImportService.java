package us.zoom.cube.site.biz;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.ReadContext;
import com.zoom.op.monitor.dao.alarm.AlarmDefinitionDao;
import com.zoom.op.monitor.domain.alarm.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import us.zoom.cube.site.biz.alarm.AlarmDefinitionCheckService;
import us.zoom.cube.site.biz.alarm.AlarmDefinitionService;
import us.zoom.cube.site.biz.biport.AbstractCubeImportService;
import us.zoom.cube.site.core.ChannelHandler;
import us.zoom.cube.site.core.DashHandler;
import us.zoom.cube.site.core.MetricsHandler;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.core.tag.ResourceTypeConstant;
import us.zoom.cube.site.core.tag.TagConvertUtil;
import us.zoom.cube.site.core.tag.service.ResourceTagService;
import us.zoom.cube.site.core.tag.service.TagService;
import us.zoom.cube.site.infra.utils.JacksonUtils;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.biport.BiImportRelationInfo;
import us.zoom.cube.site.lib.input.FullSearchDashInput;
import us.zoom.cube.site.lib.input.dataparser.StringJsonInput;
import us.zoom.cube.site.lib.input.tag.input.ResourceTagInfoInput;
import us.zoom.cube.site.lib.output.tag.TagInfoOut;
import us.zoom.infra.dao.model.DashHasUserRelaDO;
import us.zoom.infra.dao.model.MetricsDO;
import us.zoom.infra.thread.ThreadLocalStore;
import us.zoom.infra.utils.CommonSplitConstants;

import java.util.*;
import java.util.stream.Collectors;

import static us.zoom.cube.site.infra.constants.BiportConstant.KEY_ALARMS;

/**
 * @author: Starls Ding
 * @date: 2023/7/19 09:48
 * @desc:
 */
@Slf4j
@Service
public class BiAlarmsImportService extends AbstractCubeImportService {

    @Autowired
    private TenantHandler tenantHandler;
    @Autowired
    private MetricsHandler metricsHandler;
    @Autowired
    private AlarmDefinitionService alarmDefinitionService;

    @Autowired
    private AlarmDefinitionCheckService alarmDefinitionCheckService;
    @Autowired
    private AlarmDefinitionDao alarmDefinitionDao;
    @Autowired
    private ChannelHandler channelHandler;
    @Autowired
    private DashHandler dashHandler;
    @Autowired
    private ResourceTagService resourceTagService;
    @Autowired
    private TagService tagService;


    @Override
    public void setNextImportService() {
        //do nothing
    }

    @Override
    protected BiImportRelationInfo doImport(StringJsonInput jsonInput, BiImportRelationInfo relaInfo) throws Exception {
        ObjectNode jsonNodes = JacksonUtils.getObjectMapper().readValue(jsonInput.getJson(), ObjectNode.class);
        ArrayNode alarmsArrayNode = jsonNodes.withArray(KEY_ALARMS);
        Map<String, String> alarmAndMetricsNameMap = new HashMap<>();
        alarmsArrayNode.forEach(alarmNode -> alarmAndMetricsNameMap.put(alarmNode.get("name").textValue(), alarmNode.get("metricsName").textValue()));

        JavaType alarmDefinitionType = JacksonUtils.getObjectMapper().getTypeFactory().constructParametricType(List.class, AlarmDefinition.class);
        List<AlarmDefinition> alarmDefinitions = JacksonUtils.getObjectMapper().readValue(alarmsArrayNode.toString(), alarmDefinitionType);

        String userId = ThreadLocalStore.getUserInfoLocal();

        for (AlarmDefinition alarmDefinition : alarmDefinitions) {
            String alarmName = alarmDefinition.getName();
            if (relaInfo.getDuplicatedAlarms().contains(alarmName)) {
                continue;
            }

            String metricsId = relaInfo.getMetricsNameAndIds().get(alarmAndMetricsNameMap.get(alarmName));
            Assert.notNull(metricsId, String.format("Can't find the metrics for alarm : %s", alarmName));

            alarmDefinition.setMetricId(metricsId);
            alarmDefinition.setUserId(userId);
            alarmDefinition.setTenantId(jsonInput.getTenantId());

            Date date = new Date();
            alarmDefinition.setCreateTime(date);
            alarmDefinition.setModifyTime(date);
            //notification
            List<Notification> notifications = alarmDefinition.getNotifications();
            notifications.forEach(notification -> {
                notification.setCreateTime(date);
                notification.setModifyTime(date);
                Channel channel = notification.getChannel();
                channel.setId(Optional.of(relaInfo.getChannelNameMap().get(channel.getName())).orElseThrow(RuntimeException::new).getId());
            });
            //extension for dashboard
            if (relaInfo.isNeedDashboard()) {
                for (AlarmExtensionRelation extendRelation : alarmDefinition.getAlarmExtensionRelations()) {
                    if (extendRelation.getRelationType() == AlarmExtensionRelationType.dashboard) {
                        extendRelation.setRelationId(relaInfo.getExistedDashboardNameAndIds().get(extendRelation.getRelationName()).get(0));
                    }
                }
            }
            alarmDefinitionCheckService.check(alarmDefinition);
            alarmDefinitionService.save(alarmDefinition);
            if(!CollectionUtils.isEmpty(alarmDefinition.getLabelInfoList())){
                ResourceTagInfoInput resourceTagInfoInput = new ResourceTagInfoInput();
                resourceTagService.deleteResourceTagByName(alarmDefinition.getId(), ResourceTypeConstant.RESOURCE_AlARM_TYPE);
                alarmDefinition.getLabelInfoList().forEach(labInfo->{
                    TagInfoOut tagInfoOut = TagConvertUtil.toTagInfoOut(labInfo);
                    String tagId = tagService.createOrUpdateTagInfo(tagInfoOut, ThreadLocalStore.getTenantInfoLocal(), ThreadLocalStore.getUserNameLocal());
                    if(tagId != null){
                        resourceTagInfoInput.setTagId(tagId);
                        resourceTagInfoInput.setResource(alarmDefinition.getId());
                        resourceTagInfoInput.setResourceType(ResourceTypeConstant.RESOURCE_AlARM_TYPE);
                        resourceTagService.addResourceTag(resourceTagInfoInput);
                    }
                });
            }else{
                resourceTagService.deleteResourceTagByName(alarmDefinition.getId(), ResourceTypeConstant.RESOURCE_AlARM_TYPE);
            }
            relaInfo.getImportedAlarms().add(alarmName);
        }
        return relaInfo;
    }

    @Override
    protected BiImportRelationInfo doPreCheck(StringJsonInput jsonInput, BiImportRelationInfo relaInfo, boolean checkDuringImport, boolean bySelf) throws Exception {
        final String serviceId = jsonInput.getTenantId();
        final boolean isIncremental = jsonInput.isIncremental();
        ReadContext jsonContext = JsonPath.parse(jsonInput.getJson());

        int alarmConfigNum = (int) jsonContext.read("$.alarms.length()");
        if (bySelf) {
            Assert.isTrue(alarmConfigNum == 1, "No any or more than one alarm configuration in the JSON, please check it!");
            //metrics check
            Map<String, String> metricsNameAndIds = checkMetrics(serviceId, jsonContext);
            relaInfo.setMetricsNameAndIds(metricsNameAndIds);
        }

        if (alarmConfigNum <= 0) {
            return relaInfo;
        }
        relaInfo.setHasAlarms(true);

        //check the if exist duplicate alarm
        List<String> jsonAlarmNames = jsonContext.read("$.alarms[*].name");

        List<String> existedAlarms = alarmDefinitionDao.listAllAlarmNames(serviceId);
        if (CollectionUtils.isNotEmpty(existedAlarms)) {
            List<String> duplicatedAlarms = new ArrayList<>(jsonAlarmNames);
            boolean isAllDuplicated = !duplicatedAlarms.retainAll(existedAlarms);
            if (!isIncremental) {
                //should be no any duplicated alarm if do full-quantity import
                Assert.isTrue(CollectionUtils.isEmpty(duplicatedAlarms), String.format("Duplicated Alarm [%s], please check it", StringUtils.join(duplicatedAlarms, CommonSplitConstants.COMMA_SPLIT)));
            } else if (isAllDuplicated) {
                //skip all the alarm, in other words, no need to do import alarm
                relaInfo.setHasAlarms(false);
                //return biImportRelationInfo;
            }
            relaInfo.getDuplicatedAlarms().addAll(duplicatedAlarms);
            relaInfo.getNewlyAddAlarms().addAll(CollectionUtils.subtract(jsonAlarmNames, duplicatedAlarms));
        }

        if(relaInfo.isHasAlarms()) {
            if (checkDuringImport) {
                //channel check has high priority
                List<String> channelNames = jsonContext.read("$.alarms[*].notifications[*].channel.name");
                Map<String, Channel> channelNameMap = checkChannelExist(serviceId, channelNames);
                relaInfo.getChannelNameMap().putAll(channelNameMap);
            }
            // alarm-related dashboard check
            Map<String, List<String>> dashboardNameAndIds = checkAlarmRelatedDashboard(serviceId, jsonContext);
            relaInfo.setExistedDashboardNameAndIds(dashboardNameAndIds);
        }

        return relaInfo;
    }

    //Map<Channel_Name,Channel>
    private Map<String, Channel> checkChannelExist(String serviceId, List<String> jsonChannelNames) {
        boolean noAlarm = CollectionUtils.isEmpty(jsonChannelNames);
        if (noAlarm) {
            return new HashMap<>();
        }
        List<Channel> existedChannelList = channelHandler.findByTenantId(serviceId);
        if (CollectionUtils.isEmpty(existedChannelList) && !noAlarm) {
            throw new RuntimeException("Error in the mapping channel!");
        }
        List<String> existedChannelNameList = existedChannelList.stream().map(Channel::getName).collect(Collectors.toList());
        //check the channel mapping
        Optional<String> jsonNoExistedChannel = jsonChannelNames.stream().filter(jsonChannel -> !existedChannelNameList.contains(jsonChannel)).findAny();
        Assert.isTrue(!jsonNoExistedChannel.isPresent(), String.format("Channel [%s] doesn't existed, please check the mapping", jsonNoExistedChannel.orElse(StringUtils.EMPTY)));

        return existedChannelList.stream().collect(Collectors.toMap(Channel::getName, kv -> kv));
    }

    private Map<String, List<String>> checkAlarmRelatedDashboard(String serviceId, ReadContext jsonContext) {
        List<Map<String, String>> dashboardConfigItems = jsonContext.read("$.alarms..alarmExtensionRelations[?(@.relationType == 'dashboard')]");
        if (CollectionUtils.isEmpty(dashboardConfigItems)) {
            return null;
        }
        Set<String> allJsonDashboardNames = dashboardConfigItems.stream().map(entry -> entry.get("relationName")).collect(Collectors.toSet());

        Map<String, List<String>> existedDashNameAndIds = dashHandler.fullSearchByName(new FullSearchDashInput(serviceId)).stream().collect(Collectors.groupingBy(DashHasUserRelaDO::getName, Collectors.mapping(DashHasUserRelaDO::getId, Collectors.toList())));

        for (String jsonDashName : allJsonDashboardNames) {
            Assert.isTrue(existedDashNameAndIds.containsKey(jsonDashName), String.format("Can't find dashboard [%s] during the alarm import, please check or create it!", jsonDashName));
            Assert.isTrue(existedDashNameAndIds.get(jsonDashName).size() == 1, String.format("This dashboard [%s] has more than one result, we can't decide which to use! ", jsonDashName));
        }
        return existedDashNameAndIds;
    }

    private Map<String, String> checkMetrics(String serviceId, ReadContext jsonContext) {
        List<String> metricsNames = jsonContext.read("$.alarms[*].metricsName");
        Map<String, String> metricsNameAndIds = new HashMap<>();
        for (String metricsName : metricsNames) {
            MetricsDO metricsDO = metricsHandler.findMetricsByNameAndTenantId(metricsName, serviceId);
            Assert.notNull(metricsDO, "Can't find the related Metrics for this Alarm");
            metricsNameAndIds.put(metricsName, metricsDO.getId());
        }
        return metricsNameAndIds;
    }
}
