package us.zoom.cube.site.api.web.out;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import us.zoom.cube.site.api.intercept.AuthInterceptor;
import us.zoom.cube.site.biz.AuthService;
import us.zoom.cube.site.biz.MetricsService;
import us.zoom.cube.site.biz.clickhouse.ClickhouseMetricsService;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.lib.BasePara;
import us.zoom.cube.site.lib.IdPara;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.MetricsInput;
import us.zoom.cube.site.lib.output.config.metrics.SimpleMetricsAggregationInput;
import us.zoom.cube.site.lib.query.*;

import jakarta.validation.Valid;

/**
 * <AUTHOR>
 * @create 2020/7/16 4:20 PM
 * <p>
 * Metrics configuration CRUD
 */
@RestController
@RequestMapping("/out/metrics")
public class OutMetricsControllerV2 {
    @Autowired
    private MetricsService metricsService;

    @Autowired
    private ClickhouseMetricsService clickhouseMetricsService;

    @Autowired
    private AuthService authService;

    @Autowired
    private TenantHandler tenantHandler;

    /**
     * Metrics configuration list
     *
     * @param pageQuery
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/searchMetrics", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject searchMetrics(@Valid @RequestBody PageQuery<MetricsQuery> pageQuery) throws Exception {
        return metricsService.searchMetrics(pageQuery);
    }

    /**
     * check metrics name before adding
     *
     * @param metricsName
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/checkMetricsName", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject checkMetricsName(@Valid @RequestBody MetricsName metricsName) throws Exception {
        return ResponseObject.success(metricsService.checkMetricsName(metricsName.getMetricsName()));
    }

    @RequestMapping(value = "/addMetrics", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject addMetrics(@Valid @RequestBody MetricsInput metricsInput) throws Exception {
        return metricsService.addMetrics(metricsInput);
    }

    @RequestMapping(value = "/editMetrics", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject editMetrics(@Valid @RequestBody MetricsInput metricsInput) throws Exception {
        return metricsService.editMetrics(metricsInput);
    }

    @RequestMapping(value = "deleteMetrics", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject deleteMetrics(@Valid @RequestBody IdPara idPara) throws Exception {
        return metricsService.deleteMetrics(idPara);
    }


    /**
     * @param nameQuery
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "getSimpleAggregationByMetricsName", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject getSimpleAggregationByMetricsName(@Valid @RequestBody MetricsNameQuery nameQuery) throws Exception {
        return metricsService.getSimpleAggregationByMetricsName(nameQuery);
    }

    @RequestMapping(value = "editSimpleMetricsAggregation", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject editSimpleMetricsAggregation(@Valid @RequestBody SimpleMetricsAggregationInput simpleMetricsAggregationInput) throws Exception {
        return metricsService.editSimpleMetricsAggregation(simpleMetricsAggregationInput);
    }

    /**
     * get metrics for alarm creation
     *
     * @param basePara
     * @return
     */
    @RequestMapping(value = "/getMetricsByTenant", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject getMetricsByTenant(@Valid @RequestBody BasePara basePara) {
        return metricsService.getMetricsListByTenantId(basePara.getUserId(), basePara.getTenantId());
    }

    /**
     * get metrics tags and fields when one is specified for alarm creation
     *
     * @param idPara
     * @return
     */
    @RequestMapping(value = "/getFieldAndTagByMetricsId", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject getFieldsByMetricsId(@Valid @RequestBody IdPara idPara) {
        //，tagield，
        return metricsService.getFieldAndTagByMetricsId( idPara);
    }

    @RequestMapping(value = "/queryMetricsByName", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject queryMetricsByName(@Valid @RequestBody MetricsName metricsName) {
        return metricsService.queryMetricsByName(metricsName);
    }

    @RequestMapping(value = "/showClickhouseTableSchemaV2", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject showClickhouseTableSchemaV2(@Valid @RequestBody ClickhouseMetaQuery metaQuery) {
        authService.mustHasSuchDb(AuthInterceptor.getUserId(), metaQuery.getServiceName());
        return clickhouseMetricsService.showClickhouseTableSchemaV2(tenantHandler.matchInfluxDbNameToTenantName(metaQuery.getServiceName()), metaQuery.getTable());
    }
}
