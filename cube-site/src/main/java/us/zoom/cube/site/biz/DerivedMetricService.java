package us.zoom.cube.site.biz;

import com.google.common.collect.Lists;
import com.zoom.op.monitor.domain.DerivedMetric;
import com.zoom.op.monitor.domain.DerivedMetricFields;
import com.zoom.op.monitor.domain.DerivedMetricUnit;
import jakarta.validation.Valid;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.integrations.MetricsFieldTypeEnum;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.site.biz.alarm.AlarmDefinitionService;
import us.zoom.cube.site.core.schedulery.InnerReferenceScheduleHandler;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.IdPara;
import us.zoom.cube.site.lib.input.DerivedMetricInput;
import us.zoom.cube.site.lib.input.MetricsInput;
import us.zoom.cube.site.lib.output.config.metrics.FieldOrTag;
import us.zoom.infra.dao.model.MetricsDO;
import us.zoom.infra.dao.service.DerivedMetricDAO;
import us.zoom.infra.dao.service.DerivedMetricUnitDAO;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Handler for derived metric operations
 *
 * <AUTHOR>
 * @date 2025/04/21
 */
@Component
public class DerivedMetricService {

    @Autowired
    private DerivedMetricDAO derivedMetricDAO;

    @Autowired
    private DerivedMetricUnitDAO derivedMetricUnitDAO;

    @Autowired
    private InnerReferenceScheduleHandler innerReferenceScheduleHandler;

    @Autowired
    private MetricsService metricsService;

    @Autowired
    private AlarmDefinitionService alarmDefinitionService;

    /**
     * Add a new derived metric with its units
     *
     * @param derivedInput the input containing derived metric and units information
     * @return the id of newly created metric
     */
    @Transactional(rollbackFor = Exception.class)
    public DerivedMetric add(@Valid DerivedMetricInput derivedInput) {
        Assert.isTrue(!CollectionUtils.isEmpty(derivedInput.getUnits()), "Units cannot be empty");

        //add original metrics
        MetricsInput originalMetricsInput = DerivedMetricInput.toOriginalMetricsInput(derivedInput);
        MetricsDO metricsDO = metricsService.addMetricsBase(originalMetricsInput);
        String originalMetricId = metricsDO.getId();

        DerivedMetric derivedMetric = DerivedMetricInput.toDerivedMetric(derivedInput);
        derivedMetric.setId(IdUtils.generateId());
        derivedMetric.setMetricId(originalMetricId);
        derivedMetricDAO.add(derivedMetric);

        addUnits(derivedMetric);

        if(null != derivedMetric.getCheckInterval()) {
            innerReferenceScheduleHandler.addJobForDerivedMetric(derivedMetric, derivedInput.isEnableScheduler());
        }

        return derivedMetric;
    }

    /**
     * Edit an existing derived metric and its units
     *
     * @param derivedInput the metric input containing updated information
     */
    @Transactional(rollbackFor = Exception.class)
    public void edit(@Valid DerivedMetricInput derivedInput) {
        //checking
        Assert.isTrue(StringUtils.isNotBlank(derivedInput.getId()), "Id cannot be empty");
        Assert.isTrue(StringUtils.isNotBlank(derivedInput.getMetricId()), "MetricId cannot be empty");
        DerivedMetric exitDerivedMetric = derivedMetricDAO.getById(derivedInput.getId());
        if(null == exitDerivedMetric) {
            throw new IllegalArgumentException("Derived metric not found");
        }
        Assert.isTrue(StringUtils.equals(exitDerivedMetric.getMetricId(), derivedInput.getMetricId()), "Cannot change original metric(id)");

        //only update the original metrics tags, because no other column need to be updated for original metrics by derivedMetric
        MetricsInput originalMetricsInput = DerivedMetricInput.toOriginalMetricsInput(derivedInput);
        metricsService.updateMetricsTags(originalMetricsInput.buildMetricsDO());

        DerivedMetric metric = DerivedMetricInput.toDerivedMetric(derivedInput);
        derivedMetricDAO.edit(metric);

        // delete all units and add again
        derivedMetricUnitDAO.deleteByDerivedMetricId(derivedInput.getId());
        addUnits(metric);

        innerReferenceScheduleHandler.updateJobForDerivedMetric(metric, derivedInput.isEnableScheduler());
    }

    /**
     * Delete a metric and its units
     *
     * @param id the metric id to delete
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        if (StringUtils.isBlank(id)) {
            return;
        }
        derivedMetricUnitDAO.deleteByDerivedMetricId(id);

        DerivedMetric derivedMetric = derivedMetricDAO.getById(id);
        if(null != derivedMetric) {
            derivedMetricDAO.delete(id);
            metricsService.deleteMetrics(new IdPara(derivedMetric.getMetricId()));
        }

        innerReferenceScheduleHandler.deleteJobForDerivedMetric(id);
    }

    /**
     * Get a metric by id, including its units
     *
     * @param id the metric id
     * @return the metric with its units
     */
    public DerivedMetric getById(String id) {
        DerivedMetric metric = derivedMetricDAO.getById(id);
        if (metric == null) {
            return null;
        }
        List<DerivedMetricUnit> units = derivedMetricUnitDAO.getByDerivedMetric(id);
        metric.setUnits(units);
        return metric;
    }

    /**
     * Get a metric by id, including its units
     *
     * @param metricId the metric id
     * @return the metric with its units
     */
    public DerivedMetric getByMetricId(String metricId) {
        DerivedMetric metric = derivedMetricDAO.getByMetricId(metricId);
        if (metric == null) {
            return null;
        }
        List<DerivedMetricUnit> units = derivedMetricUnitDAO.getByDerivedMetric(metric.getId());
        metric.setUnits(units);
        return metric;
    }

    /**
     * Get a metric by id, including its units
     *
     * @param id the metric id
     * @return the metric with its units
     */
    public DerivedMetric get(String id) {
        DerivedMetric metric = derivedMetricDAO.getById(id);
        if (metric == null) {
            return null;
        }
        List<DerivedMetricUnit> units = derivedMetricUnitDAO.getByDerivedMetric(id);
        metric.setUnits(units);
        return metric;
    }

    /**
     * Search metrics with pagination
     *
     * @param metricId the metric id to filter by
     * @param name the name to filter by
     * @param tenantId the tenant to filter by
     * @param pageIndex the page number
     * @param pageSize the size of each page
     * @return list of matching metrics
     */
    public List<DerivedMetric> search(String metricId, String name, String tenantId, int pageIndex, int pageSize) {
        int offset = Math.max(0, pageSize * (Math.max(1, pageIndex) - 1));
        List<DerivedMetric> metrics = derivedMetricDAO.search(metricId, name, tenantId, offset, pageSize);
        if (CollectionUtils.isEmpty(metrics)) {
            return metrics;
        }
        for (DerivedMetric metric : metrics) {
            List<DerivedMetricUnit> units = derivedMetricUnitDAO.getByDerivedMetric(metric.getId());
            metric.setUnits(units);
        }
        return metrics;
    }

    /**
     * Count total number of metrics matching the search criteria
     *
     * @param metricId the metric id to filter by
     * @param name the name to filter by
     * @param tenantId the tenant to filter by
     * @return total count of matching metrics
     */
    public long count(String metricId, String name, String tenantId) {
        return derivedMetricDAO.count(metricId, name, tenantId);
    }

    /**
     * Adds multiple derived metric units to a derived metric
     *
     * @param derivedMetric the derived metric to add units to
     */
    private void addUnits(DerivedMetric derivedMetric) {
        if (derivedMetric == null || derivedMetric.getUnits() == null) {
            return;
        }

        for (DerivedMetricUnit unit : derivedMetric.getUnits()) {
            unit.setDerivedMetricId(derivedMetric.getId());
            if (StringUtils.isBlank(unit.getId())) {
                unit.setId(IdUtils.generateId());
            }
            derivedMetricUnitDAO.add(unit);
        }
    }

    public List<FieldOrTag> getTagAndField(String metricsId) {
//        authService.checkAuth(ThreadLocalStore.getUserInfoLocal(), ThreadLocalStore.getTenantInfoLocal(), ThreadLocalStore.getApiPath());
        List<FieldOrTag> fieldOrTags = Lists.newArrayList();
        DerivedMetric derivedMetric = getByMetricId(metricsId);
        if (Objects.isNull(derivedMetric)) {
            return fieldOrTags;
        }
        //add tags
        String tagNames = derivedMetric.getTags();
        if (!StringUtils.isEmpty(tagNames)) {
            String[] tagNameArr = tagNames.split(",");
            for (String tagName : tagNameArr) {
                FieldOrTag tag = FieldOrTag.newTag();
                tag.setFieldOrTagName(tagName);
                tag.setValueType(MetricsFieldTypeEnum.string.name());
                fieldOrTags.add(tag);
            }
        }
        //add fields
        if (CollectionUtils.isEmpty(derivedMetric.getUnits())) {
            return fieldOrTags;
        }
        derivedMetric.getUnits().forEach(unit -> {
            DerivedMetricFields fields = JsonUtils.toObject(unit.getFields(), DerivedMetricFields.class);
            if (fields == null) {
                return;
            }

            // Process number fields
            Optional.ofNullable(fields.getNumber())
                    .ifPresent(number -> number.forEach(name -> {
                        FieldOrTag field = FieldOrTag.newField();
                        field.setFieldOrTagName(name);
                        field.setValueType(MetricsFieldTypeEnum.number.name());
                        fieldOrTags.add(field);
                    }));

            // Process string fields
            Optional.ofNullable(fields.getString())
                    .ifPresent(string -> string.forEach(name -> {
                        FieldOrTag field = FieldOrTag.newField();
                        field.setFieldOrTagName(name);
                        field.setValueType(MetricsFieldTypeEnum.string.name());
                        fieldOrTags.add(field);
                    }));
        });

        //add previous variables
        alarmDefinitionService.extraFieldOrTag(fieldOrTags, false);
        return fieldOrTags;
    }

    public DerivedMetric getByNameAndTenantId(String name, String tenantId) {
        return derivedMetricDAO.getByNameAndTenantId(name, tenantId);
    }

}