package us.zoom.cube.site.api.web;


import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import us.zoom.cube.site.biz.AuthService;
import us.zoom.cube.site.biz.MetricsService;
import us.zoom.cube.site.biz.clickhouse.ClickhouseMetricsService;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.infra.enums.WebCodeEnum;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.query.ClickhouseMetaQuery;
import us.zoom.cube.site.lib.query.ClickhouseQuery;
import us.zoom.cube.site.lib.query.MetricQuery;
import us.zoom.cube.site.lib.query.metric.MetricFieldQuery;
import us.zoom.cube.site.lib.query.metric.MetricIdsQuery;
import us.zoom.infra.clickhouse.BucketReplace;
import us.zoom.infra.clickhouse.ClickhouseSqlUtil;
import us.zoom.infra.clickhouse.QueryMonitor;
import us.zoom.infra.clickhouse.SqlPlaceholder;
import us.zoom.infra.utils.DateUtils;

import jakarta.validation.Valid;
import java.io.IOException;
import java.util.Date;

@RestController
@RequestMapping("/api/metric")
@Slf4j
public class MetricsDisplayController {

    @Autowired
    MetricsService metricsService;

    @Autowired
    ClickhouseMetricsService clickhouseMetricsService;

    @Autowired
    private TenantHandler tenantHandler;

    @Autowired
    private AuthService authService;

    @RequestMapping(value = "/getMetricDetail", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject getMetricDetail(@Valid @RequestBody MetricQuery metricQuery) throws IOException {
     //   adjustTime(metricQuery);

        return metricsService.getMetricDetail(metricQuery);
    }

    @RequestMapping(value = "/queryClickHouse", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject queryClickHouse(@Valid @RequestBody ClickhouseQuery metricQuery) throws IOException {
        return metricsService.queryClickHouse(metricQuery);
    }

    @RequestMapping(value = "/getMetricDisplay", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject getMetricDisplay(@Valid @RequestBody MetricQuery metricQuery) throws IOException {
        return metricsService.getMetricDisplay(metricQuery);
    }

    @RequestMapping(value = "/showClickhouseTables", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject showClickhouseTables(@Valid @RequestBody ClickhouseMetaQuery metaQuery) throws IOException {
        return clickhouseMetricsService.showClickhouseTables(metaQuery);
    }

    @RequestMapping(value = "/showClickhouseTablesAndMetricId", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject showClickhouseTablesAndMetricId(@Valid @RequestBody ClickhouseMetaQuery metaQuery) throws IOException {
        return clickhouseMetricsService.showClickhouseTablesAndMetricId(metaQuery);
    }

    @RequestMapping(value = "/showAllClickhouseInnerTables", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject showAllClickhouseTables(@Valid @RequestBody ClickhouseMetaQuery metaQuery) throws IOException {
        return clickhouseMetricsService.showAllClickhouseInnerTables(metaQuery);
    }

    @RequestMapping(value = "/showClickhouseTableSchema", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject showClickhouseTableSchema(@Valid @RequestBody ClickhouseMetaQuery metaQuery) throws IOException {
        authService.mustHasSuchDb(metaQuery.getUserId(),metaQuery.getServiceName());
        return clickhouseMetricsService.showClickhouseTableSchema(tenantHandler.matchInfluxDbNameToTenantName(metaQuery.getServiceName()),metaQuery.getTable());
    }

    @RequestMapping(value = "/showClickhouseTableSchemaV2", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject showClickhouseTableSchemaV2(@Valid @RequestBody ClickhouseMetaQuery metaQuery) {
        authService.mustHasSuchDb(metaQuery.getUserId(),metaQuery.getServiceName());
        authService.checkAuth(metaQuery);
        return clickhouseMetricsService.showClickhouseTableSchemaV2(tenantHandler.matchInfluxDbNameToTenantName(metaQuery.getServiceName()),metaQuery.getTable());
    }

    @RequestMapping(value = "/getDbSchema", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject getDbSchema(@Valid @RequestBody MetricQuery metricQuery) throws IOException {
        return metricsService.getDbSchema(metricQuery);
    }


    @Deprecated
    @RequestMapping(value = "/queryRawData", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject queryRawData(@Valid @RequestBody MetricQuery metricQuery) throws IOException {
        adjustTime(metricQuery);
        return metricsService.queryRawData(metricQuery);
    }


    private void adjustTime(MetricQuery metricQuery) {
        if(metricQuery.getTimeZone() != null ){
            if(metricQuery.getBegin() != null ){
                metricQuery.setBegin( new Date(DateUtils.addHour(metricQuery.getBegin(),metricQuery.getTimeZone())));
            }

            if(metricQuery.getEnd() != null ){
                metricQuery.setEnd( new Date(DateUtils.addHour(metricQuery.getEnd(),metricQuery.getTimeZone())));
            }

        }
    }


    @RequestMapping(value = "/searchMetric", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject searchMetric(@Valid @RequestBody MetricIdsQuery metricQuery) throws IOException {
        return metricsService.getMetricsListByTenantId(metricQuery.getUserId(),metricQuery.getTenantId());
    }

    @RequestMapping(value = "/getFieldValues", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject getFieldValues(@Valid @RequestBody MetricFieldQuery metricFieldQuery) throws IOException {
        return metricsService.getFieldValues(metricFieldQuery);
    }



}
