package us.zoom.cube.site.lib.input;

import com.google.common.collect.Lists;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.BasePara;
import us.zoom.cube.site.lib.output.tag.TagInfoOut;
import us.zoom.infra.dao.model.MetricsDO;
import us.zoom.infra.enums.MetricsTypeEnum;

import jakarta.validation.constraints.NotNull;
import java.util.*;
import java.util.regex.Matcher;

import static us.zoom.infra.utils.CommonSplitConstants.COMMA_SPLIT;
import static us.zoom.infra.utils.RegexConstants.CONFIG_NAME_PATTERN;

/**
 * <AUTHOR>
 * @create 2020/7/16 5:40 PM
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MetricsInput extends BasePara {

    String selfDefinedId;

    String id;
    @NotNull
    String metricsName;

    String collectorId;

    List<String> tagNames = new ArrayList<>();

    Integer type;

    Integer originPeriod;

    String creator;

    String editor;

    /**
     * origin metrics uses
     */
    List<MetricsFieldInput> metricsFieldList = new ArrayList<>();

    /**
     * aggregate metrics uses
     */
    MetricsAggregationInput metricsAggregation = new MetricsAggregationInput();

    MetricsOriginalInput metricsOriginalInput = new MetricsOriginalInput();

    /**
     * If true, then collectorId is null
     */
    boolean isFromBigData = false;

    boolean enabled = true;

    String documentLink;

    private List<TagInfoOut> labelInfoList;

    public void check() {
        metricsName = metricsName.trim();
        Matcher metricsNameMatcher = CONFIG_NAME_PATTERN.matcher(metricsName);
        Assert.isTrue(metricsNameMatcher.find(), "Metrics names can only contain [0-9a-zA-Z_]");
        Set<String> tagSet = new HashSet<>();
        tagNames = Optional.ofNullable(tagNames).orElse(Lists.newArrayList());
        tagNames.forEach(tagName -> {
            tagName = tagName.trim();
            tagSet.add(tagName);
            Matcher tagNameMatcher = CONFIG_NAME_PATTERN.matcher(tagName);
            Assert.isTrue(tagNameMatcher.find(), "Tag names can only contain [0-9a-zA-Z_]");
        });
        Assert.isTrue(type == MetricsTypeEnum.AGGREGATION.getValue() || type == MetricsTypeEnum.ORIGINAL.getValue(), "Incorrect type num");
        if (type == MetricsTypeEnum.ORIGINAL.getValue()) {
            Assert.isTrue(!CollectionUtils.isEmpty(metricsFieldList), "metricsFieldList Input cannot be empty");
            for (MetricsFieldInput metricsFieldInput : metricsFieldList) {
                metricsFieldInput.check();
                String fieldName = metricsFieldInput.getFieldName().trim();
                if(tagSet.contains(fieldName)){
                    Assert.isTrue(false, "Not support choose the same field in Tags and Metrics Field List, the error field = " + fieldName);
                }
            }
        }
        if (!isFromBigData) {
            Assert.notNull(collectorId, "collectorId cannot be null");
        }
        if (type == MetricsTypeEnum.AGGREGATION.getValue()) {
            Assert.notNull(metricsAggregation, "MetricsAggregation Input cannot be null");
            metricsAggregation.check();
        }
        if(originPeriod != null){
            Assert.isTrue(originPeriod > 0, "period must be greater than 0");
        }
        if (selfDefinedId != null) {
            Assert.isTrue(selfDefinedId.length() <= 100, "selfDefinedId's length cannot be greater than 100");
        }
    }

    public void sortAggRules() {
        if(!type.equals(MetricsTypeEnum.AGGREGATION.getValue())){
            return;
        }
        List<AggregationFunctionRuleInput> common = metricsAggregation.getMetricsAggregationRuleCompose().getCommon();
        // sort, so original function item can generate id before conditional one
        common.sort((a,b) -> {
            if(a.getAggField().equals(b.getAggField())){
                return a.getIsConditioned().compareTo(b.getIsConditioned());
            }else {
                return a.getAggField().compareTo(b.getAggField());
            }
        });

        List<AggregationHistogramRuleInput> histogram = metricsAggregation.getMetricsAggregationRuleCompose().getHistogram();
        histogram.sort((a,b) -> {
            return a.getAggField().compareTo(b.getAggField());
        });
        // Simplify verification
        histogram.forEach(e -> {
            e.getRanges().sort((a,b) -> {
                return a.getLowerLimit().compareTo(b.getLowerLimit());
            });
        });
        List<AggregationPercentileRuleInput> percentile = metricsAggregation.getMetricsAggregationRuleCompose().getPercentile();
        percentile.sort((a,b) -> {
            return a.getAggField().compareTo(b.getAggField());
        });
        // Simplify verification
        percentile.forEach(e -> {
            e.getPercentileValues().sort((a,b) -> {
                return a.getValue().compareTo(b.getValue());
            });
        });
        List<AggregationCustomFieldRuleInput> customize = metricsAggregation.getMetricsAggregationRuleCompose().getCustomize();
        customize.sort((a,b) -> {
            return a.getFieldName().compareTo(b.getFieldName());
        });

    }

    public MetricsDO toAddMetricsDO(){
        MetricsDO metricsDO = new MetricsDO();
        BeanUtils.copyProperties(this, metricsDO);
        if (selfDefinedId != null) {
            metricsDO.setId(selfDefinedId);
        } else {
            metricsDO.setId(IdUtils.generateId());
        }
        metricsDO.setTagNames(StringUtils.join(tagNames, COMMA_SPLIT));
        return metricsDO;
    }

    public MetricsDO buildMetricsDO() {
        MetricsDO metricsDO = new MetricsDO();
        BeanUtils.copyProperties(this, metricsDO);
        metricsDO.setTagNames(StringUtils.join(tagNames, COMMA_SPLIT));
        return metricsDO;
    }


}
