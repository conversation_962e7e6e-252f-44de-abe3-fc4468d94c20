package us.zoom.cube.site.lib.input.alarm.batch;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * @description:
 * @author: <EMAIL>
 * @date: 2024-07-31 14:45
 **/
@Data
public class RuleConditionInput implements Serializable {

    @Serial
    private static final long serialVersionUID = 3771856229843443115L;
    private String id;

    private String conditionType;

    private String name;

    private String mapKey;

    private String operator;

    private String threshold;

    private String expression;
    /**
     * histogram&summary
     * histogram([1,2,10,30],[1,10,15,20],${histogramQuantile})>12
     */
    private String histogramQuantile;
}
