package us.zoom.cube.site.core.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.common.TaskTypeEnum;
import us.zoom.cube.site.biz.SysParaService;
import us.zoom.cube.site.core.RsaService;
import us.zoom.infra.dao.model.ClickhouseClusterDO;
import us.zoom.infra.dao.model.CubeServerDO;
import us.zoom.infra.dao.model.EnvironmentDO;
import us.zoom.infra.dao.model.EnvironmentRunTimeDO;
import us.zoom.infra.dao.service.*;
import us.zoom.infra.utils.IpUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-07-12 10:11
 */
@Component
@Slf4j
public class ClickhouseLoader implements CacheLoader {

    @Value("${cube-site.server.env:}")
    private String serverEnv;

    @Autowired
    private ClickhouseTenantRelationDao tenantRelationDao;

    @Autowired
    private ClickhouseClusterDao clusterDao;

    @Autowired
    private TenantDAO tenantDAO;

    @Autowired
    private ClickhouseHandlerFactory clickhouseHandlerFactory;

    @Autowired
    private RsaService rsaService;

    @Autowired
    private CubeServerDAO cubeServerDAO;

    @Autowired
    private EnvironmentRunTimeDAO environmentRunTimeDAO;

    @Autowired
    private SysParaService sysParaService;

    @Autowired
    private EnvironmentDAO environmentDAO;

    @Override
    public void load() {
        try {
            List<ClickhouseClusterDO> clusters = clusterDao.search();
            if (clickhouseHandlerFactory.isRunOnLocal()) {
                clusters = clusters.stream()
                        .filter(u -> "local".equals(u.getEnvironment())).map(u -> {
                            u.setIsDefault(true);
                            return u;
                        })
                        .collect(Collectors.toList());
            } else {
                clusters = clusters.stream()
                        .filter(u -> !"local".equals(u.getEnvironment()))
                        .collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(clusters)) {
                log.warn("No cluster has been defined");
                return;
            }
            for (ClickhouseClusterDO c : clusters) {
                try {
                    c.setUsername(rsaService.decrypt(c.getUsername()));
                    c.setPassword(rsaService.decrypt(c.getPassword()));
                } catch (Exception e) {
                    log.error("Decrypt error for cluster {} {} error ={}", c.getId(), c.getName(), e);
                }
            }
            clickhouseHandlerFactory.get().refresh(clusters, tenantRelationDao.getAll(), tenantDAO.listAll());

            if (StringUtils.isNotBlank(serverEnv)) {
                // set ClickHouse env
                log.info("[Config Loader] current ClickHouse env is: {}", serverEnv);
                clickhouseHandlerFactory.get().setEnvironment(serverEnv);
            } else {
                List<CubeServerDO> cubeServerDOS = cubeServerDAO.listByIpAndType(IpUtils.getLocalIP(), TaskTypeEnum.site.name());
                if (CollectionUtils.isEmpty(cubeServerDOS)) {
                    log.warn("Current server has not been registered.");
                } else {
                    clickhouseHandlerFactory.get().setEnvironment(cubeServerDOS.get(0).getEnv());
                }
            }

            List<EnvironmentRunTimeDO> environmentRunTimeDOS = environmentRunTimeDAO.searchAll();
            if (CollectionUtils.isEmpty(environmentRunTimeDOS)) {
                log.warn("Runtime environment has not been registered.");
            } else {
                EnvironmentRunTimeDO environmentRunTimeDO = environmentRunTimeDOS.get(0);
                String env = environmentRunTimeDO.getIsForce() == 1 ? environmentRunTimeDO.getForceEnvironment() : environmentRunTimeDO.getWorkingEnvironment();
                String systemForceClickhouseEnv = sysParaService.getSystemForceClickhouseEnv();
                if (systemForceClickhouseEnv != null) {
                    env = systemForceClickhouseEnv;
                }
                clickhouseHandlerFactory.get().setQueryEnv(env);
                log.info("Current query running on {}, is_force={}, force_env={}, working_env={}, system_force_env={}",
                        env, environmentRunTimeDO.getIsForce(), environmentRunTimeDO.getForceEnvironment(), environmentRunTimeDO.getWorkingEnvironment(), systemForceClickhouseEnv);
            }
            clickhouseHandlerFactory.getClickhouseWriter().setAllEnvs(environmentDAO.searchAll().stream().map(EnvironmentDO::getName).collect(Collectors.toSet()));
            log.info("Done load clickhouse handlers,size ={}", clickhouseHandlerFactory.get().getAll().size());
            //remove dead host
            clickhouseHandlerFactory.get().actualizeAll();
            log.info("Done refresh clickhouse handlers");
        } catch (Exception e) {
            log.error("Load Clickhouse error! ", e);
        }
    }
}
