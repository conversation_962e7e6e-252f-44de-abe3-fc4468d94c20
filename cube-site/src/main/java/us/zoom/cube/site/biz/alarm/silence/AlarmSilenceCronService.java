package us.zoom.cube.site.biz.alarm.silence;


import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.sdk.util.MonitorLogReporter;
import us.zoom.cube.site.core.SysParaHandler;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.core.UserHandler;
import us.zoom.cube.site.core.alarm.group.AlarmGroupHandler;
import us.zoom.cube.site.core.alarm.silence.AlarmSilenceHandler;
import us.zoom.cube.site.core.alarm.silence.AlarmSilenceRecurringScheduleHandler;
import us.zoom.cube.site.lib.common.MonitorTypeEnum;
import us.zoom.cube.site.lib.monitor.SilenceCallbackErrorMonitorLogEntity;
import us.zoom.cube.site.lib.monitor.alarm.SilenceAutoDeleteMonitorLogEntity;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.service.DistributedLockDAO;
import us.zoom.infra.enums.AlarmSilenceStatusEnum;
import us.zoom.infra.thread.NamedThreadFactory;
import us.zoom.infra.utils.DateUtils;
import us.zoom.infra.utils.IpUtils;
import us.zoom.cube.lib.utils.JsonUtils;

import jakarta.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import static us.zoom.infra.enums.alarmSilence.AlarmSilenceAutoDeleteEnum.AUTO;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class AlarmSilenceCronService {

    private static ScheduledExecutorService scheduler;
    private Integer scheduleSeconds= 60;
    private int lockTimeOut=(scheduleSeconds/60);

    @Autowired
    private DistributedLockDAO distributedLockDAO;

    private static TenantHandler tenantHandler;

    @Autowired
    private AlarmSilenceHandler alarmSilenceHandler;

    @Autowired
    private AlarmSilenceRecurringScheduleHandler scheduleHandler;

    @Autowired
    private AlarmSilenceService alarmSilenceService;

    @Autowired
    private AlarmSilenceUtilService alarmSilenceUtilService;

    private Logger monitorLog = LoggerFactory.getLogger("Monitor");


    public static final  String ALARM_SILENCE_MANAGE_ASSIGNER="alarm_silence_manage_assigner";

    public static final String defaultWholeServiceAlarmSilenceName = "whole_service";

    @PostConstruct
    public void init(){
        scheduler= Executors.newScheduledThreadPool(1,new NamedThreadFactory("AlarmSilence cron threadpool"));
        scheduler.scheduleWithFixedDelay(new Runnable() {
            @Override
            public void run() {
                try{
                    manageAlarmSilence();
                }catch (Exception e){
                    log.error("manageAlarmSilence error",e);
                }
            }
        },60,scheduleSeconds, TimeUnit.SECONDS);
    }

    @Autowired
    public void setTenantHandler(TenantHandler tenantHandler){
        AlarmSilenceCronService.tenantHandler = tenantHandler;
    }

    private void handleExpiredStatus(AlarmSilenceDO alarmSilenceDO) {
        if (alarmSilenceDO.getAutoDelete() != AUTO.getValue()) {
            return;
        }

        alarmSilenceService.basicDelAlarmSilenceById(alarmSilenceDO);
        MonitorLogReporter.report(monitorLog, SilenceAutoDeleteMonitorLogEntity.builder()
                .silenceId(alarmSilenceDO.getId())
                .serviceName(alarmSilenceDO.getName())
                .type(String.valueOf(alarmSilenceDO.getType()))
                .alarmSilenceDO(JsonUtils.toJsonStringIgnoreExp(alarmSilenceDO))
                .build());
    }

    private void handleOtherStatus(AlarmSilenceDO alarmSilenceDO) throws Exception{
        AlarmSilenceRecurringScheduleDO scheduleDO = scheduleHandler.findByAlarmSilenceId(alarmSilenceDO.getId());
        AlarmSilenceStatusEnum newStatus = alarmSilenceUtilService.calcAlarmSilenceStatus(
                alarmSilenceDO, scheduleDO, new Date());

        if (alarmSilenceDO.getStatus() != newStatus.getValue()) {
            alarmSilenceDO.setStatus(newStatus.getValue());
            alarmSilenceHandler.update(alarmSilenceDO);
        }
    }

    private static final String manageAlarmSilenceSysType="manageAlarmSilenceCron";
    private void manageAlarmSilence() {
        log.info("begin manageAlarmSilence!");
        if(!lock()){
            log.info("do not get the manageAlarmSilence lock");
            return;
        }
//        List<SysParaDO> sysParaDOS= sysParaHandler.listByTypeAndParaKey(SysParaEnums.alarmSilence.name(), manageAlarmSilenceSysType);
//
//        if(CollectionUtils.isEmpty(sysParaDOS)){
//            LOG.info("there is no manageAlarmSilenceCron para");
//            return;
//        }
//
//        SysParaDO sysParaDO = sysParaDOS.get(0);
//        AlarmSilenceCronManagerPara alarmSilenceCronManagerPara = JsonUtils.toObject(sysParaDO.getValue(), AlarmSilenceCronManagerPara.class);
//        String operator = alarmSilenceCronManagerPara.getOperator();
//        UserDO operatorDO = userHandler.getUserByName(operator);
//        if(operatorDO == null){
//            LOG.info("The AlarmSilenceManage operator is not exist");
//            return;
//        }
//        if(!alarmSilenceCronManagerPara.needSyncStatus){
//            LOG.info("no need to sync alarm silence status");
//            return;
//        }

        List<AlarmSilenceDO> alarmSilenceDOList = alarmSilenceHandler.findAll();
        for (AlarmSilenceDO alarmSilenceDO : alarmSilenceDOList) {
            try {
                int currentStatus = alarmSilenceDO.getStatus();
                if (currentStatus == AlarmSilenceStatusEnum.expired.getValue()) {
                    handleExpiredStatus(alarmSilenceDO);
                } else {
                    handleOtherStatus(alarmSilenceDO);
                }
            } catch (Exception e) {
                log.error("sync alarmSilence{} status failed", alarmSilenceDO.getName(), e);
            }
        }

        /*
        List<TenantDO> tenantDOList = alarmSilenceCronManagerPara.getNeedCreateAlarmSilenceServiceList();
        String operator = alarmSilenceCronManagerPara.getOperator();
        UserDO operatorDO = userHandler.getUserByName(operator);
        if(operatorDO == null){
            LOG.info("The AlarmSilenceManage operator is not exist");
            return;
        }
        for(TenantDO tenantDO:tenantDOList){
            AlarmSilenceInput alarmSilenceInput = new AlarmSilenceInput();
            try{
                alarmSilenceInput.setUserId(operatorDO.getId());
                String serviceName = tenantDO.getName();
                String alarmSilenceName = serviceName + CommonSplitConstants.SPLIT + defaultWholeServiceAlarmSilenceName;
                AlarmSilenceDO alarmSilenceDO = alarmSilenceHandler.findByName(alarmSilenceName);
                if(alarmSilenceDO != null){
                    return;
                }
                String alarmGroupName = serviceName + CommonSplitConstants.SPLIT + defaultWholeServiceAlarmGroupName;
                AlarmGroupDO alarmGroupDO = alarmGroupHandler.findByName(alarmGroupName);
                if(alarmGroupDO == null){
                    LOG.info("alarmGroup:{} is not existed, return", alarmGroupName);
                    return;
                }

                alarmSilenceInput.setName(alarmSilenceName);
                alarmSilenceInput.setAlarmGroupId(alarmGroupDO.getId());
                alarmSilenceInput.setType(AlarmSilenceTypeEnum.ONE_TIME.getValue());
                alarmSilenceInput.setComment("default AlarmSilence created by cube system");
                alarmSilenceInput.setNeedNotify(AlarmSilenceNeedNotifyEnum.no.getValue());
                alarmSilenceInput.setStatus(AlarmSilenceStatusEnum.expired.getValue());
                Date startTs = DateUtils.parseUTCDate(DateUtils.DATE_DEFAULT, DateUtils.FORMART1);
                //alarmSilenceInput.setStartTs(startTs);
                Date endTs = new Date(DateUtils.addHour(startTs, 1));
                //alarmSilenceInput.setEndTs(endTs);
                alarmSilenceInput.setAuthResourceUrl("/api/alarm/alarmSilence/addAlarmSilence");
                //alarmSilenceService.addAlarmSilence(alarmSilenceInput);

            }catch (Exception e){
                LOG.error("create AlarmSilence failed",e);
            }finally {
                alarmSilenceInput.setUserId(null);
                alarmSilenceInput.setAuthResourceUrl(null);
            }
        }
        */

        log.info("end manageAlarmSilence!");

    }

    private void printMonitor(Map<String,Object> logMap){
        logMap.put("type", MonitorTypeEnum.AlarmGroupCron.name());
        logMap.put("ts",System.currentTimeMillis());
        logMap.put("timeShow", DateUtils.format(new Date(),DateUtils.FORMART1));
        monitorLog.info(JsonUtils.toJsonStringIgnoreExp(logMap));
    }

    public boolean lock() {
        List<DistributedLockDO> distributedLocks=distributedLockDAO.listByType(ALARM_SILENCE_MANAGE_ASSIGNER);
        if(CollectionUtils.isEmpty(distributedLocks)){
            return false;
        }
        return  distributedLockDAO.lockWithType(ALARM_SILENCE_MANAGE_ASSIGNER,distributedLocks.get(0).getHandler(), IpUtils.getLocalIP(),lockTimeOut)>0;
    }

    @Data
    private static class AlarmSilenceCronManagerPara{
        private String operator;
        private String needCreateAlarmSilenceServices;
        private Boolean needCreateForAllService = false;
        private Boolean needSyncStatus = true;
        List<TenantDO> getNeedCreateAlarmSilenceServiceList(){
            List<TenantDO> serviceList = new ArrayList<>();
            if(needCreateForAllService){
                serviceList = tenantHandler.getAllTenantFromCache();
                return serviceList;
            }

            if(StringUtils.isNotEmpty(needCreateAlarmSilenceServices)){
                List<String> serviceNameList = Arrays.asList(needCreateAlarmSilenceServices.split(","));
                for(String serviceName: serviceNameList){
                    TenantDO tenantDO = tenantHandler.getTenantByNameInLowerCaseFromCache(serviceName);
                    if(tenantDO != null){
                        serviceList.add(tenantDO);
                    }
                }
                return serviceList;
            }
            return serviceList;
        }
    }
}


