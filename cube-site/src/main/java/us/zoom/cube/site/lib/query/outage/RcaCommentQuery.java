package us.zoom.cube.site.lib.query.outage;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import us.zoom.cube.site.lib.BasePara;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class RcaCommentQuery  extends BasePara {
    /**
     * rcaId
     */
    private String rcaId;
    /**
     *
     */
    private String title;
    /**
     * Sev1,Sev2,Sev3,Sev4,Sev5
     */
    private String sevLevel;
    /***
     * eg: 2024-06-01 09:00:00
     */
    private String startTime;
    private String endTime;
    /***
     * Draft,IN_PROGRESS,RCA_Reviewed,DONE
     */
    private String status;
}
