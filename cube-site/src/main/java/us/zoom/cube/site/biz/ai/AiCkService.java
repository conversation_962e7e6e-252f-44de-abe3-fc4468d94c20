package us.zoom.cube.site.biz.ai;

import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.ModelAndView;
import us.zoom.cube.site.biz.syspara.SmartCubeParaService;
import us.zoom.cube.site.core.UserHandler;
import us.zoom.cube.site.core.config.ClickhouseHandlerFactory;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static us.zoom.cube.site.api.config.SmartCubeWebClientConfig.SMART_CUBE;

@Service
@RequiredArgsConstructor
@Slf4j
public class AiCkService {
    private final ClickhouseHandlerFactory clickhouseHandlerFactory;

    private final SmartCubeParaService smartCubeParaService;
    private final UserHandler userHandler;

    private static final String TARGET_CONFIG = "targetConfig";
    private static final String FORWARD_PREFIX = "forward:";
    private static final String USER_NAME = "userName";

    public List<Map<String, Object>> executeQuery(String serviceName, String sql) {
        return clickhouseHandlerFactory.get().query(serviceName, sql);
    }

    /**
     * Safely forward request to target address
     *
     * @param target  target address
     * @param request HttpServletRequest
     * @return ModelAndView forward view
     * @throws IllegalArgumentException when target address is not in whitelist
     */
    public ModelAndView forward(String target, HttpServletRequest request) {
        // Parameter validation
        Assert.hasText(target, "Target address cannot be empty");

        // Get and validate configuration
        ForwardConfig config = getAndValidateConfig();

        // Whitelist check
        validateTargetInWhitelist(target, config);

        // Set security authentication
        setSecurityAuthentication();

        request.setAttribute(USER_NAME, config.getUserName());
        log.info("Forwarding request to target: {}", target);
        return new ModelAndView(FORWARD_PREFIX + target);
    }

    /**
     * Get and validate forward configuration
     */
    private ForwardConfig getAndValidateConfig() {
        String paramValue = smartCubeParaService.getParamValue(TARGET_CONFIG);

        if (!StringUtils.hasText(paramValue)) {
            log.error("Target config parameter is empty");
            throw new IllegalArgumentException("Target configuration not found");
        }

        try {
            ForwardConfig config = ForwardConfig.fromJson(paramValue);

            if (!config.isValid()) {
                log.error("Forward config is invalid: {}", config.getConfigSummary());
                throw new IllegalArgumentException("Whitelist configuration is empty");
            }

            log.debug("Forward config loaded successfully: {}", config.getConfigSummary());
            return config;

        } catch (IllegalArgumentException e) {
            // Re-throw IllegalArgumentException
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error parsing target config: {}", paramValue, e);
            throw new IllegalArgumentException("Invalid target configuration format", e);
        }
    }

    /**
     * Validate if target address is in whitelist
     */
    private void validateTargetInWhitelist(String target, ForwardConfig config) {
        if (!config.isTargetAllowed(target)) {
            log.error("Target address '{}' is not in whitelist. Config: {}", target, config.getConfigSummary());
            throw new IllegalArgumentException("The target address is not on the whitelist");
        }
    }

    /**
     * Set Spring Security authentication context
     */
    private void setSecurityAuthentication() {
        Authentication authentication = new UsernamePasswordAuthenticationToken(
                SMART_CUBE,
                null,
                Collections.singletonList(new SimpleGrantedAuthority(SMART_CUBE))
        );
        SecurityContextHolder.getContext().setAuthentication(authentication);
    }

}
