package us.zoom.cube.site.biz.trace;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import jakarta.annotation.PostConstruct;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import us.zoom.async.mq.openapi.model.base.Result;
import us.zoom.async.mq.openapi.model.result.topic.TopicResult;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.site.biz.AuthService;
import us.zoom.cube.site.biz.template.TemplateInnerService;
import us.zoom.cube.site.core.AsyncMqQueueHandler;
import us.zoom.cube.site.core.AsyncmqClusterHandler;
import us.zoom.cube.site.core.TemplateHandlerV2;
import us.zoom.cube.site.core.TemplateServiceRelationHandler;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.core.TraceConfigHandler;
import us.zoom.cube.site.lib.IdPara;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.dto.trace.AutoBindTemplateConfig;
import us.zoom.cube.site.lib.dto.trace.TraceConfigDTO;
import us.zoom.cube.site.lib.input.TraceConfigInput;
import us.zoom.cube.site.lib.input.template.InnerTemplateBindDTO;
import us.zoom.cube.site.lib.output.PageResult;
import us.zoom.cube.site.lib.query.AsyncMqQueueQuery;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.cube.site.lib.query.TraceConfigQuery;
import us.zoom.infra.dao.model.AsyncMqQueueDO;
import us.zoom.infra.dao.model.TemplateDO;
import us.zoom.infra.dao.model.TemplateServiceRelationDO;
import us.zoom.infra.dao.model.TenantDO;
import us.zoom.infra.dao.model.TraceConfigDO;
import us.zoom.infra.redis.RedisService;
import us.zoom.infra.syspara.SysParaEventService;
import us.zoom.infra.thread.NamedThreadFactory;
import us.zoom.infra.thread.ThreadLocalStore;
import us.zoom.infra.utils.AsyncUtils;
import us.zoom.infra.utils.IpUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.StringUtils.EMPTY;
import static us.zoom.cube.site.biz.template.TemplateConstant.SUCCESS;
import static us.zoom.cube.site.biz.template.TemplateConstant.TOPIC;

/**
 * @authoer: eason.jia
 * @createDate: 2024/8/14
 * @description:
 */
@Component
@Slf4j
public class TraceConfigService {

    private static final String AUTO_BIND_TRACE_TEMPLATE_KEY = "autoBindTraceTemplate";

    private static final int AUTO_BIND_TRACE_TEMPLATE_EXPIRE_MINUTES = 30;

    private static final int DEFAULT_PAGE_SIZE = 1000;
    private static final int DEFAULT_PAGE_INDEX = 1;

    private static final String TRACE_SYSTEM_PARA_TYPE = "trace";

    private static final String TRACE_SYSTEM_PARA_KEY = "autoBindTemplateList";

    private static final String CUBE_ADMIN_SERVICE = "cube_admin";

    @Autowired
    private TraceConfigHandler traceConfigHandler;

    @Autowired
    private TenantHandler tenantHandler;

    @Autowired
    private AuthService authService;

    @Autowired
    private TemplateInnerService templateInnerService;

    @Autowired
    private TemplateServiceRelationHandler templateServiceRelationHandler;

    @Autowired
    private TemplateHandlerV2 templateHandler;

    @Autowired
    private RedisService redisService;

    @Autowired
    private AsyncmqClusterHandler asyncmqClusterHandler;

    @Autowired
    private AsyncMqQueueHandler asyncMqQueueHandler;

    @Value("${cube.admin.user.id:23c0f3f8-1a34-48ef-8419-0b40ef073f94}")
    private String adminUserId;

    @Value("${trace.autoBindTemplate.enable:true}")
    private boolean autoBindTemplateEnable;

    private List<AutoBindTemplateConfig> autoBindTemplateList = Lists.newArrayList();

    private ExecutorService executorService = new ThreadPoolExecutor(16, 16, 10, TimeUnit.MINUTES, new ArrayBlockingQueue<>(500),
            new NamedThreadFactory("traceTemplateBinder"), new ThreadPoolExecutor.CallerRunsPolicy());

    @PostConstruct
    public void init() {
        SysParaEventService.registerListenerBySpecificSysPara(sysParaDO -> {
            try {
                autoBindTemplateList = JsonUtils.toObjectByTypeRef(sysParaDO.getValue(), new TypeReference<List<AutoBindTemplateConfig>>() {
                });
            } catch (Throwable e) {
                log.error("deserialize config for autoBindTemplateList error, value: {}", sysParaDO.getValue(), e);
            }
        }, TRACE_SYSTEM_PARA_TYPE, TRACE_SYSTEM_PARA_KEY);

    }


    @Scheduled(cron = "0 0/1 * * * ?")
    public void autoBindTraceTemplate() {
        if (!autoBindTemplateEnable) {
            return;
        }
        if (CollectionUtils.isEmpty(autoBindTemplateList)) {
            return;
        }
        boolean success = redisService.setRedisLock(AUTO_BIND_TRACE_TEMPLATE_KEY, IpUtils.getHost(), AUTO_BIND_TRACE_TEMPLATE_EXPIRE_MINUTES);
        if (!success) {
            return;
        }
        long start = System.currentTimeMillis();
        log.info("start bind trace related template");
        doBindTraceTemplate();
        log.info("end bind trace related template, cost {}ms", System.currentTimeMillis() - start);
    }

    public void doBindTraceTemplate() {
        Map<String, AutoBindTemplateConfig> templateNameMap = autoBindTemplateList.stream().collect(Collectors.toMap(AutoBindTemplateConfig::getTemplateName, Function.identity()));
        List<TemplateDO> templateDOS = templateHandler.listByNames(Lists.newArrayList(templateNameMap.keySet()));
        Set<String> serviceIds = traceConfigHandler.getTraceConfigCacheMap().keySet();
        AsyncUtils.parallelFutureJoinWithoutResult(serviceIds, serviceId -> {
            ThreadLocalStore.setUserNameLocal(CUBE_ADMIN_SERVICE);
            ThreadLocalStore.setTenantInfoLocal(serviceId);
            TenantDO tenantDO = tenantHandler.getTenantJustFromCache(serviceId);
            Set<String> templateIds = Optional.ofNullable(templateServiceRelationHandler.listByServiceId(serviceId))
                    .map(relations -> relations.stream().map(TemplateServiceRelationDO::getTemplateId).collect(Collectors.toSet())).orElse(Collections.emptySet());
            for (TemplateDO templateDO : templateDOS) {
                AutoBindTemplateConfig config = templateNameMap.get(templateDO.getName());
                Set<String> dcs = Sets.newHashSet();
                InnerTemplateBindDTO bindDTO = new InnerTemplateBindDTO();
                bindDTO.setServiceName(tenantDO.getName());
                bindDTO.setTemplateName(templateDO.getName());
                bindDTO.setUserId(adminUserId);
                if (config.isNeedTopic() && config.getTopicTemplate() != null) {
                    String topicTemplate = config.getTopicTemplate();
                    String topicSuffix = String.format(topicTemplate, tenantDO.getName());
                    AsyncMqQueueQuery asyncMqQueueQuery = new AsyncMqQueueQuery();
                    asyncMqQueueQuery.setTopic(topicSuffix);
                    List<AsyncMqQueueDO> queues = Optional.ofNullable(asyncMqQueueHandler.findByParam(asyncMqQueueQuery, DEFAULT_PAGE_INDEX, DEFAULT_PAGE_SIZE)).orElse(Collections.emptyList());
                    Set<String> topicSet = queues.stream().map(AsyncMqQueueDO::getTopic).collect(Collectors.toSet());
                    Map<String, String> queryMap = buildAsyncMqQueryMap(topicSuffix);
                    asyncmqClusterHandler.getClusterIdToOpenApiClientMap().forEach((clusterId, clientWrapper) -> {
                        try {
                            Result<us.zoom.async.mq.openapi.model.page.PageResult<TopicResult>> pageResultResult = clientWrapper.getOpenApi().pageQueryTopics(queryMap);
                            if (pageResultResult == null || !pageResultResult.isSuccess()) {
                                return;
                            }
                            List<TopicResult> content = pageResultResult.getData().getContent();
                            Set<String> collect = content.stream().filter(topicResult -> !topicSet.contains(topicResult.getName())).map(TopicResult::getDcName).collect(Collectors.toSet());
                            if (CollectionUtils.isNotEmpty(collect)) {
                                dcs.addAll(collect);
                            }
                        } catch (Throwable e) {
                            log.error("query asyncMq failed, queryMap: {}", queryMap, e);
                        }
                    });
                    if (dcs.isEmpty()) {
                        continue;
                    }
                    bindDTO.setDcs(dcs);
                } else if (templateIds.contains(templateDO.getId())) {
                    continue;
                }
                Map<String, Object> result = templateInnerService.bindAndUpdateTemplate(bindDTO);
                Map<String, String> topicResult = Collections.emptyMap();
                if (result.get(TOPIC) != null) {
                    topicResult = (Map<String, String>) result.get(TOPIC);
                }
                Set<String> successDcs = Sets.newHashSet();
                topicResult.forEach((topic, topicUpdateResult) -> {
                    String dc = topic.split("_")[0];
                    if (StringUtils.equals(topicUpdateResult, SUCCESS)) {
                        successDcs.add(dc);
                    }
                });
                if ((Boolean) result.get(SUCCESS)) {
                    log.info("bind template: {} for service: {} success, dcs: {}", templateDO.getName(), tenantDO.getName(), successDcs);
                } else {
                    log.error("bind template: {} for service: {} failed, result: {}", templateDO.getName(), tenantDO.getName(), JsonUtils.toJsonString(result));
                }
            }
        }, (throwable, entry) -> log.error("autoBindTraceTemplate error, configs: {}", entry, throwable), executorService);
    }

    private Map<String, String> buildAsyncMqQueryMap(String topic) {
        Map<String, String> queryMap = Maps.newHashMap();
        queryMap.put("page", String.valueOf(DEFAULT_PAGE_INDEX));
        queryMap.put("size", String.valueOf(DEFAULT_PAGE_SIZE));
        queryMap.put("name", topic);
        return queryMap;
    }

    public ResponseObject<String> addTraceConfig(@NonNull TraceConfigInput traceConfigInput) {
        authService.checkAuth(traceConfigInput);
        String traceConfigId = traceConfigHandler.addTraceConfig(traceConfigInput);
        return ResponseObject.success(traceConfigId);
    }

    public ResponseObject<Boolean> deleteTraceConfigById(@NonNull IdPara idPara) {
        authService.checkAuth(idPara);
        TraceConfigDO traceConfigDO = traceConfigHandler.getTraceConfigById(idPara.getId());
        if (traceConfigDO == null) {
            return ResponseObject.entityNotExited();
        }
        traceConfigHandler.deleteTraceConfigById(idPara.getId());
        return ResponseObject.success(true);
    }

    public ResponseObject<Boolean> modifyTraceConfig(@NonNull TraceConfigInput traceConfigInput) {
        authService.checkAuth(traceConfigInput);
        TraceConfigDO traceConfigDO = traceConfigHandler.getTraceConfigById(traceConfigInput.getId());
        if (traceConfigDO == null) {
            return ResponseObject.entityNotExited();
        }
        traceConfigHandler.modifyTraceConfig(traceConfigInput);
        return ResponseObject.success(true);
    }

    public ResponseObject<TraceConfigDTO> getTraceConfigById(@NonNull IdPara idPara) {
        authService.checkAuth(idPara);
        TraceConfigDO traceConfigDO = traceConfigHandler.getTraceConfigById(idPara.getId());
        if (traceConfigDO == null) {
            return ResponseObject.entityNotExited();
        }
        return ResponseObject.success(convert(traceConfigDO));
    }

    public ResponseObject<PageResult<TraceConfigDTO>> search(@NonNull PageQuery<TraceConfigQuery> pageQuery) {
        authService.checkAuth(pageQuery);
        List<TraceConfigDTO> traceConfigList = traceConfigHandler.searchByConditions(pageQuery.getQueryPara(), pageQuery.getPageIndex(), pageQuery.getPageSize()).stream()
                .filter(Objects::nonNull)
                .map(this::convert)
                .collect(Collectors.toList());
        int count = traceConfigHandler.countByConditions(pageQuery.getQueryPara());
        return ResponseObject.success(pageQuery.getOperId(), new PageResult<>(count, traceConfigList));
    }

    public ResponseObject<Boolean> modifyTraceConfigStatus(@NonNull TraceConfigInput traceConfigInput) {
        authService.checkAuth(traceConfigInput);
        TraceConfigDO traceConfigDO = traceConfigHandler.getTraceConfigById(traceConfigInput.getId());
        if (traceConfigDO == null) {
            return ResponseObject.entityNotExited();
        }
        traceConfigHandler.modifyTraceConfigStatus(traceConfigInput.getId(), traceConfigInput.getStatus());
        return ResponseObject.success(true);
    }

    private TraceConfigDTO convert(TraceConfigDO traceConfigDO) {
        return TraceConfigDTO.builder()
                .id(traceConfigDO.getId())
                .serviceId(traceConfigDO.getServiceId())
                .serviceName(Optional.ofNullable(tenantHandler.getTenantById(traceConfigDO.getServiceId()))
                        .map(TenantDO::getName)
                        .orElse(EMPTY))
                .topic(traceConfigDO.getTopic())
                .groupId(traceConfigDO.getGroupId())
                .unitTag(traceConfigDO.getUnitTag())
                .threadCount(traceConfigDO.getThreadCount())
                .protocol(traceConfigDO.getProtocol())
                .samplingRatio(traceConfigDO.getSamplingRatio())
                .status(traceConfigDO.getStatus())
                .isDeleted(traceConfigDO.getIsDeleted())
                .creator(traceConfigDO.getCreator())
                .lastModifiedUser(traceConfigDO.getLastModifiedUser())
                .gmtCreate(traceConfigDO.getGmtCreate())
                .gmtModify(traceConfigDO.getGmtModify())
                .build();
    }
}
