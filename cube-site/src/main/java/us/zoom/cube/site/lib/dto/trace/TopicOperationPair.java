package us.zoom.cube.site.lib.dto.trace;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * Topic operation combination entity class
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TopicOperationPair {
    
    /**
     * Topic name
     */
    private String topicName;
    
    /**
     * Message operation type
     */
    private String msgOperation;
} 