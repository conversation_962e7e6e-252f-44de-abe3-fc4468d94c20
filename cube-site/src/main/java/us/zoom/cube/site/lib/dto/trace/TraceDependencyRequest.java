package us.zoom.cube.site.lib.dto.trace;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import us.zoom.cube.site.lib.BasePara;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TraceDependencyRequest extends BasePara {
    
    @NotNull(message = "start time cannot be null")
    private Long start;
    
    @NotNull(message = "end time cannot be null")
    private Long end;
    
    private String region;
    
    private String cluster;
    
    public void check() {
        if (start > end) {
            throw new IllegalArgumentException("start time should not be larger than end");
        }
    }
} 