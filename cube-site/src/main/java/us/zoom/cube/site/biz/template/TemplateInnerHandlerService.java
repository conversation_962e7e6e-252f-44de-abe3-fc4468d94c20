package us.zoom.cube.site.biz.template;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zoom.op.monitor.dao.alarm.AlarmDefinitionDao;
import com.zoom.op.monitor.dao.alarm.ChannelDao;
import com.zoom.op.monitor.domain.IdAndName;
import com.zoom.op.monitor.domain.alarm.AlarmDefinition;
import com.zoom.op.monitor.domain.alarm.Channel;
import com.zoom.op.monitor.domain.alarm.ChannelParameter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;
import us.zoom.cloud.secrets.spring.annotation.SecretValue;
import us.zoom.cube.site.api.intercept.AuthInterceptor;
import us.zoom.cube.site.biz.DashTemplateService;
import us.zoom.cube.site.biz.SysParaService;
import us.zoom.cube.site.biz.clickhouse.ChPublisher;
import us.zoom.cube.site.biz.clickhouse.ChTableEvent;
import us.zoom.cube.site.biz.syspara.CubeTemplateParaService;
import us.zoom.cube.site.core.*;
import us.zoom.cube.site.lib.input.DashTemplateInput;
import us.zoom.cube.site.lib.input.template.InnerTemplateBindInput;
import us.zoom.cube.site.lib.input.template.InnerTemplateRevokeInput;
import us.zoom.cube.site.lib.input.template.model.TemplateRevokeModel;
import us.zoom.cube.site.lib.output.template.TemplateUserViewV2Output;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.service.*;
import us.zoom.infra.enums.MonitorLogTypeEnum;
import us.zoom.infra.enums.TemplateItemTypeEnum;
import us.zoom.infra.model.LogResult;
import us.zoom.cube.lib.utils.JsonUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static us.zoom.cube.site.biz.TemplateServiceV2.*;
import static us.zoom.cube.site.biz.template.TemplateConstant.*;
import static us.zoom.cube.site.biz.template.TemplateConstant.ZOOM_CHAT;
import static us.zoom.cube.site.core.DashTemplateZcpHandler.SEPARATOR;
import static us.zoom.cube.site.core.tag.ResourceTypeConstant.RESOURCE_AlARM_TYPE;
import static us.zoom.cube.site.core.tag.ResourceTypeConstant.RESOURCE_METRIC_TYPE;
import static us.zoom.cube.site.infra.enums.CubeTemplatePhaseEnum.*;
import static us.zoom.infra.notification.channel.zoomchat.IMChannelEngine.*;

@Component
@Slf4j
public class TemplateInnerHandlerService {

    @Autowired
    private CollectorMetricsDAO collectorMetricsDAO;

    @Autowired
    private MetricsFieldHandler metricsFieldHandler;

    @Autowired
    private MetricsDAO metricsDAO;

    @Autowired
    private AggregationCustomFieldRuleHandler aggregationCustomFieldRuleHandler;

    @Autowired
    private AggregationFunctionItemHandler aggregationFunctionItemHandler;

    @Autowired
    private AggregationHistogramRangeItemHandler aggregationHistogramRangeItemHandler;

    @Autowired
    private AggregationPercentileItemHandler aggregationPercentileItemHandler;

    @Autowired
    private ChannelDao channelDao;

    @Autowired
    private DataParserSourceDAO dataParserSourceDAO;

    @Autowired
    private DataParserPipelineDAO dataParserPipelineDAO;

    @Autowired
    private DataParserFilterProcessorHandler dataParserFilterProcessorHandler;

    @Autowired
    private DataParserRemapperProcessorHandler dataParserRemapperProcessorHandler;

    @Autowired
    private DataParserEncryptionProcessorHandler dataParserEncryptionProcessorHandler;

    @Autowired
    private DataParserLabelMysqlProcessorHandler dataParserLabelMysqlProcessorHandler;

    @Autowired
    private DataParserLabelRedisProcessorHandler dataParserLabelRedisProcessorHandler;

    @Autowired
    private DataParserGroovyProcessorHandler dataParserGroovyProcessorHandler;

    @Autowired
    private DataParserDAO dataParserDAO;

    @Autowired
    private AlarmDefinitionDao alarmDefinitionDao;

    @Autowired
    private DashTemplateService dashTemplateService;

    @Autowired
    private TenantHandler tenantHandler;

    @Autowired
    private DataParserPipelineHandler dataParserPipelineHandler;

    @Autowired
    private CollectorHandler collectorHandler;

    @Autowired
    private MetricsAggregationHandler metricsAggregationHandler;

    @Autowired
    private MetricsAggregationRuleHandler metricsAggregationRuleHandler;

    @Autowired
    private AlarmDefinitionHandler alarmDefinitionHandler;

    @Autowired
    private DashHandler dashHandler;

    @Autowired
    private CubeTemplateItemServiceRelaDAO cubeTemplateItemServiceRelaDAO;

    @Autowired
    private MetricsHandler metricsHandler;

    @Autowired
    private Environment environment;

    @Autowired
    private RsaService rsaService;

    @Autowired
    private TemplateServiceRelationDAO templateServiceRelationDAO;

    @Autowired
    private DataFlowDataParserRelationHandler dataFlowDataParserRelationHandler;

    @Autowired
    private AsyncMqQueueHandler asyncMqQueueHandler;

    @Autowired
    private KafkaQueueDAO kafkaQueueDAO;

    @Autowired
    private KafkaQueueHandler kafkaQueueHandler;

    @Autowired
    private DataParserTimestampProcessorHandler dataParserTimestampProcessorHandler;

    @Autowired
    private DataParserIpProcessorHandler dataParserIpProcessorHandler;

    @Autowired
    private CubeTemplateParaService cubeTemplateParaService;

    @Autowired
    private ResourceTagDAO resourceTagDAO;

    @Autowired
    private ChPublisher chPublisher;

    @SecretValue("cube.template.verification.token")
    private String cubeTemplateVerificationToken;

    private Logger monitorLog = LoggerFactory.getLogger("Monitor");

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void applyInDB(TenantDO destTenantDO,
                          List<DataParserSourceDO> addFlowDataParserSourceDOList,
                          List<DataParserSourceDO> updateFlowDataParserSourceDOList,
                          List<DataParserPipelineDO> addFlowPipelineDOList,
                          List<DataParserPipelineDO> updateFlowPipelineDOList,
                          List<DataParserRemapperProcessorDO> addFlowDataParserRemapperProcessorDOList,
                          List<DataParserFilterProcessorDO> addFlowDataParserFilterProcessorDOList,
                          List<DataParserGroovyProcessorDO> addFlowDataParserGroovyProcessorDOList,
                          List<CollectorDO> addCollectorDOList,
                          List<CollectorFieldDO> addCollectorFieldDOList,
                          List<MetricsDO> addMetricsDOList,
                          List<MetricsDO> updateMetricsDOList,
                          List<CollectorMetricsDO> addCollectorMetricsDOList,
                          List<MetricsFieldDO> addMetricsFieldDOList,
                          List<MetricsAggregationDO> addMetricsAggregationDOList,
                          List<MetricsAggregationRuleDO> addMetricsAggregationRuleDOList,
                          List<AggregationFunctionItemDO> addAggregationFunctionItemDOList,
                          List<AggregationHistogramRangeItemDO> addAggregationHistogramRangeItemDOList,
                          List<AggregationPercentileItemDO> addAggregationPercentileItemDOList,
                          List<AggregationCustomFieldRuleDO> addAggregationCustomFieldRuleDOList,
                          List<AlarmDefinition> addAlarmDefinitionList,

                          List<DataParserDO> addDataParserDOList,
                          List<DataParserDO> updateDataParserDOList,
                          List<DataParserPipelineDO> addPipelineDOList,
                          List<DataParserPipelineDO> updatePipelineDOList,
                          List<DataParserRemapperProcessorDO> addDataParserRemapperProcessorDOList,
                          List<DataParserFilterProcessorDO> addDataParserFilterProcessorDOList,
                          List<DataParserGroovyProcessorDO> addDataParserGroovyProcessorDOList,
                          List<DashTemplateInput> addDashTemplateInputList,
                          List<String> updateDashTemplateInputList,
                          List<CubeTemplateItemServiceRelaDO> addcubeTemplateItemServiceRelaDOList,
                          List<CubeTemplateItemServiceRelaDO> updatecubeTemplateItemServiceRelaDOList,
                          boolean applyInDB,
                          TemplateServiceRelationDO addTemplateServiceRelationDO,
                          TemplateServiceRelationDO updateTemplateServiceRelationDO,
                          boolean finalBind,
                          List<DataFlowDataParserRelationDO> addDataFlowDataParserRelationDOS,
                          List<AsyncMqQueueDO> asyncMqQueueDOList,
                          List<KafkaQueueDO> addKafkaQueueDOList,
                          List<DataParserTimestampProcessorDO> dataParserTimestampProcessorDOList,
                          List<DataParserIpProcessorDO> addDataParserIpProcessorDOList,
                          Map<String, Object> result,
                          String type,
                          TemplateDO templateDO,
                          Map<String, Object> metricsMap,
                          InnerTemplateBindInput input,
                          List<AsyncMqQueueDO> asyncMqQueueDOS,
                          List<ResourceTagDo> addResourceTagDoList) {

        if (!applyInDB) {
            log.info("will not apply in DB");
            return;
        }
        long start = System.currentTimeMillis();
        metricsMap.put(PHASE, applyInDBStart);
        metricsMap.put(COST, System.currentTimeMillis() - start);
        monitorLog.info(LogResult.getLogResult(MonitorLogTypeEnum.cubeTemplateStat.name(), JsonUtils.toJsonString(input), metricsMap));
        tenantHandler.addTenantOnDuplicateKeyUpdate(destTenantDO);

        addFlowDataParserSourceDOList.forEach(e -> dataParserSourceDAO.addDataParserSource(e));
        updateFlowDataParserSourceDOList.forEach(e -> dataParserSourceDAO.editDataParserSource(e));
        addDataParserDOList.forEach(e -> {
            dataParserDAO.addDataParser(e);
            kafkaQueueDAO.deleteByDataParserIdAndType(e.getId());
        });
        kafkaQueueHandler.batchAdd(addKafkaQueueDOList);
        updateDataParserDOList.forEach(e -> dataParserDAO.editDataParser(e));


        addPipelineDOList.forEach(e -> dataParserPipelineHandler.addPipeline(e));
        addFlowPipelineDOList.forEach(e -> dataParserPipelineHandler.addPipeline(e));
        updatePipelineDOList.forEach(e -> dataParserPipelineDAO.edit(e));
        updateFlowPipelineDOList.forEach(e -> dataParserPipelineDAO.edit(e));
        asyncMqQueueHandler.batchAdd(asyncMqQueueDOS);


        addDataParserRemapperProcessorDOList.forEach(e -> dataParserRemapperProcessorHandler.addRemapperProcessor(e));
        addDataParserFilterProcessorDOList.forEach(e -> dataParserFilterProcessorHandler.addFilterProcessor(e));
        addDataParserGroovyProcessorDOList.forEach(e -> dataParserGroovyProcessorHandler.addGroovyProcessor(e));
        addFlowDataParserRemapperProcessorDOList.forEach(e -> dataParserRemapperProcessorHandler.addRemapperProcessor(e));
        addFlowDataParserFilterProcessorDOList.forEach(e -> dataParserFilterProcessorHandler.addFilterProcessor(e));
        addFlowDataParserGroovyProcessorDOList.forEach(e -> dataParserGroovyProcessorHandler.addGroovyProcessor(e));
        dataParserTimestampProcessorDOList.forEach(e -> dataParserTimestampProcessorHandler.addTimestampProcessor(e));
        addDataParserIpProcessorDOList.forEach(e -> dataParserIpProcessorHandler.addTimestampProcessor(e));

        addCollectorDOList.forEach(e -> collectorHandler.insertCollector(e));
        addCollectorFieldDOList.forEach(e -> collectorHandler.insertCollectorField(e));

        addMetricsDOList.forEach(e -> metricsDAO.insertMetrics(e));
        updateMetricsDOList.forEach(e -> metricsDAO.newUpdateMetrics(e));
        addCollectorMetricsDOList.forEach(e -> collectorMetricsDAO.insertCollectorMetrics(e));
        addMetricsFieldDOList.forEach(e -> metricsFieldHandler.insertMetricsField(e));
        dataFlowDataParserRelationHandler.batchAdd(addDataFlowDataParserRelationDOS);

        addMetricsAggregationDOList.forEach(e -> metricsAggregationHandler.insertMetricsAggregation(e));
        metricsAggregationRuleHandler.batchInsertMetricsAggregationRule(addMetricsAggregationRuleDOList);
        aggregationFunctionItemHandler.batchInsertAggregationFunction(addAggregationFunctionItemDOList);
        aggregationHistogramRangeItemHandler.batchInsertAggregationHistogramRange(addAggregationHistogramRangeItemDOList);
        aggregationPercentileItemHandler.batchInsertAggregationPercentile(addAggregationPercentileItemDOList);
        aggregationCustomFieldRuleHandler.batchInsertCustomFieldRule(addAggregationCustomFieldRuleDOList);

        List<String> dashboardResultList = new ArrayList<>();
        if (!cubeTemplateParaService.getDashboardSpecialConfig()) {
            for (DashTemplateInput dashTemplateInput : addDashTemplateInputList) {
                String dashResult = (String) dashTemplateService.addTemplateByZCP(dashTemplateInput).getData();
                if (dashResult.contains(SEPARATOR)) {
                    String[] value = dashResult.split(SEPARATOR);
                    String dashTemplateRelaId = value[0];
                    DashTemplateRelaDO dashTemplateRelaDO = dashHandler.getTemplateDashRelaById(dashTemplateRelaId);
                    String newDashName = value[1];
                    dashboardResultList.add(newDashName);

                    addcubeTemplateItemServiceRelaDOList
                            .stream()
                            .filter(a -> TemplateItemTypeEnum.DASHBOARD.getCode().equals(a.getType()) && a.getSourceTemplateItemId().equals(dashTemplateRelaDO.getPointDashId()))
                            .findFirst()
                            .ifPresent(e -> {
                                e.setDashTemplateRelaId(dashTemplateRelaId);
                                e.setDestTemplateItemId(dashTemplateRelaDO.getDashId());
                            });
                }
            }
            updateDashTemplateInputList.forEach(e -> dashTemplateService.updateTemplateByZCP(e));
        }

        if (!CollectionUtils.isEmpty(addAlarmDefinitionList)) {
            List<AlarmDefinition> newAlarmDefinitionList = new ArrayList<>(addAlarmDefinitionList.stream()
                    .collect(Collectors.toMap(AlarmDefinition::getName, dto -> dto, (existing, replacement) -> existing))
                    .values());
            alarmDefinitionHandler.batchInsert(newAlarmDefinitionList);
        }

        asyncMqQueueHandler.batchAdd(asyncMqQueueDOList);
        addcubeTemplateItemServiceRelaDOList.stream()
                .filter(item -> StringUtils.isNotEmpty(item.getDestTemplateItemId()))
                .forEach(e -> cubeTemplateItemServiceRelaDAO.insert(e));
        updatecubeTemplateItemServiceRelaDOList.forEach(e -> cubeTemplateItemServiceRelaDAO.update(e));
        if (finalBind) {
            templateServiceRelationDAO.add(addTemplateServiceRelationDO);
        } else {
            templateServiceRelationDAO.update(updateTemplateServiceRelationDO);
        }

        monitorLog.info(LogResult.getSuccessLogResult(MonitorLogTypeEnum.cubeTemplateRecord.name(), "", createMonitorLog(result, type, templateDO, destTenantDO, input.getOperator())));
        metricsMap.put(PHASE, applyInDBEnd);
        metricsMap.put(COST, System.currentTimeMillis() - start);
        monitorLog.info(LogResult.getLogResult(MonitorLogTypeEnum.cubeTemplateStat.name(), JsonUtils.toJsonString(input), metricsMap));

        List<TemplateUserViewV2Output.Item.SubItem> subItems = new ArrayList<>();
        addAlarmDefinitionList.forEach(alarm -> {
            TemplateUserViewV2Output.Item.SubItem subItem = new TemplateUserViewV2Output.Item.SubItem();
            subItem.setName(alarm.getName());
            subItem.setStatus(alarm.getEnabled());
            subItems.add(subItem);
        });
        result.put(ALARM, subItems);

        result.put(DASHBOARD, dashboardResultList);
        if (result.get(TYPE).equals(UPDATE)) {
            Map<String, String> metrics = new HashMap<>();
            metrics.putAll(updateMetricsDOList.stream()
                    .collect(Collectors.toMap(MetricsDO::getMetricsName, obj -> UPDATE)));

            metrics.putAll(addMetricsDOList.stream()
                    .collect(Collectors.toMap(MetricsDO::getMetricsName, obj -> INSERT)));
            result.put(METRICS, metrics);
        } else {
            result.put(METRICS, addMetricsDOList.stream().collect(Collectors.toMap(MetricsDO::getMetricsName, obj -> INSERT)));
        }

        if(!CollectionUtils.isEmpty(addResourceTagDoList)) {
            Map<String, String> alarmNameIdMap = alarmDefinitionHandler.listAllAlarmIdAndNames(destTenantDO.getId())
                    .stream()
                    .collect(Collectors.toMap(
                            IdAndName::getName,
                            IdAndName::getId,
                            (existing, replacement) -> existing));

            List<ResourceTagDo> validTags = new ArrayList<>();
            
            addResourceTagDoList.forEach(tag -> {
                if (StringUtils.isEmpty(tag.getResource()) && RESOURCE_AlARM_TYPE.equals(tag.getResourceType())) {
                    String resourceId = alarmNameIdMap.get(tag.getCommonName());
                    if (resourceId != null) {
                        tag.setResource(resourceId);
                        validTags.add(tag);
                    } else {
                        log.warn("TemplateInnerHandlerService, Cannot find alarm resource ID for tag: commonName={}, discarding this tag, labelDTO = {}",
                                tag.getCommonName(), JsonUtils.toJsonString(tag));
                    }
                } else {
                    validTags.add(tag);
                }
            });

            if (!validTags.isEmpty()) {
                resourceTagDAO.batchAddResourceTag(validTags);
            }
        }

        //send event
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                if (CollectionUtils.isEmpty(addMetricsDOList)) {
                    return;
                }
                for (MetricsDO e : addMetricsDOList) {
                    chPublisher.publish(new ChTableEvent<>(ChTableEvent.METRIC_CREATE_UPDATE_TYPE, e.getId()));
                }
            }
        });
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void revokeInDB(TemplateRevokeModel revokeModel) {
        if (!revokeModel.isApplyInDB()) {
            log.info("will not apply in DB");
            return;
        }
        long start = System.currentTimeMillis();
        Map<String, Object> metricsMap = revokeModel.getMetricsMap();
        metricsMap.put(PHASE, revokeApplyInDBStart);
        metricsMap.put(COST, System.currentTimeMillis() - start);
        InnerTemplateRevokeInput input = revokeModel.getInput();
        monitorLog.info(LogResult.getLogResult(MonitorLogTypeEnum.cubeTemplateStat.name(), JsonUtils.toJsonString(input), metricsMap));

        List<String> delDataParserSourceDOList = revokeModel.getDelDataParserSourceDOList();
        delDataParserSourceDOList.forEach(e -> {
            dataParserSourceDAO.deleteById(e);
            dataFlowDataParserRelationHandler.deleteByDataParserId(e);
        });
        List<String> delDataParserDOList = revokeModel.getDelDataParserDOList();
        delDataParserDOList.forEach(e -> {
            dataParserDAO.deleteById(e);
            asyncMqQueueHandler.deleteBySourceId(e);
            kafkaQueueDAO.deleteByDataParserIdAndType(e);
        });

        List<String> delPipelineDOList = revokeModel.getDelPipelineDOList();
        if (!CollectionUtils.isEmpty(delPipelineDOList)) {
            dataParserPipelineDAO.delByIds(delPipelineDOList);
        }
        dataParserEncryptionProcessorHandler.delEncryptionProcessorByPipelineIds(delPipelineDOList);
        dataParserLabelMysqlProcessorHandler.delLabelMysqlProcessorByPipelineIds(delPipelineDOList);
        dataParserLabelRedisProcessorHandler.delLabelRedisProcessorByPipelineIds(delPipelineDOList);
        dataParserRemapperProcessorHandler.delRemapperProcessorByPipelineIds(delPipelineDOList);
        dataParserFilterProcessorHandler.delFilterProcessorByPipelineIds(delPipelineDOList);
        dataParserGroovyProcessorHandler.delGroovyProcessorByPipelineIds(delPipelineDOList);
        dataParserTimestampProcessorHandler.delTimestampByPipelineIds(delPipelineDOList);
        dataParserIpProcessorHandler.delIByPipelineIds(delPipelineDOList);
        Map<String, String> delAsyncqueues = revokeModel.getDelAsyncqueues();
        if (!delAsyncqueues.isEmpty()) {
            delAsyncqueues.forEach((k, v) -> {
                asyncMqQueueHandler.deleteBySourceIdAndTopicNameLike(k, v);
            });
        }
        List<String> delCollectorDOList = revokeModel.getDelCollectorDOList();
        delCollectorDOList.forEach(e -> collectorHandler.delCollector(e));
        List<String> delCollectorFieldDOList = revokeModel.getDelCollectorFieldDOList();
        delCollectorFieldDOList.forEach(e -> collectorHandler.delField(e));
        List<String> delAlarmDefinitionList = revokeModel.getDelAlarmDefinitionList();
        if (!CollectionUtils.isEmpty(delAlarmDefinitionList)) {
            delAlarmDefinitionList.forEach(e -> alarmDefinitionDao.deleteById(e));
            resourceTagDAO.deleteResourceTagByNames(delAlarmDefinitionList, RESOURCE_AlARM_TYPE);
        }
        List<String> delMetricsDOList = revokeModel.getDelMetricsDOList();
        if (!CollectionUtils.isEmpty(delMetricsDOList)) {
            delMetricsDOList.forEach(e -> metricsHandler.deleteMetrics(e));
            resourceTagDAO.deleteResourceTagByNames(delMetricsDOList, RESOURCE_METRIC_TYPE);
        }
        
        List<String> delDashboardList = revokeModel.getDelDashboardList();
        delDashboardList.forEach(e -> dashTemplateService.deleteDashByTemplateZCP(Lists.newArrayList(e)));

        List<String> delcubeTemplateItemServiceRelaDOList = revokeModel.getDelcubeTemplateItemServiceRelaDOList();
        delcubeTemplateItemServiceRelaDOList.forEach(e -> cubeTemplateItemServiceRelaDAO.delete(e));
        templateServiceRelationDAO.unbindFromTemplateAndService(revokeModel.getTemplateId(), revokeModel.getServiceId());

        Map<String, Object> success = Maps.newHashMap();
        success.put(TYPE, REVOKE);
        TenantDO destTenantDO = revokeModel.getDestTenantDO();
        TemplateDO templateDO = revokeModel.getTemplateDO();
        monitorLog.info(LogResult.getSuccessLogResult(MonitorLogTypeEnum.cubeTemplateRecord.name(), "", createMonitorLog(success, revokeModel.getType(), templateDO, destTenantDO, input.getOperator())));

        metricsMap.put(PHASE, revokeApplyInDBEnd);
        metricsMap.put(COST, System.currentTimeMillis() - start);
        monitorLog.info(LogResult.getLogResult(MonitorLogTypeEnum.cubeTemplateStat.name(), JsonUtils.toJsonString(input), metricsMap));
    }

    public Map<String, Object> createMonitorLog(Map<String, Object> result,
                                                String type,
                                                TemplateDO templateDO,
                                                TenantDO destTenantDO,
                                                String operator) {
        Map<String, Object> success = Maps.newHashMap();
        success.put("actionType", result.get("type"));
        success.put("topic", result.get("topic"));
        success.put("source", type);
        success.put("gmtCreate", new Timestamp(System.currentTimeMillis()));
        success.put("serviceId", destTenantDO.getId());
        success.put("serviceName", destTenantDO.getName());
        success.put("templateId", templateDO.getId());
        success.put("templateName", templateDO.getName());
        success.put("userName", AuthInterceptor.getUserName());
        success.put("operator", org.apache.commons.lang3.StringUtils.isBlank(operator) ? AuthInterceptor.getUserName() : operator);
        return success;
    }

    public Channel createChannel(Optional<Channel> optionalChannel, String destTenantId, String userId, UserDO userDO) throws Exception {
        Channel addDestChannel;
        if (optionalChannel.isPresent()) {
            addDestChannel = optionalChannel.get();
            if (null == optionalChannel.get().getCreateTime()) {
                addDestChannel.setCreateTime(new Date());
            }
            addDestChannel.setModifyTime(new Date());
        } else {
            addDestChannel = new Channel();
            addDestChannel.setName(CHANNEL_NAME);
            addDestChannel.setModifyTime(new Date());
            addDestChannel.setCreateTime(new Date());
            addDestChannel.setTenantId(destTenantId);
            addDestChannel.setUserId(userId);
            addDestChannel.setCreator(userDO.getName());
            addDestChannel.setEditor(userDO.getName());
            List<ChannelParameter> channelParameterList = new ArrayList<>();

            addDestChannel.setEngineName(ZOOM_CHAT);
            ChannelParameter channelParameter0 = new ChannelParameter();
            channelParameter0.setIsSecret(true);
            channelParameter0.setName(PARAM_NAME_VERIFICATION_TOKEN);
            channelParameter0.setValue(rsaService.encrypt(cubeTemplateVerificationToken));
            channelParameterList.add(channelParameter0);

            ChannelParameter channelParameter1 = new ChannelParameter();
            channelParameter1.setIsSecret(false);
            channelParameter1.setName(PARAM_NAME_ENDPOINT);
            channelParameter1.setValue(environment.getProperty("cube.template.endpoint.url"));
            channelParameterList.add(channelParameter1);

            ChannelParameter channelParameter2 = new ChannelParameter();
            channelParameter2.setIsSecret(false);
            channelParameter2.setName(IS_DEAULT);
            channelParameter2.setValue(String.valueOf(false));
            channelParameterList.add(channelParameter2);
            addDestChannel.setParameters(channelParameterList);
        }
        channelDao.save(addDestChannel);
        return addDestChannel;
    }
}
