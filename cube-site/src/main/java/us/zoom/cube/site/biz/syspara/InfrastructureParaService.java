package us.zoom.cube.site.biz.syspara;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import us.zoom.cube.site.biz.infrastructure.InfrastructureSysPara;
import us.zoom.infra.dao.model.SysParaDO;
import us.zoom.infra.syspara.SysParaEventHandlerIntf;
import us.zoom.infra.syspara.SysParaEventService;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.Arrays;
import java.util.List;

/**
 * @author: <PERSON> zhen<PERSON>
 * @date: 2023/12/05 15:06
 * @desc:
 */
@Slf4j
@Component
public class InfrastructureParaService implements SysParaEventHandlerIntf {

    public static final String INFRASTRUCTURE = "Infrastructure";
    private InfrastructureSysPara infrastructureSysPara = null;

    public InfrastructureParaService() {
        SysParaEventService.registeSysparaListener(this, Arrays.asList(INFRASTRUCTURE));
    }

    @Override
    public boolean onEventWhenHasData(List<SysParaDO> sysParas) {
        if (CollectionUtils.isEmpty(sysParas)) {
            return true;
        }
        String config = sysParas.get(0).getValue();
        try {
            infrastructureSysPara = JsonUtils.toObjectByTypeRef(config, new TypeReference<InfrastructureSysPara>() {
            });
            log.info("infrastructure param: {}", config);
        } catch (Exception e) {
            log.error("load infrastructure error, value:{}", sysParas.get(0).getValue(), e);
        }
        return true;
    }

    @Override
    public boolean onEventWhenNoData() {
        return false;
    }

    public InfrastructureSysPara getInfrastructureSysPara() {
        return infrastructureSysPara;
    }
}
