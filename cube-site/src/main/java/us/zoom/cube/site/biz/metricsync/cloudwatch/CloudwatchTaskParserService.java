package us.zoom.cube.site.biz.metricsync.cloudwatch;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.regions.Region;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import us.zoom.cube.site.biz.AuthService;
import us.zoom.cube.site.biz.metricsync.cloudwatch.SyncTask.*;
import us.zoom.cube.site.lib.query.CloudwatchSearchByTagsQuery;
import jakarta.annotation.Resource;

/**
 * @author: tobey.zhu
 * @date: /2023/03/22
 * @description:
 */
@Component
public class CloudwatchTaskParserService {
    @Resource
    private ResourceServiceInterface resourceService;

    public void setAuthService(AuthService authService) {
        this.authService = authService;
    }

    @Autowired
    private AuthService authService;

    public void setResourceService(ResourceServiceInterface resourceService) {
        this.resourceService = resourceService;
    }

    public Map<String, List<String>> getResources(CloudwatchSearchByTagsQuery cloudwatchSearchByTagsQuery) {
        return getResources(cloudwatchSearchByTagsQuery, true);
    }

    public Map<String, List<String>> getResources(CloudwatchSearchByTagsQuery cloudwatchSearchByTagsQuery, boolean checkAuth) {
        if (checkAuth) {
            authService.checkAuth(cloudwatchSearchByTagsQuery);
        }
        DimensionSetting dimensionSetting = new DimensionSetting();
        dimensionSetting.setResourceType(cloudwatchSearchByTagsQuery.getResourceType());
        dimensionSetting.setTags(cloudwatchSearchByTagsQuery.getTags());
        dimensionSetting.setRegions(cloudwatchSearchByTagsQuery.getRegionList());
        return getResources(dimensionSetting, cloudwatchSearchByTagsQuery.getAssumeRole());
    }

    private Map<String, List<String>> getResources(DimensionSetting dimensionSetting, String assumeRole) {
        Map<String, List<String>> resourceMap = new HashMap<>();
        for (String region : dimensionSetting.getRegions()) {
            List<String> resourceNameList = resourceService.getResourceNameList(Region.of(region), dimensionSetting.getResourceType(),
                    dimensionSetting.getTags(), assumeRole);
            resourceMap.put(region, resourceNameList);
        }
        return resourceMap;
    }
}
