package us.zoom.cube.site.biz;

import cn.hutool.core.lang.Assert;
import com.clickhouse.data.value.UnsignedShort;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zoom.op.monitor.domain.alarm.v2.AlarmDefinitionDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import us.zoom.cube.site.core.config.ClickhouseHandlerFactory;
import us.zoom.cube.site.lib.input.SmartRcaTimeSeriesInput;
import us.zoom.cube.site.lib.output.smartrca.RelateAlarmData;
import us.zoom.cube.site.lib.output.smartrca.SmartRcaTimeSeriesOutput;
import us.zoom.infra.clickhouse.ChNameDecoder;
import us.zoom.infra.clickhouse.ClickhouseConst;
import us.zoom.infra.dao.service.AlarmDefinitionV2DAO;
import us.zoom.infra.utils.DateUtils;


import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;



@Service
@Slf4j
public class SmartRcaService {

    @Autowired
    private AuthService authService;

    @Autowired
    private ClickhouseHandlerFactory clickhouseHandlerFactory;

    private static final String CONSTANT_SERVICE = "service";
    private static final String CONSTANT_INFRA = "infra";
    private static final String CONSTANT_ID = "id";
    private static final String CONSTANT_SERVICE_NODE = "serviceNode";
    private static final String CONSTANT_SERVICE_NAME = "serviceName";
    private static final String CONSTANT_METRIC_NAME = "metricName";
    private static final String CONSTANT_FIELD_NAME = "fieldName";
    private static final String CONSTANT_ALARM_ID = "alarmId";
    private static final String CONSTANT_INTERVAL_SECONDS = "intervalSeconds";
    private static final String CONSTANT_TIMERANGE_START = "timeRangeStart";
    private static final String CONSTANT_TIMERANGE_END = "timeRangeEnd";
    private static final String CONSTANT_CONDITION = "condition";
    private static final String CONSTANT_METRIC_TYPE ="metricType";
    private static final String CONSTANT_RELATIVE_ALARM_ID_LIST = "relativeAlarmIdListNew";
    private static final String CONSTANT_SELFALARM_ID = "selfAlarmId";
    private static final String CONSTANT_SELFALARM_NAME = "selfAlarmName";
    private static final Pattern CONDTION_PATTERN = Pattern.compile("(\\w+)\\s+IN\\s*(\\(.*?\\)|[^\\s)]+)", Pattern.CASE_INSENSITIVE);
    private static final Pattern ILLEGAL_PATTERN = Pattern.compile("\\bOR\\b|\\bNOT\\s+IN\\b|\\w+\\s*=\\s*[^\\s]+", Pattern.CASE_INSENSITIVE);
    private static final Pattern  SMART_RCA_SPECIAL_SERVICE_PATTERN = Pattern.compile(".*-infra$");

    private static ObjectMapper mapper = new ObjectMapper();

    @Autowired
    private AlarmDefinitionV2DAO alarmDefinitionV2DAO;


    public List<SmartRcaTimeSeriesOutput> getTimeSeriesQuery(SmartRcaTimeSeriesInput smartRcaTimeSeriesInput) throws ParseException{
        authService.checkAuth(smartRcaTimeSeriesInput);
        checkRcaTimeSeriesInput(smartRcaTimeSeriesInput);
        String baseSql = "SELECT * FROM (\n" +
                "  SELECT *, row_number()  OVER(PARTITION BY id, alarmId, relativeAlarmIdList, serviceNode, serviceName, metricName, fieldName ORDER BY updateTime DESC) AS rn, max(updateTime) OVER (PARTITION BY id) AS maxUpdateTime\n" +
                "  FROM %s.%s WHERE id = '%s') t WHERE t.rn = 1 AND t.updateTime = t.maxUpdateTime ORDER BY updateTime DESC";
        List<SmartRcaTimeSeriesOutput> result = new ArrayList<>();
        String finalSql = String.format(baseSql, ClickhouseConst.SMART_REC_SCHEMA, ClickhouseConst.TIME_SERIES_DATA_QUERY, smartRcaTimeSeriesInput.getId());
        List<Map<String, Object>> queryResult =
                clickhouseHandlerFactory.get().query(ChNameDecoder.decode(ClickhouseConst.SMART_REC_SCHEMA), finalSql);
        if(CollectionUtils.isNotEmpty(queryResult)){
            queryResult.stream().forEach(map -> {
                String incidentId = Optional.ofNullable(map.get(CONSTANT_ID)).map(Object::toString).orElse(null);
                if(StringUtils.isNotBlank(incidentId)){
                    SmartRcaTimeSeriesOutput srtso = new SmartRcaTimeSeriesOutput();
                    srtso.setId(incidentId);
                    String serviceNode = map.get(CONSTANT_SERVICE_NODE).toString();
                    srtso.setServiceNode(serviceNode);
                    String serviceName = map.get(CONSTANT_SERVICE_NAME).toString();
                    srtso.setServiceName(serviceName);
                    String metricName = map.get(CONSTANT_METRIC_NAME).toString();
                    srtso.setMetricName(metricName);
                    String fieldName = map.get(CONSTANT_FIELD_NAME).toString();
                    srtso.setFieldName(fieldName);
                    srtso.setAlarmId(map.get(CONSTANT_ALARM_ID).toString());
                    srtso.setSelfAlarmId(map.get(CONSTANT_SELFALARM_ID).toString());
                    srtso.setSelfAlarmName(map.get(CONSTANT_SELFALARM_NAME).toString());
                    Optional.ofNullable(alarmDefinitionV2DAO.getById(srtso.getAlarmId()))
                            .map(AlarmDefinitionDO::getName)
                            .filter(StringUtils::isNotBlank)
                            .ifPresent(srtso::setAlarmName);
                    srtso.setMetricType(map.get(CONSTANT_METRIC_TYPE).toString());
                    String querySql = "SELECT toStartOfInterval(time, INTERVAL %d SECOND) as time,  avg(%s) as `%s_avg` FROM \"%s\".\"%s\"  WHERE time >= '%s' AND time < '%s' %s GROUP BY time ORDER BY time";
                    Object value = map.get(CONSTANT_INTERVAL_SECONDS);
                    int intInterval = (value instanceof UnsignedShort) ? ((UnsignedShort) value).intValue() : (int) value;
                    int interval = smartRcaTimeSeriesInput.getIntervalSeconds() > 0 ? smartRcaTimeSeriesInput.getIntervalSeconds():  intInterval;
                    String timeStart = Optional.ofNullable(smartRcaTimeSeriesInput.getBegin())
                            .filter(t -> t > 0)
                            .map(DateUtils::fromLongToDate)
                            .orElse(map.get(CONSTANT_TIMERANGE_START).toString());
                    String timeEnd = Optional.ofNullable(smartRcaTimeSeriesInput.getEnd())
                            .filter(t -> t > 0)
                            .map(DateUtils::fromLongToDate)
                            .orElse(map.get(CONSTANT_TIMERANGE_END).toString());
                    String conditionClause = StringUtils.isNotBlank(map.get(CONSTANT_CONDITION).toString()) ? "AND (" + map.get(CONSTANT_CONDITION).toString() + ")" : "";
                    String finalQUeySql = String.format(querySql, interval, fieldName, fieldName, serviceName, metricName, timeStart, timeEnd, conditionClause);
                    srtso.setSql(finalQUeySql);
                    Map<String, List<String>> inClauseMap = new HashMap<>();
                    if((StringUtils.isNotBlank(map.get(CONSTANT_SELFALARM_ID).toString()) || SMART_RCA_SPECIAL_SERVICE_PATTERN.matcher(serviceNode).find())
                            && StringUtils.isNotBlank(map.get(CONSTANT_CONDITION).toString()) && (!ILLEGAL_PATTERN.matcher(map.get(CONSTANT_CONDITION).toString()).find())){
                        Matcher conditionMatcher = CONDTION_PATTERN.matcher(map.get(CONSTANT_CONDITION).toString());
                        while (conditionMatcher.find()){
                            String key = conditionMatcher.group(1);
                            String valuesStr = conditionMatcher.group(2);
                            String rawvalue = valuesStr.replaceAll("^\\(|\\)$", "").trim();
                            if(StringUtils.isBlank(key) || StringUtils.isBlank(rawvalue)){
                                continue;
                            }
                            List<String> values = Arrays.stream(rawvalue.split("\\s*,\\s*"))
                                    .map(v -> v.replaceAll("'", "").trim())
                                    .collect(Collectors.toList());
                            inClauseMap.put(key, values);
                        }
                    }
                    Optional.ofNullable(inClauseMap).filter(m -> !m.isEmpty()).ifPresent(srtso::setTagCondition);
                    List<RelateAlarmData> alarmList = new ArrayList<>();
                    if(StringUtils.isNotBlank(map.get(CONSTANT_RELATIVE_ALARM_ID_LIST).toString())){
                        try {
                            List<RelateAlarmData> alarmDataList = mapper.readValue(map.get(CONSTANT_RELATIVE_ALARM_ID_LIST).toString(), new TypeReference<List<RelateAlarmData>>() {
                            });
                            alarmList.addAll(alarmDataList);
                            srtso.setRelateAlarmDataList(alarmList);
                        } catch (JsonProcessingException e) {
                            log.error("error in converting Json to Object", e);
                        }
                    }
                    result.add(srtso);
                }
            });
        }
        return result;
    }

    private void checkRcaTimeSeriesInput(SmartRcaTimeSeriesInput smartRcaTimeSeriesInput){
        Assert.isTrue(StringUtils.isNotBlank(smartRcaTimeSeriesInput.getId()), "Incident Id can't be empty");
    }

    public List<String> checkExists(String[] incidentIds) {
        if (ArrayUtils.isEmpty(incidentIds)) {
            return Collections.emptyList();
        }
        String where = Arrays.stream(incidentIds).map(incidentId -> "'%s'".formatted(incidentId)).collect(Collectors.joining(","));
        String finalSql = "SELECT DISTINCT id FROM \"%s\".\"%s\" where id in (%s)".formatted(ClickhouseConst.SMART_REC_SCHEMA, ClickhouseConst.TOPN_ROOT_CAUSE_TABLE, where);
        return Optional.ofNullable(clickhouseHandlerFactory.get().query(ChNameDecoder.decode(ClickhouseConst.SMART_REC_SCHEMA), finalSql)).orElse(Collections.emptyList())
                .stream().map(map -> Optional.ofNullable(map.get(CONSTANT_ID)).map(Object::toString).orElse("")).filter(id -> StringUtils.isNotBlank(id)).collect(Collectors.toList());
    }
}
