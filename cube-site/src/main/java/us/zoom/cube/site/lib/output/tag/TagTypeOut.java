package us.zoom.cube.site.lib.output.tag;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/24 16:40
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TagTypeOut {
    private String id;
    private Integer source;
    private String desc;
    private String tagTypeName;
    private List<String> scopes;
}
