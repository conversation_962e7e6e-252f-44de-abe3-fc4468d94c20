package us.zoom.cube.site.api.web;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import us.zoom.cube.site.biz.MetricsService;
import us.zoom.cube.site.lib.BasePara;
import us.zoom.cube.site.lib.IdListPara;
import us.zoom.cube.site.lib.IdPara;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.expression.ExpressionParseInput;
import us.zoom.cube.site.lib.input.MetricsBatchAddInput;
import us.zoom.cube.site.lib.input.MetricsInput;
import us.zoom.cube.site.lib.input.MetricsNonCheckInput;
import us.zoom.cube.site.lib.input.metrics.MetricsFieldMapsQueryInput;
import us.zoom.cube.site.lib.input.metrics.MetricDistributionInput;
import us.zoom.cube.site.lib.input.metrics.MetricsBatchAddDescriptionInput;
import us.zoom.cube.site.lib.input.metrics.MetricsDocumentLinkInput;
import us.zoom.cube.site.lib.query.MetricsName;
import us.zoom.cube.site.lib.query.MetricsQuery;
import us.zoom.cube.site.lib.query.PageQuery;

import jakarta.validation.Valid;
import java.util.List;

/**
 * <AUTHOR> Junjian
 * @create 2020/7/16 4:20 PM
 *
 * Metrics configuration CRUD
 */
@RestController
@RequestMapping("/api/metrics")
@SuppressWarnings("rawtypes")
public class MetricsController {

    @Autowired
    private MetricsService metricsService;

    /**
     * Metrics configuration list
     * @param pageQuery
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/searchMetrics", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject searchMetrics(@Valid @RequestBody PageQuery<MetricsQuery> pageQuery) throws Exception {
        return metricsService.searchMetrics(pageQuery);
    }

    /**
     * check metrics name before adding
     * @param metricsName
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/checkMetricsName", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject checkMetricsName(@Valid @RequestBody MetricsName metricsName) throws Exception {
        return ResponseObject.success(metricsService.checkMetricsName(metricsName.getMetricsName()));
    }

    @RequestMapping(value = "/addMetrics", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject addMetrics(@Valid @RequestBody MetricsInput metricsInput) throws Exception {
        return metricsService.addMetrics(metricsInput);
    }

    @RequestMapping(value = "/editMetrics", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject editMetrics(@Valid @RequestBody MetricsInput metricsInput) throws Exception {
        return metricsService.editMetrics(metricsInput);
    }

    @RequestMapping(value = "deleteMetrics", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject deleteMetrics(@Valid @RequestBody IdPara idPara) throws Exception {
        return metricsService.deleteMetrics(idPara);
    }

    /**
     * get metrics detail
     * @param idPara
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "getMetricsById", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject getMetricsById(@Valid @RequestBody IdPara idPara) throws Exception {
        return metricsService.getMetricsById(idPara);
    }

    /**
     * get metrics detail
     * @param idPara
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/calcPeriodByMetricId", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject calcPeriodByMetricId(@Valid @RequestBody IdPara idPara) throws Exception {
        return metricsService.calcPeriodByMetricId(idPara);
    }


    /**
     * get metrics distribution
     * @param metricDistributionInput
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/getMetricDistribution", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject getMetricDistribution(@Valid @RequestBody MetricDistributionInput metricDistributionInput) throws Exception {
        return metricsService.getMetricDistribution(metricDistributionInput);
    }

    /**
     * get metrics for alarm creation
     * @param basePara
     * @return
     */
    @RequestMapping(value = "/getMetricsByTenant", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject getMetricsByTenant(@Valid @RequestBody BasePara basePara) {
        return metricsService.getMetricsListByTenantId(basePara.getUserId(), basePara.getTenantId());
    }

    /**
     * get metrics tags and fields when one is specified for alarm creation
     * if this interface hava modified , the below v2 interface must modify too
     * @param idPara
     * @return
     */
    @RequestMapping(value = "/getFieldAndTagByMetricsId", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject getFieldsByMetricsId(@Valid @RequestBody IdPara idPara) {
        return metricsService.getFieldAndTagByMetricsId(idPara);
    }

    /**
     * manual splicing two fields machinesIp and machinesUnitTagName
     */
    @RequestMapping(value = "/v2/getFieldAndTagByMetricsId", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject getFieldsByMetricsIdV2(@Valid @RequestBody IdPara idPara) {
        return metricsService.getFieldAndTagByMetricsIdV2(idPara.getUserId(),idPara.getTenantId(),idPara.getAuthResourceUrl(),idPara.getId(), idPara.isShowNotDerived());
    }

    /**
     * get map keys of a field from clickhouse with service, metrics name, field name
     */
    @RequestMapping(value = "/v2/getFieldMapKeys", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject getFieldMapKeys(@Valid @RequestBody MetricsFieldMapsQueryInput metricsFieldMapsQueryInput) {
        return metricsService.getFieldMapKeysFromClickHouse(metricsFieldMapsQueryInput);
    }

    /**
     * Parse the expression and get the variable
     * @param expressionParseInput input
     * @return parse result
     */
    @RequestMapping(value = "/parseVariablesFromExpression", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject parseVariablesFromExpression(@Valid @RequestBody ExpressionParseInput expressionParseInput) {
        return metricsService.parseVariablesFromExpression(expressionParseInput);
    }

    @RequestMapping(value = "/editMetricsStatus", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject editMetricsStatus(@Valid @RequestBody MetricsNonCheckInput metricsInput) throws Exception {
        return metricsService.editMetricsStatus(metricsInput);
    }

    @RequestMapping(value = "/batchTagList", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject<List<String>> getMetricsBatchTagList(@Valid @RequestBody MetricsBatchAddInput metricsBatchAddInput) throws Exception {
        return metricsService.getMetricsBatchTagList(metricsBatchAddInput);
    }

    @RequestMapping(value = "/batchTagAdd", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject<String> metricsBatchAdd(@Valid @RequestBody MetricsBatchAddInput metricsBatchAddInput) throws Exception {
        return metricsService.metricsBatchAdd(metricsBatchAddInput);
    }

    @RequestMapping(value = "/batchDelete", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject<String> metricsBatchDelete(@Valid @RequestBody IdListPara idListPara) throws Exception {
        return metricsService.metricsBatchDelete(idListPara);
    }

    @RequestMapping(value = "/getPeriodByMetricsId", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject getPeriodByMetricsId(@Valid @RequestBody IdPara idPara) {
        return metricsService.getPeriodByMetricsId(idPara);
    }

    @RequestMapping(value = "/listTagsAndFields", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject listTagsAndFields(@Valid @RequestBody IdPara idPara) {
        return metricsService.listTagsAndFields(idPara);
    }

    @RequestMapping(value = "/updateDescription", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject updateDescription(@Valid @RequestBody MetricsBatchAddDescriptionInput metricsBatchAddDescriptionInput) {
        return metricsService.updateDescription(metricsBatchAddDescriptionInput);
    }

    @RequestMapping(value = "/updateDocumentLink", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject updateDocumentLink(@Valid @RequestBody MetricsDocumentLinkInput metricsDocumentLinkInput) {
        return metricsService.updateDocumentLink(metricsDocumentLinkInput);
    }

    @RequestMapping(value = "/queryMetricsByNameAndService", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject queryMetricsByNameAndService(@Valid @RequestBody MetricsName metricsName) {
        return metricsService.queryMetricsByNameAndService(metricsName);
    }


    /**
     * get metrics variable and functions
     */
    @RequestMapping(value = "getVariableAndFunction", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject getVariableAndFunction(@Valid @RequestBody IdPara idPara) {
        return metricsService.getVariableAndFunction(idPara.getUserId(),idPara.getTenantId(),idPara.getAuthResourceUrl(),idPara.getId());
    }

}
