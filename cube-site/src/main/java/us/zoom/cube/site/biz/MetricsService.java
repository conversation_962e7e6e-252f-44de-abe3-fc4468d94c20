package us.zoom.cube.site.biz;

import com.clickhouse.client.ClickHouseException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zoom.op.monitor.domain.AggregationRuleType;
import com.zoom.op.monitor.domain.AggregationRuleVersion;
import com.zoom.op.monitor.domain.ConditionedStatus;
import com.zoom.op.monitor.domain.IdAndName;
import jakarta.annotation.PostConstruct;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import us.zoom.cube.site.biz.clickhouse.ChPublisher;
import us.zoom.cube.site.biz.clickhouse.ChTableEvent;
import us.zoom.cube.site.biz.alarm.AlarmDefinitionService;
import us.zoom.cube.site.core.tag.ResourceTypeConstant;
import us.zoom.cube.site.core.tag.service.ResourceTagService;
import us.zoom.cube.site.lib.input.metrics.MetricDistributionInput;
import us.zoom.cube.site.lib.input.metrics.MetricsFieldMapsQueryInput;
import us.zoom.cube.site.lib.output.metric.MetricDistributionOutput;
import us.zoom.cube.site.lib.output.tag.TagInfoOut;
import org.jetbrains.annotations.NotNull;
import us.zoom.cube.site.infra.constants.SymbolConstant;
import us.zoom.infra.dao.service.BatchCalculateMetricDAO;
import us.zoom.infra.dao.service.CollectorMetricsDAO;
import us.zoom.infra.dao.service.TenantDAO;
import us.zoom.jsqlparser.JSQLParserException;
import us.zoom.jsqlparser.expression.*;
import us.zoom.jsqlparser.parser.CCJSqlParserManager;
import us.zoom.jsqlparser.parser.CCJSqlParserUtil;
import us.zoom.jsqlparser.schema.Column;
import us.zoom.jsqlparser.schema.Table;
import us.zoom.jsqlparser.statement.select.*;
import org.apache.commons.lang3.StringUtils;
import org.influxdb.dto.QueryResult;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.integrations.MetricsFieldTypeEnum;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.lib.utils.expression.CustomAviatorUtils;
import us.zoom.cube.site.api.intercept.AuthInterceptor;
import us.zoom.cube.site.biz.alarm.AlarmDefinitionService;
import us.zoom.cube.site.biz.syspara.AlarmParaService;
import us.zoom.cube.site.biz.syspara.DataQueryLimitService;
import us.zoom.cube.site.biz.syspara.MetricsParaService;
import us.zoom.cube.site.biz.syspara.clickhouse.ClickhouseAuthParaService;
import us.zoom.cube.site.biz.syspara.clickhouse.ClickhouseParaService;
import us.zoom.cube.site.core.*;
import us.zoom.cube.site.core.auth.AuthHandler;
import us.zoom.cube.site.core.config.ClickhouseHandlerFactory;
import us.zoom.cube.site.core.config.ConfigCache;
import us.zoom.cube.site.core.config.PiiTableCacheLoader;
import us.zoom.cube.site.core.tag.ResourceTypeConstant;
import us.zoom.cube.site.core.tag.service.ResourceTagService;
import us.zoom.cube.site.infra.constants.SymbolConstant;
import us.zoom.cube.site.infra.enums.WebCodeEnum;
import us.zoom.cube.site.infra.utils.ConvertUtils;
import us.zoom.cube.site.infra.utils.ExceptionUtils;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.*;
import us.zoom.cube.site.lib.dto.TplMetricsOverrideDTO;
import us.zoom.cube.site.lib.dto.TplMetricsSynTenantIdDTO;
import us.zoom.cube.site.lib.input.*;
import us.zoom.cube.site.lib.input.expression.ExpressionParseInput;
import us.zoom.cube.site.lib.input.metrics.MetricDistributionInput;
import us.zoom.cube.site.lib.input.metrics.MetricsBatchAddDescriptionInput;
import us.zoom.cube.site.lib.input.metrics.MetricsDocumentLinkInput;
import us.zoom.cube.site.lib.output.PageResult;
import us.zoom.cube.site.lib.output.agg.*;
import us.zoom.cube.site.lib.output.config.metrics.*;
import us.zoom.cube.site.lib.output.expression.ExpressionParseOutput;
import us.zoom.cube.site.lib.output.metric.MetricDistributionOutput;
import us.zoom.cube.site.lib.output.metric.MetricItem;
import us.zoom.cube.site.lib.output.tag.TagInfoOut;
import us.zoom.cube.site.lib.query.*;
import us.zoom.cube.site.lib.query.metric.MetricFieldQuery;
import us.zoom.infra.clickhouse.*;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.service.BatchCalculateMetricDAO;
import us.zoom.infra.dao.service.CollectorFieldDAO;
import us.zoom.infra.dao.service.CollectorMetricsDAO;
import us.zoom.infra.dao.service.DataParserPipelineDAO;
import us.zoom.infra.enums.*;
import us.zoom.infra.influx.service.InfluxService;
import us.zoom.infra.thread.NamedThreadFactory;
import us.zoom.infra.utils.CommonSplitConstants;
import us.zoom.infra.utils.Instance;
import us.zoom.jsqlparser.JSQLParserException;
import us.zoom.jsqlparser.expression.*;
import us.zoom.jsqlparser.parser.CCJSqlParserManager;
import us.zoom.jsqlparser.parser.CCJSqlParserUtil;
import us.zoom.jsqlparser.schema.Column;
import us.zoom.jsqlparser.schema.Table;
import us.zoom.jsqlparser.statement.select.*;
import us.zoom.jsqlparser.util.deparser.ExpressionDeParser;

import java.io.StringReader;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static us.zoom.cube.site.infra.enums.WebCodeEnum.*;
import static us.zoom.cube.site.lib.output.config.metrics.MetricsTagsAndFieldsOutput.newField;
import static us.zoom.cube.site.lib.output.config.metrics.MetricsTagsAndFieldsOutput.newTag;
import static us.zoom.infra.clickhouse.ClickhouseConst.*;
import static us.zoom.infra.enums.QueryStatus.NON_SELECT;
import static us.zoom.infra.utils.CommonSplitConstants.COMMA_SPLIT;


/**
 * <AUTHOR> Junjian
 * @create 2020/7/16 4:27 PM
 */
@Service
@Slf4j
@SuppressWarnings("rawtypes")
public class MetricsService {

    @Autowired
    private AuthService authService;

    @Autowired
    private AuthHandler authHandler;

    @Autowired
    private MetricsHandler metricsHandler;

    @Autowired
    private AlarmHandler alarmHandler;

    @Autowired
    private MetricsAggregationHandler metricsAggregationHandler;

    @Autowired
    private MetricsAggregationRuleHandler metricsAggregationRuleHandler;

    @Autowired
    private AggregationCustomFieldRuleHandler aggregationCustomFieldRuleHandler;

    @Autowired
    private CollectorHandler collectorHandler;

    @Autowired
    private CollectorFieldDAO collectorFieldDAO;

    @Autowired
    private InfluxService influxService;

    @Autowired
    private TenantHandler tenantHandler;

    @Autowired
    private MetricsValidator metricsValidator;

    @Autowired
    private TemplateHandler templateHandler;

    @Autowired
    private DataParserPipelineDAO dataParserPipelineDAO;

    @Autowired
    private DataParserPipelineHandler dataParserPipelineHandler;

    @Autowired
    private ConfigCache configCache;

    private static ThreadPoolExecutor executor;

    @Autowired
    private ClickhouseHandlerFactory clickhouseHandlerFactory;


    @Autowired
    private ClickhouseAuthParaService clickhouseAuthParaService;

    @Autowired
    private SysParaService sysParaService;

    @Autowired
    private PiiTableCacheLoader piiTableLoader;

    @Autowired
    private DashHandler dashHandler;

    @Autowired
    UserHandler userHandler;

    @Autowired
    SubEnvironmentHandler subEnvironmentHandler;

    @Autowired
    AdCfgHandler adCfgHandler;

    @Autowired
    MetricsParaService metricsParaService;

    @Autowired
    MetricsServiceTransactional metricsServiceTransactional;

    @Autowired
    private ClickhouseParaService clickhouseParaService;

    @Autowired
    private DataQueryLimitService dataQueryLimitService;

    @Autowired
    private AlarmParaService alarmParaService;

    @Autowired
    private ResourceTagService resourceTagService;

    @Autowired
    private AlarmDefinitionService alarmDefinitionService;

    @Autowired
    private CollectorMetricsDAO collectorMetricsDAO;

    @Autowired
    private ChPublisher chPublisher;

    @Autowired
    private TenantDAO tenantDAO;

    public static final Pattern INFLUX_METRICS_QUERY_SQL_PATTERN = Pattern.compile("(.*FROM|from)(\\s+\"[^\"]+\"\\.\"[^\"]+\"\\.\")([^\"]+)(\".*)");

    public static final int DEFAULT_PERIOD_UNIT = 60;



    private CCJSqlParserManager parserManager = new CCJSqlParserManager();

    private Logger monitorLog = LoggerFactory.getLogger("Monitor");
    @Autowired
    private BatchCalculateMetricDAO batchCalculateMetricDAO;

    @PostConstruct
    public void init() {
        int cores = Runtime.getRuntime().availableProcessors();
        executor = new ThreadPoolExecutor(cores, cores * 2 - 1, 1, TimeUnit.HOURS,
                new LinkedBlockingQueue(50), new NamedThreadFactory("metrics service thread pool"));
    }

    public ResponseObject searchMetrics(PageQuery<MetricsQuery> pageQuery) {
        try {
            authService.checkAuth(pageQuery);
            List<SearchMetricsItemOut> searchMetricsItemOutList = new ArrayList<>();
            /*
              extract query parameter
             */
            int pageIndex = pageQuery.getPageIndex();
            int pageSize = pageQuery.getPageSize();
            MetricsQuery metricsQuery = Optional.ofNullable(pageQuery.getQueryPara()).orElse(new MetricsQuery());
            metricsQuery.adjust();
            pageQuery.setQueryPara(metricsQuery);
            String metricsName = metricsQuery.getMetricsName();
            Integer type = metricsQuery.getMetricsType();
            Integer enabled = metricsQuery.getEnabled();
            String collectorName = metricsQuery.getCollectorName();
            String dataParserName = metricsQuery.getDataParserName();
            List<String> tenantIds = Instance.toList(pageQuery.getTenantId());
            String creator = metricsQuery.getCreator();
            List<String> labels = metricsQuery.getLabelIds();
            String labelMatchType = metricsQuery.getLabelMatchType();

            Future<List<MetricsDO>> metricsDOListFuture = executor.submit(() -> metricsHandler.findByMetricsNameLikeWithTypeAndStatus(metricsName, type, enabled, collectorName, dataParserName, tenantIds, creator, labels,  labels == null ? 0 : labels.size(),  labelMatchType, pageIndex, pageSize));
            Future<Integer> countFuture = executor.submit(() -> metricsHandler.getCountByMetricsNameLikeAndType(metricsName, type, enabled, collectorName, dataParserName, tenantIds, creator, labels,labels == null ? 0: labels.size(), labelMatchType));

            List<MetricsDO> metricsDOList = metricsDOListFuture.get();
            for (int i = 0; i < metricsDOList.size(); i++) {
                searchMetricsItemOutList.add(new SearchMetricsItemOut());
                //add label Info
                List<TagInfoOut> tagInfoOuts = resourceTagService.listResourceTagOuts(pageQuery.getTenantId(), metricsDOList.get(i).getId(), ResourceTypeConstant.RESOURCE_METRIC_TYPE);
                searchMetricsItemOutList.get(i).setLabelInfoList(tagInfoOuts);
                BeanUtils.copyProperties(metricsDOList.get(i), searchMetricsItemOutList.get(i));
            }


            /*
              1. get metrics-aggregation map    metricsId -> aggId
              2. get aggregationRule map        aggId -> ruleList
              3. get detail rule count map      rule -> function, histogram, percentile
              4. get customize rule count map
             */
            List<String> metricsIds = metricsDOList.stream().map(MetricsDO::getId).collect(Collectors.toList());
            List<MetricsAggregationDO> metricsAggregationDOList = metricsHandler.listMetricsAggByMetricsIds(metricsIds);
            List<String> aggIds = metricsAggregationDOList.stream().map(MetricsAggregationDO::getId).collect(Collectors.toList());
            List<MetricsAggregationRuleDO> metricsAggregationRuleDOList = metricsHandler.listRulesByMetricsAggIds(aggIds);
            List<AggregationCustomFieldRuleDO> aggregationCustomFieldRuleDOList = aggregationCustomFieldRuleHandler.listRulesByAggIds(aggIds);

            Map<String, String> aggIdToMetricsIdMap = new HashMap<>();
            metricsAggregationDOList.forEach(e -> aggIdToMetricsIdMap.put(e.getId(), e.getMetricsId()));

            Map<String, String> ruleIdToMetricsIdMap = new ConcurrentHashMap<>();
            metricsAggregationRuleDOList.forEach(e -> {
                if (aggIdToMetricsIdMap.containsKey(e.getAggId())) {
                    ruleIdToMetricsIdMap.put(e.getId(), aggIdToMetricsIdMap.get(e.getAggId()));
                }
            });
            aggregationCustomFieldRuleDOList.forEach(e -> {
                if (aggIdToMetricsIdMap.containsKey(e.getAggId())) {
                    ruleIdToMetricsIdMap.put(e.getId(), aggIdToMetricsIdMap.get(e.getAggId()));
                }
            });

            Map<String, SearchMetricsItemOut.FieldsCount> metricsIdFieldsCountMap = new ConcurrentHashMap<>();
            Map<Integer, Set<String>> ruleMap = new HashMap<>();

            /*
              a.version = 0, count by aggTypes
              b.version = 1 {
                  rule type = 1   join function
                  rule type = 2   join histogram
                  rule type = 3   join percentile
                  }
              c.customize rule
             */

            // a
            handleV0FunctionRuleField(metricsAggregationRuleDOList, ruleIdToMetricsIdMap, metricsIdFieldsCountMap, ruleMap);

            // b
            handleV1AggRuleField(ruleIdToMetricsIdMap, metricsIdFieldsCountMap, ruleMap);

            // c
            handleCustomFieldRuleField(aggregationCustomFieldRuleDOList, ruleIdToMetricsIdMap, metricsIdFieldsCountMap);

            searchMetricsItemOutList.forEach(e -> {
                if (metricsIdFieldsCountMap.containsKey(e.getId())) {
                    e.setFieldsCount(metricsIdFieldsCountMap.get(e.getId()));
                }
            });
            int count = countFuture.get();
            ResponseObject responseObject = ResponseObject.success(pageQuery.getOperId(), new PageResult<>(count, searchMetricsItemOutList));
            return responseObject;
        } catch (InterruptedException | ExecutionException e) {
            log.error(ExceptionUtils.getTrace(e));
            throw new SiteException(InnerError.getCode(), InnerError.getErrMsg());
        }
    }

    private void handleCustomFieldRuleField(List<AggregationCustomFieldRuleDO> aggregationCustomFieldRuleDOList, Map<String, String> ruleIdToMetricsIdMap, Map<String, SearchMetricsItemOut.FieldsCount> metricsIdFieldsCountMap) {
        aggregationCustomFieldRuleDOList.stream().forEach(
                rule -> {
                    String metricsId = ruleIdToMetricsIdMap.get(rule.getId());
                    metricsIdFieldsCountMap.computeIfAbsent(metricsId, t -> new SearchMetricsItemOut.FieldsCount()).addCustomize(1);
                }
        );
    }

    private void handleV0FunctionRuleField(List<MetricsAggregationRuleDO> metricsAggregationRuleDOList, Map<String, String> ruleIdToMetricsIdMap, Map<String, SearchMetricsItemOut.FieldsCount> metricsIdFieldsCountMap, Map<Integer, Set<String>> ruleMap) {
        metricsAggregationRuleDOList.stream().forEach(
                rule -> {
                    Integer version = rule.getVersion();
                    if (version == AggregationRuleVersion.v0.value()) {
                        String aggTypes = rule.getAggTypes();
                        if (StringUtils.isNotEmpty(aggTypes)) {
                            int c = aggTypes.split(COMMA_SPLIT).length;
                            String metricsId = ruleIdToMetricsIdMap.get(rule.getId());
                            metricsIdFieldsCountMap.computeIfAbsent(metricsId, t -> new SearchMetricsItemOut.FieldsCount()).addCommon(c);
                        }
                    } else if (version == AggregationRuleVersion.v1.value()) {
                        ruleMap.computeIfAbsent(rule.getAggRuleType(), v -> new HashSet<>()).add(rule.getId());
                    }

                }
        );
    }

    private void handleV1AggRuleField(Map<String, String> ruleIdToMetricsIdMap, Map<String, SearchMetricsItemOut.FieldsCount> metricsIdFieldsCountMap, Map<Integer, Set<String>> ruleMap) throws ExecutionException, InterruptedException {
        List<Future<Boolean>> futureList = ruleMap.entrySet().stream().map(e -> {
            return executor.submit(() -> {
                Integer ruleType = e.getKey();
                List<String> ruleIds = e.getValue().stream().collect(Collectors.toList());
                if (ruleType == AggregationRuleType.function.value()) {
                    List<AggregationFunctionItemDO> aggregationFunctionItemDOList = metricsAggregationRuleHandler.listFunctionItemsByRuleIds(ruleIds);
                    aggregationFunctionItemDOList.forEach(v -> {
                        String metricsId = ruleIdToMetricsIdMap.get(v.getMetricsAggregationRuleId());
                        metricsIdFieldsCountMap.computeIfAbsent(metricsId, t -> new SearchMetricsItemOut.FieldsCount()).addCommon(1);
                    });

                } else if (ruleType == AggregationRuleType.histogram.value()) {
                    List<AggregationHistogramRangeItemDO> aggregationHistogramRangeItemDOList = metricsAggregationRuleHandler.listHistogramRangeItemsByRuleIds(ruleIds);
                    aggregationHistogramRangeItemDOList.forEach(v -> {
                        String metricsId = ruleIdToMetricsIdMap.get(v.getMetricsAggregationRuleId());
                        metricsIdFieldsCountMap.computeIfAbsent(metricsId, t -> new SearchMetricsItemOut.FieldsCount()).addHistogram(1);
                    });
                } else if (ruleType == AggregationRuleType.percentile.value()) {
                    List<AggregationPercentileItemDO> aggregationPercentileItemDOList = metricsAggregationRuleHandler.listPercentileItemsByRuleIds(ruleIds);
                    aggregationPercentileItemDOList.forEach(v -> {
                        String metricsId = ruleIdToMetricsIdMap.get(v.getMetricsAggregationRuleId());
                        metricsIdFieldsCountMap.computeIfAbsent(metricsId, t -> new SearchMetricsItemOut.FieldsCount()).addPercentile(1);
                    });
                }
                return true;
            });
        }).collect(Collectors.toList());
        for (Future<Boolean> future : futureList) {
            future.get();
        }
    }


    public ResponseObject findByMetricsNameLike(PageQuery<MetricsQuery> pageQuery) {
//        authService.mustTenantAdminOrAdmin(pageQuery.getUserId(), pageQuery.getTenantId());
        authService.checkAuth(pageQuery);
        int pageIndex = pageQuery.getPageIndex();
        int pageSize = pageQuery.getPageSize();
        if (pageQuery.getQueryPara() == null) {
            pageQuery.setQueryPara(new MetricsQuery());
        }
        pageQuery.getQueryPara().adjust();
        List<String> tenantIds = Instance.toList(pageQuery.getTenantId());
        List<MetricsDO> metricsDOList = metricsHandler.findByMetricsNameLike(pageQuery.getQueryPara().getMetricsName(), tenantIds, pageIndex, pageSize);
        int count = metricsHandler.getCountByMetricsNameLike(pageQuery.getQueryPara().getMetricsName(), tenantIds);
        return ResponseObject.success(pageQuery.getOperId(), new PageResult<>(count, metricsDOList));
    }

    public boolean checkMetricsName(String metricsName) {
        return metricsValidator.checkMetricsName(metricsName);
    }

    public ResponseObject queryMetricsByName(MetricsName metricsName) {
        checkAndAuth(metricsName);
        MetricsDO metricsDO = metricsHandler.queryMetricsByName(metricsName.getMetricsName(), metricsName.getTenantId());
        if (Objects.isNull(metricsDO)) {
            return ResponseObject.fail("This metrics does not exist");
        }
        authService.checkHasMetricsThrowExpWhenNotHave(metricsName, metricsDO.getId());
        return ResponseObject.success(metricsDO);
    }

    private void checkAndAuth(MetricsName metricsName) {
        metricsName.check();
        metricsName.setTenantId(AuthInterceptor.getTenantId());
        metricsName.setUserId(AuthInterceptor.getUserId());
        authService.checkAuth(metricsName);
    }

    @Transactional(rollbackFor = Exception.class)
    public ResponseObject addMetrics(MetricsInput metricsInput) {
        authService.checkAuth(metricsInput);
        Assert.isTrue(authService.canOperate(metricsInput.getUserId(), metricsInput.getTenantId()), "user can not operate this service");
        MetricsDO metricsDO = addMetricsBase(metricsInput);
        return ResponseObject.success(metricsInput.getOperId(), metricsDO.getId());
    }

    protected MetricsDO addMetricsBase(MetricsInput metricsInput) {
        metricsInput.sortAggRules();
        metricsValidator.checkAddMetricsParameter(metricsInput);
        //complete user info
        String userName = userHandler.getNameById(metricsInput.getUserId());
        metricsInput.setCreator(userName);
        metricsInput.setEditor(userName);
        MetricsDO metricsDO = metricsInput.toAddMetricsDO();
        CollectorMetricsDO collectorMetricsDO = new CollectorMetricsDO();
        BeanUtils.copyProperties(metricsInput, collectorMetricsDO);
        collectorMetricsDO.setId(IdUtils.generateId());
        collectorMetricsDO.setMetricsId(metricsDO.getId());

        int metricsType = metricsInput.getType();
        // big datacollectorId
        if (metricsType == MetricsTypeEnum.ORIGINAL.getValue()) {
            List<MetricsFieldDO> metricsFieldDOList = ConvertUtils.convertList2List(metricsInput.getMetricsFieldList(), MetricsFieldDO.class);
            metricsFieldDOList.forEach(metricsFieldDO -> {
                metricsFieldDO.setId(IdUtils.generateId());
                metricsFieldDO.setMetricsId(metricsDO.getId());
            });
            metricsHandler.addOriginalMetrics(metricsDO, collectorMetricsDO, metricsFieldDOList);
        } else if (metricsType == MetricsTypeEnum.AGGREGATION.getValue()) {
            /**
             * 1. construct metricsAggregation
             * 2. construct agg rule
             * 3. construct function, histogram, percentile, customField
             * 4. construct metrics field
             */

            // 1
            MetricsAggregationInput metricsAggregationInput = metricsInput.getMetricsAggregation();
            adjustMetricsAggregation(metricsAggregationInput);
            MetricsAggregationDO metricsAggregationDO = new MetricsAggregationDO();
            BeanUtils.copyProperties(metricsAggregationInput, metricsAggregationDO);
            metricsAggregationDO.setId(IdUtils.generateId());
            metricsAggregationDO.setMetricsId(metricsDO.getId());

            // 2
            List<MetricsAggregationRuleDO> metricsAggregationRuleDOList = Lists.newArrayList();
            List<AggregationFunctionItemDO> aggregationFunctionItemDOList = Lists.newArrayList();
            List<AggregationHistogramRangeItemDO> aggregationHistogramRangeItemDOList = Lists.newArrayList();
            List<AggregationPercentileItemDO> aggregationPercentileItemDOList = Lists.newArrayList();
            List<AggregationCustomFieldRuleDO> aggregationCustomFieldRuleDOList = Lists.newArrayList();
            List<MetricsFieldDO> metricsFieldDOList = new ArrayList<>();
            MetricsAggregationRuleComposeInput metricsAggregationRuleComposeInput = metricsAggregationInput.getMetricsAggregationRuleCompose();

            // 3.1 function
            handleAddMetricsFunctionRules(metricsDO, metricsAggregationDO, metricsAggregationRuleComposeInput, metricsAggregationRuleDOList, aggregationFunctionItemDOList, metricsFieldDOList);


            // 3.2 histogram
            handleAddMetricsHistogramRules(metricsDO, metricsAggregationDO, metricsAggregationRuleComposeInput, metricsAggregationRuleDOList, aggregationHistogramRangeItemDOList, metricsFieldDOList);


            // 3.3 percentile
            handleAddMetricsPercentileRules(metricsDO, metricsAggregationDO, metricsAggregationRuleComposeInput, metricsAggregationRuleDOList, aggregationPercentileItemDOList, metricsFieldDOList);

            // 3.4 customField
            handleAddMetricsCustomFieldRules(metricsDO, metricsAggregationDO, metricsAggregationRuleComposeInput, aggregationCustomFieldRuleDOList, metricsFieldDOList);

            metricsHandler.addAggregatedMetricsInSerial(metricsDO, collectorMetricsDO, metricsFieldDOList, metricsAggregationDO, metricsAggregationRuleDOList, aggregationFunctionItemDOList, aggregationHistogramRangeItemDOList, aggregationPercentileItemDOList, aggregationCustomFieldRuleDOList);
        }
        // add tag info
        if(!CollectionUtils.isEmpty(metricsInput.getLabelInfoList())) {
            List<String> tagIds = metricsInput.getLabelInfoList().stream().map(TagInfoOut::getId).filter(Objects::nonNull).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(tagIds)) {
                resourceTagService.batchAddResourceTag(tagIds, ResourceTypeConstant.RESOURCE_METRIC_TYPE, metricsDO.getId(),
                        ResourceTypeConstant.RESOURCE_TAG_OPERATION_ADD);
            }
        }

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                ChTableEvent<String> chTableEvent = new ChTableEvent<>(ChTableEvent.METRIC_CREATE_UPDATE_TYPE, metricsDO.getId());
                chPublisher.publish(chTableEvent);
            }
        });

        return metricsDO;
    }


    public List<MetricsSynDO> buildTplRelationShipTenantMetrics(String tplName, MetricsDO metricsDO, MetricsInput metricsInput, String destTenantScope, List<String> includeTenantNames, List<String> excludeTenantNames, boolean override) {
        List<MetricsSynDO> list = new ArrayList();
        TplMetricsSynTenantIdDTO tplMetricsSynTenantIdDTO = templateHandler.queryNeedTplMetricsSynTenantIdsByMetricsId(tplName, metricsInput.getCollectorId(), metricsDO.getMetricsName(), metricsDO.getTenantId(), metricsInput.getUserId(), destTenantScope, includeTenantNames, excludeTenantNames, override);
        if (CollectionUtils.isEmpty(tplMetricsSynTenantIdDTO.getNeedAddList())
                && CollectionUtils.isEmpty(tplMetricsSynTenantIdDTO.getNeedOverrideList())
                && CollectionUtils.isEmpty(tplMetricsSynTenantIdDTO.getNeedSkipList())
                && CollectionUtils.isEmpty(tplMetricsSynTenantIdDTO.getErrorList())) {
            return list;
        }
        List<MetricsSynDO> result = buildTenantMetricsList(tplMetricsSynTenantIdDTO, metricsInput, tplName);
        if (!CollectionUtils.isEmpty(result)) {
            list.addAll(result);
        }
        return list;
    }

    public List<MetricsAggregationSynDO> buildTplRelationShipTenantAggregationMetrics(String tplName, MetricsDO metricsDO, MetricsInput metricsInput, String destTenantScope, List<String> includeTenantNames, List<String> excludeTenantNames, boolean override) {
        List<MetricsAggregationSynDO> list = new ArrayList();
        TplMetricsSynTenantIdDTO tplMetricsSynTenantIdDTO = templateHandler.queryNeedTplMetricsSynTenantIdsByMetricsId(tplName, metricsInput.getCollectorId(), metricsDO.getMetricsName(), metricsDO.getTenantId(), metricsInput.getUserId(), destTenantScope, includeTenantNames, excludeTenantNames, override);
        if (CollectionUtils.isEmpty(tplMetricsSynTenantIdDTO.getNeedAddList())
                && CollectionUtils.isEmpty(tplMetricsSynTenantIdDTO.getNeedOverrideList())
                && CollectionUtils.isEmpty(tplMetricsSynTenantIdDTO.getNeedSkipList())
                && CollectionUtils.isEmpty(tplMetricsSynTenantIdDTO.getErrorList())) {
            return list;
        }
        List<MetricsAggregationSynDO> result = buildTenantAggregationMetricsList(tplMetricsSynTenantIdDTO, metricsInput, tplName);
        if (!CollectionUtils.isEmpty(result)) {
            list.addAll(result);
        }
        return list;
    }

    private List<MetricsAggregationSynDO> buildTenantAggregationMetricsList(TplMetricsSynTenantIdDTO tplMetricsSynTenantIdDTO, MetricsInput metricsInput, String tplName) {
        List<MetricsAggregationSynDO> aggregationSynDOList = new ArrayList<>();
        Set<String> tenantSet = new HashSet<>();
        Set<String> errorTenantSet = new HashSet<>();
        if (!CollectionUtils.isEmpty(tplMetricsSynTenantIdDTO.getNeedAddList())) {
            tenantSet.addAll(tplMetricsSynTenantIdDTO.getNeedAddList());
        }
        if (!CollectionUtils.isEmpty(tplMetricsSynTenantIdDTO.getNeedSkipList())) {
            tenantSet.addAll(tplMetricsSynTenantIdDTO.getNeedSkipList());
        }
        Map<String, TplMetricsOverrideDTO> overrideDTOMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(tplMetricsSynTenantIdDTO.getNeedOverrideList())) {
            overrideDTOMap.putAll(tplMetricsSynTenantIdDTO.getNeedOverrideList().stream().collect(Collectors.toMap(TplMetricsOverrideDTO::getTenantId, e -> e)));
            tenantSet.addAll(tplMetricsSynTenantIdDTO.getNeedOverrideList().stream().map(e -> e.getTenantId()).collect(Collectors.toList()));
        }
        Map<String/**tenantId*/, String/**collectorId*/> collectorIdMap = new HashMap();
        if (!CollectionUtils.isEmpty(tplMetricsSynTenantIdDTO.getNeedAddList())) {
            String sourceCollectorId = metricsInput.getCollectorId();
            String pipelineName = dataParserPipelineHandler.getPipelineByCollectorId(sourceCollectorId).getName();
            collectorIdMap.putAll(getCollectorIdMap(pipelineName, tplMetricsSynTenantIdDTO.getNeedAddList()));
        }


        if (!CollectionUtils.isEmpty(tplMetricsSynTenantIdDTO.getErrorList())) {
            errorTenantSet.addAll(tplMetricsSynTenantIdDTO.getErrorList());
        }
        Map<String/**tenantId**/, String/**tenantName**/> tenantMap = new HashMap<>();
        List<TenantInfoDO> tenantInfoDOS = templateHandler.buildTenantByTenantIds(new ArrayList<>(tenantSet));
        tenantMap.putAll(tenantInfoDOS.stream().collect(Collectors.toMap(TenantInfoDO::getId, e -> e.getName())));

        if (!CollectionUtils.isEmpty(tenantSet)) {
            tenantSet.stream().forEach(e -> {
                MetricsAggregationSynDO metricsAggregationSynDO = new MetricsAggregationSynDO();
                if (tplMetricsSynTenantIdDTO.getNeedAddList().contains(e)) {
                    MetricsAggregationSynAddDO metricsAggregationSynAddDO = new MetricsAggregationSynAddDO();
                    //0、build metrics
                    MetricsDO metricsDO = metricsInput.toAddMetricsDO();
                    metricsDO.setTenantId(e);

                    //1、build collectorMetricsDO
                    CollectorMetricsDO collectorMetricsDO = new CollectorMetricsDO();
                    BeanUtils.copyProperties(metricsInput, collectorMetricsDO);
                    collectorMetricsDO.setId(IdUtils.generateId());
                    collectorMetricsDO.setMetricsId(metricsDO.getId());
                    collectorMetricsDO.setCollectorId(collectorIdMap.get(e));

                    // 2、build metricsAggregationDO
                    MetricsAggregationInput metricsAggregationInput = metricsInput.getMetricsAggregation();
                    adjustMetricsAggregation(metricsAggregationInput);
                    MetricsAggregationDO metricsAggregationDO = new MetricsAggregationDO();
                    BeanUtils.copyProperties(metricsAggregationInput, metricsAggregationDO);
                    metricsAggregationDO.setId(IdUtils.generateId());
                    metricsAggregationDO.setMetricsId(metricsDO.getId());

                    // 3 build aggregation info
                    List<MetricsAggregationRuleDO> metricsAggregationRuleDOList = Lists.newArrayList();
                    List<AggregationFunctionItemDO> aggregationFunctionItemDOList = Lists.newArrayList();
                    List<AggregationHistogramRangeItemDO> aggregationHistogramRangeItemDOList = Lists.newArrayList();
                    List<AggregationPercentileItemDO> aggregationPercentileItemDOList = Lists.newArrayList();
                    List<AggregationCustomFieldRuleDO> aggregationCustomFieldRuleDOList = Lists.newArrayList();
                    List<MetricsFieldDO> metricsFieldDOList = new ArrayList<>();
                    MetricsAggregationRuleComposeInput metricsAggregationRuleComposeInput = metricsAggregationInput.getMetricsAggregationRuleCompose();

                    // 3.1 function
                    handleAddMetricsFunctionRules(metricsDO, metricsAggregationDO, metricsAggregationRuleComposeInput, metricsAggregationRuleDOList, aggregationFunctionItemDOList, metricsFieldDOList);


                    // 3.2 histogram
                    handleAddMetricsHistogramRules(metricsDO, metricsAggregationDO, metricsAggregationRuleComposeInput, metricsAggregationRuleDOList, aggregationHistogramRangeItemDOList, metricsFieldDOList);


                    // 3.3 percentile
                    handleAddMetricsPercentileRules(metricsDO, metricsAggregationDO, metricsAggregationRuleComposeInput, metricsAggregationRuleDOList, aggregationPercentileItemDOList, metricsFieldDOList);

                    // 3.4 customField
                    handleAddMetricsCustomFieldRules(metricsDO, metricsAggregationDO, metricsAggregationRuleComposeInput, aggregationCustomFieldRuleDOList, metricsFieldDOList);

                    metricsAggregationSynAddDO.setMetricsDO(metricsDO);
                    metricsAggregationSynAddDO.setCollectorMetricsDO(collectorMetricsDO);
                    metricsAggregationSynAddDO.setMetricsFieldDOList(metricsFieldDOList);
                    metricsAggregationSynAddDO.setMetricsAggregationDO(metricsAggregationDO);
                    metricsAggregationSynAddDO.setMetricsAggregationRuleDOList(metricsAggregationRuleDOList);
                    metricsAggregationSynAddDO.setAggregationFunctionItemDOList(aggregationFunctionItemDOList);
                    metricsAggregationSynAddDO.setAggregationHistogramRangeItemDOList(aggregationHistogramRangeItemDOList);
                    metricsAggregationSynAddDO.setAggregationPercentileItemDOList(aggregationPercentileItemDOList);
                    metricsAggregationSynAddDO.setAggregationCustomFieldRuleDOList(aggregationCustomFieldRuleDOList);
                    metricsAggregationSynAddDO.setTenantName(tenantMap.get(e));
                    metricsAggregationSynDO.setMetricsAggregationSynAddDO(metricsAggregationSynAddDO);

                }
                if (tplMetricsSynTenantIdDTO.getNeedSkipList().contains(e)) {
                    MetricsAggregationSynSkipDO metricsAggregationSynSkipDO = new MetricsAggregationSynSkipDO();
                    metricsAggregationSynSkipDO.setTenantId(e);
                    metricsAggregationSynSkipDO.setTenantName(tenantMap.get(e));
                    metricsAggregationSynDO.setMetricsAggregationSynSkipDO(metricsAggregationSynSkipDO);
                }
                if (overrideDTOMap.containsKey(e)) {
                    MetricsAggregationSynUpdateDO metricsAggregationSynUpdateDO = new MetricsAggregationSynUpdateDO();
                    metricsAggregationSynUpdateDO.setTenantId(e);
                    metricsAggregationSynUpdateDO.setTenantName(tenantMap.get(e));
                    metricsAggregationSynDO.setMetricsAggregationSynUpdateDO(metricsAggregationSynUpdateDO);
                }
                aggregationSynDOList.add(metricsAggregationSynDO);
            });
        }
        if (!CollectionUtils.isEmpty(errorTenantSet)) {
            errorTenantSet.stream().forEach(e -> {
                MetricsAggregationSynDO metricsAggregationSynDO = new MetricsAggregationSynDO();
                if (tplMetricsSynTenantIdDTO.getErrorList().contains(e)) {
                    MetricsAggregationSynErrorDO metricsAggregationSynErrorDO = new MetricsAggregationSynErrorDO();
                    metricsAggregationSynErrorDO.setTenantName(e);
                    metricsAggregationSynDO.setMetricsAggregationSynErrorDO(metricsAggregationSynErrorDO);
                    aggregationSynDOList.add(metricsAggregationSynDO);
                }
            });
        }
        return aggregationSynDOList;
    }


    private Map<String, String> getCollectorIdMap(String pipelineName, List<String> tenantIds) {
        List<List<String>> partition = Lists.partition(tenantIds, 100);
        List<CollectorIdInfoDO> list = Collections.synchronizedList(new ArrayList<>());
        partition.parallelStream().forEach(e -> {
            list.addAll(dataParserPipelineDAO.getCollectsByNameAndTenantIds(pipelineName, e));
        });
        return list.stream().collect(Collectors.toMap(CollectorIdInfoDO::getTenantId, e -> e.getCollectorId()));
    }

    private List<MetricsSynDO> buildTenantMetricsList(TplMetricsSynTenantIdDTO tplMetricsSynTenantIdDTO, MetricsInput metricsInput, String tplName) {
        List<MetricsSynDO> metricsSynDOList = new ArrayList<>();
        Set<String> tenantSet = new HashSet<>();
        Set<String> errorTenantSet = new HashSet<>();
        if (!CollectionUtils.isEmpty(tplMetricsSynTenantIdDTO.getNeedAddList())) {
            tenantSet.addAll(tplMetricsSynTenantIdDTO.getNeedAddList());
        }
        if (!CollectionUtils.isEmpty(tplMetricsSynTenantIdDTO.getNeedSkipList())) {
            tenantSet.addAll(tplMetricsSynTenantIdDTO.getNeedSkipList());
        }
        Map<String, TplMetricsOverrideDTO> overrideDTOMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(tplMetricsSynTenantIdDTO.getNeedOverrideList())) {
            overrideDTOMap.putAll(tplMetricsSynTenantIdDTO.getNeedOverrideList().stream().collect(Collectors.toMap(TplMetricsOverrideDTO::getTenantId, e -> e)));
            tenantSet.addAll(tplMetricsSynTenantIdDTO.getNeedOverrideList().stream().map(e -> e.getTenantId()).collect(Collectors.toList()));
        }
        Map<String/**tenantId*/, String/**collectorId*/> collectorIdMap = new HashMap();
        if (!CollectionUtils.isEmpty(tplMetricsSynTenantIdDTO.getNeedAddList())) {
            String sourceCollectorId = metricsInput.getCollectorId();
            String pipelineName = dataParserPipelineHandler.getPipelineByCollectorId(sourceCollectorId).getName();
            collectorIdMap.putAll(getCollectorIdMap(pipelineName, tplMetricsSynTenantIdDTO.getNeedAddList()));
        }

        if (!CollectionUtils.isEmpty(tplMetricsSynTenantIdDTO.getErrorList())) {
            errorTenantSet.addAll(tplMetricsSynTenantIdDTO.getErrorList());
        }

        Map<String/**tenantId**/, String/**tenantName**/> tenantMap = new HashMap<>();
        List<TenantInfoDO> tenantInfoDOS = templateHandler.buildTenantByTenantIds(new ArrayList<>(tenantSet));
        tenantMap.putAll(tenantInfoDOS.stream().collect(Collectors.toMap(TenantInfoDO::getId, e -> e.getName())));

        if (!CollectionUtils.isEmpty(tenantSet)) {
            tenantSet.stream().forEach(e -> {
                MetricsSynDO metricsSynDO = new MetricsSynDO();
                if (tplMetricsSynTenantIdDTO.getNeedAddList().contains(e)) {
                    MetricsDO metricsDO = metricsInput.toAddMetricsDO();
                    metricsDO.setTenantId(e);
                    CollectorMetricsDO collectorMetricsDO = new CollectorMetricsDO();
                    BeanUtils.copyProperties(metricsInput, collectorMetricsDO);
                    collectorMetricsDO.setId(IdUtils.generateId());
                    collectorMetricsDO.setCollectorId(collectorIdMap.get(e));
                    collectorMetricsDO.setMetricsId(metricsDO.getId());

                    List<MetricsFieldDO> metricsFieldDOList = ConvertUtils.convertList2List(metricsInput.getMetricsFieldList(), MetricsFieldDO.class);
                    metricsFieldDOList.forEach(metricsFieldDO -> {
                        metricsFieldDO.setId(IdUtils.generateId());
                        metricsFieldDO.setMetricsId(metricsDO.getId());
                    });
                    MetricsSynAddDO metricsSynAddDO = new MetricsSynAddDO();
                    metricsSynAddDO.setMetricsDO(metricsDO);
                    metricsSynAddDO.setCollectorMetricsDO(collectorMetricsDO);
                    metricsSynAddDO.setMetricsFieldDOS(metricsFieldDOList);
                    metricsSynAddDO.setTenantName(tenantMap.get(e));
                    metricsSynDO.setMetricsSynAddDO(metricsSynAddDO);
                }
                if (tplMetricsSynTenantIdDTO.getNeedSkipList().contains(e)) {
                    MetricsSynSkipDO metricsSynSkipDO = new MetricsSynSkipDO();
                    metricsSynSkipDO.setTenantId(e);
                    metricsSynSkipDO.setTenantName(tenantMap.get(e));
                    metricsSynDO.setMetricsSynSkipDO(metricsSynSkipDO);
                }
                if (overrideDTOMap.containsKey(e)) {
                    TplMetricsOverrideDTO tplMetricsOverrideDTO = overrideDTOMap.get(e);
                    MetricsSynUpdateDO metricsSynUpdateDO = new MetricsSynUpdateDO();
                    metricsSynUpdateDO.setMetricsId(tplMetricsOverrideDTO.getMetricsId());
                    metricsSynUpdateDO.setTenantId(e);
                    metricsSynUpdateDO.setTenantName(tenantMap.get(e));
                    metricsSynDO.setMetricsSynUpdateDO(metricsSynUpdateDO);
                }
                metricsSynDOList.add(metricsSynDO);
            });
        }
        if (!CollectionUtils.isEmpty(errorTenantSet)) {
            errorTenantSet.stream().forEach(e -> {
                MetricsSynDO metricsSynDO = new MetricsSynDO();
                if (tplMetricsSynTenantIdDTO.getErrorList().contains(e)) {
                    MetricsSynErrorDO metricsSynErrorDO = new MetricsSynErrorDO();
                    metricsSynErrorDO.setTenantName(e);
                    metricsSynDO.setMetricsSynErrorDO(metricsSynErrorDO);
                    metricsSynDOList.add(metricsSynDO);
                }
            });
        }
        return metricsSynDOList;
    }


    private void handleAddMetricsCustomFieldRules(MetricsDO metricsDO, MetricsAggregationDO metricsAggregationDO, MetricsAggregationRuleComposeInput metricsAggregationRuleComposeInput, List<AggregationCustomFieldRuleDO> aggregationCustomFieldRuleDOList, List<MetricsFieldDO> metricsFieldDOList) {
        metricsAggregationRuleComposeInput.getCustomize().forEach(e -> {
            AggregationCustomFieldRuleDO aggregationCustomFieldRuleDO = new AggregationCustomFieldRuleDO();
            aggregationCustomFieldRuleDO.setId(IdUtils.generateId());
            aggregationCustomFieldRuleDO.setAggId(metricsAggregationDO.getId());
            aggregationCustomFieldRuleDO.setExpression(e.getExpression());

            MetricsFieldDO metricsFieldDO = new MetricsFieldDO();
            metricsFieldDO.setId(IdUtils.generateId());
            metricsFieldDO.setMetricsId(metricsDO.getId());
            metricsFieldDO.setFieldType(MetricsFieldTypeEnum.number.getValue());
            String fieldName = e.getFieldName();
            metricsFieldDO.setFieldName(fieldName);
            metricsFieldDOList.add(metricsFieldDO);

            aggregationCustomFieldRuleDO.setMetricsFieldId(metricsFieldDO.getId());
            aggregationCustomFieldRuleDOList.add(aggregationCustomFieldRuleDO);

        });
    }

    private void handleAddMetricsFunctionRules(MetricsDO metricsDO, MetricsAggregationDO metricsAggregationDO, MetricsAggregationRuleComposeInput metricsAggregationRuleComposeInput, List<MetricsAggregationRuleDO> metricsAggregationRuleDOList, List<AggregationFunctionItemDO> aggregationFunctionItemDOList, List<MetricsFieldDO> metricsFieldDOList) {
        metricsAggregationRuleComposeInput.getCommon().forEach(e -> {
            // all the version of added function rule  = 1
            MetricsAggregationRuleDO metricsAggregationRuleDO = new MetricsAggregationRuleDO();
            metricsAggregationRuleDO.setId(IdUtils.generateId());
            metricsAggregationRuleDO.setAggId(metricsAggregationDO.getId());
            metricsAggregationRuleDO.setAggField(e.getAggField());
            metricsAggregationRuleDO.setAggTypes("");
            metricsAggregationRuleDO.setFilterCondition(e.getFilterCondition() == null ? "" : e.getFilterCondition());
            metricsAggregationRuleDO.setAggRuleType(AggregationRuleType.function.value());

            metricsAggregationRuleDO.setIsConditioned(e.getIsConditioned());
            metricsAggregationRuleDO.setConditionalFieldPrefix(e.getConditionalFieldPrefix() == null ? "" : e.getConditionalFieldPrefix());

            metricsAggregationRuleDO.setVersion(AggregationRuleVersion.v1.value());
            metricsAggregationRuleDOList.add(metricsAggregationRuleDO);

            //add original field for aggregation metrics
            //remove temporarily, waiting for next release
            /*String originalFieldName = e.getFieldPrefix();
            if(!metricsFieldDOList.stream().anyMatch(mf -> mf.getFieldName().contains(originalFieldName))) {
                MetricsFieldDO originalFieldDO = new MetricsFieldDO();
                originalFieldDO.setId(IdUtils.generateId());
                originalFieldDO.setMetricsId(metricsDO.getId());
                originalFieldDO.setFieldType(MetricsFieldTypeEnum.fromName(e.getFieldType()).getValue());
                originalFieldDO.setFieldName(originalFieldName);
                metricsFieldDOList.add(originalFieldDO);
            }*/

            e.getAggTypes().forEach(t -> {
                AggregationFunctionItemDO aggregationFunctionItemDO = new AggregationFunctionItemDO();
                aggregationFunctionItemDO.setId(IdUtils.generateId());
                aggregationFunctionItemDO.setMetricsAggregationRuleId(metricsAggregationRuleDO.getId());
                aggregationFunctionItemDO.setAggType(t.getValue());
                aggregationFunctionItemDO.setAggCondition(t.getCondition());

                MetricsFieldDO metricsFieldDO = new MetricsFieldDO();
                metricsFieldDO.setId(IdUtils.generateId());
                metricsFieldDO.setMetricsId(metricsDO.getId());

                String collectorFieldType = e.getFieldType(); //eg. string or number
                String aggFuntion = aggregationFunctionItemDO.getAggType(); //eg. sample
                metricsFieldDO.setFieldType(AggregationOperatorEnum.getMetricFieldTypeByAggOpr(aggFuntion, collectorFieldType).getValue()); //eg. ip.sample


                String fieldPrefix = metricsAggregationRuleDO.getFieldPrefix();
                metricsFieldDO.setFieldName(fieldPrefix + CommonSplitConstants.METRICS_FIELD_SPLIT + t.getValue());
                metricsFieldDOList.add(metricsFieldDO);

                aggregationFunctionItemDO.setTargetFieldId(metricsFieldDO.getId());
                aggregationFunctionItemDOList.add(aggregationFunctionItemDO);

            });
        });
    }

    private void handleAddMetricsHistogramRules(MetricsDO metricsDO, MetricsAggregationDO metricsAggregationDO, MetricsAggregationRuleComposeInput metricsAggregationRuleComposeInput, List<MetricsAggregationRuleDO> metricsAggregationRuleDOList, List<AggregationHistogramRangeItemDO> aggregationHistogramRangeItemDOList, List<MetricsFieldDO> metricsFieldDOList) {
        metricsAggregationRuleComposeInput.getHistogram().forEach(e -> {
            MetricsAggregationRuleDO metricsAggregationRuleDO = new MetricsAggregationRuleDO();
            metricsAggregationRuleDO.setId(IdUtils.generateId());
            metricsAggregationRuleDO.setAggId(metricsAggregationDO.getId());
            metricsAggregationRuleDO.setAggField(e.getAggField());
            metricsAggregationRuleDO.setAggTypes("");
            metricsAggregationRuleDO.setFilterCondition(e.getFilterCondition() == null ? "" : e.getFilterCondition());
            metricsAggregationRuleDO.setAggRuleType(AggregationRuleType.histogram.value());
            metricsAggregationRuleDO.setIsConditioned(ConditionedStatus.no.value());
            metricsAggregationRuleDO.setConditionalFieldPrefix("");
            metricsAggregationRuleDO.setVersion(AggregationRuleVersion.v1.value());
            metricsAggregationRuleDOList.add(metricsAggregationRuleDO);

            //add original field for aggregation metrics, histogram and percentile should all are number type
            //remove temporarily, waiting for next release
            /*String originalFieldName = e.getAggField();
            if(!metricsFieldDOList.stream().anyMatch(mf -> mf.getFieldName().contains(originalFieldName))) {
                MetricsFieldDO originalFieldDO = new MetricsFieldDO();
                originalFieldDO.setId(IdUtils.generateId());
                originalFieldDO.setMetricsId(metricsDO.getId());
                originalFieldDO.setFieldType(MetricsFieldTypeEnum.number.getValue());
                originalFieldDO.setFieldName(originalFieldName);
                metricsFieldDOList.add(originalFieldDO);
            }*/

            e.getRanges().forEach(t -> {
                AggregationHistogramRangeItemDO aggregationHistogramRangeItemDO = new AggregationHistogramRangeItemDO();
                aggregationHistogramRangeItemDO.setId(IdUtils.generateId());
                aggregationHistogramRangeItemDO.setMetricsAggregationRuleId(metricsAggregationRuleDO.getId());
                aggregationHistogramRangeItemDO.setLowerLimit(t.getLowerLimit());
                aggregationHistogramRangeItemDO.setUpperLimit(t.getUpperLimit());
                aggregationHistogramRangeItemDO.setLowerIntervalNotation(t.getLowerIntervalNotation());
                aggregationHistogramRangeItemDO.setUpperIntervalNotation(t.getUpperIntervalNotation());

                MetricsFieldDO metricsFieldDO = new MetricsFieldDO();
                metricsFieldDO.setId(IdUtils.generateId());
                metricsFieldDO.setMetricsId(metricsDO.getId());
                metricsFieldDO.setFieldType(MetricsFieldTypeEnum.number.getValue());
                metricsFieldDO.setFieldName(t.getResultLabel());
                metricsFieldDOList.add(metricsFieldDO);

                aggregationHistogramRangeItemDO.setTargetFieldId(metricsFieldDO.getId());
                aggregationHistogramRangeItemDOList.add(aggregationHistogramRangeItemDO);

            });

        });
    }

    private void handleAddMetricsPercentileRules(MetricsDO metricsDO, MetricsAggregationDO metricsAggregationDO, MetricsAggregationRuleComposeInput metricsAggregationRuleComposeInput, List<MetricsAggregationRuleDO> metricsAggregationRuleDOList, List<AggregationPercentileItemDO> aggregationPercentileItemDOList, List<MetricsFieldDO> metricsFieldDOList) {
        metricsAggregationRuleComposeInput.getPercentile().forEach(e -> {
            MetricsAggregationRuleDO metricsAggregationRuleDO = new MetricsAggregationRuleDO();
            metricsAggregationRuleDO.setId(IdUtils.generateId());
            metricsAggregationRuleDO.setAggId(metricsAggregationDO.getId());
            metricsAggregationRuleDO.setAggField(e.getAggField());
            metricsAggregationRuleDO.setAggTypes("");
            metricsAggregationRuleDO.setFilterCondition(e.getFilterCondition() == null ? "" : e.getFilterCondition());
            metricsAggregationRuleDO.setAggRuleType(AggregationRuleType.percentile.value());
            metricsAggregationRuleDO.setIsConditioned(ConditionedStatus.no.value());
            metricsAggregationRuleDO.setConditionalFieldPrefix("");
            metricsAggregationRuleDO.setVersion(AggregationRuleVersion.v1.value());
            metricsAggregationRuleDOList.add(metricsAggregationRuleDO);

            //add original field for aggregation metrics, histogram and percentile should all are number type
            //remove temporarily, waiting for next release
            /*String originalFieldName = e.getAggField();
            if(!metricsFieldDOList.stream().anyMatch(mf -> mf.getFieldName().contains(originalFieldName))) {
                MetricsFieldDO originalFieldDO = new MetricsFieldDO();
                originalFieldDO.setId(IdUtils.generateId());
                originalFieldDO.setMetricsId(metricsDO.getId());
                originalFieldDO.setFieldType(MetricsFieldTypeEnum.number.getValue());
                originalFieldDO.setFieldName(originalFieldName);
                metricsFieldDOList.add(originalFieldDO);
            }*/

            e.getPercentileValues().forEach(t -> {
                AggregationPercentileItemDO aggregationPercentileItemDO = new AggregationPercentileItemDO();
                aggregationPercentileItemDO.setId(IdUtils.generateId());
                aggregationPercentileItemDO.setMetricsAggregationRuleId(metricsAggregationRuleDO.getId());
                aggregationPercentileItemDO.setPercentileValue(t.getValue());

                MetricsFieldDO metricsFieldDO = new MetricsFieldDO();
                metricsFieldDO.setId(IdUtils.generateId());
                metricsFieldDO.setMetricsId(metricsDO.getId());
                metricsFieldDO.setFieldType(MetricsFieldTypeEnum.number.getValue());
                String fieldName = metricsAggregationRuleDO.getAggField() + CommonSplitConstants.METRICS_FIELD_SPLIT + CommonSplitConstants.P + t.getValue();
                metricsFieldDO.setFieldName(fieldName);
                metricsFieldDOList.add(metricsFieldDO);

                aggregationPercentileItemDO.setTargetFieldId(metricsFieldDO.getId());
                aggregationPercentileItemDOList.add(aggregationPercentileItemDO);
            });

        });
    }


    public void extractModifyFieldsForOriginalMetrics(MetricsInput metricsInput, MetricsOut metricsOut, List<MetricsFieldDO> addFields, List<String> deleteFields) {
        /**
         * 1. get delete removed fields
         * 2. get add fields
         */

        // 1
        Set<String> newRemainFieldIds = metricsInput.getMetricsFieldList().stream().filter(e -> !org.springframework.util.StringUtils.isEmpty(e.getId())).map(e -> e.getId()).collect(Collectors.toSet());
        Set<String> oldFieldIds = metricsOut.getMetricsFieldList().stream().map(e -> e.getId()).collect(Collectors.toSet());
        oldFieldIds.removeAll(newRemainFieldIds);
        deleteFields.addAll(oldFieldIds);

        // 2
        List<MetricsFieldInput> addedFieldInputs = metricsInput.getMetricsFieldList().stream().filter(e -> org.springframework.util.StringUtils.isEmpty(e.getId())).collect(Collectors.toList());
        List<MetricsFieldDO> addedMetricsFieldDOList = ConvertUtils.convertList2List(addedFieldInputs, MetricsFieldDO.class);
        addedMetricsFieldDOList.forEach(metricsFieldDO -> {
            metricsFieldDO.setId(IdUtils.generateId());
            metricsFieldDO.setMetricsId(metricsInput.getId());
        });
        addFields.addAll(addedMetricsFieldDOList);

    }

    @SuppressWarnings("rawtypes")
    public ResponseObject editMetricsStatus(MetricsNonCheckInput metricsInput) {

        log.info("[editMetricsStatus] metricsInput = " + JsonUtils.toJsonStringIgnoreExp(metricsInput));
        authService.checkAuth(metricsInput);
        authService.checkHasMetricsThrowExpWhenNotHave(metricsInput,metricsInput.getId());
        String metricsId = metricsInput.getId();
        Assert.isTrue(!StringUtils.isEmpty(metricsId), "metrics id can not be null");
        metricsInput.setEditor(userHandler.getNameById(metricsInput.getUserId()));
        MetricsDO metricsDO = new MetricsDO();
        BeanUtils.copyProperties(metricsInput, metricsDO);
        metricsHandler.editMetricsStatus(metricsDO);
        return ResponseObject.success(metricsInput.getOperId(), metricsDO.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    public ResponseObject editMetrics(MetricsInput metricsInput) {
        Long startTime = System.currentTimeMillis();
        log.info("[editMetrics] metricsInput = " + JsonUtils.toJsonStringIgnoreExp(metricsInput));
//        authService.mustTenantAdminOrAdmin(metricsInput.getUserId(), metricsInput.getTenantId());
        authService.checkAuth(metricsInput);
        authService.checkHasMetricsThrowExpWhenNotHave(metricsInput,metricsInput.getId());
        metricsInput.sortAggRules();
        // get origin Metrics
        String metricsId = metricsInput.getId();
        int metricsType = metricsInput.getType();
        MetricsOut metricsOut = getMetricsById(metricsId);
        // complete logic about validation in {metricsValidator}
        metricsValidator.checkEditMetricsParameter(metricsInput, metricsOut);
        //complete user info
        metricsInput.setEditor(userHandler.getNameById(metricsInput.getUserId()));
        // construct metricsDO
        MetricsDO metricsDO = new MetricsDO();
        BeanUtils.copyProperties(metricsInput, metricsDO);
        metricsDO.setTagNames(StringUtils.join(metricsInput.getTagNames(), COMMA_SPLIT));

        long midTime0 = System.currentTimeMillis();
        log.info("[editMetrics] midTime0 cost {} ms", midTime0 - startTime);

        if (metricsType == MetricsTypeEnum.ORIGINAL.getValue()) {
            List<MetricsFieldDO> addFields = Lists.newArrayList();
            List<String> deleteFieldIds = Lists.newArrayList();
            extractModifyFieldsForOriginalMetrics(metricsInput, metricsOut, addFields, deleteFieldIds);
            metricsHandler.editOriginalMetrics(metricsDO, deleteFieldIds, addFields);

        } else if (metricsType == MetricsTypeEnum.AGGREGATION.getValue()) {

            /**
             * a. handle metricsAggregation
             * b. handle function rule
             * c. handle histogram rule
             * d. handle percentile rule
             * e. handle customField rule
             */

            // a.handle metricsAggregation
            MetricsAggregationInput metricsAggregationInput = metricsInput.getMetricsAggregation();
            adjustMetricsAggregation(metricsAggregationInput);
            MetricsAggregationDO metricsAggregationDO = new MetricsAggregationDO();
            BeanUtils.copyProperties(metricsAggregationInput, metricsAggregationDO);

            MetricsAggregationRuleComposeInput metricsAggregationRuleComposeInput = metricsAggregationInput.getMetricsAggregationRuleCompose();
            MetricsAggregationRuleComposeOutput metricsAggregationRuleComposeOutput = metricsOut.getMetricsAggregation().getMetricsAggregationRuleCompose();

            List<AggregationFunctionRuleOutput> aggregationFunctionRuleOutputList = metricsAggregationRuleComposeOutput.getCommon();
            List<AggregationHistogramRuleOutput> aggregationHistogramRuleOutputList = metricsAggregationRuleComposeOutput.getHistogram();
            List<AggregationPercentileRuleOutput> aggregationPercentileRuleOutputList = metricsAggregationRuleComposeOutput.getPercentile();
            List<AggregationCustomFieldRuleOutput> aggregationCustomFieldRuleOutputList = metricsAggregationRuleComposeOutput.getCustomize();

            List<MetricsAggregationRuleDO> addMetricsAggregationRuleDOList = Lists.newArrayList();
            List<MetricsAggregationRuleDO> editMetricsAggregationRuleDOList = Lists.newArrayList();
            List<String> deleteMetricsAggregationRuleIdList = Lists.newArrayList();

            List<AggregationFunctionItemDO> addAggregationFunctionItemDOList = Lists.newArrayList();
            List<AggregationFunctionItemDO> editAggregationFunctionItemDOList = Lists.newArrayList();
            List<String> deleteAggregationFunctionItemIdList = Lists.newArrayList();

            List<AggregationHistogramRangeItemDO> addAggregationHistogramRangeItemDOList = Lists.newArrayList();
            List<AggregationHistogramRangeItemDO> editAggregationHistogramRangeItemDOList = Lists.newArrayList();
            List<String> deleteAggregationHistogramRangeItemIdList = Lists.newArrayList();

            List<AggregationPercentileItemDO> addAggregationPercentileItemDOList = Lists.newArrayList();
            List<AggregationPercentileItemDO> editAggregationPercentileItemDOList = Lists.newArrayList();
            List<String> deleteAggregationPercentileItemIdList = Lists.newArrayList();

            List<AggregationCustomFieldRuleDO> addAggregationCustomFieldRuleDOList = Lists.newArrayList();
            List<AggregationCustomFieldRuleDO> editAggregationCustomFieldRuleDOList = Lists.newArrayList();
            List<String> deleteAggregationCustomFieldRuleDOList = Lists.newArrayList();

            List<MetricsFieldDO> addMetricsFieldDOList = new ArrayList<>();
            List<MetricsFieldDO> editMetricsFieldDOList = new ArrayList<>();
            List<String> deleteMetricsFieldIdList = new ArrayList<>();

            List<MetricsAggregationRuleDO> ruleDOList = metricsAggregationRuleHandler.listRulesByAggIds(Lists.newArrayList(metricsAggregationDO.getId()));
            List<AggregationCustomFieldRuleDO> aggregationCustomFieldRuleDOList = aggregationCustomFieldRuleHandler.listRulesByAggIds(Lists.newArrayList(metricsAggregationDO.getId()));
            List<MetricsFieldDO> metricsFieldDOList = metricsHandler.listFieldsByMetricsIds(Lists.newArrayList(metricsId));

            Set<String> originalFieldDeamons = new HashSet<>();
            Set<String> toDeleteOriginalFields = new HashSet<>();

            long midTime1 = System.currentTimeMillis();
            log.info("[editMetrics] midTime1 cost {} ms", midTime1 - startTime);

            /**
             * b.1 handle add rule
             * b.2 handle edit rule
             */

            handleEditMetricsFunctionRule(metricsDO,metricsAggregationDO,ruleDOList,metricsFieldDOList, addMetricsAggregationRuleDOList,editMetricsAggregationRuleDOList, deleteMetricsAggregationRuleIdList, addAggregationFunctionItemDOList,
                    editAggregationFunctionItemDOList, deleteAggregationFunctionItemIdList, addMetricsFieldDOList, editMetricsFieldDOList, deleteMetricsFieldIdList, metricsAggregationRuleComposeInput,aggregationFunctionRuleOutputList,
                    originalFieldDeamons, toDeleteOriginalFields);

            /**
             * c.1 handle delete rule
             * c.2 handle add rule
             * c.3 handle edit rule
             */

            handleEditMetricsHistogramRule(metricsDO, metricsAggregationDO, ruleDOList, metricsFieldDOList, addMetricsAggregationRuleDOList, editMetricsAggregationRuleDOList, deleteMetricsAggregationRuleIdList, addAggregationHistogramRangeItemDOList,
                    editAggregationHistogramRangeItemDOList, deleteAggregationHistogramRangeItemIdList, addMetricsFieldDOList, editMetricsFieldDOList, deleteMetricsFieldIdList, metricsAggregationRuleComposeInput, aggregationHistogramRuleOutputList,
                    originalFieldDeamons, toDeleteOriginalFields);

            /**
             * d.1 handle delete rule
             * d.2 handle add rule
             * d.3 handle edit rule
             */
            handleEditMetricsPercentileRule(metricsDO, metricsAggregationDO, ruleDOList, metricsFieldDOList, addMetricsAggregationRuleDOList, editMetricsAggregationRuleDOList, deleteMetricsAggregationRuleIdList, addAggregationPercentileItemDOList,
                    editAggregationPercentileItemDOList, deleteAggregationPercentileItemIdList, addMetricsFieldDOList, editMetricsFieldDOList, deleteMetricsFieldIdList, metricsAggregationRuleComposeInput, aggregationPercentileRuleOutputList,
                    originalFieldDeamons, toDeleteOriginalFields);

            /**
             * e.1 handle add rule
             * e.2 handle edit rule
             * e.3 handle delete rule
             */
            handleEditCustomFieldRule(metricsDO, metricsAggregationDO, aggregationCustomFieldRuleDOList, metricsFieldDOList, addAggregationCustomFieldRuleDOList, editAggregationCustomFieldRuleDOList, deleteAggregationCustomFieldRuleDOList,
                    addMetricsFieldDOList, editMetricsFieldDOList, deleteMetricsFieldIdList, metricsAggregationRuleComposeInput, aggregationCustomFieldRuleOutputList);


            long midTime2 = System.currentTimeMillis();
            log.info("[editMetrics] midTime2 cost {} ms", midTime2 - startTime);

            prepareOriginalMetricsFieldDeletion(metricsInput, metricsOut, originalFieldDeamons, toDeleteOriginalFields, metricsFieldDOList, deleteMetricsFieldIdList);

            metricsHandler.editAggregatedMetrics(metricsDO, metricsAggregationDO, addMetricsAggregationRuleDOList, editMetricsAggregationRuleDOList, deleteMetricsAggregationRuleIdList, addAggregationFunctionItemDOList, editAggregationFunctionItemDOList, deleteAggregationFunctionItemIdList, addAggregationHistogramRangeItemDOList, editAggregationHistogramRangeItemDOList,
                    deleteAggregationHistogramRangeItemIdList, addAggregationPercentileItemDOList, editAggregationPercentileItemDOList, deleteAggregationPercentileItemIdList,addAggregationCustomFieldRuleDOList, editAggregationCustomFieldRuleDOList, deleteAggregationCustomFieldRuleDOList, addMetricsFieldDOList, editMetricsFieldDOList, deleteMetricsFieldIdList);

        }

        List<String> currentTags = Optional.ofNullable(metricsInput.getLabelInfoList()).orElse(Collections.emptyList())
        .stream().map(TagInfoOut::getId).collect(Collectors.toList());
        resourceTagService.handleEditResourceTag(currentTags, metricsInput.getTenantId(), metricsId, ResourceTypeConstant.RESOURCE_METRIC_TYPE);

        // push create event
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                ChTableEvent<String> chTableEvent = new ChTableEvent<>(ChTableEvent.METRIC_CREATE_UPDATE_TYPE, metricsDO.getId());
                chPublisher.publish(chTableEvent);
            }
        });

        log.info("[editMetrics] response = {}", metricsDO.getId());
        Long endTime = System.currentTimeMillis();
        log.info("[editMetrics] cost {} ms", endTime - startTime);
        return ResponseObject.success(metricsInput.getOperId(), metricsDO.getId());

    }



    private void prepareOriginalMetricsFieldDeletion(MetricsInput metricsInput, MetricsOut metricsOut, Set<String> originalFieldDeamons, Set<String> toDeleteOriginalFields, List<MetricsFieldDO> metricsFieldDOList, List<String> deleteMetricsFieldIdList) {
        //still keep original filed if it only existed in the Tag
        metricsOut.getTagNames().forEach(oldTag -> {
            if(!metricsInput.getTagNames().contains(oldTag)) {
                toDeleteOriginalFields.add(oldTag);
            }
        });
        originalFieldDeamons.addAll(metricsInput.getTagNames());

        toDeleteOriginalFields.removeAll(originalFieldDeamons);
        if(!CollectionUtils.isEmpty(toDeleteOriginalFields)) {
            metricsFieldDOList.forEach(metricsFieldDO -> {
                if(toDeleteOriginalFields.contains(metricsFieldDO.getFieldName())) {
                    deleteMetricsFieldIdList.add(metricsFieldDO.getId());
                }
            });
        }
    }

    private void handleEditCustomFieldRule(MetricsDO metricsDO, MetricsAggregationDO metricsAggregationDO,
                                           List<AggregationCustomFieldRuleDO> aggregationCustomFieldRuleDOList,
                                           List<MetricsFieldDO> metricsFieldDOList,
                                           List<AggregationCustomFieldRuleDO> addAggregationCustomFieldRuleDOList,
                                           List<AggregationCustomFieldRuleDO> editAggregationCustomFieldRuleDOList,
                                           List<String> deleteAggregationCustomFieldRuleDOList,
                                           List<MetricsFieldDO> addMetricsFieldDOList,
                                           List<MetricsFieldDO> editMetricsFieldDOList,
                                           List<String> deleteMetricsFieldIdList,
                                           MetricsAggregationRuleComposeInput metricsAggregationRuleComposeInput,
                                           List<AggregationCustomFieldRuleOutput> aggregationCustomFieldRuleOutputList) {
        metricsAggregationRuleComposeInput.getCustomize().forEach(e -> {
            boolean add = StringUtils.isEmpty(e.getId());
            if (add) {
                // handle add rule
                AggregationCustomFieldRuleDO aggregationCustomFieldRuleDO = new AggregationCustomFieldRuleDO();
                aggregationCustomFieldRuleDO.setId(IdUtils.generateId());
                aggregationCustomFieldRuleDO.setAggId(metricsAggregationDO.getId());
                aggregationCustomFieldRuleDO.setExpression(e.getExpression());

                MetricsFieldDO metricsFieldDO = new MetricsFieldDO();
                metricsFieldDO.setId(IdUtils.generateId());
                metricsFieldDO.setMetricsId(metricsDO.getId());
                metricsFieldDO.setFieldType(MetricsFieldTypeEnum.number.getValue());
                String fieldName = e.getFieldName();
                metricsFieldDO.setFieldName(fieldName);
                addMetricsFieldDOList.add(metricsFieldDO);

                aggregationCustomFieldRuleDO.setMetricsFieldId(metricsFieldDO.getId());
                addAggregationCustomFieldRuleDOList.add(aggregationCustomFieldRuleDO);
            } else {
                // handle edit rule
                AggregationCustomFieldRuleInput editRuleInput = e;
                AggregationCustomFieldRuleOutput oldRuleOutput = aggregationCustomFieldRuleOutputList.stream().filter(t -> t.getId().equals(e.getId())).findFirst().get();
                boolean needEditCustomFieldRule = metricsValidator.isCustomFieldRuleChanged(editRuleInput, oldRuleOutput);
                boolean needEditField = metricsValidator.isCustomFieldChanged(editRuleInput, oldRuleOutput);
                String oldFieldName = oldRuleOutput.getFieldName();
                MetricsFieldDO metricsFieldDO = metricsFieldDOList.stream().filter(f -> f.getFieldName().equals(oldFieldName)).findFirst().get();
                if (needEditCustomFieldRule) {
                    AggregationCustomFieldRuleDO oldRuleDO = aggregationCustomFieldRuleDOList.stream().filter(r -> r.getId().equals(e.getId())).findFirst().get();
                    AggregationCustomFieldRuleDO aggregationCustomFieldRuleDO = new AggregationCustomFieldRuleDO();
                    BeanUtils.copyProperties(oldRuleDO, aggregationCustomFieldRuleDO);
                    BeanUtils.copyProperties(editRuleInput, aggregationCustomFieldRuleDO);
                    editAggregationCustomFieldRuleDOList.add(aggregationCustomFieldRuleDO);
                }
                if (needEditField) {
                    String editFieldName = editRuleInput.getFieldName();
                    metricsFieldDO.setFieldName(editFieldName);
                    editMetricsFieldDOList.add(metricsFieldDO);
                }
            }
        });
        // handle delete rule
        List<AggregationCustomFieldRuleInput> editRules = metricsAggregationRuleComposeInput.getCustomize().stream().filter(e -> StringUtils.isNotEmpty(e.getId())).collect(Collectors.toList());
        List<AggregationCustomFieldRuleOutput> deleteRules = aggregationCustomFieldRuleOutputList.stream().filter(r -> {
            return !editRules.stream().anyMatch(t -> r.getId().equals(t.getId()));
        }).collect(Collectors.toList());
        deleteRules.forEach(r -> {
            deleteAggregationCustomFieldRuleDOList.add(r.getId());
            String fieldName = r.getFieldName();
            String deleteFieldId = metricsFieldDOList.stream().filter(f -> f.getFieldName().equals(fieldName)).findFirst().get().getId();
            deleteMetricsFieldIdList.add(deleteFieldId);
        });
    }

    private void handleEditMetricsPercentileRule(MetricsDO metricsDO, MetricsAggregationDO metricsAggregationDO,
                                                 List<MetricsAggregationRuleDO> ruleDOList,
                                                 List<MetricsFieldDO> metricsFieldDOList,
                                                 List<MetricsAggregationRuleDO> addMetricsAggregationRuleDOList,
                                                 List<MetricsAggregationRuleDO> editMetricsAggregationRuleDOList,
                                                 List<String> deleteMetricsAggregationRuleIdList,
                                                 List<AggregationPercentileItemDO> addAggregationPercentileItemDOList,
                                                 List<AggregationPercentileItemDO> editAggregationPercentileItemDOList,
                                                 List<String> deleteAggregationPercentileItemIdList,
                                                 List<MetricsFieldDO> addMetricsFieldDOList,
                                                 List<MetricsFieldDO> editMetricsFieldDOList,
                                                 List<String> deleteMetricsFieldIdList,
                                                 MetricsAggregationRuleComposeInput metricsAggregationRuleComposeInput,
                                                 List<AggregationPercentileRuleOutput> aggregationPercentileRuleOutputList,
                                                 Set<String> originalFieldDeamon,
                                                 Set<String> toDeleteOriginalField) {

        //d.1 handle delete rule
        List<String> inputPercentileIdList = metricsAggregationRuleComposeInput.getPercentile().stream().map(AggregationPercentileRuleInput::getId).collect(Collectors.toList());
        aggregationPercentileRuleOutputList.stream().filter(e -> !inputPercentileIdList.contains(e.getId())).forEach(deleteRule -> {
            toDeleteOriginalField.add(deleteRule.getAggField());
            //delete percentile rule
            deleteMetricsAggregationRuleIdList.add(deleteRule.getId());
            //delete percentile item
            List<AggregationPercentileItemOutput> deletePercentileValues = deleteRule.getPercentileValues();
            deletePercentileValues.forEach(r -> {
                deleteAggregationPercentileItemIdList.add(r.getId());
                //delete percentile item field
                String fieldName = deleteRule.getAggField() + CommonSplitConstants.METRICS_FIELD_SPLIT + CommonSplitConstants.P + r.getValue();
                String deleteFieldId = metricsFieldDOList.stream().filter(f -> f.getFieldName().equals(fieldName)).findFirst().get().getId();
                deleteMetricsFieldIdList.add(deleteFieldId);
            });
        });

        metricsAggregationRuleComposeInput.getPercentile().forEach(e -> {

            //add original field for aggregation metrics, histogram and percentile should all are number type
            //remove temporarily, waiting for next release
            /*String originalFieldName = e.getAggField();
            originalFieldDeamon.add(originalFieldName);
            boolean oldOriginalExisted = metricsFieldDOList.stream().anyMatch(mf -> mf.getFieldName().equals(originalFieldName));
            if(!oldOriginalExisted && !addMetricsFieldDOList.stream().anyMatch(mf -> mf.getFieldName().contains(originalFieldName))) {
                MetricsFieldDO originalFieldDO = new MetricsFieldDO();
                originalFieldDO.setId(IdUtils.generateId());
                originalFieldDO.setMetricsId(metricsDO.getId());
                originalFieldDO.setFieldType(MetricsFieldTypeEnum.number.getValue());
                originalFieldDO.setFieldName(originalFieldName);
                addMetricsFieldDOList.add(originalFieldDO);
            }*/

            boolean add = StringUtils.isEmpty(e.getId());
            //d.2 handle add rule
            if (add) {
                MetricsAggregationRuleDO metricsAggregationRuleDO = new MetricsAggregationRuleDO();
                metricsAggregationRuleDO.setId(IdUtils.generateId());
                metricsAggregationRuleDO.setAggId(metricsAggregationDO.getId());
                metricsAggregationRuleDO.setAggField(e.getAggField());
                metricsAggregationRuleDO.setAggTypes("");
                metricsAggregationRuleDO.setFilterCondition(e.getFilterCondition() == null ? "" : e.getFilterCondition());
                metricsAggregationRuleDO.setAggRuleType(AggregationRuleType.percentile.value());
                metricsAggregationRuleDO.setIsConditioned(ConditionedStatus.no.value());
                metricsAggregationRuleDO.setConditionalFieldPrefix("");
                metricsAggregationRuleDO.setVersion(AggregationRuleVersion.v1.value());
                addMetricsAggregationRuleDOList.add(metricsAggregationRuleDO);

                e.getPercentileValues().forEach(t -> {
                    AggregationPercentileItemDO aggregationPercentileItemDO = new AggregationPercentileItemDO();
                    aggregationPercentileItemDO.setId(IdUtils.generateId());
                    aggregationPercentileItemDO.setMetricsAggregationRuleId(metricsAggregationRuleDO.getId());
                    aggregationPercentileItemDO.setPercentileValue(t.getValue());

                    MetricsFieldDO metricsFieldDO = new MetricsFieldDO();
                    metricsFieldDO.setId(IdUtils.generateId());
                    metricsFieldDO.setMetricsId(metricsDO.getId());
                    metricsFieldDO.setFieldType(MetricsFieldTypeEnum.number.getValue());
                    String fieldName = metricsAggregationRuleDO.getAggField() + CommonSplitConstants.METRICS_FIELD_SPLIT + CommonSplitConstants.P + t.getValue();
                    metricsFieldDO.setFieldName(fieldName);
                    addMetricsFieldDOList.add(metricsFieldDO);
                    aggregationPercentileItemDO.setTargetFieldId(metricsFieldDO.getId());
                    addAggregationPercentileItemDOList.add(aggregationPercentileItemDO);
                });
            } else {
                //d.3 handle edit rule
                AggregationPercentileRuleInput editRule = e;
                MetricsAggregationRuleDO oldRuleDO = ruleDOList.stream().filter(r -> r.getId().equals(e.getId())).findFirst().get();
                oldRuleDO.setFilterCondition(oldRuleDO.getFilterCondition() == null ? "" : oldRuleDO.getFilterCondition());
                MetricsAggregationRuleDO metricsAggregationRuleDO = new MetricsAggregationRuleDO();
                BeanUtils.copyProperties(oldRuleDO, metricsAggregationRuleDO);
                BeanUtils.copyProperties(editRule, metricsAggregationRuleDO);
                metricsAggregationRuleDO.setFilterCondition(metricsAggregationRuleDO.getFilterCondition() == null ? "" : metricsAggregationRuleDO.getFilterCondition());

                List<AggregationPercentileItemInput> editPercentileValues = editRule.getPercentileValues();

                // handle add percentile item
                List<AggregationPercentileItemInput> addPercentileValues = editPercentileValues.stream().filter(r -> StringUtils.isEmpty(r.getId())).collect(Collectors.toList());
                addPercentileValues.forEach(t -> {
                    AggregationPercentileItemDO aggregationPercentileItemDO = new AggregationPercentileItemDO();
                    aggregationPercentileItemDO.setId(IdUtils.generateId());
                    aggregationPercentileItemDO.setMetricsAggregationRuleId(editRule.getId());
                    aggregationPercentileItemDO.setPercentileValue(t.getValue());

                    MetricsFieldDO metricsFieldDO = new MetricsFieldDO();
                    metricsFieldDO.setId(IdUtils.generateId());
                    metricsFieldDO.setMetricsId(metricsDO.getId());
                    metricsFieldDO.setFieldType(MetricsFieldTypeEnum.number.getValue());
                    String fieldName = metricsAggregationRuleDO.getAggField() + CommonSplitConstants.METRICS_FIELD_SPLIT + CommonSplitConstants.P + t.getValue();
                    metricsFieldDO.setFieldName(fieldName);
                    addMetricsFieldDOList.add(metricsFieldDO);

                    aggregationPercentileItemDO.setTargetFieldId(metricsFieldDO.getId());
                    addAggregationPercentileItemDOList.add(aggregationPercentileItemDO);
                });

                AggregationPercentileRuleOutput oldRule = aggregationPercentileRuleOutputList.stream().filter(t -> editRule.getId().equals(t.getId())).findFirst().get();
                List<AggregationPercentileItemOutput> oldPercentileValues = oldRule.getPercentileValues();

                // handle delete percentile values
                List<AggregationPercentileItemOutput> deletePercentileValues = oldPercentileValues.stream().filter(r -> {
                    return !editPercentileValues.stream().anyMatch(t -> r.getId().equals(t.getId()));
                }).collect(Collectors.toList());
                deletePercentileValues.forEach(r -> {
                    deleteAggregationPercentileItemIdList.add(r.getId());
                    String fieldName = metricsAggregationRuleDO.getAggField() + CommonSplitConstants.METRICS_FIELD_SPLIT + CommonSplitConstants.P + r.getValue();
                    String deleteFieldId = metricsFieldDOList.stream().filter(f -> f.getFieldName().equals(fieldName)).findFirst().get().getId();
                    deleteMetricsFieldIdList.add(deleteFieldId);
                });

                // handle edit percentile values
                editPercentileValues.stream().filter(editPercentileValue -> !StringUtils.isEmpty(editPercentileValue.getId())).forEach(editPercentileValue -> {
                    AggregationPercentileItemOutput oldPercentileValue = oldPercentileValues.stream().filter(t -> t.getId().equals(editPercentileValue.getId())).findFirst().get();
                    boolean needEditPercentileValueItem = metricsValidator.isPercentileValueChanged(editPercentileValue, oldPercentileValue);
                    boolean needEditField = metricsValidator.isPercentileFieldChanged(editPercentileValue, oldPercentileValue);
                    String oldFieldName = metricsAggregationRuleDO.getAggField() + CommonSplitConstants.METRICS_FIELD_SPLIT + CommonSplitConstants.P + oldPercentileValue.getValue();
                    MetricsFieldDO metricsFieldDO = metricsFieldDOList.stream().filter(f -> f.getFieldName().equals(oldFieldName)).findFirst().get();
                    if (needEditPercentileValueItem) {
                        AggregationPercentileItemDO aggregationPercentileItemDO = new AggregationPercentileItemDO();
                        BeanUtils.copyProperties(editPercentileValue, aggregationPercentileItemDO);
                        aggregationPercentileItemDO.setPercentileValue(editPercentileValue.getValue());
                        aggregationPercentileItemDO.setMetricsAggregationRuleId(editRule.getId());
                        aggregationPercentileItemDO.setTargetFieldId(metricsFieldDO.getId());
                        editAggregationPercentileItemDOList.add(aggregationPercentileItemDO);
                    }
                    if (needEditField) {
                        String editFieldName = metricsAggregationRuleDO.getAggField() + CommonSplitConstants.METRICS_FIELD_SPLIT + CommonSplitConstants.P + editPercentileValue.getValue();
                        metricsFieldDO.setFieldName(editFieldName);
                        editMetricsFieldDOList.add(metricsFieldDO);
                    }
                });
                if (!metricsAggregationRuleDO.equals(oldRuleDO)) {
                    editMetricsAggregationRuleDOList.add(metricsAggregationRuleDO);
                }
            }
        });
    }

    private void handleEditMetricsFunctionRule(MetricsDO metricsDO, MetricsAggregationDO metricsAggregationDO,
                                               List<MetricsAggregationRuleDO> ruleDOList,
                                               List<MetricsFieldDO> metricsFieldDOList,
                                               List<MetricsAggregationRuleDO> addMetricsAggregationRuleDOList,
                                               List<MetricsAggregationRuleDO> editMetricsAggregationRuleDOList,
                                               List<String> deleteMetricsAggregationRuleIdList,
                                               List<AggregationFunctionItemDO> addAggregationFunctionItemDOList,
                                               List<AggregationFunctionItemDO> editAggregationFunctionItemDOList,
                                               List<String> deleteAggregationFunctionItemIdList,
                                               List<MetricsFieldDO> addMetricsFieldDOList,
                                               List<MetricsFieldDO> editMetricsFieldDOList,
                                               List<String> deleteMetricsFieldIdList,
                                               MetricsAggregationRuleComposeInput metricsAggregationRuleComposeInput,
                                               List<AggregationFunctionRuleOutput> aggregationFunctionRuleOutputList,
                                               Set<String> originalFieldDeamon,
                                               Set<String> toDeleteOriginalField) {

        //handle delete rule
        List<String> inputFunctionIdList = metricsAggregationRuleComposeInput.getCommon().stream().map(AggregationFunctionRuleInput::getId).collect(Collectors.toList());
        aggregationFunctionRuleOutputList.stream().filter(e -> !inputFunctionIdList.contains(e.getId())).forEach(deleteRule -> {
            toDeleteOriginalField.add(deleteRule.getFieldPrefix());
            //delete rule with common function
            deleteMetricsAggregationRuleIdList.add(deleteRule.getId());
            //delete histogram item
            List<AggregationFunctionItemOutput> deleteRuleAggTypes = deleteRule.getAggTypes();
            deleteRuleAggTypes.forEach(r -> {
                deleteAggregationFunctionItemIdList.add(r.getId());
                //delete percentile item field
                String oldFieldName = r.getFieldName();
                String deleteFieldId = metricsFieldDOList.stream().filter(f -> f.getFieldName().equals(oldFieldName)).findFirst().get().getId();
                deleteMetricsFieldIdList.add(deleteFieldId);
            });
        });

        metricsAggregationRuleComposeInput.getCommon().stream().forEach(e -> {

            //add original field for aggregation metrics
            //remove temporarily, waiting for next release
            /*String originalFieldName = e.getFieldPrefix();
            originalFieldDeamon.add(originalFieldName);
            boolean oldOriginalExisted = metricsFieldDOList.stream().anyMatch(mf -> mf.getFieldName().equals(originalFieldName));
            if(!oldOriginalExisted && !addMetricsFieldDOList.stream().anyMatch(mf -> mf.getFieldName().contains(originalFieldName))) {
                MetricsFieldDO originalFieldDO = new MetricsFieldDO();
                originalFieldDO.setId(IdUtils.generateId());
                originalFieldDO.setMetricsId(metricsDO.getId());
                originalFieldDO.setFieldType(MetricsFieldTypeEnum.fromName(e.getFieldType()).getValue());
                originalFieldDO.setFieldName(originalFieldName);
                addMetricsFieldDOList.add(originalFieldDO);
            }*/

            boolean add = StringUtils.isEmpty(e.getId());
            if (add) {
                // all the added function's version = 1
                MetricsAggregationRuleDO metricsAggregationRuleDO = new MetricsAggregationRuleDO();
                metricsAggregationRuleDO.setId(IdUtils.generateId());
                metricsAggregationRuleDO.setAggId(metricsAggregationDO.getId());
                metricsAggregationRuleDO.setAggField(e.getAggField());
                metricsAggregationRuleDO.setAggTypes("");
                metricsAggregationRuleDO.setFilterCondition(e.getFilterCondition() == null ? "" : e.getFilterCondition());
                metricsAggregationRuleDO.setAggRuleType(AggregationRuleType.function.value());

                metricsAggregationRuleDO.setIsConditioned(e.getIsConditioned());
                metricsAggregationRuleDO.setConditionalFieldPrefix(e.getConditionalFieldPrefix() == null ? "" : e.getConditionalFieldPrefix());

                metricsAggregationRuleDO.setVersion(AggregationRuleVersion.v1.value());
                addMetricsAggregationRuleDOList.add(metricsAggregationRuleDO);
                e.getAggTypes().forEach(t -> {
                    AggregationFunctionItemDO aggregationFunctionItemDO = new AggregationFunctionItemDO();
                    aggregationFunctionItemDO.setId(IdUtils.generateId());
                    aggregationFunctionItemDO.setMetricsAggregationRuleId(metricsAggregationRuleDO.getId());
                    aggregationFunctionItemDO.setAggType(t.getValue());
                    aggregationFunctionItemDO.setAggCondition(t.getCondition());

                    MetricsFieldDO metricsFieldDO = new MetricsFieldDO();
                    metricsFieldDO.setId(IdUtils.generateId());
                    metricsFieldDO.setMetricsId(metricsDO.getId());

                    String collectorFieldType = e.getFieldType(); //eg. string or number
                    String aggFuntion = aggregationFunctionItemDO.getAggType(); //eg. sample
                    metricsFieldDO.setFieldType(AggregationOperatorEnum.getMetricFieldTypeByAggOpr(aggFuntion, collectorFieldType).getValue()); //eg. ip.sample

                    String fieldPrefix = metricsAggregationRuleDO.getFieldPrefix();
                    metricsFieldDO.setFieldName(fieldPrefix + CommonSplitConstants.METRICS_FIELD_SPLIT + t.getValue());
                    addMetricsFieldDOList.add(metricsFieldDO);

                    aggregationFunctionItemDO.setTargetFieldId(metricsFieldDO.getId());
                    addAggregationFunctionItemDOList.add(aggregationFunctionItemDO);
                });
            } else {
                AggregationFunctionRuleInput editRule = e;
                MetricsAggregationRuleDO oldRuleDO = ruleDOList.stream().filter(r -> r.getId().equals(e.getId())).findFirst().get();

                oldRuleDO.setFilterCondition(oldRuleDO.getFilterCondition() == null ? "" : oldRuleDO.getFilterCondition());
                AggregationFunctionRuleOutput oldRule = aggregationFunctionRuleOutputList.stream().filter(t -> editRule.getId().equals(t.getId())).findFirst().get();

                MetricsAggregationRuleDO metricsAggregationRuleDO = new MetricsAggregationRuleDO();
                BeanUtils.copyProperties(oldRuleDO, metricsAggregationRuleDO);
                BeanUtils.copyProperties(editRule, metricsAggregationRuleDO);
                metricsAggregationRuleDO.setFilterCondition(metricsAggregationRuleDO.getFilterCondition() == null ? "" : metricsAggregationRuleDO.getFilterCondition());

                List<AggregationFunctionItemOutput> oldFunctionItems = oldRule.getAggTypes();
                List<AggregationFunctionItemInput> editFunctionItems = editRule.getAggTypes();

                // handle add item
                List<AggregationFunctionItemInput> addFunctionItems = editFunctionItems.stream().filter(r -> StringUtils.isEmpty(r.getId())).collect(Collectors.toList());
                addFunctionItems.forEach(t -> {
                    AggregationFunctionItemDO aggregationFunctionItemDO = new AggregationFunctionItemDO();
                    aggregationFunctionItemDO.setId(IdUtils.generateId());
                    aggregationFunctionItemDO.setMetricsAggregationRuleId(editRule.getId());
                    aggregationFunctionItemDO.setAggCondition(t.getCondition());
                    aggregationFunctionItemDO.setAggType(t.getValue());

                    MetricsFieldDO metricsFieldDO = new MetricsFieldDO();
                    metricsFieldDO.setId(IdUtils.generateId());
                    metricsFieldDO.setMetricsId(metricsDO.getId());

                    String collectorFieldType = e.getFieldType(); //eg. string or number
                    String aggFuntion = aggregationFunctionItemDO.getAggType(); //eg. sample
                    metricsFieldDO.setFieldType(AggregationOperatorEnum.getMetricFieldTypeByAggOpr(aggFuntion, collectorFieldType).getValue()); //eg. ip.sample

                    String fieldPrefix = metricsAggregationRuleDO.getFieldPrefix();
                    metricsFieldDO.setFieldName(fieldPrefix + CommonSplitConstants.METRICS_FIELD_SPLIT + t.getValue());
                    addMetricsFieldDOList.add(metricsFieldDO);
                    aggregationFunctionItemDO.setTargetFieldId(metricsFieldDO.getId());
                    addAggregationFunctionItemDOList.add(aggregationFunctionItemDO);
                });

                // handle delete items
                List<AggregationFunctionItemOutput> deleteFunctionItems = oldFunctionItems.stream().filter(r -> {
                    return !editFunctionItems.stream().anyMatch(t -> r.getId().equals(t.getId()));
                }).collect(Collectors.toList());
                deleteFunctionItems.forEach(r -> {
                    deleteAggregationFunctionItemIdList.add(r.getId());
                    String deleteFieldId = metricsFieldDOList.stream().filter(f -> f.getFieldName().equals(r.getFieldName())).findFirst().get().getId();
                    deleteMetricsFieldIdList.add(deleteFieldId);
                });

                // handle edit items
                editFunctionItems.stream().filter(editFunctionItem -> !StringUtils.isEmpty(editFunctionItem.getId())).forEach(editFunctionItem -> {
                    AggregationFunctionItemOutput oldFunctionItem = oldFunctionItems.stream().filter(t -> t.getId().equals(editFunctionItem.getId())).findFirst().get();
                    boolean needEditFunctionItem = metricsValidator.isFunctionItemTypeChanged(editFunctionItem,oldFunctionItem) || metricsValidator.isFunctionItemConditionChanged(editFunctionItem,oldFunctionItem);
                    boolean needEditField = metricsValidator.isFunctionItemTypeChanged(editFunctionItem,oldFunctionItem);
                    MetricsFieldDO metricsFieldDO = metricsFieldDOList.stream().filter(f -> f.getFieldName().equals(oldFunctionItem.getFieldName())).findFirst().get();
                    if(needEditFunctionItem){
                        AggregationFunctionItemDO aggregationFunctionItemDO = new AggregationFunctionItemDO();
                        BeanUtils.copyProperties(editFunctionItem, aggregationFunctionItemDO);
                        aggregationFunctionItemDO.setAggCondition(editFunctionItem.getCondition());
                        aggregationFunctionItemDO.setAggType(editFunctionItem.getValue());
                        aggregationFunctionItemDO.setMetricsAggregationRuleId(editRule.getId());
                        aggregationFunctionItemDO.setTargetFieldId(metricsFieldDO.getId());
                        editAggregationFunctionItemDOList.add(aggregationFunctionItemDO);
                    }
                    if(needEditField){
                        String editFieldName = metricsAggregationRuleDO.getAggField() + CommonSplitConstants.METRICS_FIELD_SPLIT +  editFunctionItem.getValue();
                        metricsFieldDO.setFieldName(editFieldName);
                        editMetricsFieldDOList.add(metricsFieldDO);
                    }
                });
                if(!metricsAggregationRuleDO.equals(oldRuleDO)) {
                    editMetricsAggregationRuleDOList.add(metricsAggregationRuleDO);
                }
            }

        });
    }

    private void handleEditMetricsHistogramRule(MetricsDO metricsDO, MetricsAggregationDO metricsAggregationDO,
                                                List<MetricsAggregationRuleDO> ruleDOList,
                                                List<MetricsFieldDO> metricsFieldDOList,
                                                List<MetricsAggregationRuleDO> addMetricsAggregationRuleDOList,
                                                List<MetricsAggregationRuleDO> editMetricsAggregationRuleDOList,
                                                List<String> deleteMetricsAggregationRuleIdList,
                                                List<AggregationHistogramRangeItemDO> addAggregationHistogramRangeItemDOList,
                                                List<AggregationHistogramRangeItemDO> editAggregationHistogramRangeItemDOList,
                                                List<String> deleteAggregationHistogramRangeItemIdList,
                                                List<MetricsFieldDO> addMetricsFieldDOList,
                                                List<MetricsFieldDO> editMetricsFieldDOList,
                                                List<String> deleteMetricsFieldIdList,
                                                MetricsAggregationRuleComposeInput metricsAggregationRuleComposeInput,
                                                List<AggregationHistogramRuleOutput> aggregationHistogramRuleOutputList,
                                                Set<String> originalFieldDeamon,
                                                Set<String> toDeleteOriginalField) {

        //c.1
        List<String> inputHistogramIdList = metricsAggregationRuleComposeInput.getHistogram().stream().map(AggregationHistogramRuleInput::getId).collect(Collectors.toList());
        aggregationHistogramRuleOutputList.stream().filter(e -> !inputHistogramIdList.contains(e.getId())).forEach(deleteRule -> {
            toDeleteOriginalField.add(deleteRule.getAggField());
            //delete histogram rule
            deleteMetricsAggregationRuleIdList.add(deleteRule.getId());
            //delete histogram item
            List<AggregationHistogramRangeItemOutput> deleteRanges = deleteRule.getRanges();
            deleteRanges.forEach(r -> {
                deleteAggregationHistogramRangeItemIdList.add(r.getId());
                //delete percentile item field
                String deleteFieldId = metricsFieldDOList.stream().filter(f -> f.getFieldName().equals(r.getResultLabel())).findFirst().get().getId();
                deleteMetricsFieldIdList.add(deleteFieldId);
            });
        });

        metricsAggregationRuleComposeInput.getHistogram().forEach(e -> {

            //add original field for aggregation metrics, histogram and percentile should all are number type
            //remove temporarily, waiting for next release
            /*String originalFieldName = e.getAggField();
            originalFieldDeamon.add(originalFieldName);
            boolean oldOriginalExisted = metricsFieldDOList.stream().anyMatch(mf -> mf.getFieldName().equals(originalFieldName));
            if(!oldOriginalExisted && !addMetricsFieldDOList.stream().anyMatch(mf -> mf.getFieldName().contains(originalFieldName))) {
                MetricsFieldDO originalFieldDO = new MetricsFieldDO();
                originalFieldDO.setId(IdUtils.generateId());
                originalFieldDO.setMetricsId(metricsDO.getId());
                originalFieldDO.setFieldType(MetricsFieldTypeEnum.number.getValue());
                originalFieldDO.setFieldName(originalFieldName);
                addMetricsFieldDOList.add(originalFieldDO);
            }*/

            boolean add = StringUtils.isEmpty(e.getId());
            if (add) {
                //c.2
                MetricsAggregationRuleDO metricsAggregationRuleDO = new MetricsAggregationRuleDO();
                metricsAggregationRuleDO.setId(IdUtils.generateId());
                metricsAggregationRuleDO.setAggId(metricsAggregationDO.getId());
                metricsAggregationRuleDO.setAggField(e.getAggField());
                metricsAggregationRuleDO.setAggTypes("");
                metricsAggregationRuleDO.setFilterCondition(e.getFilterCondition() == null ? "" : e.getFilterCondition());
                metricsAggregationRuleDO.setAggRuleType(AggregationRuleType.histogram.value());
                metricsAggregationRuleDO.setIsConditioned(ConditionedStatus.no.value());
                metricsAggregationRuleDO.setConditionalFieldPrefix("");
                metricsAggregationRuleDO.setVersion(AggregationRuleVersion.v1.value());
                addMetricsAggregationRuleDOList.add(metricsAggregationRuleDO);

                e.getRanges().forEach(t -> {
                    AggregationHistogramRangeItemDO aggregationHistogramRangeItemDO = new AggregationHistogramRangeItemDO();
                    aggregationHistogramRangeItemDO.setId(IdUtils.generateId());
                    aggregationHistogramRangeItemDO.setMetricsAggregationRuleId(metricsAggregationRuleDO.getId());
                    aggregationHistogramRangeItemDO.setLowerLimit(t.getLowerLimit());
                    aggregationHistogramRangeItemDO.setUpperLimit(t.getUpperLimit());
                    aggregationHistogramRangeItemDO.setLowerIntervalNotation(t.getLowerIntervalNotation());
                    aggregationHistogramRangeItemDO.setUpperIntervalNotation(t.getUpperIntervalNotation());

                    MetricsFieldDO metricsFieldDO = new MetricsFieldDO();
                    metricsFieldDO.setId(IdUtils.generateId());
                    metricsFieldDO.setMetricsId(metricsDO.getId());
                    metricsFieldDO.setFieldType(MetricsFieldTypeEnum.number.getValue());
                    metricsFieldDO.setFieldName(t.getResultLabel());
                    addMetricsFieldDOList.add(metricsFieldDO);

                    aggregationHistogramRangeItemDO.setTargetFieldId(metricsFieldDO.getId());
                    addAggregationHistogramRangeItemDOList.add(aggregationHistogramRangeItemDO);
                });

            } else {
                //c.3
                AggregationHistogramRuleInput editRule = e;
                MetricsAggregationRuleDO oldRuleDO = ruleDOList.stream().filter(r -> r.getId().equals(e.getId())).findFirst().get();
                oldRuleDO.setFilterCondition(oldRuleDO.getFilterCondition() == null ? "" : oldRuleDO.getFilterCondition());
                MetricsAggregationRuleDO metricsAggregationRuleDO = new MetricsAggregationRuleDO();
                BeanUtils.copyProperties(oldRuleDO, metricsAggregationRuleDO);
                BeanUtils.copyProperties(editRule, metricsAggregationRuleDO);
                metricsAggregationRuleDO.setFilterCondition(metricsAggregationRuleDO.getFilterCondition() == null ? "" : metricsAggregationRuleDO.getFilterCondition());

                List<AggregationHistogramRangeItemInput> editRanges = editRule.getRanges();

                // handle add range
                List<AggregationHistogramRangeItemInput> addRanges = editRanges.stream().filter(r -> StringUtils.isEmpty(r.getId())).collect(Collectors.toList());
                addRanges.forEach(t -> {
                    AggregationHistogramRangeItemDO aggregationHistogramRangeItemDO = new AggregationHistogramRangeItemDO();
                    aggregationHistogramRangeItemDO.setId(IdUtils.generateId());
                    aggregationHistogramRangeItemDO.setMetricsAggregationRuleId(editRule.getId());
                    aggregationHistogramRangeItemDO.setLowerLimit(t.getLowerLimit());
                    aggregationHistogramRangeItemDO.setUpperLimit(t.getUpperLimit());
                    aggregationHistogramRangeItemDO.setLowerIntervalNotation(t.getLowerIntervalNotation());
                    aggregationHistogramRangeItemDO.setUpperIntervalNotation(t.getUpperIntervalNotation());

                    MetricsFieldDO metricsFieldDO = new MetricsFieldDO();
                    metricsFieldDO.setId(IdUtils.generateId());
                    metricsFieldDO.setMetricsId(metricsDO.getId());
                    metricsFieldDO.setFieldType(MetricsFieldTypeEnum.number.getValue());
                    metricsFieldDO.setFieldName(t.getResultLabel());
                    addMetricsFieldDOList.add(metricsFieldDO);

                    aggregationHistogramRangeItemDO.setTargetFieldId(metricsFieldDO.getId());
                    addAggregationHistogramRangeItemDOList.add(aggregationHistogramRangeItemDO);
                });

                AggregationHistogramRuleOutput oldRule = aggregationHistogramRuleOutputList.stream().filter(t -> editRule.getId().equals(t.getId())).findFirst().get();
                List<AggregationHistogramRangeItemOutput> oldRanges = oldRule.getRanges();

                // handle delete range
                List<AggregationHistogramRangeItemOutput> deleteRanges = oldRanges.stream().filter(r -> {
                    return !editRanges.stream().anyMatch(t -> r.getId().equals(t.getId()));
                }).collect(Collectors.toList());
                deleteRanges.forEach(r -> {
                    deleteAggregationHistogramRangeItemIdList.add(r.getId());
                    String deleteFieldId = metricsFieldDOList.stream().filter(f -> f.getFieldName().equals(r.getResultLabel())).findFirst().get().getId();
                    deleteMetricsFieldIdList.add(deleteFieldId);
                });

                // handle edit range
                editRanges.stream().filter(editRange -> !StringUtils.isEmpty(editRange.getId())).forEach(editRange -> {
                    AggregationHistogramRangeItemOutput oldRange = oldRanges.stream().filter(t -> t.getId().equals(editRange.getId())).findFirst().get();
                    boolean needEditRangeItem = metricsValidator.isRangeChanged(editRange, oldRange);
                    boolean needEditField = metricsValidator.isResultLabelChanged(editRange, oldRange);
                    MetricsFieldDO metricsFieldDO = metricsFieldDOList.stream().filter(f -> f.getFieldName().equals(oldRange.getResultLabel())).findFirst().get();
                    if (needEditRangeItem) {
                        AggregationHistogramRangeItemDO aggregationHistogramRangeItemDO = new AggregationHistogramRangeItemDO();
                        BeanUtils.copyProperties(editRange, aggregationHistogramRangeItemDO);
                        aggregationHistogramRangeItemDO.setMetricsAggregationRuleId(editRule.getId());
                        aggregationHistogramRangeItemDO.setTargetFieldId(metricsFieldDO.getId());
                        editAggregationHistogramRangeItemDOList.add(aggregationHistogramRangeItemDO);
                    }
                    if (needEditField) {
                        metricsFieldDO.setFieldName(editRange.getResultLabel());
                        editMetricsFieldDOList.add(metricsFieldDO);
                    }
                });
                if (!metricsAggregationRuleDO.equals(oldRuleDO)) {
                    editMetricsAggregationRuleDOList.add(metricsAggregationRuleDO);
                }

            }
        });
    }


    private void adjustMetricsAggregation(MetricsAggregationInput metricsAggregationInput) {
        if (StringUtils.isBlank(metricsAggregationInput.getFilterCondition())) {
            metricsAggregationInput.setFilterCondition(StringUtils.EMPTY);
        }
        if (StringUtils.isBlank(metricsAggregationInput.getAggregationResultFilter())) {
            metricsAggregationInput.setAggregationResultFilter(StringUtils.EMPTY);
        }
    }

    public ResponseObject getSimpleAggregationByMetricsName(MetricsNameQuery metricsNameQuery){
        metricsNameQuery.validate();
        String userId = metricsNameQuery.getUserId();
        String tenantId = metricsNameQuery.getTenantId();
        String metricsName = metricsNameQuery.getName();
//        authService.mustTenantAdminOrAdmin(userId,tenantId);
        authService.checkAuth(metricsNameQuery);
//        authService.checkHasMetricsThrowExpWhenNotHave(metricsInput,metricsInput.getId());
        MetricsDO metricsDO = metricsHandler.findMetricsByNameAndTenantId(metricsName, tenantId);
        Assert.isTrue(metricsDO != null, "the metrics is not exist");

        if(metricsDO.getType().intValue() != MetricsTypeEnum.AGGREGATION.getValue()){
            Assert.isTrue(false, "the metrics is not aggregate metrics");
        }
        MetricsAggregationDO metricsAggregationDO = metricsAggregationHandler.getByMetricsId(metricsDO.getId());
        Assert.isTrue(metricsAggregationDO != null, "the metricsAggregation attribute is not exist");
        SimpleMetricsAggregationOut simpleMetricsAggregationOut = SimpleMetricsAggregationOut.of(metricsDO, metricsAggregationDO);
        return ResponseObject.success(simpleMetricsAggregationOut);
    }

    public ResponseObject editSimpleMetricsAggregation(SimpleMetricsAggregationInput simpleMetricsAggregationInput){
        metricsValidator.checkSimpleMetricsAggregationInput(simpleMetricsAggregationInput);

        String userId = simpleMetricsAggregationInput.getUserId();
        String tenantId = simpleMetricsAggregationInput.getTenantId();
        String metricsName = simpleMetricsAggregationInput.getMetricsName();
//        authService.mustTenantAdminOrAdmin(userId,tenantId);
        authService.checkAuth(simpleMetricsAggregationInput);
        MetricsDO metricsDO = metricsHandler.findMetricsByNameAndTenantId(metricsName, tenantId);
        Assert.isTrue(metricsDO != null, "the metrics is not exist");

        if(metricsDO.getType().intValue() != MetricsTypeEnum.AGGREGATION.getValue()){
            Assert.isTrue(false, "the metrics is not aggregate metrics");
        }

        MetricsAggregationDO metricsAggregationDO = metricsAggregationHandler.getByMetricsId(metricsDO.getId());
        metricsAggregationDO.setAggPeriod(simpleMetricsAggregationInput.getAggPeriod());
        metricsAggregationDO.setSubPeriod(simpleMetricsAggregationInput.getSubPeriod());
        metricsAggregationDO.setFilterCondition(simpleMetricsAggregationInput.getFilterCondition());
        metricsAggregationHandler.updateMetricsAggregation(metricsAggregationDO);
        SimpleMetricsAggregationOut simpleMetricsAggregationOut = SimpleMetricsAggregationOut.of(metricsDO, metricsAggregationDO);
        return ResponseObject.success(simpleMetricsAggregationOut);
    }

    public ResponseObject getMetricsById(IdPara idPara) {
        return getMetricsById(idPara.getUserId(),idPara.getTenantId(),idPara.getAuthResourceUrl(),idPara.getId());
    }

    public ResponseObject getMetricsById(String userId,String tenantId,String apiPath ,String metricsId) {
            Assert.notNull(userId, "user id is null");
            authService.checkAuth(userId, tenantId, apiPath);
            return  ResponseObject.success(getMetricsById(metricsId));
        }

     MetricsOut getMetricsById(String metricsId) {
        try{
            Future<MetricsDO> metricsDOFuture = executor.submit(() -> metricsHandler.getMetricsById(metricsId));
            Future<List<MetricsFieldDO>> metricsFieldDOListFuture = executor.submit(() -> metricsHandler.listFieldsByMetricsIds(Arrays.asList(metricsId)));
            Future<MetricsAggregationDO> metricsAggregationDOFuture = executor.submit(() -> metricsAggregationHandler.getByMetricsId(metricsId));
            //big data，collectorId
            String collectorId = metricsHandler.getCollectorIdByMetricsId(metricsId);

            MetricsDO metricsDO = metricsDOFuture.get();
            MetricsOut metricsOut = new MetricsOut();
            BeanUtils.copyProperties(metricsDO, metricsOut);
            //add tag info
            List<TagInfoOut> tagInfoOuts = resourceTagService.listResourceTagOuts(metricsDO.getTenantId(), metricsId, ResourceTypeConstant.RESOURCE_METRIC_TYPE);
            metricsOut.setLabelInfoList(tagInfoOuts);
            List<String> tagNames = Lists.newArrayList(StringUtils.split(metricsDO.getTagNames(), COMMA_SPLIT));
            metricsOut.setTagNames(tagNames);

            try {
                Future<List<CollectorFieldDO>> collectorFieldDOFuture = executor.submit(() -> collectorFieldDAO.getFiledByCollectorId(collectorId));
                Map<String, String> filedDescriptionMap = collectorFieldDOFuture.get().stream()
                        .filter(field -> StringUtils.isNotBlank(field.getDescription()))
                        .collect(Collectors.toMap(
                                CollectorFieldDO::getSourceField,
                                dto -> Objects.requireNonNull(dto.getDescription())));
                List<MetricsOut.TagDetails> tagDetails = new ArrayList<>();
                if (!CollectionUtils.isEmpty(tagNames)) {
                    tagNames.forEach(tag -> {
                        MetricsOut.TagDetails details = new MetricsOut.TagDetails();
                        details.setTagName(tag);
                        if (filedDescriptionMap.containsKey(tag)) {
                            details.setDescription(filedDescriptionMap.get(tag));
                        }
                        tagDetails.add(details);
                    });
                }
                metricsOut.setTagDetails(tagDetails);
            } catch (Exception e) {
                log.error("get tag detail error", e);
            }

            List<MetricsFieldDO> metricsFieldDOList = metricsFieldDOListFuture.get();
            List<MetricsFieldOut> metricsFieldOutList = Instance.ofNullable(metricsFieldDOList).stream().map(metricsFieldDO -> {
                MetricsFieldOut metricsFieldOut = new MetricsFieldOut();
                BeanUtils.copyProperties(metricsFieldDO, metricsFieldOut);
                return metricsFieldOut;
            }).collect(Collectors.toList());
            metricsOut.setMetricsFieldList(metricsFieldOutList);

            if (metricsOut.getType() == MetricsTypeEnum.AGGREGATION.getValue()) {
                MetricsAggregationDO metricsAggregationDO = metricsAggregationDOFuture.get();
                MetricsAggregationOut metricsAggregationOut = new MetricsAggregationOut();
                MetricsAggregationRuleComposeOutput ruleComposeOutput = new MetricsAggregationRuleComposeOutput();
                metricsAggregationOut.setMetricsAggregationRuleCompose(ruleComposeOutput);
                BeanUtils.copyProperties(metricsAggregationDO, metricsAggregationOut);
                metricsOut.setMetricsAggregation(metricsAggregationOut);
                String aggId = metricsAggregationDO.getId();

                /**
                 * 1. get rules by aggId
                 * 2. get function, histogram, percentile item
                 * 3. compose ruleOutput
                 * 4. pay attention to the version and conditioned of function rule
                 */
                List<MetricsAggregationRuleDO> metricsAggregationRuleDOList = metricsAggregationRuleHandler.listRulesByAggIds(Lists.newArrayList(aggId));
                List<AggregationCustomFieldRuleDO> aggregationCustomFieldRuleDOList = aggregationCustomFieldRuleHandler.listRulesByAggIds(Lists.newArrayList(aggId));

                fillAggregationRule(ruleComposeOutput, metricsAggregationRuleDOList, aggregationCustomFieldRuleDOList, metricsFieldOutList, collectorId);

            }
            metricsOut.sortAggRules();
            metricsOut.setCollectorId(collectorId);

            //add batch aggregate flag
            Optional.ofNullable(metricsDO.getMetricsName())
                    .map(name -> batchCalculateMetricDAO.getBatchMetricByMetricName(name, metricsDO.getTenantId()))
                    .ifPresent(batch -> {
                        metricsOut.setHasBatchAggMetric(true);
                        metricsOut.setBatchAggMetricId(batch.getId());
                    });
            return metricsOut;
        } catch (InterruptedException | ExecutionException e) {
            log.error(ExceptionUtils.getTrace(e));
            throw new SiteException(InnerError.getCode(), InnerError.getErrMsg());
        }
    }

    private static final String CALC_PERIOD_SQL = """
              WITH
              time_intervals AS (
                SELECT
                  arrayDifference (arraySort (groupArray (time))) AS intervals
                FROM
                  "%s"."%s"
                WHERE
                  time >= subtractMinutes (now (), 725)
                GROUP BY %s
              )
            SELECT
              round(time_interval, -1) AS interval,
              count(*) AS interval_count
            FROM
              time_intervals ARRAY
              JOIN intervals AS time_interval
            GROUP BY
              round(time_interval, -1)
            ORDER BY
              interval_count DESC;
    """;

    private static final String INTERVAL_COUNT = "interval_count";

    private static final String INTERVAL = "interval";

    public ResponseObject calcPeriodByMetricId(IdPara idPara) {
        String metricId = Optional.ofNullable(idPara).map(IdPara::getId).orElse(null);
        Assert.notNull(metricId, "metric id is null");
        authService.checkAuth(idPara.getUserId(), idPara.getTenantId(), idPara.getAuthResourceUrl());
        return  ResponseObject.success(calcPeriod(metricId, idPara.getTenantId()));
    }

    public Integer calcPeriod(String metricId, String tenantId) {
        MetricsOut metricsOut = getMetricsById(metricId);
        Assert.notNull(metricsOut, "metrics is not exist");
        if (MetricsTypeEnum.AGGREGATION.getValue() == metricsOut.getType()) {
            return Optional.of(metricsOut).map(MetricsOut::getMetricsAggregation).map(MetricsAggregationOut::getAggPeriod).orElse(DEFAULT_PERIOD_UNIT);
        }
        int period = Objects.isNull(metricsOut.getOriginPeriod()) ? DEFAULT_PERIOD_UNIT : metricsOut.getOriginPeriod();
        String serviceName = Optional.ofNullable(tenantHandler.getTenantById(tenantId)).map(TenantDO::getName).orElse(null);
        String metricsName = ClickhouseSqlUtil.toClickhouseName(metricsOut.getMetricsName());
        if (StringUtils.isBlank(serviceName) || StringUtils.isBlank(metricsName)) {
            return period;
        }
        String findPeriodSql = String.format(CALC_PERIOD_SQL, ClickhouseSqlUtil.toClickhouseName(serviceName), metricsName, String.join(",", metricsOut.getTagNames()));
        List<Map<String, Object>> periodQueryResult = clickhouseHandlerFactory.get().query(serviceName, findPeriodSql, subEnvironmentHandler.getAlarmInsightEnv(serviceName));
        // find max interval value with count larger than 10.
        period = periodQueryResult.stream()
                .filter(row -> Long.parseLong(row.get(INTERVAL_COUNT).toString()) > 10L)
                .mapToInt(row -> Integer.parseInt(row.get(INTERVAL).toString()))
                .max()
                .orElse(period);
        return period;
    }

    private static final String OVERALL_SPARSITY = "overall_sparsity";

    private static final String CALC_SPARSITY_SQL_V2 = """
            WITH
                      time_intervals AS (
                        SELECT
                          arrayDifference (arraySort (groupArray (time))) AS intervals
                        FROM
                          "%s"."%s"
                        WHERE
                          time >= subtractMinutes (now (), 725)
                        GROUP BY
                          %s
                      ),
                      interval_counts AS (
                        SELECT
                          round(time_interval, -1) AS interva,
                          count(*) AS interval_count
                        FROM
                          time_intervals ARRAY
                          JOIN intervals AS time_interval
                        GROUP BY
                          round(time_interval, -1)
                      ),
                      max_interval AS (
                        SELECT
                          interva
                        FROM
                          interval_counts
                        WHERE
                          interva != 0
                        ORDER BY
                          interval_count DESC
                        LIMIT
                          1
                      ),
                      total_count AS (
                        SELECT
                          count(*) AS total_count
                        FROM
                          "%s"."%s"
                        WHERE
                          time >= subtractMinutes (now (), 725)
                      ),
                      group_count AS (
                        SELECT
                          count(DISTINCT (%s)) AS group_count
                        FROM
                          "%s"."%s"
                        WHERE
                          time >= subtractMinutes (now (), 725)
                          and time < subtractMinutes (now (), 5)
                      )
                    SELECT
                      max_interval.interva,
                      total_count.total_count,
                      group_count.group_count,
                      (
                        43200 / max_interval.interva * group_count.group_count - total_count.total_count
                      ) / (43200 / max_interval.interva * group_count.group_count) AS overall_sparsity
                    FROM
                      max_interval,
                      total_count,
                      group_count;
    """;
    private static final String CALC_SPARSITY_SQL = """
            WITH
                      total_counts AS (
                        SELECT
                          %s,
                          COUNT(*) AS total_count
                        FROM
                          "%s"."%s"
                        WHERE
                          time >= subtractMinutes (now (), 725)
                          AND time < subtractMinutes (now (), 5)
                        GROUP BY
                          %s
                      ),
                      time_intervals AS (
                        SELECT
                          %s,
                          arrayDifference(arraySort(groupArray(time))) AS intervals
                        FROM
                          "%s"."%s"
                        WHERE
                          time >= subtractMinutes (now (), 725)
                          AND time < subtractMinutes (now (), 5)
                        GROUP BY
                          %s
                      ),
                      flattened_intervals AS (
                        SELECT
                          %s,
                          interval
                        FROM
                          time_intervals
                        ARRAY JOIN intervals AS interval
                      ),
                      interval_counts AS (
                        SELECT
                          %s,
                          interval,
                          COUNT(*) AS interval_count
                        FROM
                          flattened_intervals
                        GROUP BY
                          %s,
                          interval
                      ),
                      ranked_intervals AS (
                        SELECT
                          %s,
                          interval,
                          interval_count,
                          ROW_NUMBER() OVER (PARTITION BY %s ORDER BY interval_count DESC) AS rank
                        FROM
                          interval_counts
                        WHERE interval != 0
                      ),
                      final_results AS (
                        SELECT
                          %s,
                          t.total_count,
                          r.interval AS most_frequent_interval,
                          toUInt64(720 * 60 / r.interval) AS expected_total_count,
                          (expected_total_count - t.total_count) / expected_total_count AS sparsity
                        FROM
                          total_counts t
                        JOIN
                          ranked_intervals r
                        ON
                          %s
                        WHERE
                          r.rank = 1
                      ),
                      overall_sparsity AS (
                        SELECT
                          SUM(sparsity * expected_total_count) / SUM(expected_total_count) AS overall_sparsity
                        FROM
                          final_results
                      )
                    SELECT
                      %s,
                      total_count,
                      most_frequent_interval,
                      expected_total_count,
                      sparsity,
                      (SELECT overall_sparsity FROM overall_sparsity) AS overall_sparsity
                    FROM
                      final_results;
    """;

    public Double calcSparsity(String metricId, String tenantId) {
        MetricsOut metricsOut = getMetricsById(metricId);
        Assert.notNull(metricsOut, "metrics is not exist");
        String serviceName = Optional.ofNullable(tenantHandler.getTenantById(tenantId)).map(TenantDO::getName).orElse(null);
        String clickhouseServiceName = ClickhouseSqlUtil.toClickhouseName(serviceName);
        String metricsName = ClickhouseSqlUtil.toClickhouseName(metricsOut.getMetricsName());
        String tagNames = String.join(",", metricsOut.getTagNames());
        String v1 = metricsOut.getTagNames().stream()
                .map(tagName -> "t." + tagName)
                .collect(Collectors.joining(","));

        String v2 = metricsOut.getTagNames().stream()
                .map(tagName -> "t." + tagName + " = r." + tagName)
                .collect(Collectors.joining(" AND "));

        if (StringUtils.isBlank(serviceName) || StringUtils.isBlank(metricsName)) {
            return null;
        }
        String sql = String.format(CALC_SPARSITY_SQL, tagNames, clickhouseServiceName, metricsName, tagNames, tagNames, clickhouseServiceName, metricsName, tagNames, tagNames, tagNames, tagNames, tagNames, tagNames, v1, v2, tagNames);
        List<Map<String, Object>> queryResult = clickhouseHandlerFactory.get().query(serviceName, sql, subEnvironmentHandler.getAlarmInsightEnv(serviceName));
        return Optional.ofNullable(queryResult).filter(list -> !list.isEmpty()).map(list -> list.get(0)).map(m -> m.get(OVERALL_SPARSITY)).map(Object::toString).map(Double::parseDouble).orElse(null);

    }

    public Double calcSparsityV2(String metricId, String tenantId) {
        MetricsOut metricsOut = getMetricsById(metricId);
        Assert.notNull(metricsOut, "metrics is not exist");
        String serviceName = Optional.ofNullable(tenantHandler.getTenantById(tenantId)).map(TenantDO::getName).orElse(null);
        String clickhouseServiceName = ClickhouseSqlUtil.toClickhouseName(serviceName);
        String metricsName = ClickhouseSqlUtil.toClickhouseName(metricsOut.getMetricsName());
        String tagNames = String.join(",", metricsOut.getTagNames());
        if (StringUtils.isBlank(serviceName) || StringUtils.isBlank(metricsName)) {
            return null;
        }
        String sql = String.format(CALC_SPARSITY_SQL_V2, clickhouseServiceName, metricsName, tagNames, clickhouseServiceName, metricsName, tagNames, clickhouseServiceName, metricsName);
        List<Map<String, Object>> queryResult = clickhouseHandlerFactory.get().query(serviceName, sql, subEnvironmentHandler.getAlarmInsightEnv(serviceName));
        return Optional.ofNullable(queryResult).filter(list -> !list.isEmpty()).map(list -> list.get(0)).map(m -> m.get(OVERALL_SPARSITY)).map(Object::toString).map(Double::parseDouble).orElse(null);

    }

    private static final String CALC_BASIC_DISTRIBUTION_SQL = """
            SELECT
              avg("%s") as avg,
              median ("%s") as median,
              min("%s") as min,
              max("%s") as max,
              count("%s") as count
            FROM
              "%s"."%s"
            WHERE
              time > subtractMinutes (now (), 43205)
              AND time < now ()
    """;

    private static final String AVG_FIELD = "avg";
    private static final String MEDIAN_FIELD = "median";
    private static final String MIN_FIELD = "min";
    private static final String MAX_FIELD = "max";
    private static final String COUNT_FIELD = "count";



    public ResponseObject getMetricDistribution(MetricDistributionInput metricDistributionInput) {
        String metricId = Optional.ofNullable(metricDistributionInput).map(MetricDistributionInput::getMetricId).orElse(null);
        String tenantId = Optional.ofNullable(metricDistributionInput).map(MetricDistributionInput::getTenantId).orElse(null);
        String fieldName = Optional.ofNullable(metricDistributionInput).map(MetricDistributionInput::getFieldName).orElse(null);
        if (StringUtils.isBlank(fieldName) || StringUtils.isBlank(metricId) || StringUtils.isBlank(tenantId)) {
            throw new IllegalArgumentException("getMetricDistribution input is invalid");
        }
//        authService.checkAuth(idPara.getUserId(), idPara.getTenantId(), idPara.getAuthResourceUrl());
        MetricDistributionOutput metricDistributionOutput = new MetricDistributionOutput();
        metricDistributionOutput.setMetricId(metricId);

        MetricsOut metricsOut = getMetricsById(metricId);
        Assert.notNull(metricsOut, "metrics is not exist");
        String serviceName = Optional.ofNullable(tenantHandler.getTenantById(tenantId)).map(TenantDO::getName).orElse(null);
        String metricsName = ClickhouseSqlUtil.toClickhouseName(metricsOut.getMetricsName());
        metricDistributionOutput.setMetricName(metricsName);

        if (StringUtils.isBlank(serviceName) || StringUtils.isBlank(metricsName)) {
            return ResponseObject.success(metricDistributionOutput);
        }

        //query basic distribution

        String sql = String.format(CALC_BASIC_DISTRIBUTION_SQL, fieldName, fieldName, fieldName, fieldName, fieldName, ClickhouseSqlUtil.toClickhouseName(serviceName), metricsName);
        List<Map<String, Object>> queryResult = clickhouseHandlerFactory.get().query(serviceName, sql, subEnvironmentHandler.getAlarmInsightEnv(serviceName));
        if (queryResult.isEmpty()) {
            return ResponseObject.success(metricDistributionOutput);
        }
        Map<String, Object> row = queryResult.get(0);
        Optional.of(row.get(AVG_FIELD)).map(Object::toString).map(Double::parseDouble).ifPresent(metricDistributionOutput::setAvg);
        Optional.of(row.get(MEDIAN_FIELD)).map(Object::toString).map(Double::parseDouble).ifPresent(metricDistributionOutput::setMid);
        Optional.of(row.get(MIN_FIELD)).map(Object::toString).map(Double::parseDouble).ifPresent(metricDistributionOutput::setMin);
        Optional.of(row.get(MAX_FIELD)).map(Object::toString).map(Double::parseDouble).ifPresent(metricDistributionOutput::setMax);
        Optional.of(row.get(COUNT_FIELD)).map(Object::toString).map(Long::parseLong).ifPresent(metricDistributionOutput::setCount);
        ;
        if (Objects.isNull(metricDistributionOutput.getCount()) || metricDistributionOutput.getCount() == 0) {
            return ResponseObject.success(metricDistributionOutput);
        }
        boolean newSparsityCalc = alarmParaService.getParamBooleanValue(AlarmParaService.AI_SPARSITY_CALC_FUNC_SWITCH, true);
        Double sparsity = newSparsityCalc ? calcSparsityV2(metricId, tenantId) : calcSparsity(metricId, tenantId);
        metricDistributionOutput.setSparsity(sparsity);

        double threshold = alarmParaService.getParamDoubleValue(AlarmParaService.AI_SPARSITY_THRESHOLD, 0.5D);
        metricDistributionOutput.setSparsityThreshold(threshold);

        return  ResponseObject.success(metricDistributionOutput);
    }

    private void fillAggregationRule(MetricsAggregationRuleComposeOutput ruleComposeOutput,
                                     List<MetricsAggregationRuleDO> metricsAggregationRuleDOList,
                                     List<AggregationCustomFieldRuleDO> aggregationCustomFieldRuleDOList,
                                     List<MetricsFieldOut> metricsFieldOutList,
                                     String collectorId
    ) throws ExecutionException, InterruptedException {
        List<String> ruleIds = metricsAggregationRuleDOList.stream().map(e -> e.getId()).collect(Collectors.toList());
        Future<List<AggregationFunctionItemDO>> aggregationFunctionItemDOListFuture = executor.submit(() -> metricsAggregationRuleHandler.listFunctionItemsByRuleIds(ruleIds));
        Future<List<AggregationHistogramRangeItemDO>> aggregationHistogramRangeItemDOListFuture = executor.submit(() -> metricsAggregationRuleHandler.listHistogramRangeItemsByRuleIds(ruleIds));
        Future<List<AggregationPercentileItemDO>> aggregationPercentileItemDOListFuture = executor.submit(() -> metricsAggregationRuleHandler.listPercentileItemsByRuleIds(ruleIds));
        Future<List<CollectorFieldDO>> collectorFieldsFuture = executor.submit(() -> collectorFieldDAO.listFieldByCollectorIds(Collections.singletonList(collectorId)));
        Map<String, String> collectorFieldAndTypeMap = Optional.ofNullable(collectorFieldsFuture.get()).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(CollectorFieldDO::getTargetField, CollectorFieldDO::getFieldType));

        List<AggregationFunctionItemDO> aggregationFunctionItemDOList = aggregationFunctionItemDOListFuture.get();
        List<AggregationHistogramRangeItemDO> aggregationHistogramRangeItemDOList = aggregationHistogramRangeItemDOListFuture.get();
        List<AggregationPercentileItemDO> aggregationPercentileItemDOList = aggregationPercentileItemDOListFuture.get();

        Map<String, List<AggregationFunctionItemDO>> functionItemMap = new HashMap<>();
        Map<String, List<AggregationHistogramRangeItemDO>> histogramRangeItemMap = new HashMap<>();
        Map<String, List<AggregationPercentileItemDO>> percentileItemMap = new HashMap<>();
        aggregationFunctionItemDOList.forEach(item -> {
            functionItemMap.computeIfAbsent(item.getMetricsAggregationRuleId(), e -> Lists.newArrayList()).add(item);
        });
        aggregationHistogramRangeItemDOList.forEach(item -> {
            histogramRangeItemMap.computeIfAbsent(item.getMetricsAggregationRuleId(), e -> Lists.newArrayList()).add(item);
        });
        aggregationPercentileItemDOList.forEach(item -> {
            percentileItemMap.computeIfAbsent(item.getMetricsAggregationRuleId(), e -> Lists.newArrayList()).add(item);
        });

        metricsAggregationRuleDOList.stream().forEach(rule -> {
            Integer version = rule.getVersion();
            Integer ruleType = rule.getAggRuleType();
            String ruleId = rule.getId();
            if (AggregationRuleType.function.value() == ruleType) {
                AggregationFunctionRuleOutput aggregationFunctionRuleOutput = new AggregationFunctionRuleOutput();
                BeanUtils.copyProperties(rule, aggregationFunctionRuleOutput);
                List<AggregationFunctionItemOutput> fOutList = new ArrayList<>();
                if (version == 0) {
                    fOutList = Arrays.asList(rule.getAggTypes().split(COMMA_SPLIT)).stream().map(e -> {
                        AggregationFunctionItemOutput item = new AggregationFunctionItemOutput();
                        item.setValue(e);
                        return item;
                    }).collect(Collectors.toList());
                } else if (version == 1) {
                    List<AggregationFunctionItemDO> fDOList = functionItemMap.getOrDefault(ruleId, Lists.newArrayList());
                    fOutList = fDOList.stream().map(e -> {
                        AggregationFunctionItemOutput item = new AggregationFunctionItemOutput();
                        BeanUtils.copyProperties(e, item);
                        item.setValue(e.getAggType());
                        item.setCondition(e.getAggCondition());
                        String fieldName = metricsFieldOutList.stream().filter(t -> t.getId().equals(e.getTargetFieldId())).findFirst().get().getFieldName();
                        item.setFieldName(fieldName);
                        return item;
                    }).collect(Collectors.toList());
                }
                aggregationFunctionRuleOutput.setAggTypes(fOutList);
                //aggregationFunctionRuleOutput set field type by aggField mapping with collector field
                aggregationFunctionRuleOutput.setFieldType(collectorFieldAndTypeMap.getOrDefault(rule.getAggField(), MetricsFieldTypeEnum.number.name()));
                ruleComposeOutput.getCommon().add(aggregationFunctionRuleOutput);
            } else if (AggregationRuleType.histogram.value() == ruleType) {
                AggregationHistogramRuleOutput aggregationHistogramRuleOutput = new AggregationHistogramRuleOutput();
                BeanUtils.copyProperties(rule, aggregationHistogramRuleOutput);
                List<AggregationHistogramRangeItemDO> hDOList = histogramRangeItemMap.getOrDefault(ruleId, Lists.newArrayList());
                List<AggregationHistogramRangeItemOutput> hOutList = hDOList.stream().map(e -> {
                    AggregationHistogramRangeItemOutput item = new AggregationHistogramRangeItemOutput();
                    BeanUtils.copyProperties(e, item);
                    String fieldName = metricsFieldOutList.stream().filter(t -> t.getId().equals(e.getTargetFieldId())).findFirst().get().getFieldName();
                    item.setResultLabel(fieldName);
                    item.setFieldName(fieldName);
                    return item;
                }).collect(Collectors.toList());
                aggregationHistogramRuleOutput.setRanges(hOutList);
                ruleComposeOutput.getHistogram().add(aggregationHistogramRuleOutput);
            } else if (AggregationRuleType.percentile.value() == ruleType) {
                AggregationPercentileRuleOutput aggregationPercentileRuleOutput = new AggregationPercentileRuleOutput();
                BeanUtils.copyProperties(rule, aggregationPercentileRuleOutput);
                List<AggregationPercentileItemDO> pDOList = percentileItemMap.getOrDefault(ruleId, Lists.newArrayList());
                List<AggregationPercentileItemOutput> pOutList = pDOList.stream().map(e -> {
                    AggregationPercentileItemOutput item = new AggregationPercentileItemOutput();
                    BeanUtils.copyProperties(e, item);
                    item.setValue(e.getPercentileValue());
                    String fieldName = metricsFieldOutList.stream().filter(t -> t.getId().equals(e.getTargetFieldId())).findFirst().get().getFieldName();
                    item.setFieldName(fieldName);
                    return item;
                }).collect(Collectors.toList());
                aggregationPercentileRuleOutput.setPercentileValues(pOutList);
                ruleComposeOutput.getPercentile().add(aggregationPercentileRuleOutput);
            }
        });
        aggregationCustomFieldRuleDOList.forEach(rule -> {
            AggregationCustomFieldRuleOutput aggregationCustomFieldRuleOutput = new AggregationCustomFieldRuleOutput();
            BeanUtils.copyProperties(rule, aggregationCustomFieldRuleOutput);
            String fieldName = metricsFieldOutList.stream().filter(t -> t.getId().equals(rule.getMetricsFieldId())).findFirst().get().getFieldName();
            aggregationCustomFieldRuleOutput.setFieldName(fieldName);
            aggregationCustomFieldRuleOutput.setExpression(rule.getExpression().replaceAll(StringUtils.SPACE,StringUtils.EMPTY));
            ruleComposeOutput.getCustomize().add(aggregationCustomFieldRuleOutput);
        });

    }


    private void setCollectorId(MetricsOut metricsOut) {
        metricsOut.setCollectorId(metricsHandler.getCollectorIdByMetricsId(metricsOut.getId()));
    }

    public ResponseObject getMetricsListByTenantId(String userId, String tenantId) {
        Assert.notNull(userId, "user id is null");
        Assert.notNull(tenantId, "tenant id is null");
        if (!authService.hasSuchTenant(userId, tenantId)) {
            return ResponseObject.hasNoSuchTenant();
        }
        return ResponseObject.success(metricsHandler.getMetricsByTenant(tenantId));
    }

    public ResponseObject getFieldAndTagByMetricsId(IdPara idPara) {
        Assert.notNull(idPara.getUserId(), "user id is null");
        Assert.notNull(idPara.getId(), "metricsId is null");
        if (!authService.hasSuchMetrics(idPara, idPara.getId())) {
            return ResponseObject.hasNoSuchMetrics();
        }
        return ResponseObject.success(null, metricsHandler.getTagAndField(idPara.getId()));
    }

    public ResponseObject getPeriodByMetricsId(IdPara idPara) {
        Assert.notNull(idPara.getUserId(), "user id is null");
        Assert.notNull(idPara.getId(), "metricsId is null");
        authService.checkAuth(idPara);
        if (!authService.hasSuchMetrics(idPara, idPara.getId())) {
            return ResponseObject.hasNoSuchMetrics();
        }
        Integer period = getPeriod(idPara.getId());
        return ResponseObject.success(null, period);
    }

    public Integer getPeriod(String metricsId) {
        if (StringUtils.isBlank(metricsId)) {
            return null;
        }
        MetricsOut metricsOut = getMetricsById(metricsId);
        Integer period;
        if (Objects.isNull(metricsOut)) {
            return null;
        }
        if (MetricsTypeEnum.AGGREGATION.getValue() == metricsOut.getType()) {
            period = Optional.of(metricsOut).map(MetricsOut::getMetricsAggregation).map(MetricsAggregationOut::getAggPeriod).orElse(DEFAULT_PERIOD_UNIT);
        } else {
            period = Objects.isNull(metricsOut.getOriginPeriod()) ? DEFAULT_PERIOD_UNIT : metricsOut.getOriginPeriod();
        }
        return period;
    }

    public ResponseObject getFieldAndTagByMetricsIdV2(String userId,String tenantId ,String apiPath, String metricsId,boolean isOut) {
        Assert.notNull(userId, "user id is null");
        authService.checkAuth(userId, tenantId, apiPath);
        if (!authService.hasSuchMetrics(userId, tenantId, apiPath, metricsId)) {
            return ResponseObject.hasNoSuchMetrics();
        }
        List<FieldOrTag> fieldOrTags = getFieldAndTagByMetricsIdV2(metricsId);
        if(!isOut) {
            alarmDefinitionService.extraFieldOrTag(fieldOrTags, true);
        }
        return ResponseObject.success(null, fieldOrTags);
    }

    public List<FieldOrTag> getFieldAndTagByMetricsIdV2(String metricsId) {
        Assert.notNull(metricsId, "metricsId is null");
        List<FieldOrTag> fieldOrTags = metricsHandler.getTagAndField(metricsId);
        List<FieldOrTag> filterList = fieldOrTags.stream().filter(f ->
                MetricsFieldTypeEnum.number.name().equals(f.getValueType())
                        || MetricsFieldTypeEnum.string.name().equals(f.getValueType())
                        || MetricsFieldTypeEnum.histogram.name().equals(f.getValueType())
                        || MetricsFieldTypeEnum.mapString.name().equals(f.getValueType())
                        || MetricsFieldTypeEnum.mapNumber.name().equals(f.getValueType())
        ).collect(Collectors.toList());

        List<CollectorFieldDO> piiFieldByMetricId = collectorFieldDAO.getPiiFieldByMetricId(metricsId);

        if (CollectionUtils.isEmpty(piiFieldByMetricId)) {
            return filterList;
        }

        Set<String> piiFieldNames = piiFieldByMetricId.stream()
                .map(CollectorFieldDO::getTargetField)
                .collect(Collectors.toSet());

        filterList.forEach(fieldOrTag -> {
            if (piiFieldNames.contains(fieldOrTag.getFieldOrTagName())) {
                fieldOrTag.setIsPi(true);
            }
        });
        return filterList;
    }

    public ResponseObject getFieldMapKeysFromClickHouse(MetricsFieldMapsQueryInput input) {
        String userId = input.getUserId();
        String tenantId = input.getTenantId();
        String metricsName = input.getMetricsName();
        String fieldName = input.getFieldName();
        String filter = input.getFilter();
        Assert.notNull(userId, "userId is null");
        Assert.notNull(tenantId, "tenantId is null");
        Assert.notNull(metricsName, "metricsName is null");
        Assert.notNull(fieldName, "fieldName is null");
        authService.checkAuth(input);

        String serviceName = tenantDAO.getTenantNameById(tenantId);
        String dbName = ChNameEncoder.encode(serviceName);
        filter = filter == null ? "" : filter.replace("'", "\\'");
        int hours = 24;

        String sql = String.format(
                "SELECT DISTINCT arrayJoin(arrayFilter(k -> k ILIKE '%%%1$s%%', mapKeys(%2$s))) AS field_name" +
                        " FROM %3$s.%4$s" +
                        " WHERE time >= now() - INTERVAL %5$d HOUR",
                filter,
                fieldName,
                dbName,
                metricsName,
                hours
        );

        Set<String> resultSet;
        try {
            List<Map<String, Object>> queryResult = clickhouseHandlerFactory.get().query(serviceName, sql);
            resultSet = queryResult.stream()
                    .map(map -> map.values().iterator().next().toString())
                    .collect(Collectors.toSet());

        } catch (Exception e){
            log.error("Error when getFieldMapKeysFromClickHouse, serviceName = {}, sql={}, {}", serviceName, sql, e);
            return ResponseObject.fail("getFieldMapKeys failed!");
        }
        return ResponseObject.success(resultSet);
    }

    public ResponseObject getVariableAndFunction(String userId,String tenantId ,String apiPath, String metricsId) {

        Assert.notNull(userId, "user id is null");
        Assert.notNull(metricsId, "metricsId is null");
        authService.checkAuth(userId,tenantId,apiPath);
        if (!authService.hasSuchMetrics(userId,tenantId,apiPath, metricsId)) {
            return ResponseObject.hasNoSuchMetrics();
        }

        MetricsOut metricsOut = getMetricsById(metricsId);
        if (Objects.isNull(metricsOut)) {
            return ResponseObject.fail("metrics is not exist");
        }

        List<MetricsTagsAndFieldsOutput> result = getMetricsTagsAndFieldsOutputs(metricsOut);

        Set<String> targetType = Set.of(MetricsFieldTypeEnum.number.name(),
                MetricsFieldTypeEnum.string.name(),
                MetricsFieldTypeEnum.histogram.name());

        List<MetricsVarAndFunc.Variable> metricsVar = result.stream()
                .map(MetricsVarAndFunc.Variable::new).filter(f -> targetType.contains(f.getType())).toList();

        String preVarPrefix = alarmParaService.getParamValue(AlarmParaService.PRE_VAR_PREFIX);
        List<MetricsVarAndFunc.Variable> derivedVar = result.stream()
                .map(var -> {
                    var.setName(preVarPrefix + var.getName());
                    return new MetricsVarAndFunc.Variable(var);
                })
                .collect(Collectors.toList());

        String metricsVarAndFuncStr = alarmParaService.getParamValue(AlarmParaService.VARIABLE_AND_FUNCTION);
        Assert.notNull(metricsVarAndFuncStr, "please contact cube admin to add system parameters named 'variableAndFunctions'");
        MetricsVarAndFunc metricsVarAndFunc = JsonUtils.toObject(metricsVarAndFuncStr, MetricsVarAndFunc.class);
        Assert.notNull(metricsVarAndFunc, "please contact cube admin to update system parameters named 'variableAndFunctions'");

        addDynamicVar(metricsVarAndFunc, "Metric Variables", metricsVar);
        addDynamicVar(metricsVarAndFunc, "Derived Variables", derivedVar);

        return ResponseObject.success(null, metricsVarAndFunc);
    }

    private static void addDynamicVar(MetricsVarAndFunc construct, String Derived_Variables, List<MetricsVarAndFunc.Variable> derivedVar) {
        MetricsVarAndFunc.VariableCategory derivedVariables = construct.getVariables().stream().filter(f -> f.getCategoryName().equals(Derived_Variables)).findFirst().orElse(null);
        if (derivedVariables != null) {
            derivedVariables.getList().addAll(derivedVar);
        }
    }


    public ResponseObject parseVariablesFromExpression(ExpressionParseInput expressionParseInput) {
        Assert.notNull(expressionParseInput.getUserId(), "user id is null");
        Assert.notNull(expressionParseInput.getMetricsId(), "metricsId is null");
        Assert.notNull(expressionParseInput.getExpression(), "expression is null");
        authService.checkAuth(expressionParseInput);
        ExpressionParseOutput expressionParseOutput = new ExpressionParseOutput();
        List<FieldOrTag> fieldOrTags = metricsHandler.getTagAndField(expressionParseInput.getMetricsId());
        if (CollectionUtils.isEmpty(fieldOrTags)) {
            return ResponseObject.fail("metrics is invalid");
        }
        List<String> fieldOrTagNames = fieldOrTags.stream().map(FieldOrTag::getFieldOrTagName).toList();
        expressionParseOutput.setMetricsFieldAndTags(fieldOrTagNames);
        try {
            List<String> variables = CustomAviatorUtils.compile(expressionParseInput.getExpression(), true).getVariableFullNames();
            variables.retainAll(fieldOrTagNames);
            expressionParseOutput.setValidVariables(variables);
        } catch (Exception e) {
            return ResponseObject.fail("Compile Expression failed");
        }

        return ResponseObject.success(expressionParseOutput);
    }

    public boolean isAggMetrics(String metricsId) {
        Assert.notNull(metricsId, "metricsId is null");
        return Optional.ofNullable(metricsHandler.getMetricsById(metricsId))
                .map(metricsDO -> MetricsTypeEnum.AGGREGATION.getValue() == metricsDO.getType())
                .orElse(false);
    }


    public ResponseObject deleteMetrics(@Valid IdPara idPara) {
        Assert.isTrue(authService.canOperate(idPara.getUserId(), idPara.getTenantId()), "user can not operate this service");
        authService.checkAuth(idPara);
        authService.checkHasMetricsThrowExpWhenNotHave(idPara,idPara.getId());

        List<String> alarmNameList = alarmHandler.listAlarmIdNames(idPara.getId()).stream().map(e -> e.getName()).collect(Collectors.toList());
        Assert.isTrue(CollectionUtils.isEmpty(alarmNameList), "not support delete metrics bind by alarms, alarms =  " + JsonUtils.toJsonStringIgnoreExp(alarmNameList));
        if(metricsParaService.isAiCheckOpen()) {
            List<String> adNames = adCfgHandler.getAdNameByMetricsId(Collections.singletonList(idPara.getId()));
            Assert.isTrue(CollectionUtils.isEmpty(adNames), "No support delete metrics bound by AI Model, model =  " + JsonUtils.toJsonStringIgnoreExp(adNames));
        }

        try {
            String metricsId = idPara.getId();
            metricsHandler.deleteMetrics(metricsId);
            resourceTagService.deleteResourceTagByName(metricsId, ResourceTypeConstant.RESOURCE_METRIC_TYPE);
            return ResponseObject.success(null, "Metrics deleted!");
        } catch (Exception e) {
            log.error("delete metrics error! metricsId: {}. ", idPara.getId(), e);
            return ResponseObject.fail("Metrics deletion exception!");
        }
    }

    public ResponseObject<String> metricsBatchDelete(IdListPara idListPara) {
        Assert.isTrue(authService.canOperate(idListPara.getUserId(), idListPara.getTenantId()), "user can not operate this service");
        authService.checkAuth(idListPara);
        authService.checkHasMetricsThrowExpWhenNotHave(idListPara, idListPara.getIds());

//        List<String> alarmNameList = alarmHandler.listAlarmsByMetricsIdList(idListPara.getIds()).stream().map(e -> e.getName()).collect(Collectors.toList());
        List<String> alarmNameList = Optional.ofNullable(alarmHandler.listAlarmIdNames(idListPara.getIds()))
                .filter(list -> !list.isEmpty())
                .map(list -> list.stream().map(AlarmDO::getName).collect(Collectors.toList()))
                .orElse(Collections.emptyList());
//        Assert.isTrue(CollectionUtils.isEmpty(alarmNameList), "not support delete metrics bind by alarms, alarms =  " + JsonUtils.toJsonStringIgnoreExp(alarmNameList));
        if (!CollectionUtils.isEmpty(alarmNameList)) {
            return ResponseObject.fail("Not support delete metrics bound by alarms. Alarms = " + JsonUtils.toJsonStringIgnoreExp(alarmNameList));
        }
        if(metricsParaService.isAiCheckOpen()) {
            List<String> adNames = adCfgHandler.getAdNameByMetricsId(idListPara.getIds());
            Assert.isTrue(CollectionUtils.isEmpty(adNames), "No support delete metrics bound by AI Model, model list =  " + JsonUtils.toJsonStringIgnoreExp(adNames));
        }
        try {
            List<String> metricsIds = idListPara.getIds();
            metricsHandler.deleteMetricsList(metricsIds);
            resourceTagService.deleteResourceTagByNames(metricsIds, ResourceTypeConstant.RESOURCE_METRIC_TYPE);
            return ResponseObject.success(null, "Metrics List deleted!");
        } catch (EmptyResultDataAccessException e) {
            log.error("Batch delete alarm EmptyResultDataAccessException! alarm Id: {}. ", idListPara.getIds(), e);
            return ResponseObject.fail("contains target alarm definition is not exists.");
        } catch (Exception e) {
            log.error("Batch delete metrics error! metricsId: {}. ", idListPara.getIds(), e);
            return ResponseObject.fail("Metrics deletion exception!");
        }
    }

    public ResponseObject getFieldValues(MetricFieldQuery metricFieldQuery) {
        String metricsId = metricFieldQuery.getMetricId();
        String tenantId = metricFieldQuery.getTenantId();
        String tenantName = tenantHandler.getTenantById(tenantId).getName();
        return ResponseObject.success(influxService.distinctData(tenantName,
                metricsHandler.getMetricsById(metricsId),
                metricFieldQuery.getField(),
                metricFieldQuery.getBegin(),
                metricFieldQuery.getEnd()));
    }

    private static final Integer defaultRawDataMaxSize = 6000;

    public ResponseObject getMetricDetail(MetricQuery metricQuery) {
        String groupFields = metricQuery.getGroupFields();
        String tenantName = tenantHandler.getTenantById(metricQuery.getTenantId()).getName();
        MetricsDO metricsDO = metricsHandler.getMetricsById(metricQuery.getMetricId());
        List<MetricsFieldDO> metricsFields = collectorHandler.listMetricsFieldByMetricsIds(Arrays.asList(metricQuery.getMetricId()));
        MetricItem metricItem = new MetricItem();

        Map<String, List<String>> filteredParas = filterEmptyParas(metricQuery.getParas());
        metricItem.setAggItems(influxService.queryAgg(tenantName, metricsDO, metricsFields, groupFields, metricQuery.getBegin(), metricQuery.getEnd(), filteredParas, defaultRawDataMaxSize));
        metricItem.setGroupFields(Instance.toList(StringUtils.split(metricsDO.getTagNames(), ",")));

        return ResponseObject.success(metricItem.outG2PlotObj());

    }

    public MetricQuery genSql(ClickhouseQuery clickhouseQuery) throws JSQLParserException {
        MetricQuery q=clickhouseQuery.toMetricQuery();
        String tenantName=tenantHandler.matchInfluxDbNameToTenantName(clickhouseQuery.getDatabase());
        q.setDatabase(tenantName);
        Map<String, Object> params = clickhouseQuery.getParams();

        Map<String,ClickhouseVariable>clickhouseVariableMap=new HashMap<>();
        for(Map.Entry<String,Object> en:params.entrySet()){
            String vName=en.getKey();
            ClickhouseVariable v=new ClickhouseVariable(vName, (Map<String, Object>) en.getValue());
            clickhouseVariableMap.put(vName,v);
        }
        SqlPlaceholder placeholder = new SqlPlaceholder();
        String sql = ClickhouseSqlUtil.doPlaceholder(clickhouseQuery.getQuery(), placeholder);
        if (sysParaService.allowNoSelect(AuthInterceptor.getUserName()) && !(ClickhouseSqlUtil.getStatement(sql) instanceof Select)){
            q.setQuery(clickhouseQuery.getQuery());
            return q;
        }
        us.zoom.jsqlparser.statement.Statement statement = ClickhouseSqlUtil.getStatementThrowException(sql);
        if(statement instanceof Select) {
            SelectBody selectBody = ((Select) statement).getSelectBody();
            replaceSelectBody(clickhouseQuery, selectBody,  q, clickhouseVariableMap );
            if(((Select) statement).getWithItemsList()!=null){
                for(WithItem w:((Select) statement).getWithItemsList()){
                    SelectBody selectBodyInWith = w.getSubSelect().getSelectBody();
                    replaceSelectBody(clickhouseQuery, selectBodyInWith,  q, clickhouseVariableMap );
                }
            }
            sql = statement.toString();
            // process function variable replace
//            List<String> comparesNew = new ArrayList<>();
//            boolean allReplaced = true;
//            try{
//                sql = replaceParams(sql, clickhouseVariableMap);
//            }catch (Exception e){
//                log.error(e.getMessage(), e);
//                allReplaced = false;
//            }
//            List<String> originalCompares = q.getQueryCompares();
//            if (!CollectionUtils.isEmpty(originalCompares)) {
//                for (String sqlItem : originalCompares) {
//                    try {
//                        String sqlItemNew = replaceParams(sqlItem, clickhouseVariableMap);
//                        comparesNew.add(sqlItemNew);
//                    } catch (Exception e) {
//                        log.error("Failed to replace parameters in queryCompare: {}", sqlItem, e);
//                        allReplaced = false;
//                        break;
//                    }
//                }
//            }else{
//                allReplaced = false;
//            }
//            if (allReplaced && comparesNew.size() == originalCompares.size()) {
//                originalCompares.clear();
//                originalCompares.addAll(comparesNew);
//            }
            //sql = statement.toString();
            if(sysParaService.enhancedVariableReplacement()) {
                for (Map.Entry<String, ClickhouseVariable> en : clickhouseVariableMap.entrySet()) {
                    Object valueWithType = en.getValue().getValueWithType();
                    if (!(valueWithType instanceof StringValue)) {
                        continue;
                    }
                    String valueName = ClickhouseSqlUtil.toTranslatable(en.getKey());
                    sql = sql.replace(valueName, ((StringValue) valueWithType).getValue());
                }
            }
            q.setQuery(sql);
        } else {
            log.warn("Blocking non-select sql {}",clickhouseQuery.getQuery());
        }
        return q;
    }

    private void replaceSelectBody(ClickhouseQuery clickhouseQuery, SelectBody selectBody, MetricQuery q, Map<String, ClickhouseVariable> clickhouseVariableMap ) {
        if(selectBody instanceof SetOperationList){
            for(SelectBody s:((SetOperationList) selectBody).getSelects()){
                repalceVariableInPlainSQL(clickhouseQuery, q, clickhouseVariableMap,   s);
            }
        }else if(selectBody instanceof PlainSelect){
            repalceVariableInPlainSQL(clickhouseQuery, q, clickhouseVariableMap,   selectBody);
        }
    }

    public static String replaceParams(String sql, Map<String, ClickhouseVariable> clickhouseVariableMap) throws Exception {
        Select select = (Select) CCJSqlParserUtil.parse(sql);
        SelectBody selectBody = select.getSelectBody();

        selectBody.accept(new SelectVisitorAdapter() {
            @Override
            public void visit(PlainSelect plainSelect) {
                ExpressionDeParser deParser = new ExpressionDeParser() {
                    @Override
                    public void visit(StringValue stringValue) {
                        String val = stringValue.getValue();
                        if (val.startsWith("_") && val.endsWith("_")) {
                            String paramName = val.substring(1, val.length() - 1);
                            if (clickhouseVariableMap.containsKey(paramName)) {
                                ClickhouseVariable clickhouseVariable = clickhouseVariableMap.get(paramName);
                                Object valueWithType = clickhouseVariable.getValueWithType();
                                createLiteral(valueWithType);
                            }else{
                                super.visit(stringValue);
                            }
                        }else{
                            super.visit(stringValue);
                        }
                    }

                    @Override
                    public void visit(Column column){
                        String colName = column.getColumnName();
                        if (colName.startsWith("_") && colName.endsWith("_")) {
                            String paramName = colName.substring(1, colName.length() - 1);
                            if (clickhouseVariableMap.containsKey(paramName)) {
                                ClickhouseVariable clickhouseVariable = clickhouseVariableMap.get(paramName);
                                Object valueWithType = clickhouseVariable.getValueWithType();
                                createLiteral(valueWithType);
                            } else {
                                super.visit(column);
                            }
                        }else{
                            super.visit(column);
                        }
                    }

                    private void createLiteral( Object valueWithType) {
                        Expression newExpr;
                        if (valueWithType instanceof LongValue) {
                            newExpr = (LongValue)valueWithType;
                        }else {
                            newExpr = (StringValue)valueWithType;
                        }
                        this.getBuffer().append(newExpr.toString());
                    }
                };

                StringBuilder buffer = new StringBuilder();
                deParser.setBuffer(buffer);

                if (plainSelect.getWhere() != null) {
                    plainSelect.getWhere().accept(deParser);
                    try {
                        plainSelect.setWhere(CCJSqlParserUtil.parseCondExpression(buffer.toString()));
                    } catch (Exception e) {
                        throw new RuntimeException("Failed to parse WHERE after replacement", e);
                    }
                    buffer.setLength(0);
                }

                if (plainSelect.getSelectItems() != null) {
                    for (SelectItem item : plainSelect.getSelectItems()) {
                        item.accept(new SelectItemVisitorAdapter() {
                            @Override
                            public void visit(SelectExpressionItem item) {
                                Expression expr = item.getExpression();
                                expr.accept(deParser);
                                try {
                                    item.setExpression(CCJSqlParserUtil.parseExpression(deParser.getBuffer().toString()));
                                } catch (Exception e) {
                                    throw new RuntimeException("Failed to parse expression after replacement", e);
                                }
                                deParser.getBuffer().setLength(0);
                            }
                        });
                    }
                }
            }
        });

        return select.toString();
    }

    private void repalceVariableInPlainSQL(ClickhouseQuery clickhouseQuery, MetricQuery q, Map<String, ClickhouseVariable> clickhouseVariableMap,  SelectBody selectBody) {
        if (selectBody instanceof PlainSelect) {
            SelectSqlPartFinder spf=new SelectSqlPartFinder(selectBody);
            spf.parse();
            if(clickhouseVariableMap.containsKey("INTERVAL")) {
                for (SelectExpressionItem ps : spf.getSelectExpressionItems()) {
                    ClickhouseSqlUtil.replaceTimeInterval(ps, (IntervalExpression) new ClickhouseVariable("INTERVAL", (Map<String, Object>) clickhouseVariableMap.get("INTERVAL")).getValueWithType());
                }
            }
            for(BinaryExpression b:spf.getConditions()){
                ClickhouseSqlUtil.replaceWhereCondition(selectBody,b, clickhouseVariableMap, clickhouseQuery.isReplaceVariableRecursively());
            }
            String sql=selectBody.toString();
            if(!StringUtils.isEmpty(clickhouseQuery.getCompare())){
                String compare = clickhouseQuery.getCompare();
                Long minute=ClickhouseSqlUtil.getMinute(compare);
                if(sysParaService.enhancedVariableReplacement()) {
                    for (Map.Entry<String, ClickhouseVariable> en : clickhouseVariableMap.entrySet()) {
                        Object valueWithType = en.getValue().getValueWithType();
                        if (!(valueWithType instanceof StringValue)) {
                            continue;
                        }
                        String valueName = ClickhouseSqlUtil.toTranslatable(en.getKey());
                        sql = sql.replace(valueName, ((StringValue) valueWithType).getValue());
                    }
                }
                if(minute!=null){
                    String compareSql = ClickhouseSqlUtil.getCompareSql(sql, compare);
                    List<String>compareSqls=new ArrayList<>();
                    compareSqls.add(sql);
                    compareSqls.add(compareSql);
                    q.setQueryCompares(compareSqls);
                }
            }
        }
    }


    public ResponseObject queryRawData(MetricQuery metricQuery) {
        MetricItem metricItem = new MetricItem();
        String tenantName = tenantHandler.getTenantById(metricQuery.getTenantId()).getName();
        MetricsDO metricsDO = metricsHandler.getMetricsById(metricQuery.getMetricId());
        metricItem.setAggItems(influxService.queryRawData(tenantName, metricsDO, null, metricQuery.getBegin(), metricQuery.getEnd(), metricQuery.getParas(), defaultRawDataMaxSize));
        metricItem.setGroupFields(Instance.toList(StringUtils.split(metricsDO.getTagNames(), ",")));
        return ResponseObject.success(metricItem.outG2Plot());
    }


    private Map<String, List<String>> filterEmptyParas(Map<String, List<String>> paras) {
        Map<String, List<String>> filteredParas = Maps.newHashMap();
        if (CollectionUtils.isEmpty(paras)) {
            return filteredParas;
        }
        for (Map.Entry<String, List<String>> entry : paras.entrySet()) {
            String key = entry.getKey();
            List<String> value = entry.getValue();
            if (CollectionUtils.isEmpty(value)) {
                continue;
            }
            List<String> filteredValue = value.stream()
                    .filter(e -> !StringUtils.isEmpty(e))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filteredValue)) {
                continue;
            }
            filteredParas.put(key, filteredValue);
        }
        return filteredParas;
    }


    public List<ChTable>getTables(String sql){
        List<ChTable>tables=new LinkedList<>();
        try {
            us.zoom.jsqlparser.statement.Statement statement = parserManager.parse(new StringReader(sql));
            if(statement instanceof Select){
                SelectBody selectBody = ((Select) statement).getSelectBody();
                if(selectBody instanceof PlainSelect) {
                    SelectSqlPartFinder sspf = new SelectSqlPartFinder(selectBody);
                    sspf.parse();
                    for (FromItem fromItem : sspf.getFrom()) {
                        if (!(fromItem instanceof Table)) {
                            continue;
                        }
                        ChTable chTable = new ChTable((Table) fromItem);
                        if(StringUtils.isEmpty(chTable.getDbName()) || StringUtils.isEmpty(chTable.getFullTableName())){
                            continue;
                        }
                        tables.add(chTable);

                    }
                }}
        }catch (Exception e){
            log.info("Encounter error when doing table encoding for {}",sql);
        }
        return tables;
    }

    public String tableNameEncoding(String sql,Set<ChTable>needDesensitizedTable){
        try {
            us.zoom.jsqlparser.statement.Statement statement = parserManager.parse(new StringReader(sql));
            if(statement instanceof Select){
                SelectBody selectBody = ((Select) statement).getSelectBody();
                encodeTableItem(selectBody,needDesensitizedTable);
                if(((Select) statement).getWithItemsList()!=null) {
                    for (WithItem w : ((Select) statement).getWithItemsList()) {
                        encodeTableItem(w.getSubSelect().getSelectBody(), needDesensitizedTable);
                    }
                }
                sql = statement.toString();
            }
        }catch (Exception e){
            log.info("Encounter error when doing table encoding for {}",sql);
        }
        return sql;
    }

    private void encodeTableItem(SelectBody plainSelect,Set<ChTable>needDesensitizedTable) {
        SelectSqlPartFinder sspf=new SelectSqlPartFinder(plainSelect);
        sspf.parse();
        for(FromItem fromItem:sspf.getFrom()){
            if(fromItem instanceof Table){
                List<String> partItems = ((Table) fromItem).getPartItems();
                if(partItems.size()!=2){
                    continue;
                }

                String tableName=partItems.get(TABLE_INDEX_IN_PARTS_ITEM);
                String tableNameInClickHouse=ClickhouseSqlUtil.encodeClickhouseName(tableName);
                if(needDesensitizedTable.contains(new ChTable((Table) fromItem))){
                    tableNameInClickHouse=ClickhouseSqlUtil.fromItemsToDesensitized(tableNameInClickHouse);
                }
                partItems.set(TABLE_INDEX_IN_PARTS_ITEM,tableNameInClickHouse);
                String dbName=tenantHandler.matchInfluxDbNameToTenantName(partItems.get(DB_INDEX_IN_PARTS_ITEM));
                String dbNameInClickHouse=ClickhouseSqlUtil.encodeClickhouseName(dbName);
                partItems.set(DB_INDEX_IN_PARTS_ITEM,dbNameInClickHouse);

            }
        }
    }

    private String adjustSql(String oriSql,int limit,Set<ChTable>needDesensitizedTable) {
        String adjustSql = tableNameEncoding(oriSql,needDesensitizedTable);
        adjustSql = ClickhouseSqlUtil.adjustSelectOrderAndAddLimit(adjustSql,limit);
        adjustSql = ClickhouseSqlUtil.removeGroupByIfNotAggrFuncs(adjustSql);
        return adjustSql;
    }

    private String adjustSql(String oriSql,int limit) {
        return adjustSql(oriSql,limit,new HashSet<>());
    }

    private boolean sqlTranslate(MetricQuery metricQuery, QueryMonitor monitor) {
        if(metricQuery.getQuery().startsWith("show ")){
            return false;
        }
        String tenantName=tenantHandler.matchInfluxDbNameToTenantName(metricQuery.getDatabase());
        if(sysParaService.isForceToClickhouseQuery(tenantName)){
            if(!metricQuery.useClickhosue()) {
                metricQuery.setDatabase(tenantName);
                metricQuery.setDbType(ClickhouseConst.CLICKHOUSE_DB_TYPE);
                monitor.setMode(CLICKHOUSE_QUERY_MODE);
                if(metricQuery.hasCompare()){
                    List<String>ss=new ArrayList<>();
                    for(String s: metricQuery.getQueryCompares()){
                        ss.add(ClickhouseSqlUtil.influxQL2ChSql(s,monitor));
                    }
                    metricQuery.setQueryCompares(ss);
                }else{
                    metricQuery.setQuery(ClickhouseSqlUtil.influxQL2ChSql(metricQuery.getQuery(),monitor));
                }
            }
            return true;
        }
        return false;
    }

    private boolean checkDashPublic(MetricQuery metricQuery){
        if(StringUtils.equals(DashPubilcEnum.publicDash.name(),metricQuery.getScene())){
            DashDO dashDO = dashHandler.getDashById(metricQuery.getDashId());
            if (dashDO.getVisibility().equals(DashVisibilityEnum.PUBLIC.getCode())){
                return true;
            }
        }
        return false;
    }

    private boolean checkAuthQuery(MetricQuery metricQuery){
        if(!StringUtils.isEmpty(metricQuery.getScene())){
            if(StringUtils.equals(CapacityEnum.capacity.name(),metricQuery.getScene())){
                return false;
            }
            if(checkDashPublic(metricQuery)){
                return false;
            }
        }
        return true;
    }

    public ResponseObject getMetricDisplay(MetricQuery metricQuery) {
        return getMetricDisplay(metricQuery,new BucketReplace(), null, metricQuery.isShowMs());
    }

    public void legalSql(String sql)  throws AuthException{
        us.zoom.jsqlparser.statement.Statement statement = ClickhouseSqlUtil.getStatement(sql);
        if(!(statement instanceof Select)) {
            log.warn("User {} try to run invalid sql {}",AuthInterceptor.getUserName(),sql);
            throw new AuthException( WebCodeEnum.InvalidSQL);
        }
        List<ChTable> tables = getTables(sql);
        for(ChTable t:tables){
            if(!sysParaService.allowCrossDbWithoutCheckPermission()) {
                authService.mustHasSuchDb(AuthInterceptor.getUserId(), ChNameDecoder.decode(t.getDbName()));
            }
            if(!piiTableLoader.auth(t,AuthInterceptor.getUserId(),AuthInterceptor.getRealIp())){
                log.warn("User {} try to access {} from {}",AuthInterceptor.getUserName(),t.toString(),AuthInterceptor.getRealIp());
                throw new AuthException(QueryPIINoPermit);
            }
        }
    }

    public ResponseObject queryClickHouse(ClickhouseQuery metricQuery) {
        try {
            BucketReplace br=new BucketReplace();
            metricQuery.setQuery(br.replace(metricQuery.getQuery()));
            MetricQuery query = genSql(metricQuery);
            ResponseObject metricDisplay = getMetricDisplay(query,br, metricQuery.getTags(), metricQuery.isShowMs());
            return metricDisplay;
        } catch (AuthException au) {
            throw au;
        } catch (IllegalArgumentException e) {
            log.error("Unknow Error", e);
            return ResponseObject.failWithCode(null, String.format("Illegal_Argument|||%s", e.getMessage()), WebCodeEnum.IllegalArgument.getCode());
        } catch (Exception e) {
            log.error("Unknow Error", e);
            return ResponseObject.failWithCode(null, String.format("INNER_ERROR|||%s", e.getMessage()), WebCodeEnum.DbError.getCode());
        }
    }

    public ResponseObject getMetricDisplay(MetricQuery metricQuery,BucketReplace bucketReplace, List<String> tags, boolean timeFormat) {
        long startTime = System.currentTimeMillis();
        Assert.notNull(metricQuery.getDatabase(), "database is null");
//        Boolean checkAuth = checkAuthQuery(metricQuery);
//        if(checkAuth){
//            authService.mustHasSuchDb(metricQuery.getUserId(),metricQuery.getDatabase());
//        }
        QueryMonitor monitor = new QueryMonitor();
        monitor.appendInputSql(metricQuery.getQuery());
        sqlTranslate(metricQuery, monitor);
        monitor.setDbType(metricQuery.getDbType());
        TimeFiller timeFiller = new TimeFiller(0, startTime, dataQueryLimitService.getFillLimit(), null);
        us.zoom.jsqlparser.statement.Statement statement = ClickhouseSqlUtil.getStatement(metricQuery.getQuery());
        if (!(statement instanceof Select)) {
            if (!sysParaService.allowNoSelect(AuthInterceptor.getUserName())) {
                monitor.setInputSql(metricQuery.getQuery());
                monitor.setStatus(NON_SELECT.name());
                monitorLog.info(JsonUtils.toJsonStringIgnoreExp(monitor.finish()));
                return ResponseObject.fail("Non-Select SQL is not allowed");
            }
        }
        List<ChTable> tables = getTables(metricQuery.getQuery());

        Boolean hasPiiData = getHasPiiData(tags, tables);

        Set<ChTable> needDesensitizedTable = new HashSet<>();
        for (ChTable t : tables) {
            if (!sysParaService.allowCrossDbWithoutCheckPermission() && !sysParaService.allowNoSelect(AuthInterceptor.getUserName())) {
                authService.mustHasSuchDb(metricQuery.getUserId(), t.getDbName());
            }
            if (!piiTableLoader.auth(t, AuthInterceptor.getUserId(), AuthInterceptor.getRealIp())) {
                if (clickhouseParaService.enablePiiDesensitizedView()) {
                    needDesensitizedTable.add(t);
                    if (hasPiiData) {
                        log.warn("User {} try to access {} to get pii field from {}, but he/she didn't have pii permission", AuthInterceptor.getUserName(), t, AuthInterceptor.getRealIp());
                        return ResponseObject.failWithCode(null, "This is a PII table. Your role is not permit to access it.", PIIPermitError.getCode());
                    }
                } else {
                    log.warn("User {} try to access {} from {}", AuthInterceptor.getUserName(), t.toString(), AuthInterceptor.getRealIp());
                    return ResponseObject.failWithCode(null, "This is a PII table. Your role is not permit to access it.", PIIPermitError.getCode());
                }
            }

        }
        String serviceName = tenantHandler.matchInfluxDbNameToTenantName(metricQuery.getDatabase());
        metricQuery.setDatabase(serviceName);
        int selectSerivceLimit = sysParaService.getSelectSerivceLimit(serviceName);
        String routedEnv = subEnvironmentHandler.getDataQueryEnv(serviceName);
        if (routedEnv == null) {
            routedEnv = sysParaService.getClickhouseEnvRoute(serviceName);
        }
        QueryResult data = null;
        if (metricQuery.hasCompare()) {
            List<Map<String, Object>>[] qr = new List[2];
            for (int i = 0; i < 2; i++) {
                String oriSql = metricQuery.getQueryCompares().get(i);
                String adjustSql = adjustSql(oriSql, selectSerivceLimit, needDesensitizedTable);
                adjustSql = bucketReplace.recover(adjustSql);
                qr[i] = clickhouseHandlerFactory.get().query(serviceName, adjustSql, routedEnv, monitor);
            }
//                fillSql=metricQuery.getQueryCompares().get(0);
//                Integer gapMinute = ClickhouseSqlUtil.getGapMinute(metricQuery.getQueryCompares().get(1));
            for (int i = 0; i < 2; i++) {
                String sql = metricQuery.getQueryCompares().get(i);
                QueryResult queryResult = InfluxResultAdapter.wrapToInfluxLike(qr[i], sql, timeFormat);
                try {
                    if (SymbolConstant.NULL_STRING.equals(metricQuery.getFill())) {
                        timeFiller.fillTimeWithNull(sql, queryResult);
                    } else if (SymbolConstant.ZERO.equals(metricQuery.getFill())) {
                        timeFiller.setFilling(0);
                        timeFiller.fillTimeWithNull(sql, queryResult);
                    }
                } catch (Exception e) {
                }
                data = InfluxResultAdapter.merge(data, queryResult);
            }
//                TimeSeriesMatcher timeSeriesMatcher = new TimeSeriesMatcher(qr[0], qr[1], gapMinute);
//                timeSeriesMatcher.run();
//                data= InfluxResultAdapter.merge(data, InfluxResultAdapter.wrapToInfluxLike(timeSeriesMatcher.getNewL1(),metricQuery.getQueryCompares().get(0)));
//                data= InfluxResultAdapter.merge(data, InfluxResultAdapter.wrapToInfluxLike(timeSeriesMatcher.getNewL2(),metricQuery.getQueryCompares().get(1)));
        } else {
            String oriSql = metricQuery.getQuery();
            String adjustSql = adjustSql(oriSql, selectSerivceLimit, needDesensitizedTable);
            adjustSql = bucketReplace.recover(adjustSql);
            List<Map<String, Object>> query = clickhouseHandlerFactory.get().query(serviceName, adjustSql, routedEnv, monitor);
            data = InfluxResultAdapter.merge(data, InfluxResultAdapter.wrapToInfluxLike(query, oriSql, timeFormat));
            if (SymbolConstant.NULL_STRING.equals(metricQuery.getFill())) {
                timeFiller.fillTimeWithNull(adjustSql, data);
            } else if (SymbolConstant.ZERO.equals(metricQuery.getFill())) {
                timeFiller.setFilling(0);
                timeFiller.fillTimeWithNull(adjustSql, data);
            }
        }
        monitorLog.info(JsonUtils.toJsonStringIgnoreExp(monitor.finish()));

        if (QueryStatus.QUERY_FAIL.name().equals(monitor.getStatus())) {
            String msg = filterSensitiveInfo(monitor.getMsg());
            return ResponseObject.failWithCode(null, msg, WebCodeEnum.DbError.getCode());
        }
        return ResponseObject.success(data);
    }

    @NotNull
    private Boolean getHasPiiData(List<String> tags, List<ChTable> tables) {
        Boolean hasPiiData = false;
        if (tags != null && !tags.isEmpty()) {
            try {
                String finalTableName = tables.get(0).getFullTableName().split("\\.")[1];
                TenantDO tenant = tenantHandler.getTenantByName(tables.get(0).getDbName());
                List<MetricsDO> metricNames = metricsHandler.getMetricsByTenant(tenant.getId()).stream().filter(u -> StringUtils.equalsIgnoreCase(u.getMetricsName(), finalTableName)).collect(Collectors.toList());
                if (metricNames.isEmpty()) {
                    return hasPiiData;
                }
                String collectorId = collectorMetricsDAO.getCollectorIdByMetricsId(metricNames.get(0).getId());
                if (StringUtils.isEmpty(collectorId)) {
                    return hasPiiData;
                }
                List<CollectorFieldDO> collectorFieldDOS = collectorFieldDAO.getFiledByCollectorId(collectorId);
                if (collectorFieldDOS.isEmpty()) {
                    return hasPiiData;
                }
                List<String> piiList = collectorFieldDOS.stream().filter(dto -> dto.getHasPii() == 1)
                        .map(CollectorFieldDO::getSourceField)
                        .collect(Collectors.toList());

                hasPiiData = tags.stream().anyMatch(piiList::contains);
            } catch (Exception e) {
                log.error("get contains PII tag error", e);
            }
        }
        return hasPiiData;
    }

    private String filterSensitiveInfo(String input) {
        String filterStart = "DB::Exception";
        String filterEnd = "(version";
        if (StringUtils.contains(input, filterStart)) {
            input = input.substring(input.indexOf(filterStart));
        }
        if (StringUtils.contains(input, filterEnd)) {
            input = input.substring(0, input.indexOf(filterEnd) - 1);
        }
        String errDesc = "sql error";
        String regex = "\\(([^)]*)\\)\\s*$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        if (matcher.find()) {
            errDesc = matcher.group(1);
        }
        return String.format("%s|||%s", errDesc , input);
    }

    private String postHandleInfluxSql(String sql, String dbName){
        if(Optional.ofNullable(sysParaService.getInfluxRemainCaseSensitive()).orElse(new HashSet<>()).contains(dbName)){
            return sql;
        }
       Matcher matcher =  INFLUX_METRICS_QUERY_SQL_PATTERN.matcher(sql);
        if(matcher.matches() && matcher.groupCount() == 4){
            sql = matcher.group(1)+matcher.group(2)+matcher.group(3).toLowerCase()+matcher.group(4);
        }
        return sql;
    }

    private Object refactoryDataInTop(Object data, TopStruct topStruct) {
        if (null == data) {
            return null;
        }
        QueryResult queryResult = (QueryResult) data;
        List<QueryResult.Result> results = queryResult.getResults();
        if (CollectionUtils.isEmpty(results)) {
            return data;
        }
        List<QueryResult.Series> seriesList = results.get(0).getSeries();
        if (CollectionUtils.isEmpty(seriesList)) {
            return data;
        }

        QueryResult.Series series = seriesList.get(0);
        List<List<Object>> values = series.getValues();
        Map tags = series.getTags();
        List<String> columns = series.getColumns();
        String name = series.getName();
        List<String> newColumns = columns.subList(0, 2);
        if (CollectionUtils.isEmpty(values)) {
            series.setTags(Collections.EMPTY_MAP);
            series.setColumns(newColumns);
            return data;
        }

        //Map<tag,List<Map>>
        Map<String, QueryResult.Series> newSeriesMap = new LinkedHashMap();
        for (List value : values) {
            List<String> tagsValue = value.subList(2, value.size());
            String tagsKey = Instance.ofNullable(tagsValue).stream().collect(Collectors.joining("_"));
            QueryResult.Series newSeries = newSeriesMap.computeIfAbsent(tagsKey, f -> new QueryResult.Series());
            newSeries.setColumns(newColumns);
            LinkedHashMap newTags = new LinkedHashMap(10);
            if (null != tags) {
                newTags.putAll(tags);
            }
            Instance.ofNullable(topStruct.getTags()).forEach(tag -> {
                int index = topStruct.getTags().indexOf(tag);
                newTags.put(tag, tagsValue.get(index));
            });
            newSeries.setTags(newTags);
            newSeries.setName(name);
            if (null == newSeries.getValues()) {
                newSeries.setValues(new ArrayList<>());
            }
            newSeries.getValues().add(value.subList(0, 2));
        }

        results.get(0).setSeries(new ArrayList<>(newSeriesMap.values()));
        return data;
    }


    public ResponseObject getDbSchema(MetricQuery metricQuery) {
        List<TenantDO> tenantDOS = authHandler.getAllTenantUserHas(metricQuery.getUserId());
        Map<String, Object> result = new HashMap<>();

        if (StringUtils.isNotBlank(metricQuery.getDatabase())) {
            String dbName = StringUtils.lowerCase(metricQuery.getDatabase());
            authService.mustHasSuchDb(metricQuery.getUserId(), dbName);
            String tenantId = metricQuery.getTenantId();
            TenantDO tenantDO = tenantHandler.getTenantById(tenantId);
            Object value = influxService.getMetricDisplay(tenantDO.getName(), metricQuery.getQuery());
            result.put(dbName, value);
        } else {
            Instance.ofNullable(tenantDOS).forEach(tenantDO -> {
                String dbName = StringUtils.lowerCase(tenantDO.getName());
                Object value = influxService.getMetricDisplay(tenantDO.getName(), metricQuery.getQuery());
                result.put(dbName, value);
            });
        }

        return ResponseObject.success(result);
    }

    /**
     * query for Metric list infos by service name
     * @param serviceName original tenant name，is more handy than teanntId for user
     * @return
     */
    public ResponseObject queryMetricsListByServiceName(String serviceName) {
        Assert.state(StringUtils.isNotBlank(serviceName), "Service name is required");
        TenantDO serviceTenant = tenantHandler.getTenantByName(serviceName);
        if (null == serviceTenant) {
            return ResponseObject.hasNoSuchTenant();
        }

        //logic is almost the same as getMetricsListByTenantId
        return ResponseObject.success(metricsHandler.getMetricsByTenant(serviceTenant.getId()));

    }

    public OriginResultData getOriginDBForUser(UserDO user) {
        OriginResultData originResultData = new OriginResultData();
        List<Map<String, Object>> meta = new ArrayList<>();
        List<Map<String, Object>> data = new ArrayList<>();
        Map<String, Object> metaObj = new HashMap<>();
        metaObj.put("name","name");
        metaObj.put("type","String");
        meta.add(metaObj);
        List<TenantDO> tenants= tenantHandler.myTenants(user.getId());
        for (TenantDO tenantDO : tenants){
            Map<String, Object> dataObj = new HashMap<>();
            dataObj.put("name",tenantDO.getName());
            data.add(dataObj);
        }
        originResultData.setMeta(meta);
        originResultData.setData(data);
        originResultData.setRows(data.size());
        return originResultData;
    }

    public OriginResultData getOriginTableForUser(UserDO user, String database) {
        String serviceName= database;
        authService.mustHasSuchDb(user.getId(),database);
        Assert.notNull(serviceName,"ServiceName is null when show tables");
        List<Map<String, Object>> queryResult = clickhouseHandlerFactory.get().query(serviceName,String.format("use %s;show tables;", ClickhouseSqlUtil.toClickhouseName(tenantHandler.matchInfluxDbNameToTenantName(serviceName))));
        List<String> ret = queryResult.stream().map(u->u.get("name")).filter(Objects::nonNull).map(Object::toString)
                .filter(u->!u.endsWith("_click_local")).filter(u->!u.startsWith(ClickhouseConst.ALARM_RECORD_TABLE_PREFIX))
                .filter(u->!u.startsWith(ClickhouseConst.ALARM_MATCH_RECORD_TABLE_PREFIX))
                .map(ChNameDecoder::decode).collect(Collectors.toList());
        OriginResultData originResultData = new OriginResultData();
        List<Map<String, Object>> meta = new ArrayList<>();
        List<Map<String, Object>> data = new ArrayList<>();
        Map<String, Object> metaObj = new HashMap<>();
        metaObj.put("name","name");
        metaObj.put("type","String");
        meta.add(metaObj);
        for (String table : ret){
            Map<String, Object> dataObj = new HashMap<>();
            dataObj.put("name",table);
            data.add(dataObj);
        }
        originResultData.setMeta(meta);
        originResultData.setData(data);
        originResultData.setRows(data.size());
        return originResultData;
    }

    public List<IdAndName> fuzzySearchMetricsByName(MetricsQuery metricsQuery) {
        List<MetricsDO> metricsList = StringUtils.isNotBlank(metricsQuery.getMetricsName()) ?
                metricsHandler.findAllByMetricsNameLike(metricsQuery.getMetricsName(), metricsQuery.getServiceId()) :
                metricsHandler.getMetricsByTenant(metricsQuery.getServiceId());
        return metricsList.stream().map(metricsDO -> new IdAndName() {
            @Override
            public String getId() {
                return metricsDO.getId();
            }
            @Override
            public String getName() {
                return metricsDO.getMetricsName();
            }
        }).collect(Collectors.toList());
    }

    public ResponseObject<List<String>> getMetricsBatchTagList(MetricsBatchAddInput metricsBatchAddInput) {
        metricsBatchAddInput.checkBatchTagList(metricsBatchAddInput);
        if (metricsBatchAddInput.getIds().size()==0){
            return ResponseObject.success(new ArrayList<>());
        }
        List<MetricsDO> metricsDOList = metricsHandler.listMetricsByIds(metricsBatchAddInput.getIds());
        List<MetricsFieldDO> metricsFieldDOList = metricsHandler.listFieldsByMetricsIds(metricsBatchAddInput.getIds());
        Map<String,List<MetricsFieldDO>> metricsFieldMap = new HashMap<>();
        for (MetricsFieldDO fieldDO : metricsFieldDOList){
            List<MetricsFieldDO> fieldDOS = metricsFieldMap.computeIfAbsent(fieldDO.getMetricsId(), d -> new ArrayList<>());
            fieldDOS.add(fieldDO);
        }
        List<CollectorMetricsDO> collectorMetricsDOList = collectorHandler.listCollectorByMetricsIds(metricsBatchAddInput.getIds());
        Map<String,String> collectorMetricsMap = Instance.ofNullable(collectorMetricsDOList).stream().collect(Collectors.toMap(CollectorMetricsDO::getMetricsId,CollectorMetricsDO::getCollectorId));
        List<CollectorFieldDO> collectorFieldDOList = collectorHandler.listFieldByCollectorIds(Instance.ofNullable(collectorMetricsDOList).stream().map(item->item.getCollectorId()).collect(Collectors.toList()));
        Map<String,List<String>> collectorFieldMap = new HashMap<>();
        for (CollectorFieldDO fieldDO : collectorFieldDOList){
            List<String> fields = collectorFieldMap.computeIfAbsent(fieldDO.getCollectorId(), d -> new ArrayList<>());
            fields.add(fieldDO.getTargetField());
        }
        List<String> tagResults = new ArrayList<>();

        for (int i = 0; i< metricsDOList.size();i++){
            List<String> tagExist = Arrays.asList(metricsDOList.get(i).getTagNames().split(","));
            List<String> fieldExist = Instance.ofNullable(metricsFieldMap.get(metricsDOList.get(i).getId())).stream().map(item->item.getFieldName()).collect(Collectors.toList());
            fieldExist.addAll(tagExist);
            List<String> collectorFields = collectorFieldMap.get(collectorMetricsMap.get(metricsDOList.get(i).getId()));
            collectorFields = collectorFields.stream().filter(item -> !fieldExist.contains(item)).collect(Collectors.toList());
            if (i ==0){
                tagResults = collectorFields;
            }else {
                List<String> finalCollectorFields = collectorFields;
                tagResults = tagResults.stream().filter(item -> finalCollectorFields.contains(item)).collect(Collectors.toList());
            }
        }
        return ResponseObject.success(tagResults);
    }

    public ResponseObject<String> metricsBatchAdd(MetricsBatchAddInput metricsBatchAddInput) {
        authService.checkAuth(metricsBatchAddInput);
        metricsBatchAddInput.checkBatchAdd(metricsBatchAddInput);
        if (metricsBatchAddInput.getIds().size()==0||metricsBatchAddInput.getTags().size()==0){
            return ResponseObject.success("success");
        }
        Set<String> tags = new HashSet<>(metricsBatchAddInput.getTags());
        List<MetricsDO> metricsDOList = metricsHandler.listMetricsByIds(metricsBatchAddInput.getIds());
        for (MetricsDO metricsDO : metricsDOList){
            Set<String> tagExist = Arrays.stream(metricsDO.getTagNames().split(",")).collect(Collectors.toSet());
            tagExist.addAll(tags);
            metricsDO.setModifyTime(new Date());
            metricsDO.setTagNames(StringUtils.join(tagExist, COMMA_SPLIT));
        }
        metricsHandler.batchUpdateMetrics(metricsDOList);
        return ResponseObject.success("success");
    }
    public Object getOriginMetricFromClickHouse(String query, ServletRequest servletRequest, ServletResponse servletResponse) {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;
        try {
            log.info(query);
            UserDO user = userHandler.getUserByName(request.getHeader("User"));
            Assert.isTrue(user != null, "can not find this user");
            MetricQuery metricQuery = new MetricQuery();
            metricQuery.setQuery(metricQuery.checkQueryFromGrafana(query));
            metricQuery.setDbType(CLICKHOUSE_DB_TYPE);
            List<ChTable> tables = getTables(metricQuery.getQuery());
            metricQuery.setDatabase(tables.get(0).getDbName());
            if (metricQuery.getQuery().startsWith("SELECT name FROM system.databases")) {
                // 
                OriginResultData result = getOriginDBForUser(user);
                return result;
            } else if (metricQuery.getQuery().startsWith("SELECT name FROM system.tables")) {
                // 
                String database = metricQuery.getQuery().substring(metricQuery.getQuery().indexOf("'") + 1, metricQuery.getQuery().lastIndexOf("'"));
                OriginResultData result = getOriginTableForUser(user, database);
                return result;
            } else {
                OriginResultData result = getOriginMetricFromClickHouse(metricQuery, user);
                return result;
            }
        }catch (Exception e){
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            if (e instanceof AuthException) {
                AuthException authException = (AuthException) e;
                return  authException.getTips();
            }
            if (e instanceof ClickHouseException){
                String errorMsg = "inner error";
                try {
                    errorMsg = e.getCause().getMessage();
                }catch (Exception inner){}
                return errorMsg;
            }
            return e.getMessage();
        }
    }

    public OriginResultData getOriginMetricFromClickHouse(MetricQuery metricQuery,UserDO user) throws SQLException {
        Assert.notNull(metricQuery.getDatabase(),"database is null");
        authService.mustHasSuchDb(user.getId(),metricQuery.getDatabase());
        QueryMonitor monitor=new QueryMonitor();
        monitor.appendInputSql(metricQuery.getQuery());
        monitor.setDbType(metricQuery.getDbType());
        // elect
        us.zoom.jsqlparser.statement.Statement statement = ClickhouseSqlUtil.getStatement(metricQuery.getQuery());
        if (!(statement instanceof Select)) {
            Assert.isTrue(false,"Non-Select SQL is not allowed");
        }
        //
        List<ChTable> tables = getTables(metricQuery.getQuery());
        for (ChTable t : tables) {
            if (!sysParaService.allowCrossDbWithoutCheckPermission()) {
                authService.mustHasSuchDb(user.getId(), t.getDbName());
            }
            if (!clickhouseAuthParaService.hasAccess(t.getFullTableName(), user.getName())) {
                Assert.isTrue(false,"Do not have access privilege to this table");
            }
            if(piiTableLoader.existPii(t)){
                log.warn("User {} try to access {} from {}",user.getName(),t.toString(),"GlobalTracing");
                Assert.isTrue(false,"This is a PII table, please query in cube system");
            }
        }
        metricQuery.setDatabase(tenantHandler.matchInfluxDbNameToTenantName(metricQuery.getDatabase()));
        int selectSerivceLimit = sysParaService.getSelectSerivceLimit(metricQuery.getDatabase());
        OriginResultData result = null;
        if (!metricQuery.hasCompare()) {
            String oriSql = metricQuery.getQuery();
            String adjustSql = adjustSql(oriSql, selectSerivceLimit);
            result = clickhouseHandlerFactory.get().queryOri(metricQuery.getDatabase(), adjustSql);
        }
        monitorLog.info(JsonUtils.toJsonStringIgnoreExp(monitor.finish()));
        return result;
    }

    public ResponseObject listTagsAndFields(IdPara idPara) {
        Assert.notNull(idPara.getUserId(), "user id is null");
        Assert.notNull(idPara.getId(), "metricsId is null");
        authService.checkAuth(idPara);
        if (!authService.hasSuchMetrics(idPara, idPara.getId())) {
            return ResponseObject.hasNoSuchMetrics();
        }
        MetricsOut metricsOut = getMetricsById(idPara.getId());
        if (Objects.isNull(metricsOut)) {
            return ResponseObject.fail("metrics is not exist");
        }

        List<MetricsTagsAndFieldsOutput> result = getMetricsTagsAndFieldsOutputs(metricsOut);
        return ResponseObject.success(result);
    }

    @NotNull
    private List<MetricsTagsAndFieldsOutput> getMetricsTagsAndFieldsOutputs(MetricsOut metricsOut) {
        Map<String, String> tagMap = metricsOut.getTagDetails()
                .stream()
                .filter(tag -> StringUtils.isNotEmpty(tag.getDescription()))
                .collect(Collectors.toMap(MetricsOut.TagDetails::getTagName, MetricsOut.TagDetails::getDescription));

        List<MetricsTagsAndFieldsOutput> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(metricsOut.getTagNames())) {
            metricsOut.getTagNames().forEach(m -> {
                MetricsTagsAndFieldsOutput metricsTagsAndFieldsOutput = newTag();
                metricsTagsAndFieldsOutput.setName(m);
                if (tagMap.containsKey(m)) {
                    metricsTagsAndFieldsOutput.setDescription(tagMap.get(m));
                }
                result.add(metricsTagsAndFieldsOutput);
            });
        }

        List<CollectorFieldDO> collectorFieldDOS = collectorFieldDAO.getFiledByCollectorId(metricsOut.getCollectorId());
        Map<String, String> filedDescriptionMap = collectorFieldDOS.stream()
                .filter(field -> StringUtils.isNotBlank(field.getDescription()))
                .collect(Collectors.toMap(
                        CollectorFieldDO::getSourceField,
                        dto -> Objects.requireNonNull(dto.getDescription())));

        List<MetricsFieldOut> metricsFieldList = metricsOut.getMetricsFieldList();
        if (!CollectionUtils.isEmpty(metricsFieldList)) {
            metricsFieldList.forEach(m -> {
                MetricsTagsAndFieldsOutput metricsTagsAndFieldsOutput = newField();
                metricsTagsAndFieldsOutput.setId(m.getId());
                metricsTagsAndFieldsOutput.setName(m.getFieldName());
                metricsTagsAndFieldsOutput.setFieldType(Integer.toString(m.getFieldType()));
                if (filedDescriptionMap.containsKey(m.getFieldName())) {
                    metricsTagsAndFieldsOutput.setDescription(filedDescriptionMap.get(m.getFieldName()));
                } else {
                    metricsTagsAndFieldsOutput.setDescription(m.getDescription());
                }
                result.add(metricsTagsAndFieldsOutput);
            });
        }
        return result;
    }

    public ResponseObject updateDescription(MetricsBatchAddDescriptionInput metricsBatchAddDescriptionInput) {
        metricsBatchAddDescriptionInput.check();
        authService.checkAuth(metricsBatchAddDescriptionInput);
        if (!authService.hasSuchMetrics(metricsBatchAddDescriptionInput, metricsBatchAddDescriptionInput.getMetricsId())) {
            return ResponseObject.hasNoSuchMetrics();
        }

        String metricsId = metricsBatchAddDescriptionInput.getMetricsId();
        try {
            Future<List<MetricsFieldDO>> metricsFieldDOListFuture = executor.submit(() -> metricsHandler.listFieldsByMetricsIds(Collections.singletonList(metricsId)));
            List<MetricsFieldDO> metricsFieldDOS = metricsFieldDOListFuture.get();
            Map<String, MetricsFieldDO> metricsFieldDOMap = Instance.ofNullable(metricsFieldDOS).stream()
                    .collect(Collectors.toMap(MetricsFieldDO::getFieldName, dto -> dto));

            String collectorId = metricsHandler.getCollectorIdByMetricsId(metricsId);

            Future<List<CollectorFieldDO>> collectorFieldFuture = executor.submit(() -> collectorHandler.getFiledByCollectorId(collectorId));
            List<CollectorFieldDO> collectorFieldDOS = collectorFieldFuture.get();
            Map<String, CollectorFieldDO> collectorFieldDOMap = Instance.ofNullable(collectorFieldDOS).stream()
                    .collect(Collectors.toMap(CollectorFieldDO::getSourceField, dto -> dto));

            if (CollectionUtils.isEmpty(metricsBatchAddDescriptionInput.getDescriptionList())) {
                return ResponseObject.success(null);
            }


            List<MetricsBatchAddDescriptionInput.Description> descriptions = metricsBatchAddDescriptionInput.getDescriptionList()
                    .stream().filter(desc -> desc.getDescription() != null).toList();

            Set<String> matchedNames = new HashSet<>();
            List<CollectorFieldDO> dBBatchCollectorFieldDO = descriptions
                    .stream()
                    .map(input -> {
                        CollectorFieldDO collectorFieldDO = collectorFieldDOMap.get(input.getName());
                        if (collectorFieldDO != null) {
                            matchedNames.add(input.getName());
                            CollectorFieldDO c = new CollectorFieldDO();
                            BeanUtils.copyProperties(collectorFieldDO, c);
                            c.setDescription(input.getDescription());
                            return c;
                        }
                        return null;
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            List<MetricsFieldDO> dBBatchmetricsFieldDOList = descriptions
                    .stream()
                    .map(input -> {
                        if (matchedNames.contains(input.getName())) {
                            return null;
                        }
                        MetricsFieldDO metricsFieldDO = metricsFieldDOMap.get(input.getName());
                        if (metricsFieldDO != null) {
                            MetricsFieldDO m = new MetricsFieldDO();
                            BeanUtils.copyProperties(metricsFieldDO, m);
                            m.setDescription(input.getDescription());
                            return m;
                        }
                        return null;
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            metricsServiceTransactional.applyDB(dBBatchCollectorFieldDO, dBBatchmetricsFieldDOList);
            return ResponseObject.success(metricsBatchAddDescriptionInput.getMetricsId());
        } catch (Exception e) {
            log.error(ExceptionUtils.getTrace(e));
            throw new SiteException(InnerError.getCode(), InnerError.getErrMsg());
        }
    }

    public ResponseObject updateDocumentLink(MetricsDocumentLinkInput metricsDocumentLinkInput) {
        metricsDocumentLinkInput.check();
        authService.checkAuth(metricsDocumentLinkInput);
        if (!authService.hasSuchMetrics(metricsDocumentLinkInput, metricsDocumentLinkInput.getMetricsId())) {
            return ResponseObject.hasNoSuchMetrics();
        }

        String metricsId = metricsDocumentLinkInput.getMetricsId();
        try {
            MetricsDO metricsDO = metricsHandler.getMetricsById(metricsId);
            Assert.notNull(metricsDO, "metrics do not exist");
            metricsDO.setDocumentLink(metricsDocumentLinkInput.getDocumentLink());
            metricsDO.setModifyTime(new Date());
            metricsDO.setEditor(AuthInterceptor.getUserName());
            metricsHandler.updateDocumentLink(metricsDO);
            return ResponseObject.success(metricsDO.getId());
        } catch (Exception e) {
            log.error("update metrics document link error", e);
            throw new SiteException(InnerError.getCode(), InnerError.getErrMsg());
        }
    }

    public ResponseObject queryMetricsByNameAndService(MetricsName metricsName) {
        checkAndAuth(metricsName);
        MetricsDO metricsDO = metricsHandler.queryMetricsByName(metricsName.getMetricsName(), metricsName.getTenantId());
        MetricsOut metricsOut = metricsDO != null ? getMetricsById(metricsDO.getId()) : new MetricsOut();
        return ResponseObject.success(metricsOut);
    }

    /**
     * get all tag keys
     * @param metricsId metric id
     * @return tag keys
     */
    public List<String> getTagKeysByMetricsId(String metricsId) {
        MetricsDO metricsDO = metricsHandler.getMetricsById(metricsId);
        if (metricsDO == null || StringUtils.isBlank(metricsDO.getTagNames())) {
            return new ArrayList<>();
        }
        return Arrays.asList(metricsDO.getTagNames().split(CommonSplitConstants.COMMA_SPLIT));
    }

    public void updateMetricsTags(MetricsDO metricsDO) {
        metricsHandler.updateMetricsTags(metricsDO);
    }

}
