package us.zoom.cube.site.api.web.out;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import us.zoom.cube.site.biz.alarm.AlarmInsightService;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.alarm.insight.*;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: canyon.li
 * @date: 2025/07/31
 **/
@SuppressWarnings("rawtypes")
@RestController
@RequestMapping("/out/alarmInsight")
public class OutAlarmInsightController {

    @Autowired
    private AlarmInsightService alarmInsightService;

    @PostMapping("/getAlarming")
    public ResponseObject getAlarmingOverview(@RequestBody AlarmOverviewInput overviewInput) {
        return ResponseObject.success(alarmInsightService.getAlarmingOverview(overviewInput));
    }

    @PostMapping("/getAlertDistribution")
    public ResponseObject getAlertDistribution(@RequestBody AlarmInsightAlertDistributionInput alarmInsightAlertDistributionInput) {
        return alarmInsightService.getAlertDistribution(alarmInsightAlertDistributionInput);
    }

    @PostMapping("/getQueryTags")
    public ResponseObject getQueryTags(@RequestBody AlarmInsightInput alarmInsightInput) {
        return alarmInsightService.getQueryTags(alarmInsightInput);
    }

    @PostMapping("/getTagValue")
    public ResponseObject getTagValue(@RequestBody AlarmInsightTagValueInput alarmInsightTagValueInput) {

        return alarmInsightService.getTagValue(alarmInsightTagValueInput);
    }

    @PostMapping("/getAlertCount")
    public ResponseObject getAlertCount(@RequestBody AlarmInsightAlertDistributionInput alarmInsightAlertDistributionInput) {

        return alarmInsightService.getAlertCount(alarmInsightAlertDistributionInput);
    }

    @PostMapping("/getAlarmRecords")
    public ResponseObject getAlarmRecords(@RequestBody AlarmRecordsInput alarmRecordsInput) {
        return alarmInsightService.getAlarmRecords(alarmRecordsInput);
    }

    @PostMapping("/getAlarmRecordDetail")
    public ResponseObject getAlarmRecordDetail(@RequestBody @Validated AlarmDetailInput alarmDetailInput) {

        Map<String, Object> detailResult = new HashMap<>(1);
        detailResult.put("alarmRecordDetail", alarmInsightService.getAlarmRecordDetail(alarmDetailInput));
        return ResponseObject.success(detailResult);
    }

    @PostMapping("/getAlarmRecordNotifications")
    public ResponseObject getAlarmRecordNotifications(@RequestBody @Validated AlarmDetailInput alarmDetailInput) {
        return ResponseObject.success(alarmInsightService.getAlarmRecordNotifications(alarmDetailInput));
    }

    @PostMapping("/getSuppressedAlarmRecords")
    public ResponseObject getSuppressedAlarmRecords(@RequestBody AlarmRecordsInput alarmRecordsInput) {
        return ResponseObject.success(alarmInsightService.getSuppressedAlarmRecords(alarmRecordsInput));
    }
}