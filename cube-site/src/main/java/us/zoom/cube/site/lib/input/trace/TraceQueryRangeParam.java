package us.zoom.cube.site.lib.input.trace;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * @author: eason.jia
 * @date: 2024/8/17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TraceQueryRangeParam {
    private long start;
    private long end;
    private int step;
    private CompositeQuery compositeQuery;
    private Map<String, Object> variables;
    private boolean noCache;
    private String version;
    private boolean formatForWeb;
    private String dataSource;
    /**
     * Whether to query cold data
     */
    private boolean queryColdData;
}