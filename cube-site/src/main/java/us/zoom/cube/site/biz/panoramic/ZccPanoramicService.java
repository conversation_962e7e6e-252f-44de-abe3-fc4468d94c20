package us.zoom.cube.site.biz.panoramic;

import com.alibaba.fastjson.JSON;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.site.biz.syspara.PanoramicMonitorParaService;
import us.zoom.cube.site.lib.input.panoramic.PanoramicBase;
import us.zoom.cube.site.lib.input.panoramic.PanoramicConfigQueryInput;
import us.zoom.cube.site.lib.input.panoramic.ZccQueryInput;
import us.zoom.cube.site.lib.output.panoramic.*;
import us.zoom.infra.model.alarm.AlarmLevel;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class ZccPanoramicService {
    private final String CHANNEL_RANK = "zcc.channelRank";
    private final String CUSTOMER_RANK = "zcc.customerRank";
    private final String CUSTOMER = "zcc.customer";
    private final String TIME_SCALE = "zcc.timeScale";
    private final String TIME_INTERVAL = "zcc.timeInterval";
    private final String VALUE = "value";

    private final String CLUSTER_PARAM = "clusterId=";
    private final String REGION_PARAM = "regionId=";
    private final String TIME_INTERVAL_PARAM = "timeInterval=";
    private final String TIME_RANGE_PARAM = "timeRange=";

    private final String CLUSTER_ID = "cluster";
    private final String REGION_ID = "region";
    private final String TOTAL_COUNT = "totalCount";
    private final String ERROR_COUNT = "errorCount";
    private final String HEALTH_SLA = "100";
    private final String LINK = "link";
    private final String COMMA = ",";
    private final String SINGLE_QUOTE = "'";
    private final String SLA = "sla";
    private final String NAN = "NaN";
    private final String ACCOUNT_ID = "accountId";
    private final String ACCOUNT_LIST = "accountList";
    private final String CUSTOMER_RESULT = "customerResult";
    private final String TIME_RANGE_JSON = """
               {"type":"absolute","value":["%s1","%s2"]}
            """;
    // timed task
    private static final int LOAD_INTERVAL = 60;
    private static final int LOAD_INITIAL_DELAY = 10;
    private static final int NULL_VALUE = 999;
    private final IZccQueryDataService zccQueryDataService;
    private final PanoramicMonitorParaService panoramicMonitorParaService;
    private final List<IAlarmRecordsService> alarmRecordsService;
    private final static Logger monitorLog = LoggerFactory.getLogger("Monitor");

    @PostConstruct
    public void automaticWarn() {
        Executors.newSingleThreadScheduledExecutor()
                .scheduleAtFixedRate(() -> {
                    PanoramicBase panoramicBase = new PanoramicBase();
                    LocalDateTime endTime = LocalDateTime.now();
                    LocalDateTime startTime = endTime.minusMinutes(10);
                    panoramicBase.setEndTime(endTime);
                    panoramicBase.setStartTime(startTime);
                    AlarmResult alarmResult = queryAlarmRecords(panoramicBase);
                    AlarmResultLog alarmResultLog = new AlarmResultLog();
                    BeanUtils.copyProperties(alarmResultLog, alarmResult);
                    monitorLog.info(JsonUtils.toJsonStringIgnoreExp(alarmResultLog));
                }, LOAD_INITIAL_DELAY, LOAD_INTERVAL, TimeUnit.SECONDS);
    }


    /**
     * Global SLA Ranking Menu Data Access
     *
     * @param panoramicBase Start time End time
     * @return
     */
    public List<Map<String, Object>> queryChannelRank(PanoramicBase panoramicBase) {
        Map<String, Object> param = InitParam.init(() -> JSON.parseObject(JSON.toJSONString(panoramicBase), Map.class), "global sla ranking Configuration information error");
        List<Map<String, Object>> resultList = new ArrayList<>(zccQueryDataService.getQueryResult(CHANNEL_RANK, param).values().stream().flatMap(Collection::stream).toList());
        //Special Handling of Null Data Sorting
        resultList.sort(Comparator.comparing(map -> (Objects.isNull(map.get(SLA)) || Objects.equals(NAN, map.get(SLA))) ? NULL_VALUE : Double.parseDouble(String.valueOf(map.get(SLA))), Comparator.naturalOrder()));
        return resultList;
    }

    /**
     * channel detail
     *
     * @param queryInput inbound&outbound&recording
     *                   1.current 2.lowest 3.successRate 4.volume
     */
    public List<BusinessTypeResult> queryChannelByTime(ZccQueryInput queryInput) {
        Map<String, Object> param = InitParam.init(() -> JSON.parseObject(JSON.toJSONString(queryInput), Map.class), "channel Configuration information error");

        Map<PanoramicConfigQueryInput.ConfigSql, List<Map<String, Object>>> queryResult = zccQueryDataService.getQueryResult(queryInput.getChannel(), param);
        if (!CollectionUtils.isEmpty(queryResult)) {
            List<String> sortList = new ArrayList<>();
            List<BusinessDetailResult> resultList = queryResult.keySet().stream().map(e -> {
                BusinessDetailResult detailResult = new BusinessDetailResult();
                detailResult.setTitle(e.getBusinessName());
                detailResult.setParentName(e.getName());
                detailResult.setType(e.getType());
                detailResult.setLink(e.getLink());
                detailResult.setValue(queryResult.get(e));
                detailResult.setColumns(e.getColumns());
                if (!sortList.contains(e.getName())) {
                    sortList.add(e.getName());
                }
                return detailResult;
            }).toList();

            return resultList.stream().collect(Collectors.groupingBy(BusinessDetailResult::getParentName)).entrySet().stream().map(e -> {
                BusinessTypeResult businessTypeResult = new BusinessTypeResult();
                businessTypeResult.setTitle(e.getKey());
                Optional<BusinessDetailResult> successRate = e.getValue().stream().filter(f -> Objects.equals(f.getType(), BusinessDetailEnum.rate.name())).findFirst();
                successRate.ifPresent(businessTypeResult::setSuccessRate);
                Optional<BusinessDetailResult> volume = e.getValue().stream().filter(f -> Objects.equals(f.getType(), BusinessDetailEnum.count.name())).findFirst();
                volume.ifPresent(businessTypeResult::setVolume);


                Optional<BusinessDetailResult> cluster = e.getValue().stream().filter(f -> Objects.equals(f.getType(), BusinessDetailEnum.cluster.name())).findFirst();
                cluster.ifPresent(businessTypeResult::setCluster);
                //cluster dimension aggregation
                if (!Objects.isNull(businessTypeResult.getCluster())) {

                    List<Map<String, Object>> clusterList = businessTypeResult.getCluster().getValue();
                    if (!CollectionUtils.isEmpty(clusterList)) {
                        //Add business card link
                        String businessLink = getLink(queryInput, new HashMap<>(), businessTypeResult.getCluster().getLink());
                        businessTypeResult.setLink(businessLink);
                        List<BusinessClusterResult<List<Map<String, Object>>>> clusterData = clusterList.stream()
                                .collect(Collectors.groupingBy(clusterObj -> String.valueOf(clusterObj.get(CLUSTER_ID)),
                                        LinkedHashMap::new,
                                        Collectors.toList())).entrySet().stream().map(clusterMap -> {
                                    // add link
                                    clusterMap.getValue().forEach(data -> {
                                        String link = getLink(queryInput, data, businessTypeResult.getCluster().getLink());
                                        data.put(LINK, link);
                                    });
                                    BusinessClusterResult<List<Map<String, Object>>> clusterResult = new BusinessClusterResult();
                                    Double totalCount = clusterMap.getValue().stream().mapToDouble(map -> Double.valueOf(String.valueOf(map.getOrDefault(TOTAL_COUNT, 0d)))).sum();
                                    Double errorCount = clusterMap.getValue().stream().mapToDouble(map -> Double.valueOf(String.valueOf(map.getOrDefault(ERROR_COUNT, 0d)))).sum();
                                    clusterResult.setClusterId(clusterMap.getKey());
                                    clusterResult.setTotalCount(totalCount.longValue());
                                    clusterResult.setErrorCount(errorCount.longValue());
                                    if (totalCount == 0L) {
                                        clusterResult.setClusterSla(HEALTH_SLA);
                                    } else {
                                        BigDecimal sla = BigDecimal.valueOf((totalCount - errorCount) / totalCount * 100D).setScale(4, RoundingMode.HALF_UP);

                                        clusterResult.setClusterSla(sla.toString());
                                    }
                                    String columnStr = businessTypeResult.getCluster().getColumns();
                                    Assert.hasText(columnStr, "The cluster must be configured with columns");
                                    List<ColumnConfigResult> columnConfigResultList = JSON.parseArray(columnStr, ColumnConfigResult.class);
                                    clusterResult.setColumnNameList(columnConfigResultList);
                                    clusterResult.setValue(clusterMap.getValue());
                                    return clusterResult;
                                }).toList();

                        businessTypeResult.setClusterData(clusterData);
                    }
                }


                List<Map<String, Object>> value = businessTypeResult.getSuccessRate().getValue();
                if (!CollectionUtils.isEmpty(value)) {
                    Optional<Double> minValue = value.stream().map(map -> Double.valueOf(String.valueOf(map.get(VALUE)))).min(Double::compareTo);
                    minValue.ifPresent(businessTypeResult::setLowest);
                    //Time for the latest entry
                    businessTypeResult.setCurrent(Double.valueOf(String.valueOf(value.get(value.size() - 1).get(VALUE))));
                }
                return businessTypeResult;
            }).sorted(Comparator.comparing(businessTypeResult -> sortList.indexOf(businessTypeResult.getTitle()))).toList();
        }
        return new ArrayList<>();
    }

    private @NotNull String getLink(ZccQueryInput queryInput, Map<String, Object> data, String link) {
        if (!Objects.isNull(data.get(CLUSTER_ID))) {
            String clusterName = String.valueOf(data.get(CLUSTER_ID));
            link = link.replaceAll(CLUSTER_PARAM, CLUSTER_PARAM + clusterName);
        }
        if (!Objects.isNull(data.get(REGION_ID))) {
            String regionName = String.valueOf(data.get(REGION_ID));
            link = link.replaceAll(REGION_PARAM, REGION_PARAM + regionName);
        }
        String formatTimeRange = URLEncoder.encode(String.format(TIME_RANGE_JSON, queryInput.getStartTime(), queryInput.getEndTime()));
        link = link.replaceAll(TIME_RANGE_PARAM, TIME_RANGE_PARAM + formatTimeRange);
        if (!Objects.isNull(queryInput.getTimeInterval())) {
            link = link.replaceAll(TIME_INTERVAL_PARAM, TIME_INTERVAL_PARAM + queryInput.getTimeInterval());
        }
        return link;
    }

    /**
     * Customer SLA Ranking
     *
     * @param zccQueryInput
     * @return
     */
    public List<CustomerResult> queryCustomerRank(ZccQueryInput zccQueryInput) {
        Map<String, Object> param = initCustomerParam(zccQueryInput);
        Map<PanoramicConfigQueryInput.ConfigSql, List<Map<String, Object>>> queryResult = zccQueryDataService.getQueryResult(CUSTOMER_RANK, param);
        Optional<PanoramicConfigQueryInput.ConfigSql> first = queryResult.keySet().stream().filter(f -> Objects.equals(f.getName(), zccQueryInput.getChannel())).findFirst();
        List<CustomerResult> customerResult = (List<CustomerResult>) param.get(CUSTOMER_RESULT);
        first.ifPresent((key) -> {
            List<Map<String, Object>> queryList = queryResult.get(key);
            queryList.forEach(e -> {
                String accountId = String.valueOf(e.get(ACCOUNT_ID));
                String sla = String.valueOf(e.get(SLA));
                customerResult.stream().filter(f -> Objects.equals(f.getAccountId(), accountId)).findFirst().ifPresent(f -> {
                    f.setSla(sla);
                });
            });
        });
        customerResult.sort(Comparator
                .comparing((CustomerResult c) -> (c.getSla() == null) ? NULL_VALUE : Double.parseDouble(c.getSla()))
                .thenComparing(CustomerResult::getCompanyName));
        return customerResult;
    }

    private Map<String, Object> initCustomerParam(ZccQueryInput zccQueryInput) {
        return InitParam.init(() -> {
            Map<String, Object> param = JSON.parseObject(JSON.toJSONString(zccQueryInput), Map.class);
            String paramValue = panoramicMonitorParaService.getParamValue(CUSTOMER);
            List<CustomerResult> customerResults = JSON.parseArray(paramValue, CustomerResult.class);
            List<String> customerList = customerResults.stream().map(e -> SINGLE_QUOTE + e.getAccountId() + SINGLE_QUOTE).toList();
            String accountList = String.join(COMMA, customerList);
            param.put(ACCOUNT_LIST, accountList);
            param.put(CUSTOMER_RESULT, customerResults);
            return param;
        }, "Key Account  Configuration Errors");
    }

    /**
     * Global Alarm Item Interface
     *
     * @param panoramicBase
     * @return
     */
    public AlarmResult queryAlarmRecords(PanoramicBase panoramicBase) {
        AlarmResult result = new AlarmResult();
        List<AlarmRecordsResult> recordsList = new ArrayList<>();
        alarmRecordsService.forEach(e -> {
            recordsList.addAll(e.queryAlarmResult(panoramicBase));
        });
        long alarmCount = recordsList.stream().mapToLong(AlarmRecordsResult::getAlarmCount).sum();
        result.setTotalCount(alarmCount);
        long p0 = recordsList.stream().filter(f -> Objects.equals(f.getAlarmLevel(), AlarmLevel.FATAL.getLevel())).mapToLong(AlarmRecordsResult::getAlarmCount).sum();
        long p1 = recordsList.stream().filter(f -> Objects.equals(f.getAlarmLevel(), AlarmLevel.ERROR.getLevel())).mapToLong(AlarmRecordsResult::getAlarmCount).sum();
        long p2 = recordsList.stream().filter(f -> Objects.equals(f.getAlarmLevel(), AlarmLevel.INFO.getLevel())).mapToLong(AlarmRecordsResult::getAlarmCount).sum();
        long p3 = recordsList.stream().filter(f -> Objects.equals(f.getAlarmLevel(), AlarmLevel.WARN.getLevel())).mapToLong(AlarmRecordsResult::getAlarmCount).sum();
        result.getAlarmLevelCount().setP0(p0);
        result.getAlarmLevelCount().setP1(p1);
        result.getAlarmLevelCount().setP2(p2);
        result.getAlarmLevelCount().setP3(p3);
        Map<String, List<AlarmRecordsResult>> alarmMap = recordsList.stream().collect(Collectors.groupingBy(AlarmRecordsResult::getAlarmId));

        List<AlarmRecordsResult> alarmRecordsList = alarmMap.entrySet().stream().map(e -> {
            AlarmRecordsResult firstRecords = e.getValue().get(0);
            AlarmRecordsResult recordsResult = new AlarmRecordsResult();
            recordsResult.setServiceName(firstRecords.getServiceName());
            recordsResult.setAlarmName(firstRecords.getAlarmName());
            recordsResult.setAlarmId(firstRecords.getAlarmId());
            long count = e.getValue().stream().mapToLong(AlarmRecordsResult::getAlarmCount).sum();
            recordsResult.setAlarmCount(count);
            recordsResult.setType(firstRecords.getType());
            recordsResult.setAlertUrl(firstRecords.getAlertUrl());
            return recordsResult;
        }).toList();

        result.setAlarmRecordsList(alarmRecordsList);
        return result;
    }

    public List<Map> queryTimeScale() {
        return getValueFrmPara(TIME_SCALE);
    }

    public List<Map> queryTimeInterval() {
        return getValueFrmPara(TIME_INTERVAL);
    }

    public List<Map> getValueFrmPara(String key) {
        String paramValue = panoramicMonitorParaService.getParamValue(key);
        return JSON.parseArray(paramValue, Map.class);
    }
}
