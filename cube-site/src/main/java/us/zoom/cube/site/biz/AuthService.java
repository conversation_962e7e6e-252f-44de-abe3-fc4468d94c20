package us.zoom.cube.site.biz;

import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.api.intercept.AuthInterceptor;
import us.zoom.cube.site.core.DashHandler;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.core.auth.AuthHandler;
import us.zoom.cube.site.infra.enums.WebCodeEnum;
import us.zoom.cube.site.lib.AuthException;
import us.zoom.cube.site.lib.BasePara;
import us.zoom.cube.site.lib.BusinessException;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.infra.dao.model.DashDO;
import us.zoom.infra.dao.model.TenantDO;
import us.zoom.infra.dao.service.DashUserRelaDAO;
import us.zoom.infra.model.menu.MenuTree;
import us.zoom.infra.utils.CommonSplitConstants;
import us.zoom.infra.utils.Instance;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
public class AuthService {

    @Autowired
    private DashHandler dashHandler;

    @Autowired
    private DashService dashService;

    @Autowired
    private AuthHandler authHandler;

    @Autowired
    private TenantHandler tenantHandler;

    @Autowired
    private DashUserRelaDAO dashUserRelaDAO;

    public boolean isTenantExist(String tenantId){
        TenantDO tenant = tenantHandler.getTenantById(tenantId);
        if(tenant == null){
            return false;
        }
        return true;
    }

    private List<String> listAllCollectors(String userId) {
        return authHandler.listCollectorIds(userId);
    }

    public boolean hasSuchTenant(String userId, String tenantId) {
        return authHandler.hasSuchTenant(userId,tenantId);
    }

    public TenantDO mustHaveSuchServiceWithUser(String userId, String serviceTenantName, String apiPath) {
        return mustHaveSuchServiceTenant(userId,serviceTenantName,apiPath);
    }

    public TenantDO mustHaveSuchServiceTenant(String userId, String serviceTenantName, String apiPath) {
        if(StringUtils.isBlank(userId)){
            //auth by AuthInterceptor
            throw new AuthException( WebCodeEnum.HasNoSuchTenant.getCode(), "No Such User or Service!");
        }
        TenantDO tenantDO = tenantHandler.getTenantByNameInLowerCaseFromCache(serviceTenantName);
        if (null == tenantDO) {
            throw new AuthException( WebCodeEnum.HasNoSuchTenant.getCode(), "No Such User or Service!");
        }

        authHandler.checkAuth(userId,tenantDO.getId(),apiPath);
        return tenantDO;
    }

    public boolean hasSuchCollector(BasePara basePara, String collectorId) {
        if(authHandler.hasCrosseServiceAuth(basePara.getUserId(),basePara.getTenantId())){
            return true;
        }
        return Instance.ofNullable(listAllCollectors(basePara.getUserId())).contains(collectorId);
    }

    public void checkHasMetricsThrowExpWhenNotHave(BasePara basePara, String metricsId) {
        if(!hasSuchMetrics(basePara,metricsId)){
            throw new AuthException( WebCodeEnum.HasNoSuchMetrics);
        }
    }

    public void checkHasMetricsThrowExpWhenNotHave(BasePara basePara, List<String> metricsIds) {
        if(!hasSuchMetricsList(basePara, metricsIds)){
            throw new AuthException( WebCodeEnum.HasNoSuchMetrics);
        }
    }

    public boolean hasSuchMetrics(BasePara basePara, String metricsId) {

        if(authHandler.hasCrosseServiceAuth(basePara.getUserId(),basePara.getTenantId())){
            return true;
        }
        List<String> tenantIds=authHandler.listTanentIdsByUserId(basePara.getUserId());
        if(CollectionUtils.isEmpty(tenantIds)){
            return  false;
        }
        List<String> metricsIdList = authHandler.listMetricsIdsByTenantIds(tenantIds);
        return Instance.ofNullable(metricsIdList).contains(metricsId);
    }

    public boolean hasSuchMetricsList(BasePara basePara, List<String> metricsIds) {
        if(authHandler.hasCrosseServiceAuth(basePara.getUserId(),basePara.getTenantId())){
            return true;
        }
        List<String> tenantIds=authHandler.listTanentIdsByUserId(basePara.getUserId());
        if(CollectionUtils.isEmpty(tenantIds)){
            return  false;
        }
        List<String> metricsIdList = authHandler.listMetricsIdsByTenantIds(tenantIds);
        return Instance.ofNullable(metricsIdList).containsAll(metricsIds);
    }


    public boolean hasSuchMetrics(String userId,String tenantId,String apiPath,  String metricsId) {

        if(authHandler.hasCrosseServiceAuth(userId,tenantId)){
            return true;
        }
        List<String> tenantIds=authHandler.listTanentIdsByUserId(userId);
        if(CollectionUtils.isEmpty(tenantIds)){
            return  false;
        }
        List<String> metricsIdList = authHandler.listMetricsIdsByTenantIds(tenantIds);
        return Instance.ofNullable(metricsIdList).contains(metricsId);
    }

    public boolean hasSuchPattern(String userId, String patternId) {
        if(authHandler.hasCrossServiceAuthIgnoreTenant(userId)){
            return true;
        }
        List<String> tenantIds=authHandler.listTanentIdsByUserId(userId);
        if(CollectionUtils.isEmpty(tenantIds)){
            return  false;
        }
        List<String> patternIds= authHandler.listPatternIdByTenantIds(tenantIds);
        return !CollectionUtils.isEmpty(patternIds);
    }

    public boolean hasSuchDataParser(BasePara basePara,String dataParserId) {
        if(authHandler.hasCrosseServiceAuth(basePara.getUserId(),basePara.getTenantId())){
            return true;
        }
        List<String> tenantIds=authHandler.listTanentIdsByUserId(basePara.getUserId());
        if(CollectionUtils.isEmpty(tenantIds)){
            return  false;
        }
        List<String> patternIds= authHandler.listDataParserIdByTenantIds(tenantIds);

        return Instance.ofNullable(patternIds).contains(dataParserId);
    }

    public boolean hasSuchDataParserSource(BasePara basePara,String dataParserId) {
        if(authHandler.hasCrosseServiceAuth(basePara.getUserId(),basePara.getTenantId())){
            return true;
        }
        List<String> tenantIds=authHandler.listTanentIdsByUserId(basePara.getUserId());
        if(CollectionUtils.isEmpty(tenantIds)){
            return  false;
        }
        List<String> patternIds= authHandler.listDataParserSourceIdByTenantIds(tenantIds);

        return Instance.ofNullable(patternIds).contains(dataParserId);
    }


    public void checkHasAlarmThrowExpWhenNotHave(BasePara basePara,String alarmId){
        if(!hasSuchAlarm(basePara, alarmId)){
            throw new AuthException(WebCodeEnum.HasNoSuchAlarm.getCode(), "Don't Have Such Alarm!");
        }
    }

    public void checkHasAlarmThrowExpWhenNotHave(BasePara basePara,List<String> alarmIds){
        if(!hasSuchAlarmList(basePara, alarmIds)){
            throw new AuthException(WebCodeEnum.HasNoSuchAlarm.getCode(), "Don't Have Such Alarm!");
        }
    }

    public boolean hasSuchAlarm(BasePara basePara, String alarmId) {
        if(authHandler.hasCrosseServiceAuth(basePara.getUserId(),basePara.getTenantId())){
            return true;
        }
        List<String> tenantIds=authHandler.listTanentIdsByUserId(basePara.getUserId());
        if(CollectionUtils.isEmpty(tenantIds)){
            return  false;
        }

        List<String> alarmIdList = authHandler.listAlarmIdsByTenantIds(tenantIds);
        return Instance.ofNullable(alarmIdList).contains(alarmId);
    }

    public boolean hasSuchAlarmList(BasePara basePara, List<String> alarmIds) {
        if(authHandler.hasCrosseServiceAuth(basePara.getUserId(),basePara.getTenantId())){
            return true;
        }
        List<String> tenantIds=authHandler.listTanentIdsByUserId(basePara.getUserId());
        if(CollectionUtils.isEmpty(tenantIds)){
            return  false;
        }

        List<String> alarmIdList = authHandler.listAlarmIdsByTenantIds(tenantIds);
        return Instance.ofNullable(alarmIdList).containsAll(alarmIds);
    }

    public ResponseObject<MenuTree> getMenuTree(BasePara basePara) {
        return  ResponseObject.success(authHandler.getMenuTree(basePara));
    }

    public boolean mustHasSuchDashIncludePublic(String userId, String dashId) {
        return isHasTenantInDash(userId, dashId);
    }

    public void mustHasSuchDash(String userId, String dashId) {
        boolean hasTenantInDash = isHasTenantInDash(userId, dashId);

        if (!hasTenantInDash) {
            throw new AuthException(WebCodeEnum.HasNoSuchTenant.getCode(), "Permit error!!");
        }
    }

    private boolean isHasTenantInDash(String userId, String dashId) {
        DashDO dashById = dashHandler.getDashById(dashId);
        String tenantIds = dashById.getTenantId();

        List<String> allTenantUserHas = authHandler.getAllTenantUserHas(userId).stream().toList().stream().map(x -> x.getId()).collect(Collectors.toList());
        boolean hasTenantInDash = CollectionUtils.containsAny(Splitter.on(CommonSplitConstants.COMMA_SPLIT).splitToList(tenantIds), allTenantUserHas);
        return hasTenantInDash;
    }

    public void mustDashOwner(String userId, String dashId) {

        DashDO dashById = dashHandler.getDashById(dashId);
        if (dashById == null) {
            throw new BusinessException(WebCodeEnum.DashboardWasDeleted.getCode());
        }

        boolean contains = StringUtils.contains(dashById.getSharedOwners(), userId)
                || StringUtils.equals(dashById.getCreator(), AuthInterceptor.getUserName())
                || dashService.getAllDashUsers(dashId).contains(userId);

        if (!contains) {
            throw new AuthException(WebCodeEnum.PermitError);
        }
    }

    public void mustHasSuchDb(String userId, String database) {
        TenantDO tenant = tenantHandler.getTenantByNameInLowerCaseFromCache(database);
        if(tenant==null){
            //log.error("tenantDOS is empty, userId = {}, database = {}", userId, database);
            throw new AuthException(WebCodeEnum.ParaError.getCode(), "Database " + database + " doesn't exist");
        }

        boolean have=authHandler.hasSuchTenant(userId,tenant.getId());
        if(!have){
            throw new AuthException(WebCodeEnum.PermitError.getCode(), database);
        }
    }

    public void checkAuth(BasePara basePara) {
        authHandler.checkAuth(basePara);
    }


    public void checkAuthIgnoreTenant(BasePara basePara){
        authHandler.checkAuthIgnoreTenant(basePara.getUserId(),basePara.getAuthResourceUrl());
    }

    public void checkAuth(String userId, String tenantId, String apiPath) {
        authHandler.checkAuth(userId,tenantId,apiPath);
    }

    public void checkAuthByGetAuthFromAuthInterceptor() {
        authHandler.checkAuth(AuthInterceptor.getUserId(),AuthInterceptor.getTenantId(),AuthInterceptor.getApiPath());
    }

    public void checkAuthFromAuthInterceptorIgnoreTenant() {
        authHandler.checkAuthIgnoreTenant(AuthInterceptor.getUserId(), AuthInterceptor.getApiPath());
    }

    public boolean canCrossAndOperate(String userId) {
        return authHandler.canCrossAndOperate(userId);
    }

    public boolean hasSuchRole(String userId, String tenantId, String role) {
        return authHandler.hasSuchRole(userId, tenantId, role);
    }

    public Boolean canOperate(String userId, String serviceId){
        return authHandler.canOperate(userId, serviceId);
    }

    public boolean hasSuchTenantIds(String userId, List<String> tenantIds) {
        return authHandler.hasSuchTenantIds(userId,tenantIds);
    }

    public boolean hasCrosseServiceAuth(String userId, String tenantId) {
        return authHandler.hasCrosseServiceAuth(userId, tenantId);
    }
}
