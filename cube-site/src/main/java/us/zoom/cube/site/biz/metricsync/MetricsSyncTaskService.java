package us.zoom.cube.site.biz.metricsync;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import us.zoom.cube.lib.probe.SchedulerJobInfo;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.site.api.intercept.AuthInterceptor;
import us.zoom.cube.site.biz.AuthService;
import us.zoom.cube.site.biz.ProbeAuthService;
import us.zoom.cube.site.biz.SchedulerJobService;
import us.zoom.cube.site.biz.metricsync.cloudwatch.*;
import us.zoom.cube.site.core.SysParaHandler;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.IdPara;
import us.zoom.cube.site.lib.input.MetricsSyncTaskInput;
import us.zoom.cube.site.lib.query.CloudwatchSearchByTagsQuery;
import us.zoom.cube.site.lib.query.MetricsSyncTaskQuery;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.infra.dao.model.MetricsSyncTaskDO;
import us.zoom.infra.dao.model.MetricsSyncTemplateDO;
import us.zoom.infra.dao.service.MetricsSyncTaskDAO;
import us.zoom.infra.dao.service.MetricsSyncTemplateDAO;
import us.zoom.infra.redis.RedisService;
import us.zoom.infra.syspara.SysParaEventHandlerIntf;
import us.zoom.infra.syspara.SysParaEventService;
import us.zoom.infra.utils.CommonSplitConstants;
import us.zoom.infra.utils.IpUtils;

import java.util.*;
import java.util.stream.Collectors;

import static us.zoom.cube.site.biz.template.TemplateConstant.CUBE;

/**
 * @author: tobey.zhu
 * @date: 2022/09/13
 * @description:
 */
@Component
@Slf4j
public class MetricsSyncTaskService {

    private static final String JOB_GROUP = "hub_scheduler";
    private static final String JOB_TOPIC_NAME = "us_cube_metric_sync_task";
    private static final int SCHEDULER_JOB_TASK_TYPE = 1;
    private static final String SCHEDULER_JOB_TYPE = "ASYNCMQ";
    private static final int SCHEDULER_JOB_SIMPLE_TRIGGER_TYPE = 2;

    // Distributed lock related constants
    private static final String AUTO_REFRESH_CLOUDWATCH_TASK_KEY = "autoRefreshCloudwatchTask";
    private static final String SYS_PARA_TYPE = "hub-scheduler";
    private static final int AUTO_REFRESH_CLOUDWATCH_TASK_EXPIRE_MINUTES = 30;

    @Resource
    private MetricsSyncTaskDAO metricsSyncTaskDAO;
    @Resource
    private SchedulerJobService schedulerJobService;
    @Autowired
    private AuthService authService;
    @Autowired
    private MetricsSyncTaskProcessRegister metricsSyncTaskProcessRegister;
    @Autowired
    private ProbeAuthService probeAuthService;

    @Autowired
    private RedisService redisService;

    @Resource
    private CloudwatchTaskParserService cloudwatchTaskParserService;

    @Resource
    private MetricsSyncTemplateDAO metricsSyncTemplateDAO;

    private boolean enableAutoRefreshTask = false;


    @PostConstruct
    public void init() {
        SysParaEventService.registerListenerBySpecificSysPara(para -> {
            enableAutoRefreshTask = Boolean.parseBoolean(para.getValue());
        }, SYS_PARA_TYPE, AUTO_REFRESH_CLOUDWATCH_TASK_KEY);
    }

    public int getCount(PageQuery<MetricsSyncTaskQuery> query) {
        authService.checkAuth(query);
        return metricsSyncTaskDAO.getCount(probeAuthService.adaptServiceNames(query.getQueryPara().getService()),
                query.getQueryPara().getName(),
                query.getQueryPara().getTaskType(),
                query.getQueryPara().getStatus());
    }

    public List<MetricsSyncTaskDO> getList(PageQuery<MetricsSyncTaskQuery> pageQuery) {
        authService.checkAuth(pageQuery);

        List<MetricsSyncTaskDO> metricsSyncTaskDOList = metricsSyncTaskDAO.getList(
                probeAuthService.adaptServiceNames(pageQuery.getQueryPara().getService()),
                pageQuery.getQueryPara().getName(),
                pageQuery.getQueryPara().getTaskType(),
                pageQuery.getQueryPara().getStatus(),
                (pageQuery.getPageIndex() - 1) * pageQuery.getPageSize(),
                pageQuery.getPageSize());
        metricsSyncTaskDOList.forEach(this::unProcessTaskByType);
        return metricsSyncTaskDOList;
    }

    public void delete(IdPara idPara) {
        MetricsSyncTaskDO metricsSyncTaskDO = metricsSyncTaskDAO.getById(idPara.getId());
        if (metricsSyncTaskDO == null) {
            return;
        }
        idPara.setAuthResourceUrl(idPara.getAuthResourceUrl() + "/" + metricsSyncTaskDO.getTaskType());
        authService.checkAuth(idPara);
        deleteSchedulerJob(metricsSyncTaskDO);
    }

    private void unProcessTaskByType(MetricsSyncTaskDO metricsSyncTaskDO) {
        try {
            MetricsSyncTaskProcess metricsSyncTaskProcess = metricsSyncTaskProcessRegister.getByTaskType(metricsSyncTaskDO.getTaskType());
            if (metricsSyncTaskProcess != null) {
                metricsSyncTaskDO.setTaskConfig(metricsSyncTaskProcess.unProcess(metricsSyncTaskDO.getTaskConfig()));
            }
        } catch (Throwable t) {
            throw new IllegalArgumentException("unParse task failed please contact administrator");
        }
    }

    private void processTaskByType(MetricsSyncTaskInput input) {
        try {
            MetricsSyncTaskProcess metricsSyncTaskProcess = metricsSyncTaskProcessRegister.getByTaskType(input.getTaskType());
            if (metricsSyncTaskProcess != null) {
                input.setTaskConfig(metricsSyncTaskProcess.process(input.getService(), input.getTaskConfig()));
            }
        } catch (IllegalArgumentException ia) {
            throw ia;

        } catch (Throwable t) {
            throw new IllegalArgumentException("parse task failed please contact administrator");
        }
    }

    public String add(@NonNull MetricsSyncTaskInput input) {
        input.trim();
        input.setAuthResourceUrl(input.getAuthResourceUrl() + "/" + input.getTaskType());
        authService.checkAuth(input);
        return addWithoutCheckAuth(input);
    }

    public String addWithoutCheckAuth(@NonNull MetricsSyncTaskInput input) {
        input.trim();
        if (input.getId() == null) {
            input.setId(IdUtils.generateId());
        }
        processTaskByType(input);
        MetricsSyncTaskDO existedDO = metricsSyncTaskDAO.getByName(input.getName());
        if (existedDO != null) {
            throw new IllegalArgumentException("task name conflict");
        }
        MetricsSyncTaskDO metricsSyncTaskDO = input2Do(input);
        metricsSyncTaskDO.setCreatedUser(AuthInterceptor.getUserName());
        metricsSyncTaskDO.setLastModifiedUser(AuthInterceptor.getUserName());
        addSchedulerJob(metricsSyncTaskDO);
        return metricsSyncTaskDO.getId();
    }

    public MetricsSyncTaskDO getByName(@NonNull String name) {
        return metricsSyncTaskDAO.getByName(name);
    }

    public void modify(@NonNull MetricsSyncTaskInput input) {
        input.trim();
        input.setAuthResourceUrl(input.getAuthResourceUrl() + "/" + input.getTaskType());
        authService.checkAuth(input);
        modifyWithoutAuthCheck(input);
    }

    public void modifyWithoutAuthCheck(@NonNull MetricsSyncTaskInput input) {
        input.trim();
        if (input.getId() == null) {
            throw new IllegalArgumentException("no id found");
        }
        processTaskByType(input);
        MetricsSyncTaskDO oldOne = metricsSyncTaskDAO.getById(input.getId());
        if (oldOne == null) {
            throw new IllegalArgumentException("no task found");
        }
        // change task name need check conflict
        if (!input.getName().equals(oldOne.getName())) {
            MetricsSyncTaskDO existedDO = metricsSyncTaskDAO.getByName(input.getName());
            if (existedDO != null) {
                throw new IllegalArgumentException("task name conflict");
            }
        }
        MetricsSyncTaskDO newOne = input2Do(input);
        newOne.setLastModifiedUser(AuthInterceptor.getUserName());
        modifySchedulerJob(newOne, oldOne);
    }

    private void updateSubTasks(MetricsSyncTaskDO metricsSyncTaskDO, List<MetricsSyncTaskDO> subTaskList) {
        List<String> subTaskNameList = subTaskList.stream().map(MetricsSyncTaskDO::getName).collect(Collectors.toList());
        String subTaskNamesString = String.join(",", subTaskNameList);
        metricsSyncTaskDAO.updateSubTasks(metricsSyncTaskDO.getId(), subTaskNamesString);
    }

    private void addSchedulerJob(MetricsSyncTaskDO metricsSyncTaskDO) {
        metricsSyncTaskDAO.add(metricsSyncTaskDO);
        List<MetricsSyncTaskDO> subTaskList = new ArrayList<>();
        MetricsSyncTaskProcess metricsSyncTaskProcess = metricsSyncTaskProcessRegister.getByTaskType(metricsSyncTaskDO.getTaskType());
        if (metricsSyncTaskProcess != null) {
            subTaskList = metricsSyncTaskProcess.split(metricsSyncTaskDO);
        } else {
            subTaskList.add(metricsSyncTaskDO);
        }
        try {
            updateSubTasks(metricsSyncTaskDO, subTaskList);
            for (MetricsSyncTaskDO syncTaskDO : subTaskList) {
                schedulerJobService.addSchedulerJob(buildSchedulerJobInfo(syncTaskDO));
            }
        } catch (Throwable t) {
            // delete all subTasks if one of them add failed
            for (MetricsSyncTaskDO syncTaskDO : subTaskList) {
                schedulerJobService.deleteSchedulerJob(JOB_GROUP, syncTaskDO.getName());
            }
        }
    }

    private void modifySchedulerJob(MetricsSyncTaskDO newOne, MetricsSyncTaskDO oldOne) {
        deleteSchedulerJob(oldOne);
        addSchedulerJob(newOne);
    }

    private void deleteSchedulerJob(MetricsSyncTaskDO metricsSyncTaskDO) {
        String subTaskNamesString = metricsSyncTaskDO.getSubTasks();
        if (subTaskNamesString != null) {
            String[] subTaskNameList = subTaskNamesString.split(",");
            for (String subTaskName : subTaskNameList) {
                schedulerJobService.deleteSchedulerJob(JOB_GROUP, subTaskName);
            }
        } else {
            schedulerJobService.deleteSchedulerJob(JOB_GROUP, metricsSyncTaskDO.getName());
        }
        metricsSyncTaskDAO.deleteById(metricsSyncTaskDO.getId());
    }

    private MetricsSyncTaskDO input2Do(MetricsSyncTaskInput input) {
        MetricsSyncTaskDO metricsSyncTaskDO = new MetricsSyncTaskDO();
        BeanUtils.copyProperties(input, metricsSyncTaskDO);
        if (metricsSyncTaskDO.getUnitTag() == null ||
                metricsSyncTaskDO.getUnitTag().isEmpty()) {
            metricsSyncTaskDO.setUnitTag(SyncTaskConstant.UNIT_TAG_AA);
        }
        return metricsSyncTaskDO;
    }

    private int parseSchedulerJobTimeType(@NonNull String interval) {
        String unit = interval.substring(interval.length() - 1);
        switch (unit) {
            case "s":
                return 1;
            case "m":
                return 2;
            case "h":
                return 3;
            default:
                throw new IllegalArgumentException("illegal time unit");
        }
    }

    private int parseSchedulerJobTimeNum(@NonNull String interval) {
        return Integer.parseInt(interval.substring(0, interval.length() - 1));
    }

    private SchedulerJobInfo buildSchedulerJobInfo(MetricsSyncTaskDO metricsSyncTaskDO) {
        SchedulerJobInfo schedulerJobInfo = new SchedulerJobInfo();
        schedulerJobInfo.setJobGroup(JOB_GROUP);
        schedulerJobInfo.setJobName(metricsSyncTaskDO.getName());
        schedulerJobInfo.setTaskType(SCHEDULER_JOB_TASK_TYPE);
        schedulerJobInfo.setTriggerType(SCHEDULER_JOB_SIMPLE_TRIGGER_TYPE);
        schedulerJobInfo.setTimeType(parseSchedulerJobTimeType(metricsSyncTaskDO.getInterval()));
        schedulerJobInfo.setTimeNum(parseSchedulerJobTimeNum(metricsSyncTaskDO.getInterval()));
        schedulerJobInfo.setType(SCHEDULER_JOB_TYPE);
        schedulerJobInfo.setTopicName(JOB_TOPIC_NAME);
        schedulerJobInfo.setConfig(JsonUtils.toJsonString(metricsSyncTaskDO));
        return schedulerJobInfo;
    }


    /**
     * Distributed scheduled task to query all cloudwatch tasks and process those with autoRefreshByTag = true
     */
    @Scheduled(cron = "0 0 */2 * * ?")
    public void autoRefreshCloudwatchTask() {
        if (!enableAutoRefreshTask) {
            return;
        }

        // Try to acquire distributed lock
        boolean success = redisService.setRedisLock(AUTO_REFRESH_CLOUDWATCH_TASK_KEY, IpUtils.getHost(), AUTO_REFRESH_CLOUDWATCH_TASK_EXPIRE_MINUTES);
        if (!success) {
            return;
        }

        long start = System.currentTimeMillis();
        log.info("start auto refresh cloudwatch task");

        try {
            doAutoRefreshCloudwatchTask();
        } catch (Exception e) {
            log.error("auto refresh cloudwatch task error", e);
        } finally {
            log.info("end auto refresh cloudwatch task, cost {}ms", System.currentTimeMillis() - start);
        }
    }

    /**
     * Execute the specific logic for auto-refreshing cloudwatch tasks
     */
    public void doAutoRefreshCloudwatchTask() {
        // Query all cloudwatch type tasks
        List<MetricsSyncTaskDO> cloudwatchTasks = metricsSyncTaskDAO.getAllEnabledTasks(TaskType.cloudwatch.name());
        log.info("found {} cloudwatch tasks", cloudwatchTasks.size());

        // Iterate through all tasks and check the autoRefreshByTag field
        for (MetricsSyncTaskDO task : cloudwatchTasks) {
            try {
                processAutoRefreshTask(task);
            } catch (Exception e) {
                log.error("process auto refresh task failed, taskId: {}, taskName: {}", task.getId(), task.getName(), e);
            }
        }
    }

    /**
     * Process a single auto-refresh task
     *
     * @param task Task object
     */
    private void processAutoRefreshTask(MetricsSyncTaskDO task) {
        String taskConfig = task.getTaskConfig();
        if (taskConfig == null || taskConfig.trim().isEmpty()) {
            log.warn("task config is empty, taskId: {}, taskName: {}", task.getId(), task.getName());
            return;
        }

        try {
            // Parse task configuration
            SyncTask.CloudwatchSyncTask syncTask = JsonUtils.toObject(taskConfig, SyncTask.CloudwatchSyncTask.class);
            if (syncTask == null) {
                log.warn("failed to parse task config, taskId: {}, taskName: {}", task.getId(), task.getName());
                return;
            }

            // Check if autoRefreshByTag is true
            if (syncTask.isAutoRefreshByTag()) {
                log.info("found auto refresh task, taskId: {}, taskName: {}, service: {}",
                        task.getId(), task.getName(), task.getService());
                handleAutoRefreshTask(task, syncTask);
            }

        } catch (Exception e) {
            log.error("parse task config failed, taskId: {}, taskName: {}, taskConfig: {}",
                    task.getId(), task.getName(), taskConfig, e);
        }
    }

    /**
     * Handle the specific business logic for auto-refresh tasks
     * This method is left for users to implement themselves
     *
     * @param task           Task object
     * @param syncTask       Parsed task configuration
     */
    private void handleAutoRefreshTask(MetricsSyncTaskDO task, SyncTask.CloudwatchSyncTask syncTask) {

        log.info("handling auto refresh task: taskId={}, taskName={}, service={}, namespace={}, metricType={}",
                task.getId(), task.getName(), task.getService(),
                syncTask.getNamespace(), syncTask.getMetricType());

        SyncTask.DimensionSetting dimensionSetting = syncTask.getDimensionSetting();
        if (dimensionSetting == null || dimensionSetting.getRegions() == null || dimensionSetting.getTags() == null) {
            log.error("need to configure searchTags for auto refresh task: taskId={}, taskName={}, service={}, namespace={}, metricType={}", task.getId(), task.getName(), task.getService(),
                    syncTask.getNamespace(), syncTask.getMetricType());
            return;
        }
        MetricsSyncTemplateDO metricTypeTemplateDO = metricsSyncTemplateDAO.getByName(syncTask.getMetricType());
        if (metricTypeTemplateDO == null) {
            log.error("could not find metric template for metric type: {}",  syncTask.getMetricType());
            return;
        }
        MetricTypeTemplate metricTypeTemplate = JsonUtils.toObject(metricTypeTemplateDO.getTemplate(), MetricTypeTemplate.class);
        CloudwatchSearchByTagsQuery query = new CloudwatchSearchByTagsQuery();
        query.setTags(dimensionSetting.getTags());
        query.setRegionList(dimensionSetting.getRegions());
        query.setResourceType(metricTypeTemplate.getResourceType());
        query.setAssumeRole(syncTask.getAssumeRole());
        Map<String, List<String>> regionResources = cloudwatchTaskParserService.getResources(query, false);
        String resourceDimension = metricTypeTemplate.getResourceDimension();
        List<SyncTask.DimensionItem> dimensionList = Lists.newArrayList();
        Set<String> originalDimensionSet = syncTask.getDimensionList().stream()
                .map(dimension -> dimension.getRegion() + CommonSplitConstants.UNDER_SCORE_SPLIT + dimension.getDimensionGroup().get(resourceDimension))
                .collect(Collectors.toSet());
        regionResources.forEach((region, resourceDimensions) -> {
            resourceDimensions.forEach(resource -> {
                if (originalDimensionSet.contains(region + CommonSplitConstants.UNDER_SCORE_SPLIT + resource)) {
                    return;
                }
                Map<String, String> dimensionMap = Maps.newHashMap();
                SyncTask.DimensionItem dimensionItem = new SyncTask.DimensionItem();
                dimensionItem.setRegion(region);
                dimensionItem.setService(task.getService());
                dimensionItem.setDimensionGroup(dimensionMap);
                dimensionMap.put(resourceDimension, resource);
                dimensionList.add(dimensionItem);
            });
        });
        if (dimensionList.isEmpty()) {
            return;
        }
        syncTask.setDimensionList(dimensionList);
        MetricsSyncTaskDO newTask = task.cloneOne();
        newTask.setTaskConfig(JsonUtils.toJsonString(syncTask));
        newTask.setLastModifiedUser(CUBE);
        newTask.setGmtModify(new Date());
        modifySchedulerJob(newTask, task);
    }
}
