package us.zoom.cube.site.lib.dto.rca.jira;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;


@Data
public class Fields {

    @JsonProperty("customfield_17542")
    private List<CustomField> environment;
    @JsonProperty("customfield_17593")
    private List<CustomField> productType;
    @JsonProperty("customfield_16286")
    private List<CustomField> serviceOfProductImpact;
    @JsonProperty("customfield_16283")
    private CustomField failureType;
    @JsonProperty("customfield_16284")
    private CustomField rootCause;
    @JsonProperty("customfield_16274")
    private CustomField rootCauseClass;
    @JsonProperty("customfield_16282")
    private CustomField ownerTeam;
    @JsonProperty("customfield_16275")
    private CustomField sevLevel;
    @JsonProperty("customfield_17365")
    private CustomField rcaOwner;
    @JsonProperty("customfield_13244")
    private CustomField zoomModule;
    @JsonProperty("customfield_17424")
    private CustomField serviceEngineerTicket;
    private Status status;
    private String summary;
    private Assignee assignee;
    @JsonProperty("customfield_15504")
    private String assigneeManager;
    @JsonProperty("customfield_15503")
    private String managerPlus;
    private Reporter reporter;
    @JsonProperty("customfield_15917")
    private CustomField rcaReviewed;
    @JsonProperty("customfield_17622")
    private CustomField nocInternalReviewer;
    @JsonProperty("customfield_17486")
    private CustomField impact;
    private String dueDate;
    @JsonProperty("customfield_12750")
    private String startDate;
    @JsonProperty("customfield_17711")
    private CustomField rcaRegionReviewed;
    private List<String> labels;
    @JsonProperty("customfield_17371")
    private String rcaWikiLink;
    @JsonProperty("created")
    private String created;
}