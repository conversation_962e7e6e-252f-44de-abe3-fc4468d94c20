package us.zoom.cube.site.biz.agent;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.agent.AgentHeartInfo;
import us.zoom.cube.lib.agent.AgentHeartMonitorTypeEnum;
import us.zoom.cube.lib.agent.AgentMsgTypeEnum;
import us.zoom.cube.sdk.util.MonitorLogReporter;
import us.zoom.cube.site.biz.syspara.AlarmParaService;
import us.zoom.cube.site.infra.AsyncMQInstance;
import us.zoom.infra.model.alarm.AgentKeepAliveConfigModel;
import us.zoom.infra.model.alarm.KeepAliveLevel;
import us.zoom.infra.monitor.AgentHeartMonitor;
import us.zoom.infra.thread.CacheLoaderScheduler;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.mq.common.Result;
import us.zoom.mq.common.client.task.Task;
import us.zoom.mq.common.response.ProduceResult;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * Send keep-alive message for agent heart
 *
 * @author: canyon.li
 * @date: 2024/06/18
 **/
@Component
@Slf4j
public class AgentKeepAliveSendService {

    private static final int INITIAL_DELAY_IN_SECONDS = 60;

    private static final int INTERVAL_IN_SECONDS = 60;

    private static final String KEEP_ALIVE = "keepAlive";

    private final Logger monitorLog = LoggerFactory.getLogger("Monitor");
    @Autowired
    private AlarmParaService alarmParaService;

    @PostConstruct
    public void send() {
        CacheLoaderScheduler.getInstance().getScheduler().scheduleAtFixedRate(
                this::sendKeepAliveMsg, INITIAL_DELAY_IN_SECONDS, INTERVAL_IN_SECONDS, TimeUnit.SECONDS);
    }

    public void sendKeepAliveMsg() {
        AgentKeepAliveConfigModel config = alarmParaService.getAgentKeepAliveConfig();
        List<String> topicList = config.getTopicList();
        if (CollectionUtils.isEmpty(topicList)) {
            log.info("agentKeepAliveConfig sysParam is Empty.");
            return;
        }
        String level = config.getLevel();
        for (String topic : topicList) {
            try {
                if (KeepAliveLevel.topic.name().equals(level)) {
                    //1. prepare keepAlive heartInfo model.
                    AgentHeartInfo agentHeartInfo = new AgentHeartInfo();
                    agentHeartInfo.setMsgType(AgentMsgTypeEnum.keepAlive.name());
                    agentHeartInfo.setTs(System.currentTimeMillis());
                    //2. send keepAlive message.
                    Task<String> task = createTask(agentHeartInfo, topic);
                    Result<ProduceResult> result = AsyncMQInstance.getInstance().getProducer().sendSync(task);
                    if (result == null || !result.isSuccess()) {
                        MonitorLogReporter.report(monitorLog, new AgentHeartMonitor(AgentHeartMonitorTypeEnum.sendKeepAlive.name(), topic, null, 0, null,
                                Optional.ofNullable(result).map(Result::getReason).orElse(null)));
                    } else {
                        MonitorLogReporter.report(monitorLog, new AgentHeartMonitor(AgentHeartMonitorTypeEnum.sendKeepAlive.name(), topic, null, 1, null));
                    }
                } else if (KeepAliveLevel.partition.name().equals(level)) {
                    //TODO send with partition level
                }
            } catch (Exception e) {
                MonitorLogReporter.report(monitorLog, new AgentHeartMonitor(AgentHeartMonitorTypeEnum.sendKeepAlive.name(), topic, null, 0, e.getMessage()));
            }
        }
    }

    private Task<String> createTask(AgentHeartInfo agentHeartInfo, String topicName) {
        Task<String> task = new Task<>();
        task.setTopicName(topicName);
        task.setTaskType(KEEP_ALIVE);
        task.setPayload(JsonUtils.toJsonString(agentHeartInfo));
        return task;
    }

}
