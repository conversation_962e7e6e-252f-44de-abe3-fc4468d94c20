package us.zoom.cube.site.lib.input.outage;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import us.zoom.cube.site.lib.BasePara;

import java.util.Date;
import java.util.List;

@Data
public class OutageEditInput extends BasePara {
   private String id;
   private String title;
   /**
    * @see us.zoom.cube.site.lib.outage.OutageLevelEnum
    */
   private String level;
   /**
    * @see us.zoom.cube.site.lib.outage.OutageStatusEnum
    */
   private String status;
   private String summary;
   private String customerImpact;
   private String rootCause;
   private String doWell;
   private String doPoorly;
   /**
    * Rich text comment content
    */
   private List<OutageCommentInput> outageCommentInputs;
   private String serviceIds;
   private String outageHandler;
   /**
    * @see us.zoom.cube.site.lib.outage.OutageTypeEnum
    */
   private String type;
   private String impactedService;
   private boolean customerImpactFlag;
   private String detectDesc;
   private String detectChannel;
   private String recoveryMode;
   private boolean coverByTa;
   private String earlyDetectedBy;
   @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
   private Date modifyTime;
   @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
   private Date startTime;
   @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
   private Date detectTime;
   @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
   private Date processTime;
   @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
   private Date recoveryTime;
   private String creator;
   private String lastModifiedBy;
   private String fiveWhy;
   private List<OutageLinkInput> outageLinks;
   private List<OutageOwnerInput> outageOwners;
}
