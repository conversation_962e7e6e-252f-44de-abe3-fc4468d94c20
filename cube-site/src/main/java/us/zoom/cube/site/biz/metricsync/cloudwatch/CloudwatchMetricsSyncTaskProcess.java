package us.zoom.cube.site.biz.metricsync.cloudwatch;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import us.zoom.cube.lib.common.ProbeTaskStatusEnum;
import us.zoom.cube.site.biz.metricsync.MetricsSyncTaskProcess;
import us.zoom.cube.site.biz.metricsync.TaskType;
import us.zoom.cube.site.lib.query.CloudwatchSearchByTagsQuery;
import us.zoom.infra.dao.model.MetricsSyncTaskDO;
import us.zoom.infra.dao.model.MetricsSyncTemplateDO;
import us.zoom.infra.dao.service.MetricsSyncTaskDAO;
import us.zoom.infra.dao.service.MetricsSyncTemplateDAO;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.infra.redis.RedisService;
import us.zoom.infra.utils.IpUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import us.zoom.cube.site.biz.metricsync.cloudwatch.SyncTask.*;

import jakarta.annotation.Resource;

/**
 * @author: tobey.zhu
 * @date: /2023/06/07
 * @description:
 */
@Component
@Slf4j
public class CloudwatchMetricsSyncTaskProcess implements MetricsSyncTaskProcess {
    private static final ObjectMapper mapper = new ObjectMapper();
    private static final int maxResourcePerTask = 100;

    @Resource
    private MetricsSyncTemplateDAO metricsSyncTemplateDAO;

    public List<MetricsSyncTaskDO> split(MetricsSyncTaskDO metricsSyncTaskDO) {
        List<MetricsSyncTaskDO> subTaskList = new ArrayList<>();
        CloudwatchSyncTask cloudwatchSyncTask = JsonUtils.toObject(metricsSyncTaskDO.getTaskConfig(), CloudwatchSyncTask.class);
        List<List<DimensionItem>> subList = Lists.partition(cloudwatchSyncTask.getDimensionList(), maxResourcePerTask);
        int index = 1;
        for (List<DimensionItem> subDimensionList : subList) {
            MetricsSyncTaskDO subTask = metricsSyncTaskDO.cloneOne();
            subTask.setName(subTask.getName());
            if (subList.size() > 1) {
                subTask.setName(subTask.getName() + "_" + index);
            }
            cloudwatchSyncTask.setDimensionList(subDimensionList);
            subTask.setTaskConfig(JsonUtils.toJsonString(cloudwatchSyncTask));
            subTaskList.add(subTask);
            index++;
        }
        return subTaskList;
    }

    public String process(String service, String taskConfig) {
        return foreground2Scheduler(service, taskConfig);
    }

    public String unProcess(String taskConfig) {
        return scheduler2Foreground(taskConfig);
    }

    public MetricTypeTemplate getMetricTypeTemplate(String metricName) {
        MetricsSyncTemplateDO metricsSyncTemplateDO = metricsSyncTemplateDAO.getByName(metricName);
        return JsonUtils.toObject(metricsSyncTemplateDO.getTemplate(), MetricTypeTemplate.class);
    }

    public String foreground2Scheduler(String service, String taskConfig) {
        ForegroundTask foregroundTask = JsonUtils.toObject(taskConfig, ForegroundTask.class);
        MetricTypeTemplate metricTypeTemplate = getMetricTypeTemplate(foregroundTask.getMetricType());
        if (metricTypeTemplate == null) {
            throw new IllegalArgumentException("no template found");
        }
        foregroundTask.isLegal(metricTypeTemplate);
        foregroundTask.trim();
        return foreground2Scheduler(service, metricTypeTemplate, foregroundTask);
    }

    public String foreground2Scheduler(String service, MetricTypeTemplate metricTypeTemplate, ForegroundTask foregroundTask) {
        try {
            List<SyncTask.MetricsItem> metricsList = new ArrayList<>();
            for (String metric : foregroundTask.getMetricList()) {
                metricsList.add(SyncTask.MetricsItem.builder()
                        .metricsName(metric)
                        .period(foregroundTask.getPeriod())
                        .statList(SyncTask.MetricsItem.getDefaultStatList())
                        .build());
            }
            List<SyncTask.DimensionItem> dimensionList = new ArrayList<>();
            for (Map<String, String> dimensionGroup : foregroundTask.getDimensionList()) {
                String region = dimensionGroup.get(CloudwatchConstant.reservedFieldRegion);
                String zmCluster = dimensionGroup.get(CloudwatchConstant.reservedFieldZmCluster);
                String zmRegion = dimensionGroup.get(CloudwatchConstant.reservedFieldZmRegion);
                dimensionGroup.remove(CloudwatchConstant.reservedFieldRegion);
                dimensionGroup.remove(CloudwatchConstant.reservedFieldZmCluster);
                dimensionGroup.remove(CloudwatchConstant.reservedFieldZmRegion);
                dimensionList.add(SyncTask.DimensionItem.builder()
                        .service(service)
                        .region(region)
                        .zmCluster(zmCluster)
                        .zmRegion(zmRegion)
                        .dimensionGroup(dimensionGroup)
                        .build());
            }
            DimensionSetting dimensionSetting = buildDimensionSetting(metricTypeTemplate.getResourceType(), foregroundTask);
            SyncTask.CloudwatchSyncTask cloudwatchSyncTask = SyncTask.CloudwatchSyncTask.builder()
                    .metricsList(metricsList)
                    .namespace(foregroundTask.getNamespace())
                    .metricType(foregroundTask.getMetricType())
                    .assumeRole(foregroundTask.getAssumeRole())
                    .autoRefreshByTag(foregroundTask.isAutoRefreshByTag())
                    .resourceDimension(metricTypeTemplate.getResourceDimension())
                    .dimensionList(dimensionList)
                    .dimensionSetting(dimensionSetting)
                    .delay(metricTypeTemplate.getDelay())
                    .build();
            return mapper.writeValueAsString(cloudwatchSyncTask);
        } catch (Throwable t) {
            throw new IllegalArgumentException("parse foreground task failed");
        }
    }

    private DimensionSetting buildDimensionSetting(String resourceType, ForegroundTask foregroundTask) {
        ForegroundTask.SearchTags searchTags = foregroundTask.getSearchTags();
        if (searchTags == null ||
                searchTags.getTagList() == null ||
                searchTags.getRegionList() == null) {
            return null;
        }
        DimensionSetting dimensionSetting = new DimensionSetting();
        dimensionSetting.setResourceType(resourceType);
        dimensionSetting.setRegions(searchTags.getRegionList());
        dimensionSetting.setTags(searchTags.getTagList());
        return dimensionSetting;
    }

    public String scheduler2Foreground(String taskConfig) {
        CloudwatchSyncTask cloudwatchSyncTask = JsonUtils.toObject(taskConfig, CloudwatchSyncTask.class);
        return scheduler2Foreground(cloudwatchSyncTask);
    }

    public String scheduler2Foreground(CloudwatchSyncTask cloudwatchSyncTask) {
        try {
            List<String> metricsList = new ArrayList<>();
            for (SyncTask.MetricsItem metricsItem : cloudwatchSyncTask.getMetricsList()) {
                metricsList.add(metricsItem.getMetricsName());
            }
            List<Map<String, String>> dimensionList = new ArrayList<>();
            for (SyncTask.DimensionItem dimensionItem : cloudwatchSyncTask.getDimensionList()) {
                dimensionItem.getDimensionGroup().put(CloudwatchConstant.reservedFieldRegion, dimensionItem.getRegion());
                dimensionItem.getDimensionGroup().put(CloudwatchConstant.reservedFieldZmCluster, dimensionItem.getZmCluster());
                dimensionItem.getDimensionGroup().put(CloudwatchConstant.reservedFieldZmRegion, dimensionItem.getZmRegion());
                dimensionList.add(dimensionItem.getDimensionGroup());
            }

            ForegroundTask.SearchTags searchTags = null;
            DimensionSetting dimensionSetting = cloudwatchSyncTask.getDimensionSetting();
            if (dimensionSetting != null) {
                searchTags = new ForegroundTask.SearchTags();
                searchTags.setRegionList(dimensionSetting.getRegions());
                searchTags.setTagList(dimensionSetting.getTags());
            }
            ForegroundTask foregroundTask = ForegroundTask.builder()
                    .metricType(cloudwatchSyncTask.getMetricType())
                    .period(cloudwatchSyncTask.getMetricsList().get(0).getPeriod())
                    .metricList(metricsList)
                    .dimensionList(dimensionList)
                    .autoRefreshByTag(cloudwatchSyncTask.isAutoRefreshByTag())
                    .metricType(cloudwatchSyncTask.getMetricType())
                    .namespace(cloudwatchSyncTask.getNamespace())
                    .assumeRole(cloudwatchSyncTask.getAssumeRole())
                    .searchTags(searchTags)
                    .build();
            return mapper.writeValueAsString(foregroundTask);
        } catch (Throwable t) {
            throw new IllegalArgumentException("parse cloudwatchSyncTask task failed");
        }
    }
}
