package us.zoom.cube.site.biz.alarm.group;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.core.SysParaHandler;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.core.UserHandler;
import us.zoom.cube.site.core.alarm.group.AlarmGroupHandler;
import us.zoom.cube.site.lib.common.MonitorTypeEnum;
import us.zoom.cube.site.lib.input.alarm.group.AlarmGroupAlarmItemInput;
import us.zoom.cube.site.lib.input.alarm.group.AlarmGroupInput;
import us.zoom.cube.site.lib.input.alarm.group.AlarmGroupServiceItemInput;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.service.DistributedLockDAO;
import us.zoom.infra.enums.SysParaEnums;
import us.zoom.infra.thread.NamedThreadFactory;
import us.zoom.infra.utils.CommonSplitConstants;
import us.zoom.infra.utils.DateUtils;
import us.zoom.infra.utils.IpUtils;
import us.zoom.cube.lib.utils.JsonUtils;

import jakarta.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import static us.zoom.infra.utils.AlarmGroupConstants.ALL_ALARM_ID;
import static us.zoom.infra.utils.AlarmGroupConstants.ALL_ALARM_NAME;

/**
 * <AUTHOR>
 */
@Component
public class AlarmGroupCronService {

    private static ScheduledExecutorService scheduler;
    private Integer scheduleSeconds= 120;
    private int lockTimeOut=(scheduleSeconds/60);

    @Autowired
    private SysParaHandler sysParaHandler;
    @Autowired
    private DistributedLockDAO distributedLockDAO;

    private static TenantHandler tenantHandler;

    @Autowired
    private UserHandler userHandler;

    private static final Logger LOG= LoggerFactory.getLogger(AlarmGroupCronService.class.getName());

    @Autowired
    private AlarmGroupService alarmGroupService;

    @Autowired
    private AlarmGroupHandler alarmGroupHandler;

    private Logger monitorLog = LoggerFactory.getLogger("Monitor");


    public static final  String ALARM_GROUP_MANAGE_ASSIGNER="alarm_group_manage_assigner";

    public static final String defaultWholeServiceAlarmGroupName = "whole_service";

    @PostConstruct
    public void init(){
        scheduler= Executors.newScheduledThreadPool(1,new NamedThreadFactory("AlarmGroup cron threadpool"));
        scheduler.scheduleWithFixedDelay(new Runnable() {
            @Override
            public void run() {
                try{
                    manageAlarmGroup();
                }catch (Exception e){
                    LOG.error("manageAlarmGroup error",e);
                }
            }
        },60,scheduleSeconds, TimeUnit.SECONDS);
    }

    @Autowired
    public void setTenantHandler(TenantHandler tenantHandler){
        AlarmGroupCronService.tenantHandler = tenantHandler;
    }

    private static final String manageAlarmGroupSysType="manageAlarmGroupCron";
    private void manageAlarmGroup() {
        LOG.info("begin manageAlarmGroup!");
        if(!lock()){
            LOG.info("do not get the manageAlarmGroup lock");
            return;
        }

        alarmGroupHandler.deleteInvalidAlarmItemList();

        List<SysParaDO> sysParaDOS= sysParaHandler.listByTypeAndParaKey(SysParaEnums.alarmGroup.name(), manageAlarmGroupSysType);
        if(CollectionUtils.isEmpty(sysParaDOS)){
            LOG.info("there is no manageAlarmGroupCron para");
            return;
        }

        SysParaDO sysParaDO = sysParaDOS.get(0);
        AlarmGroupCronManagerPara alarmGroupCronManagerPara = JsonUtils.toObject(sysParaDO.getValue(), AlarmGroupCronManagerPara.class);
        List<TenantDO> tenantDOList = alarmGroupCronManagerPara.getNeedCreateAlarmGroupServiceList();
        String operator = alarmGroupCronManagerPara.getOperator();
        UserDO operatorDO = userHandler.getUserByName(operator);
        if(operatorDO == null){
            LOG.info("The AlarmGroupManage operator is not exist");
            return;
        }
        for(TenantDO tenantDO:tenantDOList){
            AlarmGroupInput alarmGroupInput = new AlarmGroupInput();
            try{
                alarmGroupInput.setUserId(operatorDO.getId());
                String serviceName = tenantDO.getName();
                String alarmGroupName = serviceName + CommonSplitConstants.SPLIT + defaultWholeServiceAlarmGroupName;
                AlarmGroupDO alarmGroupDO = alarmGroupHandler.findByName(alarmGroupName);
                if(alarmGroupDO != null){
                    return;
                }
                alarmGroupInput.setName(alarmGroupName);
                List<AlarmGroupServiceItemInput> serviceItemList = new ArrayList<>();
                alarmGroupInput.setServiceItemList(serviceItemList);
                AlarmGroupServiceItemInput alarmGroupServiceItemInput = new AlarmGroupServiceItemInput();
                alarmGroupServiceItemInput.setServiceId(tenantDO.getId());
                alarmGroupServiceItemInput.setServiceName(tenantDO.getName());
                serviceItemList.add(alarmGroupServiceItemInput);

                List<AlarmGroupAlarmItemInput> alarmItemList = new ArrayList<>();
                alarmGroupServiceItemInput.setAlarmItemList(alarmItemList);
                AlarmGroupAlarmItemInput alarmGroupAlarmItemInput = new AlarmGroupAlarmItemInput();
                alarmItemList.add(alarmGroupAlarmItemInput);
                alarmGroupAlarmItemInput.setAlarmId(ALL_ALARM_ID);
                alarmGroupAlarmItemInput.setAlarmName(ALL_ALARM_NAME);

                alarmGroupInput.setAuthResourceUrl("/api/alarm/alarmGroup/addAlarmGroup");
                alarmGroupService.addAlarmGroup(alarmGroupInput);

            }catch (Exception e){
                LOG.error("create AlarmGroup failed",e);
            }finally {
                alarmGroupInput.setUserId(null);
                alarmGroupInput.setAuthResourceUrl(null);
            }
        }
        LOG.info("end manageAlarmGroup!");
    }

    private void printMonitor(Map<String,Object> logMap){
        logMap.put("type", MonitorTypeEnum.AlarmGroupCron.name());
        logMap.put("ts",System.currentTimeMillis());
        logMap.put("timeShow", DateUtils.format(new Date(),DateUtils.FORMART1));
        monitorLog.info(JsonUtils.toJsonStringIgnoreExp(logMap));
    }

    public boolean lock() {
        List<DistributedLockDO> distributedLocks=distributedLockDAO.listByType(ALARM_GROUP_MANAGE_ASSIGNER);
        if(CollectionUtils.isEmpty(distributedLocks)){
            return false;
        }
        return  distributedLockDAO.lockWithType(ALARM_GROUP_MANAGE_ASSIGNER,distributedLocks.get(0).getHandler(), IpUtils.getLocalIP(),lockTimeOut)>0;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class AlarmGroupCronManagerPara{
        private String operator;
        private String needCreateAlarmGroupServices;
        private Boolean needCreateForAllService = false;
        List<TenantDO> getNeedCreateAlarmGroupServiceList(){
            List<TenantDO> serviceList = new ArrayList<>();
            if(needCreateForAllService){
                serviceList = tenantHandler.getAllTenantFromCache();
                return serviceList;
            }

            if(StringUtils.isNotEmpty(needCreateAlarmGroupServices)){
                List<String> serviceNameList = Arrays.asList(needCreateAlarmGroupServices.split(","));
                for(String serviceName: serviceNameList){
                    TenantDO tenantDO = tenantHandler.getTenantByNameInLowerCaseFromCache(serviceName);
                    if(tenantDO != null){
                        serviceList.add(tenantDO);
                    }
                }
                return serviceList;
            }
            return serviceList;
        }
    }


}


