package us.zoom.cube.site.lib.input;

import com.okta.commons.lang.Collections;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import us.zoom.cube.site.lib.BasePara;
import us.zoom.cube.site.lib.output.config.metrics.FieldOrTag;
import us.zoom.infra.enums.ExpressionCheckTypeEnum;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @author: canyon.li
 * @date: 2023/04/19
 **/
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ExpressionCheckInput extends BasePara {

    private static final String COMMON_REGEX = "\\$\\{(.*?)}";

    String text;

    List<Map<String, String>> params;

    Map<String, Object> convertedParams;

    /**
     * input type, content/expression
     */
    String type;

    List<FieldOrTag> data;

    public void adjust() {

        //if not contains placeholder ${}, text will be considered an expression
        if (StringUtils.isEmpty(type)) {
            Pattern pattern = Pattern.compile(COMMON_REGEX);
            Matcher matcher = pattern.matcher(text);
            type = matcher.find() ? ExpressionCheckTypeEnum.content.name() : ExpressionCheckTypeEnum.expression.name();
        }

        if (Objects.isNull(params)) {
            params = new ArrayList<>(1);
            return ;
        }

        convertedParams = new HashMap<>();
        for (Map<String, String> entry : params) {
            String name = entry.get("name");
            String value = entry.get("value");
            String type = entry.get("type");
            Assert.notNull(value, "Value fields cannot be blank");

            if ("number".equals(type)) {
                try {
                    convertedParams.put(name, Double.parseDouble(value));
                } catch (NumberFormatException e) {
                    convertedParams.put(name, value);
                }
            } else if ("mapString".equals(type) || "mapNumber".equals(type)) {
                try {
                    convertedParams.put(name, JsonUtils.toObject(value, Map.class));
                } catch (Exception e) {
                    throw new IllegalArgumentException(String.format(
                            "Field '%s' is defined as map type, but the provided value is not valid JSON format: '%s'. " +
                                    "Please provide a valid JSON map (e.g., {\"key\": \"value\"})",
                            name, value));
                }
            } else {
                convertedParams.put(name, value);
            }
        }
    }
}
