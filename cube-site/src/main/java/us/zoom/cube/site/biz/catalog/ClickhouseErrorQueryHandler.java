package us.zoom.cube.site.biz.catalog;

import com.fasterxml.jackson.core.type.TypeReference;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.site.core.config.ClickhouseHandlerFactory;
import us.zoom.cube.site.infra.utils.ConvertUtils;
import us.zoom.cube.site.infra.utils.TemplateUtils;
import us.zoom.cube.site.lib.dto.catalog.ErrorQueryConfig;
import us.zoom.cube.site.lib.dto.catalog.ErrorQueryParam;
import us.zoom.cube.site.lib.output.catalog.ErrorInfo;
import us.zoom.cube.site.lib.output.catalog.ErrorQueryResult;
import us.zoom.infra.clickhouse.ClickhouseSqlUtil;
import us.zoom.infra.syspara.SysParaEventService;
import us.zoom.infra.thread.ThreadLocalStore;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static us.zoom.cube.site.infra.constants.catalog.ServiceCatalogConstant.SERVICE_CATALOG;

/**
 * Abstract base class for Clickhouse-based error query handlers
 * Implements template pattern to reduce code duplication
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class ClickhouseErrorQueryHandler implements ErrorQueryHandler {

    @Autowired
    protected ClickhouseHandlerFactory clickhouseHandlerFactory;

    // Configuration cache
    protected ErrorQueryConfig config;

    @PostConstruct
    public void init() {
        SysParaEventService.registerListenerBySpecificSysPara(sysParaDO -> {
            try {
                config = JsonUtils.toObjectByTypeRef(sysParaDO.getValue(), new TypeReference<ErrorQueryConfig>() {
                });
            } catch (Throwable e) {
                log.error("deserialize config for {} error, value: {}", getConfigKey(), sysParaDO.getValue(), e);
            }
        }, SERVICE_CATALOG, getConfigKey());
    }

    @Override
    public ErrorQueryResult queryErrors(ErrorQueryParam param) {
        log.info("Querying errors for {} error scope, region: {}, cluster: {}",
                getSupportedErrorScope().getValue(), param.getRegion(), param.getCluster());

        try {
            if (config == null) {
                log.error("Query errors error, configuration not loaded for {}", getSupportedErrorScope().getValue());
                return ErrorQueryResult.builder()
                        .total(0)
                        .items(Collections.emptyList())
                        .build();
            }
            // Query total count
            String actualDatabaseName = config.getFixedDatabaseName() == null ? ThreadLocalStore.getTenantNameLocal() : config.getFixedDatabaseName();

            String countSql = QueryParamUtils.generateSql(replacePlaceholders(config.getErrorCountQuerySql(), param, ThreadLocalStore.getTenantNameLocal()), actualDatabaseName, param);
            List<Map<String, Object>> countResults = clickhouseHandlerFactory.get().query(actualDatabaseName, countSql);
            int total = 0;
            if (!countResults.isEmpty()) {
                Object countValue = countResults.get(0).get("count");
                total = countValue != null ? Integer.parseInt(countValue.toString()) : 0;
            }

            // Query error items
            String itemsSql = QueryParamUtils.generateSql(replacePlaceholders(config.getErrorItemsQuerySql(), param, ThreadLocalStore.getTenantNameLocal()), actualDatabaseName, param);
            List<Map<String, Object>> itemResults = clickhouseHandlerFactory.get().query(actualDatabaseName, itemsSql);

            List<ErrorInfo> errorItems = new ArrayList<>();
            for (Map<String, Object> row : itemResults) {
                ErrorInfo errorInfo = ConvertUtils.mapToBean(row, ErrorInfo.class);
                errorInfo.setExtraTags(extractExtraTags(row));
                errorItems.add(errorInfo);
            }

            log.info("Retrieved {} error items for {} error scope", errorItems.size(), getSupportedErrorScope().getValue());
            return ErrorQueryResult.builder()
                    .total(total)
                    .items(errorItems)
                    .build();
        } catch (Exception e) {
            log.error("Error querying errors for {}", getSupportedErrorScope().getValue(), e);
            return ErrorQueryResult.builder()
                    .total(0)
                    .items(Collections.emptyList())
                    .build();
        }
    }

    /**
     * Extract extra tags from the query result row
     *
     * @param row The query result row
     * @return Map of extra tags
     */
    protected Map<String, Object> extractExtraTags(Map<String, Object> row) {
        // This method can be overridden by subclasses to extract specific extra tags
        // For now, return empty map as default implementation
        if (CollectionUtils.isEmpty(config.getExtraTags())) {
            return Collections.emptyMap();
        }
        return config.getExtraTags().stream().filter(row::containsKey).collect(Collectors.toMap(Function.identity(), row::get));
    }

    /**
     * Get the configuration key for this handler
     *
     * @return configuration key
     */
    protected abstract String getConfigKey();

    /**
     * Fallback method for simple placeholder replacement when genSql fails
     *
     * @param sqlTemplate The SQL template with placeholders
     * @param param       The error query parameters
     * @param database    The database name
     * @return SQL with placeholders replaced
     */
    private String replacePlaceholders(String sqlTemplate, ErrorQueryParam param, String database) {
        Map<String, Object> params = new HashMap<>();
        
        // Replace database placeholder
        params.put("database", ClickhouseSqlUtil.encodeClickhouseName(database));
        
        // Replace pagination placeholders
        if (param.getPageSize() != null && param.getPageIndex() != null) {
            params.put("limit", param.getPageSize());
            int offset = (param.getPageIndex() - 1) * param.getPageSize();
            params.put("offset", offset);
        }

        // Replace filters placeholder
        String filterClause = QueryParamUtils.buildFilterClause(param.getFilters());
        if (StringUtils.equals(filterClause, StringUtils.EMPTY)) {
            filterClause = " AND 1=1";
        }
        params.put("filters", filterClause);

        return TemplateUtils.process(sqlTemplate, params);
    }
} 