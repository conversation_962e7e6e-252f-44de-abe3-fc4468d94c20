package us.zoom.cube.site.api.web.smartcube;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import us.zoom.cube.site.lib.input.smartcube.SmartAgentInput;
import us.zoom.infra.thread.ThreadLocalStore;

@RestController
@RequestMapping("/api/smart/cube")
@Slf4j
@RequiredArgsConstructor
public class SmartCubeProxyController {

    @Qualifier("smartCubeChatClient")
    private final WebClient smartCubeChatClient;

    @Qualifier("smartCubeCommonlClient")
    private final WebClient smartCubeGeneralClient;

    /**
     * Streaming chat API - returns stream of responses
     */
    @PostMapping(value = "/stream/v1/chat/completions", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public ResponseEntity<Flux<String>> chatStream(@RequestBody SmartAgentInput smartAgentDO) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.set("X-Accel-Buffering", "no");
            headers.set("Cache-Control", "no-cache");
            return ResponseEntity
                    .ok()
                    .headers(headers)
                    .body(smartCubeChatClient.post()
                            .uri("/api/smart/cube/stream/v1/chat/completions")
                            .contentType(MediaType.APPLICATION_JSON)
                            .accept(MediaType.TEXT_EVENT_STREAM)
                            .bodyValue(JSON.toJSONString(smartAgentDO))
                            .retrieve()
                            .bodyToFlux(String.class)
                            .doOnNext(response -> {
                                // Collect each response data
                                log.debug("Received streaming response: {}", response);
                            })
                            .doOnError(error -> {
                                log.error("WebClient request failed: {}", error.getMessage());
                            })
                            .doOnComplete(() -> {
                                log.info("WebClient request completed");
                            }));
        } catch (Exception e) {
            log.error("Exception occurred while processing chat request: {}", e.getMessage(), e);
            return ResponseEntity.ok(Flux.error(e));
        }
    }

    /**
     * Non-streaming chat API - returns complete response
     */
    @PostMapping(value = "/**", produces = MediaType.APPLICATION_JSON_VALUE)
    public Mono<?> proxyPost(@RequestBody @Valid JSONObject jsonObject, HttpServletRequest httpServletRequest) {
        try {
            String userName = ThreadLocalStore.getUserNameLocal();
            jsonObject.put("userName", userName);
            String requestURI = httpServletRequest.getRequestURI();
            return smartCubeGeneralClient.post()
                    .uri(requestURI)
                    .contentType(MediaType.APPLICATION_JSON)
                    .bodyValue(jsonObject.toJSONString())
                    .retrieve()
                    .bodyToMono(Object.class)
                    .doOnSuccess(response -> {
                        log.info("Non-streaming chat request completed successfully");
                    })
                    .doOnError(error -> {
                        log.error("Non-streaming WebClient request failed: {}", error.getMessage());
                    });
        } catch (Exception e) {
            log.error("Exception occurred while processing non-streaming chat request: {}", e.getMessage(), e);
            return Mono.error(e);
        }
    }


}
