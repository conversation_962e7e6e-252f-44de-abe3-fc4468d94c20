package us.zoom.cube.site.core;

import com.google.common.base.Splitter;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Sets;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;
import us.zoom.cube.lib.common.CubeConstants;
import us.zoom.cube.sdk.util.MonitorLogReporter;
import us.zoom.cube.site.api.intercept.AuthInterceptor;
import us.zoom.cube.site.biz.dashboard.DashSysParaService;
import us.zoom.cube.site.core.usergroup.UserGroupHandler;
import us.zoom.cube.site.lib.BusinessException;
import us.zoom.cube.site.lib.input.UserInCache;
import us.zoom.cube.site.lib.monitor.LoadDashTreeMonitor;
import us.zoom.cube.site.lib.query.dashtree.*;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.service.DashTreeItemDAO;
import us.zoom.infra.dao.service.DashTreeUserRelaDAO;
import us.zoom.infra.dao.service.UserGroupDAO;
import us.zoom.infra.enums.DashModuleEnum;
import us.zoom.infra.enums.DashTreeItemTypeEnum;
import us.zoom.infra.enums.DashTreeUserRelaTypeEnum;
import us.zoom.infra.model.IdAndName;
import us.zoom.infra.model.dash.DashTree;
import us.zoom.infra.utils.CommonSplitConstants;
import us.zoom.infra.utils.IdUtils;
import us.zoom.infra.utils.Instance;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class DashTreeHandler {
    private final Logger monitorLog = LoggerFactory.getLogger("Monitor");

    // Cache<Id, UserGroupName>
    private final Cache<String, String> userGroupIdNameMap = CacheBuilder.newBuilder().expireAfterWrite(1, TimeUnit.HOURS).build();

    //Map<ItemId, DashTreeItem>
    private final AtomicReference<Map<String, DashTreeItemDO>> itemIdDashItemMapRef = new AtomicReference<>(new HashMap<>());

    // Map<ItemId, UserIdLists> from own owner/user and usergroup, no inheritance
    private final AtomicReference<Map<String, Set<String>>> itemUserMap = new AtomicReference<>(new HashMap<>());

    // Map<ItemId, UserIdLists> from parent owner/user and usergroup
    // filled after call loadDashTree() once, build the hierarchy data from root to leaf
    // inheritItemAllUserMap = inheritItemUserGroupMap + inheritItemUserMap
    private final AtomicReference<Map<String, Set<String>>> inheritItemAllUserMap = new AtomicReference<>(new HashMap<>());

    // Map<ItemId, UserGroupIdLists> only from parent usergroup
    private final AtomicReference<Map<String, Set<String>>> inheritItemUserGroupMap = new AtomicReference<>(new HashMap<>());

    // Map<ItemId, UserGroupIdLists> only from parent owner/user
    private final AtomicReference<Map<String, Set<String>>> inheritItemUserMap = new AtomicReference<>(new HashMap<>());

    private final AtomicBoolean loading = new AtomicBoolean(false);
    private final AtomicBoolean init = new AtomicBoolean(false);
    private static final String FOLDER_TIP = "folder:";
    private static final String DASH_TIP = "dashboard:";

    @Autowired
    private UserHandler userHandler;

    @Autowired
    private DashHandler dashHandler;

    @Autowired
    private DashTreeItemDAO dashTreeItemDAO;

    @Autowired
    private DashTreeUserRelaDAO dashTreeUserRelaDAO;

    @Autowired
    private UserGroupHandler userGroupHandler;

    @Autowired
    private DashSysParaService dashSysParaService;

    @Autowired
    private UserGroupDAO userGroupDAO;

    @Value("${cache-loader.startup.dash-config.enable:true}")
    private Boolean loadHubCfgEnable;

    public List<DashTreeItemDO> listAll() {
        return dashTreeItemDAO.listAll();
    }

    @PostConstruct
    public void init() {
        if (loadHubCfgEnable) {
            loadDashTree();
        }
    }

    public void loadDashTree() {
        LoadDashTreeMonitor.LoadDashTreeMonitorBuilder builder = LoadDashTreeMonitor.builder();

        StopWatch sw = new StopWatch();
        sw.start();

        if (loading.get()) {
            return;
        }

        try {
            loading.getAndSet(true);

            //1.load all the dash item
            List<DashTreeItemDO> allTreeItems = dashTreeItemDAO.listAll();
            sw.stop();
            long lastTaskTimeMillis = sw.getLastTaskTimeMillis();
            builder.costLoadItem(lastTaskTimeMillis);

            if (CollectionUtils.isEmpty(allTreeItems)) {
                builder.phase("no_item");
                builder.status("fail");
                MonitorLogReporter.report(monitorLog, builder.build());
                return;
            }
            sw.start();

            //2.load user/usergroup map
            List<DashTreeUserRelaDO> allTreeUserRela = dashTreeUserRelaDAO.listAll();
            Map<String, List<DashTreeUserRelaDO>> itemIdRelaMap = Instance.ofNullable(allTreeUserRela).stream().filter(x -> x.getTreeItemId() != null).collect(Collectors.groupingBy(DashTreeUserRelaDO::getTreeItemId));
            sw.stop();
            lastTaskTimeMillis = sw.getLastTaskTimeMillis();
            builder.costLoadUser(lastTaskTimeMillis);

            sw.start();
            List<UserGroupUserRelaDO> allUserGroupUserRela = userGroupHandler.getAllUserGroupUserRela();
            Map<String, List<UserGroupUserRelaDO>> groupIdUserMap = Instance.ofNullable(allUserGroupUserRela).stream().collect(Collectors.groupingBy(UserGroupUserRelaDO::getGroupId));
            sw.stop();
            lastTaskTimeMillis = sw.getLastTaskTimeMillis();
            builder.costLoadUserGroup(lastTaskTimeMillis);

            sw.start();
            Map<String, DashTreeItemDO> itemIdMap = new HashMap<>();

            //3.build item/user map, not fill inherit permission data
            allTreeItems.forEach(item -> {
                buildUserRelaMap(itemIdRelaMap, groupIdUserMap, item);
                itemIdMap.put(item.getId(), item);

            });
            sw.stop();
            lastTaskTimeMillis = sw.getLastTaskTimeMillis();
            builder.costBuildRela(lastTaskTimeMillis);

            if (MapUtils.isNotEmpty(itemIdMap)) {
                itemIdDashItemMapRef.set(itemIdMap);
                builder.status("success");
            }

            if (!init.get()) {
                init.set(true);
                getDashTree(new GetDashTreeQuery());
            }

        } catch (Exception e) {
            log.error("load menu tree error ", e);
            builder.status("fail");
            builder.msg(e.getMessage());
            builder.exp(JsonUtils.toJsonStringIgnoreExp(e));
        } finally {
            loading.getAndSet(false);
            builder.cost(sw.getTotalTimeMillis());
            MonitorLogReporter.report(monitorLog, builder.build());
        }

    }


    private void buildUserRelaMap(Map<String, List<DashTreeUserRelaDO>> itemIdRelaMap, Map<String, List<UserGroupUserRelaDO>> groupIdUserMap, DashTreeItemDO item) {
        List<DashTreeUserRelaDO> dashTreeUserRelaDOS = itemIdRelaMap.get(item.getId());
        //build dash tree user/usergroup map
        Map<Integer, List<DashTreeUserRelaDO>> relaTypeUserMap = Instance.ofNullable(dashTreeUserRelaDOS).stream().collect(Collectors.groupingBy(DashTreeUserRelaDO::getRelaType));
        fillUserId(item, relaTypeUserMap);

        List<DashTreeUserRelaDO> dashTreeUserGroupRelaDOList = fillUserRela(item, relaTypeUserMap);

        Set<String> allUser = Sets.newHashSet();
        allUser.addAll(item.getOwnerId().stream().map(IdAndName::getId).collect(Collectors.toList()));
        Instance.ofNullable(dashTreeUserGroupRelaDOList).stream().forEach(x -> {
            allUser.addAll(Instance.ofNullable(groupIdUserMap.get(x.getUserRelaId())).stream().map(UserGroupUserRelaDO::getUserId).collect(Collectors.toList()));
        });

        itemUserMap.get().put(item.getId(), allUser);
    }

    //build item user group and display name
    private List<DashTreeUserRelaDO> fillUserRela(DashTreeItemDO item, Map<Integer, List<DashTreeUserRelaDO>> relaTypeUserMap) {
        List<DashTreeUserRelaDO> dashTreeUserGroupRelaDOList = relaTypeUserMap.get(DashTreeUserRelaTypeEnum.USER_GROUP.getCode());
        List<String> userGroupId = Instance.ofNullable(dashTreeUserGroupRelaDOList).stream().map(DashTreeUserRelaDO::getUserRelaId).collect(Collectors.toList());

        List<String> notInCacheIds = userGroupId.stream().filter(x -> StringUtils.isEmpty(userGroupIdNameMap.getIfPresent(x))).toList();

        Map<String, String> userGroupNameMap = Instance.ofNullable(userGroupDAO.getByIds(notInCacheIds)).stream().collect(Collectors.toMap(UserGroupDO::getId, UserGroupDO::getName));

        List<IdAndName> userGroupIdNames = userGroupId.stream().map(x -> {
            IdAndName in = new IdAndName();

            String userGroupName = this.userGroupIdNameMap.getIfPresent(x);
            if (StringUtils.isEmpty(userGroupName)) {
                userGroupName = userGroupNameMap.get(x);
                if (StringUtils.isNotEmpty(userGroupName)) {
                    userGroupIdNameMap.put(x, userGroupName);
                }
            }

            in.setId(x);
            in.setName(userGroupName);

            return in;
        }).collect(Collectors.toList());
        item.setUserGroupId(userGroupIdNames);

        //build init user group for item
        Set<String> parentUserGroup = inheritItemUserGroupMap.get().get(item.getParentId());
        if (CollectionUtils.isNotEmpty(parentUserGroup)) {
            userGroupId.addAll(parentUserGroup);
        }
        inheritItemUserGroupMap.get().put(item.getId(), Sets.newHashSet(userGroupId));

        return dashTreeUserGroupRelaDOList;
    }

    //build item user list and display name
    private void fillUserId(DashTreeItemDO item, Map<Integer, List<DashTreeUserRelaDO>> relaTypeUserMap) {
        List<DashTreeUserRelaDO> dashTreeUserRelaDOList = relaTypeUserMap.get(DashTreeUserRelaTypeEnum.USER.getCode());
        List<String> userIds = Instance.ofNullable(dashTreeUserRelaDOList).stream().map(DashTreeUserRelaDO::getUserRelaId).collect(Collectors.toList());
        List<IdAndName> collect = userIds.stream().map(x -> {
            IdAndName in = new IdAndName();
            in.setId(x);
            UserInCache userFromCache = userHandler.getUserFromCache(x);
            in.setName(userFromCache != null ? userFromCache.getName() : null);

            return in;
        }).collect(Collectors.toList());
        item.setOwnerId(collect);
    }

    public List<String> getDashTreeIdByDashId(String dashId) {
        List<DashTreeItemDO> dashTreeItemDOS = dashTreeItemDAO.listByDashId(dashId);
        return Instance.ofNullable(dashTreeItemDOS).stream().map(DashTreeItemDO::getId).collect(Collectors.toList());
    }

    public List<String> getParentDashTreeIdByDashId(String dashId) {
        List<DashTreeItemDO> dashTreeItemDOS = dashTreeItemDAO.listByDashId(dashId);
        return Instance.ofNullable(dashTreeItemDOS).stream().map(DashTreeItemDO::getParentId).collect(Collectors.toList());
    }

    public Set<String> getOwnerListInDashTree(List<String> itemIds) {
        return Instance.ofNullable(itemIds).stream().map(x -> inheritItemUserMap.get().get(x)).filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toSet());
    }

    public Set<String> getUserGroupOwnerListInDashTree(List<String> itemIds) {
        return Instance.ofNullable(itemIds).stream().map(x -> inheritItemUserGroupMap.get().get(x)).filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toSet());
    }

    public String getParentDashTreePathByDashId(String dashId) {
        List<String> dashTreeIdByDashId = getDashTreeIdByDashId(dashId);
        return Instance.ofNullable(dashTreeIdByDashId).stream().map(dashTreeItemId -> getDashTreePathByItemId(dashTreeItemId, false)).filter(Objects::nonNull).collect(Collectors.joining(CommonSplitConstants.COMMA_SPLIT));
    }


    public String getDashTreePathByDashId(String dashId) {
        List<String> dashTreeIdByDashId = getDashTreeIdByDashId(dashId);
        return Instance.ofNullable(dashTreeIdByDashId).stream().map(dashTreeItemId -> getDashTreePathByItemId(dashTreeItemId, false)).filter(Objects::nonNull).collect(Collectors.joining(CommonSplitConstants.COMMA_SPLIT));
    }

    @Nullable
    private String getDashTreePathByItemId(String dashTreeItemId, boolean includeSelf) {
        DashTreeItemDO dashTreeItemDO = itemIdDashItemMapRef.get().get(dashTreeItemId);
        if (dashTreeItemDO == null) {
            return null;
        }

        StringBuilder sb = new StringBuilder();
        int i = 0;
        String itemName = dashTreeItemDO.getName();
        while (!dashTreeItemDO.getParentId().equals(CubeConstants.DEFAULT_DASH_TREE_ROOT) && i < 20) {
            i++;
            dashTreeItemDO = itemIdDashItemMapRef.get().get(dashTreeItemDO.getParentId());
            if (dashTreeItemDO != null) {
                sb.insert(0, dashTreeItemDO.getName());
                sb.insert(0, CommonSplitConstants.SLASH);
            }
        }

        if (includeSelf) {
            sb.append(CommonSplitConstants.SLASH);
            sb.append(itemName);
        }

        return sb.toString();
    }


    public DashTree getDashTree(GetDashTreeQuery basePara) {
        log.info("begin getDashTree");

        if (basePara.isLoadFromDB()) {
            loadDashTree();
        }

        Set<String> allItemIds = itemIdDashItemMapRef.get().keySet();
        if (CollectionUtils.isEmpty(allItemIds)) {
            return DashTree.emptyTree();
        }
        return getDashTreeFromCacheByIds(allItemIds, basePara.getModule());
    }

    private DashTree getDashTreeFromCacheByIds(Set<String> allItemIds, Integer dashTreeType) {
        if (CollectionUtils.isEmpty(allItemIds)) {
            return DashTree.emptyTree();
        }

        List<DashTreeItemDO> dashTreeItemDOS = new ArrayList<>();
        allItemIds.forEach(itemId -> {
            DashTreeItemDO itemDO = itemIdDashItemMapRef.get().get(itemId);
            if (null != itemDO) {
                dashTreeItemDOS.add(itemDO);
            }
        });

        String rootItemId = dashSysParaService.getFinalParentDashItemId(null, dashTreeType);
        rootItemId = StringUtils.isBlank(rootItemId) ? CubeConstants.DEFAULT_DASH_TREE_ROOT : rootItemId;
        DashTreeItemDO root = itemIdDashItemMapRef.get().get(rootItemId);
        if (null == root) {
            throw new BusinessException("Item id [" + rootItemId + "] not found");
        }

        DashTree rootTree = this.buildCurrentItem(root);

        loopLoadDashTree(dashTreeItemDOS, rootTree, new HashSet<>());
        return rootTree;
    }

    private void loopLoadDashTree(List<DashTreeItemDO> allItems, DashTree rootTree, Set<String> missedParents) {

        //clear all inherit cache
        //inheritItemUserMap.get().clear();
        List<DashTreeItemDO> nextLoopItems = allItems;
        //Map<TreeItemId, DashTree>
        Map<String, DashTree> dashTreeMap = new HashMap<>();
        dashTreeMap.put(rootTree.getId(), rootTree);
        Set<String> parentSourceIds = new HashSet<>(Arrays.asList(rootTree.getId()));

        int loopDeepIndex = 1;
        while (CollectionUtils.isNotEmpty(parentSourceIds)) {
            Set<String> newParentIds = new HashSet<>();
            for (DashTreeItemDO nextLoopMenuItem : nextLoopItems) {
                if (nextLoopMenuItem.getId().equals(dashSysParaService.getApmRootItemId())
                        && dashSysParaService.isApmRootItemHide()) {
                    continue;
                }

                if (parentSourceIds.contains(nextLoopMenuItem.getParentId())) {
                    DashTree father = dashTreeMap.get(nextLoopMenuItem.getParentId());
                    if (null == father) {
                        if (StringUtils.isNotBlank(nextLoopMenuItem.getParentId())) {
                            missedParents.add(nextLoopMenuItem.getParentId());
                        }
                        continue;
                    }
                    DashTree son = buildCurrentItem(nextLoopMenuItem);

                    father.addSon(son);
                    Collections.sort(father.getSons());
                    dashTreeMap.put(son.getId(), son);
                    newParentIds.add(son.getId());
                }
            }
            if (CollectionUtils.isNotEmpty(nextLoopItems)) {
                nextLoopItems = nextLoopItems.stream().filter(item -> !newParentIds.contains(item.getId())).collect(Collectors.toList());
            }
            parentSourceIds = newParentIds;

            loopDeepIndex++;
            if (loopDeepIndex > 20) {
                LoadDashTreeMonitor monitor =
                        LoadDashTreeMonitor.builder()
                                .phase("loop_dash_tree")
                                .status("fail")
                                .msg("loop deep exceed")
                                .build();

                MonitorLogReporter.report(monitorLog, monitor);

                log.error("loopLoadDashTree, loopDeepIndex>20");
                break;
            }
        }
    }

    @NotNull
    private DashTree buildCurrentItem(DashTreeItemDO nextLoopMenuItem) {
        DashTree son = DashTree.copyFromDO(nextLoopMenuItem);

        Set<String> userIds = new HashSet<>();
        if (null != this.itemUserMap.get().get(son.getId())) {
            userIds.addAll(this.itemUserMap.get().get(son.getId()));
        }
        if (null != this.inheritItemAllUserMap.get().get(son.getParentId())) {
            userIds.addAll(this.inheritItemAllUserMap.get().get(son.getParentId()));
        }

        if (CollectionUtils.isNotEmpty(userIds)) {
            son.setEditable(userIds.contains(AuthInterceptor.getUserId()));
            this.inheritItemAllUserMap.get().put(son.getId(), userIds);
        }

        //build item user list by inheritance
        Set<String> parentUserList = inheritItemUserMap.get().get(son.getParentId());
        if (CollectionUtils.isNotEmpty(parentUserList)) {
            userIds.addAll(parentUserList);
        }
        inheritItemUserMap.get().put(son.getId(), Sets.newHashSet(userIds));

        //build item user group by inheritance
        Set<String> parentUserGroup = inheritItemUserGroupMap.get().get(son.getParentId());
        Set<String> userGroupForItem = Sets.newHashSet();
        if (CollectionUtils.isNotEmpty(inheritItemUserGroupMap.get().get(son.getId()))) {
            userGroupForItem.addAll(inheritItemUserGroupMap.get().get(son.getId()));
        }
        if (CollectionUtils.isNotEmpty(parentUserGroup)) {
            userGroupForItem.addAll(parentUserGroup);
        }
        inheritItemUserGroupMap.get().put(son.getId(), userGroupForItem);


        return son;
    }

    private void checkPermission(String itemId) {
        Set<String> userIdList = this.inheritItemAllUserMap.get().get(itemId);
        // only work after call getDashTree() once
        if (CollectionUtils.isNotEmpty(userIdList) && userIdList.contains(AuthInterceptor.getUserId())) {
            return;
        }

        if (CubeConstants.DEFAULT_DASH_TREE_ROOT.equals(itemId)) {
            throw new BusinessException("root item is not allowed to operate");
        }

        DashTreeItemDO dashTreeItemDO = this.getDashTreeItemFromCacheAndDB(itemId);
        String name = FOLDER_TIP + dashTreeItemDO.getName();
        //dashboard item
        if (dashTreeItemDO.getItemType().equals(DashTreeItemTypeEnum.DASH.getCode())) {
            DashDO dashById = dashHandler.getDashById(dashTreeItemDO.getDashId());
            name = DASH_TIP + dashById.getName();
        }

        if (checkPermissionInDB(itemId)) {
            return;
        }

        throw new BusinessException("No permission to operate " + name);

    }

    private boolean checkPermissionInDB(String itemId) {
        DashTreeItemDO parent = this.getDashTreeItemFromCacheAndDB(itemId);
        int levelCount = 0;
        List<String> itemIdSet = new ArrayList<>();
        while (!CubeConstants.DEFAULT_DASH_TREE_ROOT.equals(parent.getName()) && levelCount < 30) {
            Set<String> inheritUser;
            Set<String> itemUsers;

            inheritUser = inheritItemAllUserMap.get().get(parent.getId());
            itemUsers = itemUserMap.get().get(parent.getId());

            itemIdSet.add(parent.getId());


            //check cache
            if ((itemUsers != null && itemUsers.contains(AuthInterceptor.getUserId()))
                    || (inheritUser != null && inheritUser.contains(AuthInterceptor.getUserId()))) {
                return true;
            }

            parent = this.getDashTreeItemFromCacheAndDB(parent.getParentId());
            levelCount++;
        }

        //retry in db data
        List<DashTreeUserRelaDO> byItemIdAndUserIds =
                dashTreeUserRelaDAO.getByItemIdAndUserIds(AuthInterceptor.getUserId(), itemIdSet);
        if (CollectionUtils.isNotEmpty(byItemIdAndUserIds)) {
            return true;
        }

        return false;
    }


    @Transactional
    public String createTreeItem(CreateTreeItemQuery basePara) {

        String finalParentDashItemId = dashSysParaService.getFinalParentDashItemId(basePara.getParentTreeItemId(),
                basePara.getModule());

        checkNewItemAndParent(finalParentDashItemId, basePara.getName());

        if (null == basePara.getModule()
                || null == DashModuleEnum.getByValue(basePara.getModule())) {
            checkPermission(basePara.getParentTreeItemId());
        }

        DashTreeItemDO dashTreeItemDO = createItem(basePara, finalParentDashItemId);

        //add self as owner
        if (dashTreeItemDO.getParentId().equals(CubeConstants.DEFAULT_DASH_TREE_ROOT)) {
            if (!basePara.getOwnerId().contains(basePara.getUserId())) {
                basePara.getOwnerId().add(basePara.getUserId());
            }

            saveOwnerAndUserGroup(basePara.getOwnerId(), basePara.getUserGroupId(), dashTreeItemDO.getId());
        }

        return dashTreeItemDO.getId();
    }

    private void checkNewItemAndParent(String parentId, String newItemName) {
        List<DashTreeItemDO> byParentIdAndName = dashTreeItemDAO.getByParentIdAndName(parentId, newItemName);
        if (CollectionUtils.isNotEmpty(byParentIdAndName)) {
            throw new BusinessException("Same item exists, please pick an another name");
        }

        DashTreeItemDO parent = getDashTreeItemFromCacheAndDB(parentId);
        if (null == parent) {
            throw new BusinessException("Parent item id not exists");
        }
    }

    @NotNull
    private DashTreeItemDO createItem(CreateTreeItemQuery basePara, String finalParentDashItemId) {
        DashTreeItemDO dashTreeItemDO = new DashTreeItemDO();
        dashTreeItemDO.setId(IdUtils.generateId());
        dashTreeItemDO.setCreator(basePara.getUserName());
        dashTreeItemDO.setItemType(DashTreeItemTypeEnum.FOLDER.getCode());
        dashTreeItemDO.setItemOrder(basePara.getItemOrder());
        dashTreeItemDO.setModifier(basePara.getUserName());
        dashTreeItemDO.setName(basePara.getName());
        dashTreeItemDO.setParentId(finalParentDashItemId);
        dashTreeItemDAO.addDashTreeItem(dashTreeItemDO);

        return dashTreeItemDO;
    }

    private void saveOwnerAndUserGroup(List<String> owners, List<String> userGroup, String dashTreeItemId) {

        for (String owner : owners) {
            if (null == userHandler.getUserFromCache(owner)) {
                continue;
            }

            DashTreeUserRelaDO relaDO = new DashTreeUserRelaDO();
            relaDO.setId(IdUtils.generateId());
            relaDO.setTreeItemId(dashTreeItemId);
            relaDO.setRelaType(DashTreeUserRelaTypeEnum.USER.getCode());
            relaDO.setUserRelaId(owner);
            dashTreeUserRelaDAO.add(relaDO);
        }

        if (CollectionUtils.isEmpty(userGroup)) {
            return;
        }

        List<UserGroupDO> userGroupDOList = userGroupHandler.findByParam(userGroup, null, null, null, 0, 1000);
        for (UserGroupDO ug : userGroupDOList) {

            DashTreeUserRelaDO relaDO = new DashTreeUserRelaDO();
            relaDO.setId(IdUtils.generateId());
            relaDO.setTreeItemId(dashTreeItemId);
            relaDO.setRelaType(DashTreeUserRelaTypeEnum.USER_GROUP.getCode());
            relaDO.setUserRelaId(ug.getId());
            dashTreeUserRelaDAO.add(relaDO);
        }
    }

    @Transactional
    public String deleteTreeItem(DeleteTreeItemQuery basePara) {
        checkPermission(basePara.getTreeItemId());
        checkSubTreeExist(basePara.getTreeItemId());

        deleteSelfAndSons(basePara.getTreeItemId());

        return basePara.getTreeItemId();
    }

    private void checkSubTreeExist(String treeItemId) {
        List<DashTreeItemDO> byParentId = dashTreeItemDAO.getByParentId(treeItemId);
        if (CollectionUtils.isNotEmpty(byParentId)) {
            throw new BusinessException("Please delete all sub-folder and dashboard before delete folder");
        }
    }

    private void deleteSelfAndSons(String treeItemId) {
        this.itemIdDashItemMapRef.get().values().stream()
                .filter(x -> x.getParentId().equals(treeItemId)).forEach(x -> {
                    deleteSelfAndSons(x.getId());

                    dashTreeItemDAO.deleteById(x.getId());
                    dashTreeUserRelaDAO.deleteByTreeItemId(x.getId());
                });

        dashTreeItemDAO.deleteById(treeItemId);
        dashTreeUserRelaDAO.deleteByTreeItemId(treeItemId);
    }

    @Transactional
    public String updateTreeItem(UpdateTreeItemQuery basePara) {
        checkPermission(basePara.getTreeItemId());

        DashTreeItemDO dashTreeItemDO = this.getDashTreeItemFromCacheAndDB(basePara.getTreeItemId());
        if (null == dashTreeItemDO) {
            throw new BusinessException("Tree item not exists:[{}]", basePara.getTreeItemId());
        }

        List<DashTreeItemDO> existItem = this.itemIdDashItemMapRef.get().values().stream()
                .filter(x -> x.getParentId().equals(dashTreeItemDO.getParentId())
                        && basePara.getName().equals(x.getName())
                        && !basePara.getTreeItemId().equals(x.getId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(existItem)) {
            throw new BusinessException("Same item exists, please pick an another name");
        }


        dashTreeItemDO.setItemOrder(basePara.getItemOrder());
        dashTreeItemDO.setName(basePara.getName());
        dashTreeItemDO.setModifier(basePara.getUserName());
        dashTreeItemDAO.update(dashTreeItemDO);

        dashTreeUserRelaDAO.deleteByTreeItemId(basePara.getTreeItemId());

        //add creator as owner
        UserDO userByName = userHandler.getUserByName(dashTreeItemDO.getCreator());
        if (!basePara.getOwnerId().contains(userByName == null ? null : userByName.getId())) {
            basePara.getOwnerId().add(basePara.getUserId());
        }
        saveOwnerAndUserGroup(basePara.getOwnerId(), basePara.getUserGroupId(), basePara.getTreeItemId());


        return basePara.getTreeItemId();
    }

    public void linkDash2TreeByPath(String userName, String dashId, String parentTreePath, int itemOrder) {
        List<String> pathList = Splitter.on(CommonSplitConstants.SLASH).splitToList(parentTreePath).stream().toList();
        if (CollectionUtils.isEmpty(pathList)) {
            return;
        }
        String lastLevelPath = pathList.get(pathList.size() - 1);

        //find dash tree item in tree
        Optional<DashTreeItemDO> any = itemIdDashItemMapRef.get().values().stream()
                .filter(x -> x != null
                        && x.getName() != null
                        && x.getName().equals(lastLevelPath)
                        && x.getItemType() != null
                        && x.getItemType().equals(DashTreeItemTypeEnum.FOLDER.getCode()))
                .filter(x -> {
                    String dashTreePath = getDashTreePathByItemId(x.getId(), true);
                    return parentTreePath.equals(dashTreePath);
                }).findAny();
        if (any.isPresent()) {
            linkDash2Tree(userName, dashId, any.get().getId(), itemOrder);
        }

    }


    public String linkDash2Tree(String userName, String dashId, String parentTreeItemId, int itemOrder) {

        checkNewDashAndParent(dashId, parentTreeItemId);

        DashTreeItemDO dashTreeItemDO = new DashTreeItemDO();
        dashTreeItemDO.setId(IdUtils.generateId());
        dashTreeItemDO.setDashId(dashId);
        dashTreeItemDO.setCreator(userName);
        dashTreeItemDO.setItemType(DashTreeItemTypeEnum.DASH.getCode());
        dashTreeItemDO.setItemOrder(itemOrder);
        dashTreeItemDO.setModifier(userName);
        dashTreeItemDO.setParentId(parentTreeItemId);

        DashDO dashById = null;
        dashById = dashHandler.getDashById(dashTreeItemDO.getDashId());
        dashTreeItemDO.setName(dashById == null ? dashId : dashById.getName());

        dashTreeItemDAO.addDashTreeItem(dashTreeItemDO);

        return dashId;

    }

    private void checkNewDashAndParent(String dashId, String parentTreeItemId) {
        DashDO dashById = dashHandler.getDashById(dashId);
        if (null == dashById) {
            throw new BusinessException("Dashboard id not exists");
        }

        DashTreeItemDO dashTreeItem = getDashTreeItemFromCacheAndDB(parentTreeItemId);
        if (null == dashTreeItem) {
            throw new BusinessException("Dashboard tree parent node not exists");
        }

        List<DashTreeItemDO> dashTreeItemDOS = dashTreeItemDAO.listByDashId(dashId);

        Instance.ofNullable(dashTreeItemDOS).stream().filter(x -> x.getParentId().equals(parentTreeItemId)).
                findAny().ifPresent(x -> {
                    throw new BusinessException("Duplicated dashboard exists");
                });
    }

    @Transactional
    public void adjustDashTree(AdjustDashTreeQuery basePara) {
        //id of root item is DEFAULT_DASH_TREE_ROOT
        String id = basePara.getId();
        if (null != id && !CubeConstants.DEFAULT_DASH_TREE_ROOT.equals(id)) {
            DashTreeItemDO prev = getDashTreeItemFromCacheAndDB(id);
            if (prev != null && !prev.getParentId().equals(basePara.getParentId())) {
                checkPermission(prev.getParentId());
                checkPermission(basePara.getParentId());

                String previousParentId = prev.getParentId();
                prev.setItemOrder(basePara.getItemOrder());
                prev.setParentId(basePara.getParentId());
                dashTreeItemDAO.update(prev);

            } else if (!prev.getItemOrder().equals(basePara.getItemOrder())) {
                prev.setItemOrder(basePara.getItemOrder());
                dashTreeItemDAO.update(prev);
            }
        }


        for (AdjustDashTreeQuery son : basePara.getSons()) {
            adjustDashTree(son);
        }
    }

    public DashTreeItemDO getDashTreeItemFromCacheAndDB(String id) {
        DashTreeItemDO prev = itemIdDashItemMapRef.get().get(id);
        if (null == prev) {
            prev = dashTreeItemDAO.getById(id);
        }

        if (prev != null) {
            itemIdDashItemMapRef.get().put(id, prev);
        }
        return prev;
    }

    public List<String> getDashTreeParentId(String dashId) {
        List<DashTreeItemDO> dashTreeItemDOS = dashTreeItemDAO.listByDashId(dashId);
        return Instance.ofNullable(dashTreeItemDOS).stream().map(x -> x.getParentId()).collect(Collectors.toList());
    }

    public String getUserGroupNameFromCache(String userGroupId) {
        return userGroupIdNameMap.getIfPresent(userGroupId);
    }

    public void deleteDashTreeItemByDashId(String dashId) {
        dashTreeItemDAO.deleteByDashId(dashId);
    }
}
