<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<artifactId>cube</artifactId>
		<groupId>us.zoom</groupId>
		<version>2.0.19-CL-SNAPSHOT</version>
	</parent>
	<artifactId>cube-site</artifactId>
	<packaging>jar</packaging>
	<name>cube-site</name>
	<description>zoom monitor web api(spring boot module)</description>

	<dependencies>
		<!--============= Spring Boot start here =============-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-webflux</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-security</artifactId>
		</dependency>
		<!-- introduced to replace org.springframework.security.oauth:spring-security-oauth2-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-oauth2-client</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>us.zoom.springframework</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>
		<!--============= Spring Boot end here =============-->

		<!-- ============= Spring Framework module start here ============= -->
		<dependency>
			<groupId>org.springframework.session</groupId>
			<artifactId>spring-session-jdbc</artifactId>
		</dependency>
		<!-- ============= Spring Framework module end here ============= -->

		<!-- ============= Miscellaneous start here ============= -->
		<dependency>
			<groupId>com.nimbusds</groupId>
			<artifactId>nimbus-jose-jwt</artifactId>
			<version>9.37.3</version>
		</dependency>
		<dependency>
			<groupId>com.okta.spring</groupId>
			<artifactId>okta-spring-boot-starter</artifactId>
			<version>3.0.6</version>
		</dependency>
		<dependency>
			<groupId>net.java.dev.jna</groupId>
			<artifactId>jna</artifactId>
			<version>4.5.2</version>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
			<version>2.0.12</version>
		</dependency>
		<dependency> <!-- JNA dependency -->
			<groupId>net.java.dev.jna</groupId>
			<artifactId>jna-platform</artifactId>
			<version>5.17.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.kafka</groupId>
			<artifactId>kafka-clients</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.xerial.snappy</groupId>
					<artifactId>snappy-java</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.influxdb</groupId>
			<artifactId>influxdb-java</artifactId>
			<version>2.17</version>
			<exclusions>
				<exclusion>
					<groupId>com.squareup.okhttp3</groupId>
					<artifactId>okhttp</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.mybatis.spring.boot</groupId>
			<artifactId>mybatis-spring-boot-starter</artifactId>
			<version>3.0.3</version>
		</dependency>
		<dependency>
			<groupId>com.github.pagehelper</groupId>
			<artifactId>pagehelper-spring-boot-starter</artifactId>
			<version>2.1.0</version>
			<exclusions>
				<exclusion>
					<groupId>com.github.jsqlparser</groupId>
					<artifactId>jsqlparser</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.tomcat.embed</groupId>
			<artifactId>tomcat-embed-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.tomcat.embed</groupId>
			<artifactId>tomcat-embed-el</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.tomcat.embed</groupId>
			<artifactId>tomcat-embed-websocket</artifactId>
		</dependency>
		<dependency>
			<groupId>com.mysql</groupId>
			<artifactId>mysql-connector-j</artifactId>
			<exclusions>
				<exclusion>
					<groupId>com.google.protobuf</groupId>
					<artifactId>protobuf-java</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.datatype</groupId>
			<artifactId>jackson-datatype-hibernate5</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>jackson-databind</artifactId>
					<groupId>com.fasterxml.jackson.core</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>javax.persistence</groupId>
			<artifactId>javax.persistence-api</artifactId>
			<version>2.2</version>
		</dependency>

		<dependency>
			<groupId>org.mindrot</groupId>
			<artifactId>jbcrypt</artifactId>
			<version>0.4</version>
		</dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>5.4.1</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-compress</artifactId>
			<version>1.26.1</version>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>
		<dependency>
			<groupId>org.codehaus.groovy</groupId>
			<artifactId>groovy-all</artifactId>
			<version>3.0.22</version>
			<type>pom</type>
			<exclusions>
				<exclusion>
					<groupId>junit</groupId>
					<artifactId>junit</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.codehaus.groovy</groupId>
					<artifactId>groovy-testng</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.sun</groupId>
					<artifactId>tools</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.jayway.jsonpath</groupId>
			<artifactId>json-path</artifactId>
			<version>2.9.0</version>
		</dependency>

		<!-- ============= Miscellaneous end here ============= -->

		<!-- ============= Test start here ============= -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.reflections</groupId>
			<artifactId>reflections</artifactId>
			<version>0.10.2</version>
			<scope>test</scope>
		</dependency>


		<!-- ============= Zoom Component start here ============= -->
		<dependency>
			<groupId>us.zoom</groupId>
			<artifactId>cube-infra</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-slf4j-impl</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-api</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>commons-codec</artifactId>
					<groupId>commons-codec</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>us.zoom</groupId>
			<artifactId>cube-notice</artifactId>
		</dependency>
		<dependency>
			<groupId>us.zoom</groupId>
			<artifactId>cube-metric-dao</artifactId>
		</dependency>

		<!-- CSMS start -->
		<dependency>
			<groupId>us.zoom.commons</groupId>
			<artifactId>zoom-commons-jwt</artifactId>
		</dependency>
		<dependency>
			<groupId>org.bitbucket.b_c</groupId>
			<artifactId>jose4j</artifactId>
		</dependency>
        <dependency>
            <groupId>us.zoom</groupId>
            <artifactId>cloud-secrets-management-service-sdk</artifactId>
        </dependency>
		<dependency>
			<groupId>us.zoom</groupId>
			<artifactId>csms-sdk-bridge-common</artifactId>
		</dependency>
		<dependency>
			<groupId>us.zoom</groupId>
			<artifactId>csms-sdk-api</artifactId>
		</dependency>
		<!-- CSMS end -->

		<dependency>
			<groupId>us.zoom.geoip</groupId>
			<artifactId>zoom-geoip-client</artifactId>
			<version>0.8.10-maxmind-20241105</version>
		</dependency>

		<dependency>
			<groupId>us.zoom.commons</groupId>
			<artifactId>zoom-commons-kms</artifactId>
			<version>0.2.0</version>
		</dependency>
		<dependency>
			<groupId>us.zoom.commons</groupId>
			<artifactId>zoom-threadpool</artifactId>
			<version>1.0.20231108</version>
			<exclusions>
				<exclusion>
					<groupId>org.bouncycastle</groupId>
					<artifactId>bcprov-jdk15on</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>us.zoom.commons</groupId>
			<artifactId>zoom-commons-apm</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.bouncycastle</groupId>
					<artifactId>bcprov-jdk15on</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.bouncycastle</groupId>
					<artifactId>bcpkix-jdk15on</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!--             Async                 -->
		<dependency>
			<groupId>us.zoom.mq</groupId>
			<artifactId>asyncmq-client-ha</artifactId>
		</dependency>
		<dependency>
			<groupId>us.zoom.async.file</groupId>
			<artifactId>fileserver-sdk</artifactId>
			<version>2.1.19</version>
		</dependency>

		<dependency>
			<groupId>software.amazon.awssdk</groupId>
			<artifactId>resourcegroupstaggingapi</artifactId>
			<version>2.20.26</version>
		</dependency>
		<dependency>
			<groupId>software.amazon.awssdk</groupId>
			<artifactId>s3</artifactId>
			<version>2.19.8</version>
		</dependency>

		<dependency>
			<groupId>us.zoom.async</groupId>
			<artifactId>zoom-async-aws-sts</artifactId>
			<version>0.0.5</version>
		</dependency>

		<dependency>
			<groupId>com.zaxxer</groupId>
			<artifactId>HikariCP</artifactId>
		</dependency>
		<!-- ============= Zoom Component end here ============= -->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>lock4j-redis-template-spring-boot-starter</artifactId>
			<version>2.2.7</version>
		</dependency>
        <dependency>
            <groupId>us.zoom</groupId>
            <artifactId>cube-scheduler-lib</artifactId>
            <version>0.0.1-RELEASE</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>


	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.bouncycastle</groupId>
				<artifactId>bcprov-jdk18on</artifactId>
				<version>1.78.1</version>
			</dependency>
			<dependency>
				<groupId>org.xerial.snappy</groupId>
				<artifactId>snappy-java</artifactId>
				<version>1.1.10.5</version>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<finalName>cube-site</finalName>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<jvmArguments>
						--add-opens java.base/java.io=ALL-UNNAMED
					</jvmArguments>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<version>${springboot.version}</version>
				<configuration>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
        </plugins>
	</build>

	<repositories>
		<repository>
			<id>spring-snapshots</id>
			<name>Spring Snapshots</name>
			<url>https://repo.spring.io/snapshot</url>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>
		<repository>
			<id>spring-milestones</id>
			<name>Spring Milestones</name>
			<url>https://repo.spring.io/milestone</url>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</repository>
		<repository>
			<id>maven_central</id>
			<name>Maven Central</name>
			<url>https://repo.maven.apache.org/maven2/</url>
		</repository>
	</repositories>

</project>
