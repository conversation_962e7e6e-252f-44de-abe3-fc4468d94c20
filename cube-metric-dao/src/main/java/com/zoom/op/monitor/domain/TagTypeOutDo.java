package com.zoom.op.monitor.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/27 09:56
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TagTypeOutDo {
    private String id;
    private Integer source;
    private String desc;
    private String tagTypeName;
    private List<String> scopes;
}
