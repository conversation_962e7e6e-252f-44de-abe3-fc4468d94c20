package com.zoom.op.monitor.domain.alarm;


import com.zoom.op.monitor.domain.BaseDomain;
import lombok.Data;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;

import jakarta.persistence.*;

import java.io.Serializable;

@Data
@Entity
@ToString
@Table(name = "alarm_rule_condition")
public class RuleCondition implements BaseDomain<String>, Serializable {

    @Id
    @GenericGenerator(name = "uuid", strategy = "com.zoom.op.monitor.config.UUIDGenerator")
    @GeneratedValue(generator = "uuid")
    @Column(length = 64)
    private String id;

    @Convert(converter = ConditionType.ValueConverter.class)
    private ConditionType conditionType;

    private String name;

    private String mapKey;

    private String operator;

    private String threshold;
    /**
     * histogram&summary
     * histogram([1,2,10,30],[1,10,15,20],${histogramQuantile})>12
     */
    private String histogramQuantile;

    private String expression;

    @Transient
    private String valueType;

    private String extension;
}
