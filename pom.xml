<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>us.zoom</groupId>
    <artifactId>cube</artifactId>
    <packaging>pom</packaging>
    <version>2.0.19-CL-SNAPSHOT</version>

    <description>zoom monitor system</description>
    <modules>
        <module>cube-alarm</module>
        <module>cube-notice</module>
        <module>cube-metric-dao</module>
        <module>cube-site</module>
        <module>cube-infra</module>
        <module>cube-lib</module>
        <module>cube-sink</module>
        <module>cube-duty</module>
    </modules>
    <!-- Allow users to pass custom connector versions -->

    <developers>
        <developer>
            <id>Pine</id>
            <name><PERSON></name>
            <email><EMAIL></email>
            <roles>
                <role>designer</role>
                <role>developer</role>
            </roles>
        </developer>
        <developer>
            <id>Kian</id>
            <name>Kian Han</name>
            <email><EMAIL></email>
            <roles>
                <role>designer</role>
                <role>developer</role>
            </roles>
        </developer>
    </developers>

    <properties>
        <!--system property start-->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <compiler.plugin.version>3.12.1</compiler.plugin.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <!--system property end-->

        <!--self module version start-->
        <jsqlparser.version>4.3.9</jsqlparser.version>
        <cube.sdk.version>1.0.8</cube.sdk.version>
        <!--self module version end-->

        <!--second party module version start-->
        <apm.version>2.2.20250604</apm.version>
        <spring.actuator.version>3.3.7</spring.actuator.version>
        <hikariCP.version>5.0.1.11-zoom</hikariCP.version>
        <common.jwt.version>2.0.11</common.jwt.version>
        <csms.version>0.6.16</csms.version>
        <csms.sdk.version>0.4.10</csms.sdk.version>
        <asyncmq.openapi.version>0.1.12</asyncmq.openapi.version>
        <asyncmq.version>2.7.13</asyncmq.version>
        <!--second party module version end-->

        <!--third party module version start-->
        <slf4j.version>2.0.17</slf4j.version>
        <springboot.version>3.5.4</springboot.version>
        <micrometer.version>1.9.6</micrometer.version>
        <aws.java.version>1.12.686</aws.java.version>
        <nimbusds.version>9.37.3</nimbusds.version>
        <bcprov.version>1.78.1</bcprov.version>
        <jsonpath.version>2.9.0</jsonpath.version>
        <hutool.version>5.8.39</hutool.version>
        <kafka.version>3.9.1</kafka.version>
        <snappy.java.version>1.1.10.5</snappy.java.version>
        <aviator.version>5.4.1</aviator.version>
        <javax.mail.version>1.6.2</javax.mail.version>
        <mysql.connector.version>9.3.0</mysql.connector.version>
        <commons.lang3.version>3.18.0</commons.lang3.version>
        <influxdb.version>2.17</influxdb.version>
        <influxdb.client.version>3.2.0</influxdb.client.version>
        <guava.version>33.4.8-jre</guava.version>
        <jose4j.version>0.9.5</jose4j.version>
        <clickhouse.jdbc.version>0.6.0</clickhouse.jdbc.version>
        <lz4.version>1.8.0</lz4.version>
        <tomcat.version>11.0.9</tomcat.version>
        <commons-beanutils.version>1.11.0</commons-beanutils.version>
        <!--third party module version end-->

    </properties>

    <dependencyManagement>
        <dependencies>
            <!--self dependencies start-->
            <dependency>
                <groupId>us.zoom</groupId>
                <artifactId>cube-infra</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>us.zoom</groupId>
                <artifactId>cube-metric-dao</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>us.zoom</groupId>
                <artifactId>cube.lib</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>us.zoom</groupId>
                <artifactId>cube-notice</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>us.zoom</groupId>
                <artifactId>cube-config-client</artifactId>
                <version>2.0.17-RELEASE</version>
                <exclusions>
                    <exclusion>
                        <groupId>us.zoom.commons</groupId>
                        <artifactId>zoom-web-util</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>us.zoom</groupId>
                <artifactId>cube-jsqlparser</artifactId>
                <version>${jsqlparser.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.jsqlparser</groupId>
                <artifactId>jsqlparser</artifactId>
                <version>5.0</version>
            </dependency>
            <dependency>
                <groupId>us.zoom</groupId>
                <artifactId>cube-sdk</artifactId>
                <version>${cube.sdk.version}</version>
            </dependency>
            <!--self dependencies end-->

            <!--second party dependencies start-->
            <dependency>
                <groupId>us.zoom.commons</groupId>
                <artifactId>zoom-commons-apm-spring-boot-starter</artifactId>
                <version>${apm.version}</version>
            </dependency>
            <dependency>
                <groupId>us.zoom.commons</groupId>
                <artifactId>zoom-commons-apm</artifactId>
                <version>${apm.version}</version>
            </dependency>
            <dependency>
                <groupId>us.zoom.springframework</groupId>
                <artifactId>spring-boot-starter-actuator</artifactId>
                <version>${spring.actuator.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zaxxer</groupId>
                <artifactId>HikariCP</artifactId>
                <version>${hikariCP.version}</version>
            </dependency>
            <dependency>
                <groupId>us.zoom.commons</groupId>
                <artifactId>zoom-commons-jwt</artifactId>
                <version>${common.jwt.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.bouncycastle</groupId>
                        <artifactId>bcprov-jdk15on</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.bouncycastle</groupId>
                        <artifactId>bcpkix-jdk15on</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--csms dependencies-->
            <dependency>
                <groupId>us.zoom</groupId>
                <artifactId>cloud-secrets-management-service-sdk</artifactId>
                <version>${csms.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.squareup.okhttp3</groupId>
                        <artifactId>okhttp</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.google.code.gson</groupId>
                        <artifactId>gson</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.bouncycastle</groupId>
                        <artifactId>bcprov-jdk15on</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.bouncycastle</groupId>
                        <artifactId>bcpkix-jdk15on</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>us.zoom</groupId>
                <artifactId>csms-sdk-bridge-common</artifactId>
                <version>${csms.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>us.zoom</groupId>
                <artifactId>csms-sdk-api</artifactId>
                <version>${csms.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>us.zoom.commons</groupId>
                <artifactId>zoom-web-util</artifactId>
                <version>1.0.20250604</version>
            </dependency>
            <!--csms dependencies end-->

            <!-- asyncMQ Start -->
            <dependency>
                <groupId>us.zoom.mq</groupId>
                <artifactId>asyncmq-client-ha</artifactId>
                <version>${asyncmq.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.bouncycastle</groupId>
                        <artifactId>bcprov-jdk15on</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.bouncycastle</groupId>
                        <artifactId>bcpkix-jdk15on</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>us.zoom.async.mq</groupId>
                <artifactId>openapi-client</artifactId>
                <version>${asyncmq.openapi.version}</version>
            </dependency>
            <!-- asyncMQ End -->
            <!--second party dependencies end-->

            <!--third party dependencies start-->
            <!--spring boot version-->
            <!-- https://mvnrepository.com/artifact/io.netty/netty-bom -->
<!--            <dependency>-->
<!--                <groupId>io.netty</groupId>-->
<!--                <artifactId>netty-bom</artifactId>-->
<!--                <version>4.1.118.Final</version>-->
<!--                <type>pom</type>-->
<!--                <scope>import</scope>-->
<!--            </dependency>-->

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${springboot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-core</artifactId>
                <version>${tomcat.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-el</artifactId>
                <version>${tomcat.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-websocket</artifactId>
                <version>${tomcat.version}</version>
            </dependency>

            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-core</artifactId>
                <version>${aws.java.version}</version>
            </dependency>
            <dependency>
                <groupId>com.nimbusds</groupId>
                <artifactId>nimbus-jose-jwt</artifactId>
                <version>${nimbusds.version}</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk18on</artifactId>
                <version>${bcprov.version}</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcpkix-jdk18on</artifactId>
                <version>${bcprov.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jayway.jsonpath</groupId>
                <artifactId>json-path</artifactId>
                <version>${jsonpath.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>${commons-beanutils.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-clients</artifactId>
                <version>${kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.xerial.snappy</groupId>
                <artifactId>snappy-java</artifactId>
                <version>${snappy.java.version}</version>
            </dependency>
            <dependency>
                <groupId>com.googlecode.aviator</groupId>
                <artifactId>aviator</artifactId>
                <version>${aviator.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.mail</groupId>
                <artifactId>javax.mail</artifactId>
                <version>${javax.mail.version}</version>
                <!--<scope>provided</scope>-->
            </dependency>
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql.connector.version}</version>
            </dependency>
            <!--log-->
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons.lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>org.influxdb</groupId>
                <artifactId>influxdb-java</artifactId>
                <version>${influxdb.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.squareup.okhttp3</groupId>
                        <artifactId>okhttp</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.influxdb</groupId>
                <artifactId>influxdb-client-java</artifactId>
                <version>${influxdb.client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.google.code.gson</groupId>
                        <artifactId>gson</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.squareup.okio</groupId>
                        <artifactId>okio</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.squareup.okhttp3</groupId>
                        <artifactId>okhttp</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>net.minidev</groupId>
                <artifactId>json-smart</artifactId>
                <version>2.5.2</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>org.bitbucket.b_c</groupId>
                <artifactId>jose4j</artifactId>
                <version>${jose4j.version}</version>
            </dependency>
            <dependency>
                <groupId>com.clickhouse</groupId>
                <artifactId>clickhouse-jdbc</artifactId>
                <version>${clickhouse.jdbc.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.google.code.gson</groupId>
                        <artifactId>gson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.lz4</groupId>
                <artifactId>lz4-java</artifactId>
                <version>${lz4.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents.client5</groupId>
                <artifactId>httpclient5</artifactId>
                <version>5.4.4</version>
            </dependency>
            <!--third party dependencies end-->
        </dependencies>
    </dependencyManagement>

    <profiles>
        <profile>
            <id>local</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <activatedProperties>local</activatedProperties>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <activatedProperties>prod</activatedProperties>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <activatedProperties>dev</activatedProperties>
            </properties>
        </profile>
    </profiles>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${compiler.plugin.version}</version>
                    <configuration>
                        <source>${maven.compiler.source}</source>
                        <target>${maven.compiler.target}</target>
                        <showDeprecation>true</showDeprecation>
                        <showWarnings>true</showWarnings>
                        <encoding>UTF-8</encoding>
                        <parameters>true</parameters>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>1.18.38</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>