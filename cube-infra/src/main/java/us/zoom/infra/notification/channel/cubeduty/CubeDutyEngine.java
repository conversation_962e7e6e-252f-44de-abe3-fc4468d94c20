package us.zoom.infra.notification.channel.cubeduty;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import us.zoom.cube.lib.common.AlarmMonitorTypeEnum;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.infra.model.LogResult;
import us.zoom.infra.notification.channel.ChannelEngine;
import us.zoom.infra.notification.channel.ChannelSendModel;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> song
 */
@Component
@Slf4j
@Order(2)
public class CubeDutyEngine implements ChannelEngine {

    public static final String CUBEDUTY_INTEGRATION_KEY = "Integration Key";

    public static final String SOURCE = "Cube-Alarm";

    private CloseableHttpClient httpClient;

    private Logger monitorLog = LoggerFactory.getLogger("Monitor");

    public static final String NAME = "CubeDuty";

    @Value("${cube-duty.endpoint:}")
    private String endpoint;

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public List<Parameter> getParameters() {
        return List.of(Parameter.builder().name(CUBEDUTY_INTEGRATION_KEY).placeHolder("integration_key").build());
    }

    @Override
    public void send(ChannelSendModel channelSendModel) throws Exception {
        Map<String, Object> body = new HashMap<>();
        body.put("title", channelSendModel.getRecord().getTitle());
        String message = channelSendModel.getMessage();
        if (StringUtils.isNotBlank(channelSendModel.getRunBook())) {
            message += "\nRunbook: " + channelSendModel.getRunBook();
        }
        body.put("description", message);
        body.put("level", channelSendModel.getRecord().getAlarmLevel().getPeril());
        body.put("source", SOURCE);
        body.put("service", channelSendModel.getRecord().getTenantName());
        body.put("original_id", channelSendModel.getRecord().getId());
        Map<String, Object> tags = new HashMap<>();
        tags.putAll(channelSendModel.getRecord().getMetricFields());
        tags.putAll(channelSendModel.getRecord().getMetricsTags());
        body.put("tags", tags);

        Map<String, String> parameters = channelSendModel.getParameters();
        String endpoint = this.endpoint + "/push_cube_alarm/" + parameters.get(CUBEDUTY_INTEGRATION_KEY);
        HttpPost httpPost = new HttpPost(endpoint);
        StringEntity stringEntity = new StringEntity(JsonUtils.toJsonString(body), "UTF-8");
        stringEntity.setContentType("application/json;charset=utf-8");
        httpPost.setEntity(stringEntity);
        try {
            long startTime = System.currentTimeMillis();
            CloseableHttpResponse response = httpClient.execute(httpPost);
            long endTime = System.currentTimeMillis();
            log.info("[CubeDutyEngine#send] request is: {}, response is: {}|{}, cost time : {} ms",
                    JsonUtils.toJsonStringIgnoreExp(httpPost), response.getStatusLine(), response.getEntity() != null ? EntityUtils.toString(response.getEntity(), "UTF-8") : null, endTime - startTime);
        } catch (Exception e) {
            monitorLog.info(LogResult.getErrorLogResult(AlarmMonitorTypeEnum.retrySendCubeDuty.name(), e, Maps.newHashMap(), Maps.newHashMap()));
        } finally {
            httpPost.releaseConnection();
        }
    }

    @Autowired(required = false)
    public void setHttpClient(CloseableHttpClient httpClient) {
        this.httpClient = httpClient;
    }

}
