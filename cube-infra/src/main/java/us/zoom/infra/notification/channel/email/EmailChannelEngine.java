package us.zoom.infra.notification.channel.email;

import com.google.common.collect.Maps;
import com.zoom.op.monitor.domain.alarm.AlarmExtensionRelation;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import us.zoom.cloud.secrets.spring.annotation.SecretValue;
import us.zoom.cube.lib.common.AlarmMonitorTypeEnum;
import us.zoom.infra.influx.model.alarm.AlarmMatchRecord;
import us.zoom.infra.model.LogResult;
import us.zoom.infra.notification.NotificationHelper;
import us.zoom.infra.notification.channel.ChannelEngine;
import us.zoom.infra.notification.channel.ChannelSendModel;
import us.zoom.infra.notification.channel.zoomchat.IMChannelEngine;

import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static java.nio.charset.StandardCharsets.UTF_8;
import static us.zoom.infra.notification.channel.zoomchat.IMChannelEngine.*;

@Component
@Slf4j
@Order(2)
public class EmailChannelEngine implements ChannelEngine {

    private final Logger monitorLog = LoggerFactory.getLogger("Monitor");

    private static final String TEMPLATE_PATH = "template/email_template.html";

    private String sender;

    private String from;

    private String smtp;

    private String port;

    private String username;

    private String password;

    private Properties properties;

    @Autowired
    private NotificationHelper notificationHelper;

    @Autowired
    private IMChannelEngine imChannelEngine;

    @Value("${alarm.channel.email.sender:cube-team}")
    public void setSender(String sender) {
        this.sender = sender;
    }

    @Value("${alarm.channel.email.from:<EMAIL>}")
    public void setFrom(String from) {
        this.from = from;
    }

    @Value("${alarm.channel.email.smtp.server:smtp.gmail.com}")
    public void setSmtp(String smtp) {
        this.smtp = smtp;
    }

    @Value("${alarm.channel.email.smtp.port:587}")
    public void setPort(String port) {
        this.port = port;
    }

    @SecretValue("alarm.channel.email.username")
    public void setUsername(String username) {
        this.username = username;
    }

    @SecretValue("alarm.channel.email.password")
    public void setPassword(String password) {
        this.password = password;
    }

    @PostConstruct
    public void init() {

        this.properties = prepareProperties();
    }

    @Override
    public String getName() {

        return "Email";
    }

    @Override
    public List<Parameter> getParameters() {

        return Collections.singletonList(

                Parameter.builder()
                        .name("Recipients")
                        .explain("You can enter multiple email addresses using a \";\" separator")
                        .placeHolder("recipients")
                        .build()
        );
    }

    @Override
    public void send(ChannelSendModel channelSendModel) throws Exception {

        Map<String, String> parameters = channelSendModel.getParameters();
        String title = channelSendModel.getTitle();
        String message = channelSendModel.getMessage();
        AlarmMatchRecord record = channelSendModel.getRecord();
        Map retrySendIM = channelSendModel.getRetrySendIM();
        String newEndpoint = channelSendModel.getNewEndpoint();
        String runbook = channelSendModel.getRunBook();

        Session session = Session.getInstance(properties);

        MimeMessage mimeMessage = buildMimeMessage(parameters.get("Recipients"), title, message, session, record, newEndpoint, runbook);

        if (mimeMessage == null) {
            return;
        }
        boolean switchRetrySendIM = Objects.nonNull(retrySendIM) && !retrySendIM.isEmpty() && Objects.nonNull(retrySendIM.get(SWITCH_RETRY)) && (Boolean) retrySendIM.get(SWITCH_RETRY);
        if (switchRetrySendIM) {
            int times = 1;
            long retrySleepTime = Objects.nonNull(retrySendIM.get(RETRY_SLEEP_TIME)) ? Long.parseLong(String.valueOf(retrySendIM.get(RETRY_SLEEP_TIME))) : 1000L;
            int retryCount = Objects.nonNull(retrySendIM.get(RETRY_COUNT)) ? (int) retrySendIM.get(RETRY_COUNT) : 3;
            while (times <= retryCount) {
                try (Transport transport = session.getTransport()) {
                    transport.connect(username, password);
                    transport.sendMessage(mimeMessage, mimeMessage.getAllRecipients());
                    if (times > 1) {
                        Map<String, Object> success = Maps.newHashMap();
                        success.put("count", times);
                        monitorLog.info(LogResult.getSuccessLogResult(AlarmMonitorTypeEnum.retrySendEmail.name(), success, new HashMap<>()));
                    }
                } catch (Exception e) {
                    Map<String, Object> result = Maps.newHashMap();
                    result.put("count", times);
                    monitorLog.info(LogResult.getErrorLogResult(AlarmMonitorTypeEnum.retrySendEmail.name(), e, new HashMap<>(), result));
                    if (times >= retryCount) {
                        throw new RuntimeException(e);
                    }
                    Thread.sleep(retrySleepTime);
                } finally {
                    times++;
                }
            }
        } else {
            try (Transport transport = session.getTransport()) {

                transport.connect(username, password);

                transport.sendMessage(mimeMessage, mimeMessage.getAllRecipients());
            }
        }

    }

    public void sendHtmlEmail( Map<String, String> parameters,String title,String message) throws Exception {
        Session session = Session.getInstance(properties);

        MimeMessage mimeMessage = buildHtmlMimeMessage(parameters.get("Recipients"), title, message, session);

        if (mimeMessage == null) {
            return;
        }

        try (Transport transport = session.getTransport()) {

            transport.connect(username, password);

            transport.sendMessage(mimeMessage, mimeMessage.getAllRecipients());
        }

    }



    private Properties prepareProperties() {

        Properties properties = new Properties();
        properties.setProperty("mail.transport.protocol", "smtp");
        properties.setProperty("mail.smtp.host", smtp);
        properties.setProperty("mail.smtp.port", port);
        properties.setProperty("mail.smtp.auth", "true");
        properties.setProperty("mail.smtp.user", username);
        properties.setProperty("mail.smtp.starttls.enable", "true");
        properties.setProperty("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");

        return properties;
    }

    private MimeMessage buildMimeMessage(
            String recipients, String title, String message, Session session, AlarmMatchRecord record, String endpoint, String runbook) throws MessagingException {

        MimeMessage mimeMessage = new MimeMessage(session);

        Optional<InternetAddress> sendAddress = buildInternetAddress(from, sender);

        if (sendAddress.isEmpty()) {
            return null;
        }

        mimeMessage.setFrom(sendAddress.get());

        InternetAddress[] addresses = buildAddresses(recipients);

        if (addresses.length == 0) {
            return null;
        }

        mimeMessage.setRecipients(Message.RecipientType.TO, addresses);

        mimeMessage.setSubject(title, UTF_8.name());
        mimeMessage.setContent(buildHtmlContent(message, title, record, endpoint, runbook), "text/html; charset=" + UTF_8);

        mimeMessage.setSentDate(new Date());

        mimeMessage.saveChanges();

        return mimeMessage;
    }

    public MimeMessage buildHtmlMimeMessage(
            String recipients, String title, String message, Session session) throws MessagingException {
        MimeMessage mimeMessage = new MimeMessage(session);

        Optional<InternetAddress> sendAddress = buildInternetAddress(from, sender);

        if (sendAddress.isEmpty()) {
            return null;
        }

        mimeMessage.setFrom(sendAddress.get());

        InternetAddress[] addresses = buildAddresses(recipients);

        if (addresses.length == 0) {
            return null;
        }

        mimeMessage.setRecipients(Message.RecipientType.TO, addresses);

        mimeMessage.setSubject(title, UTF_8.name());

        mimeMessage.setContent(message, "text/html; charset=" + UTF_8);

        mimeMessage.setSentDate(new Date());
        mimeMessage.saveChanges();
        return mimeMessage;
    }

    private String buildMessage(String message, AlarmMatchRecord record, String newEndpoint) {
        StringBuilder content = new StringBuilder();
        content.append(message);
        if (!StringUtils.isBlank(newEndpoint)) {
            String endpoint = notificationHelper.buildAlarmDetailUrl(newEndpoint, record);
            if (isHttpUrl(endpoint)) {
                content.append("\n");
                content.append("Click here for details: ").append(endpoint);
            }
        }

        return content.toString();
    }

    private String buildHtmlContent(String message, String title, AlarmMatchRecord record, String newEndpoint, String runbook) {
        String url = StringUtils.EMPTY;
        if(!StringUtils.isBlank(newEndpoint)){
           String endpoint = notificationHelper.buildAlarmDetailUrl(newEndpoint, record);
            if(isHttpUrl(endpoint)){
                url = endpoint;
            }
        }
        if(StringUtils.isNotBlank(url)){
            url = String.format("<span class=\"actions\" style=\"display: inline-block; font-size: 16px; margin: 32px 0; text-align: center; width: 100%%;\">\n" +
                    "<a class=\"button\" href=\"%s\" target=\"_blank\" style=\"background-color: #0b5cff; border: 0px solid #333333; border-color: #333333; border-radius: 20px; border-style: solid; border-width: 0px; color: #ffffff; display: inline-block; font-size: 16px; font-weight: bold; letter-spacing: 0px; line-height: normal; min-width: 20%%; padding: 12px 40px; text-align: center; text-decoration: none;\">Click here for details</a>\n" +
                    "</span>", url);
        }
        StringBuilder formattedMessage = new StringBuilder(message.replace("\n", "<br>"));
        if(Objects.nonNull(record) && CollectionUtils.isNotEmpty(record.getAlarmExtensionRelations())) {
            Map<String, String> dashBoardLinkMap = new HashMap<>();
            Map<String, String> userLinkMap = new HashMap<>();
            List<AlarmExtensionRelation> links = record.getAlarmExtensionRelations();
            if (CollectionUtils.isNotEmpty(links)) {
                for (AlarmExtensionRelation alarmExtensionRelation : links) {
                    switch (alarmExtensionRelation.getRelationType()) {
                        case dashboard -> {
                            String dashboardUrl = imChannelEngine.buildDashboardLinkParamsAndMaskPiiField(record, record.getMetricsTags(), alarmExtensionRelation.getRelationId());
                            if(isHttpUrl(dashboardUrl)){
                                dashBoardLinkMap.put(alarmExtensionRelation.getRelationName(), dashboardUrl);
                            }
                        }
                        case userlink -> {
                            if (StringUtils.isNotBlank(alarmExtensionRelation.getConfigValue())) {
                                String encodeUrl = NotificationHelper.encodeUrlQueryString(NotificationHelper.decode(alarmExtensionRelation.getConfigValue()));
                                String finalUrl = StringUtils.isBlank(encodeUrl) ? alarmExtensionRelation.getConfigValue() : encodeUrl;
                                if(isHttpUrl(finalUrl)){
                                    userLinkMap.put(alarmExtensionRelation.getRelationName(), finalUrl);
                                }
                            }

                        }
                    }
                }
            }
            if (!dashBoardLinkMap.isEmpty()) {
                formattedMessage.append("<br><br><strong>Dashboard Links:</strong><br>");
                for (Map.Entry<String, String> entry : dashBoardLinkMap.entrySet()) {
                    formattedMessage.append(String.format(
                            "<a href='%s' target='_blank'>%s</a><br>", entry.getValue(), entry.getKey()));
                }
            }
            if (!userLinkMap.isEmpty()) {
                formattedMessage.append("<br><strong>User Links:</strong><br>");
                for (Map.Entry<String, String> entry : userLinkMap.entrySet()) {
                    formattedMessage.append(String.format(
                            "<a href='%s' target='_blank'>%s</a><br>", entry.getValue(), entry.getKey()));
                }
            }
        }
        if(StringUtils.isNotBlank(runbook)){
            formattedMessage.append(String.format("<br><a href='%s' target='_blank'>Runbook</a><br>", runbook));
        }
        String html = readTemplate();
        return html.replace("{title}", title)
                .replace("{message}", formattedMessage.toString())
                .replace("{button}", StringUtils.isNotBlank(url)? url: "");
    }

    private static String readTemplate() {
        try (InputStream inputStream = EmailChannelEngine.class.getClassLoader().getResourceAsStream(TEMPLATE_PATH);
             BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, UTF_8))){
            return reader.lines().collect(Collectors.joining("\n"));
        } catch (Exception e){
            log.error("Failed to load email template", e);
            return null;
        }
    }

    public static boolean isHttpUrl(String input) {
        String regex = "^(http|https)://.*$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        return matcher.matches();
    }

    private InternetAddress[] buildAddresses(String recipients) {
        return Arrays.stream(recipients.split(";"))
                .flatMap(recipient -> buildInternetAddress(recipient, recipient).stream()).toArray(InternetAddress[]::new);
    }

    private Optional<InternetAddress> buildInternetAddress(String recipient, String personal) {
        try {
            return Optional.of(new InternetAddress(recipient, personal, UTF_8.name()));
        } catch (UnsupportedEncodingException e) {
            log.error("Failed to build InternetAddress with encoding exception.", e);
            return Optional.empty();
        } catch (Exception e) {
            log.error("Failed to build InternetAddress.", e);
            return Optional.empty();
        }
    }
}
