package us.zoom.infra.notification.channel.pagerduty;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.common.AlarmMonitorTypeEnum;
import us.zoom.infra.influx.model.alarm.AlarmMatchRecord;
import us.zoom.infra.model.LogResult;
import us.zoom.infra.notification.NotificationHelper;
import us.zoom.infra.notification.channel.ChannelEngine;
import us.zoom.infra.notification.channel.ChannelSendModel;
import us.zoom.cube.lib.utils.JsonUtils;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static us.zoom.cube.lib.common.CubeConstants.TURN_ON_PAGER_DUTY_DEDUP;

@Component
@Slf4j
@Order(3)
public class PagerDutyEngine implements ChannelEngine {
    private Logger monitorLog = LoggerFactory.getLogger("Monitor");
    private static final long LOG_COUNT = 100L;
    private static final AtomicLong LOG_INDEX = new AtomicLong();

    public static final String PAGERDUTY_INTEGRATION_URL = "Integration URL";

    public static final String PAGERDUTY_INTEGRATION_KEY = "Integration Key";

    public static final String SWITCH_RETRY = "switchRetry";

    public static final String RETRY_SLEEP_TIME = "retrySleepTime";

    public static final String RETRY_COUNT = "retryCount";

    public static final String PAGER_DUTY = "PagerDuty";

    private CloseableHttpClient httpClient;
    private NotificationHelper notificationHelper;

    @Autowired
    public void setNotificationHelper(NotificationHelper notificationHelper) {
        this.notificationHelper = notificationHelper;
    }

    @Autowired(required = false)
    public void setHttpClient(CloseableHttpClient httpClient) {
        this.httpClient = httpClient;
    }

    @Override
    public String getName() {
        return "PagerDuty";
    }

    @Override
    public List<Parameter> getParameters() {
        return Arrays.asList(

                Parameter.builder()
                        .name(PAGERDUTY_INTEGRATION_KEY)
                        .isSecret(true)
                        .placeHolder("Integration Key")
                        .build(),
                Parameter.builder()
                        .name(PAGERDUTY_INTEGRATION_URL)
                        .placeHolder("Integration URL (Alert Events) for Events API v2")
                        .build()

        );
    }

    @Override
    public void send(ChannelSendModel channelSendModel) throws Exception {

        Map<String, String> parameters = channelSendModel.getParameters();
        String title = channelSendModel.getTitle();
        String message = channelSendModel.getMessage();
        AlarmMatchRecord record = channelSendModel.getRecord();
        Map retrySendIM = channelSendModel.getRetrySendIM();
        Map<String,Object> extendInfo = channelSendModel.getExtendInfo();
        String newEndpoint = channelSendModel.getNewEndpoint();
        String runbook = channelSendModel.getRunBook();

        String endpoint = parameters.get(PAGERDUTY_INTEGRATION_URL);
        String jsonBody = generateAlertEventJson(parameters, title, message, record, extendInfo, newEndpoint, runbook);
        HttpPost post = new HttpPost(endpoint.trim());
        StringEntity stringEntity = new StringEntity(jsonBody, "UTF-8");
        stringEntity.setContentType("application/json;charset=utf-8");
        post.setEntity(stringEntity);

        boolean switchRetrySendIM = !retrySendIM.isEmpty() && Objects.nonNull(retrySendIM.get(SWITCH_RETRY)) && (Boolean) retrySendIM.get(SWITCH_RETRY);
        if (switchRetrySendIM) {
            long retrySleepTime = Objects.nonNull(retrySendIM.get(RETRY_SLEEP_TIME)) ? Long.parseLong(String.valueOf(retrySendIM.get(RETRY_SLEEP_TIME))) : 1000L;
            int retryCount = Objects.nonNull(retrySendIM.get(RETRY_COUNT)) ? (int) retrySendIM.get(RETRY_COUNT) : 3;
            int times = 1;
            while (times <= retryCount) {
                try {
                    long startTime = System.currentTimeMillis();
                    CloseableHttpResponse result = execute(post);
                    long endTime = System.currentTimeMillis();
                    if (times > 1) {
                        Map<String, Object> success = Maps.newHashMap();
                        success.put("count", times);
                        monitorLog.info(LogResult.getSuccessLogResult(AlarmMonitorTypeEnum.retrySendPagerDuty.name(), success, new HashMap<>()));
                    }
                    printToLog(post, result, endTime - startTime);
                    return;
                } catch (RuntimeException e) {
                    Map<String, Object> result = Maps.newHashMap();
                    result.put("count", times);
                    monitorLog.info(LogResult.getErrorLogResult(AlarmMonitorTypeEnum.retrySendPagerDuty.name(), e, new HashMap<>(), result));
                    Thread.sleep(retrySleepTime);
                    if (times > retryCount) {
                        throw new RuntimeException(e);
                    }
                } catch (UnsupportedEncodingException e) {
                    log.error("[PagerDutyEngine#encoding] error! ", e);
                } catch (ClientProtocolException e) {
                    log.error("[PagerDutyEngine#clientProtocol] error! ", e);
                } catch (IOException e) {
                    log.error("[PagerDutyEngine#send] error! ", e);
                } finally {
                    times++;
                    post.releaseConnection();
                }
            }
        } else {
            try {
                long startTime = System.currentTimeMillis();
                CloseableHttpResponse result = execute(post);
                long endTime = System.currentTimeMillis();
                printToLog(post, result, endTime - startTime);
            } catch (UnsupportedEncodingException e) {
                log.error("[PagerDutyEngine#encoding] error! ", e);
            } catch (ClientProtocolException e) {
                log.error("[PagerDutyEngine#clientProtocol] error! ", e);
            } catch (IOException e) {
                log.error("[PagerDutyEngine#send] error! ", e);
            } finally {
                post.releaseConnection();
            }
        }
    }

    private String generateAlertEventJson(Map<String, String> parameters, String title, String message, AlarmMatchRecord record, Map<String,Object> extendInfo, String newEndpoint, String runbook) {
        String routingKey = parameters.get(PAGERDUTY_INTEGRATION_KEY);

        AlertEvent alertEvent = new AlertEvent();
        alertEvent.setRouting_key(routingKey);
        alertEvent.setEvent_action("trigger");
        alertEvent.setLinks(Arrays.asList(new Link(notificationHelper.buildAlarmDetailUrl(newEndpoint, record), "Alarm Detail")));
        if (!CollectionUtils.isEmpty(extendInfo) && Boolean.TRUE.equals(extendInfo.get(TURN_ON_PAGER_DUTY_DEDUP))) {
            Map<String, String> dedupMetricsTags = Maps.newHashMap();
            if (StringUtils.isBlank(record.getPdGroupTags())) {
                dedupMetricsTags.putAll(record.getMetricsTags());
            } else {
                Map<String, String> metricsTags = record.getMetricsTags();
                List<String> tagList = Arrays.asList(record.getPdGroupTags().split(","));
                dedupMetricsTags.putAll(tagList.stream()
                        .filter(metricsTags::containsKey)
                        .collect(Collectors.toMap(tag -> tag, metricsTags::get)));
                if (dedupMetricsTags.isEmpty()) {
                    dedupMetricsTags.putAll(record.getMetricsTags());
                }
            }
            alertEvent.setDedup_key(record.generateDedupKey(dedupMetricsTags));
        }
        Payload payload = new Payload();
        payload.setSummary(title);
        payload.setSource("cube");
        String detail = message;
        if (StringUtils.isNotBlank(runbook)) {
            detail += "\nRunbook: " + runbook;
        }
        payload.setCustom_details(detail);
        payload.setSeverity(Severity.fromAlarmLevel(record.getAlarmLevel()).getSeverity());

        alertEvent.setPayload(payload);

        return JsonUtils.toJsonStringIgnoreExp(alertEvent);
    }

    private CloseableHttpResponse execute(HttpPost post) throws Exception {
        CloseableHttpResponse result = httpClient.execute(post);
        if (null == result || null == result.getStatusLine() || 202 != result.getStatusLine().getStatusCode()) {
            throw new RuntimeException("[PagerDutyEngine#send] Send Fail " + (result != null && result.getStatusLine() != null ? JsonUtils.toJsonStringIgnoreExp(result.getStatusLine()) : ""));
        }
        return result;
    }

    public static void printToLog(HttpPost post, CloseableHttpResponse result, long costTime) {
        if (LOG_INDEX.incrementAndGet() % LOG_COUNT == 0) {
            log.info("[PagerDutyEngine#send] request is: {}, result is: {}, cost time : {} ms",
                    JsonUtils.toJsonStringIgnoreExp(post), JsonUtils.toJsonStringIgnoreExp(result), costTime);
        }
        LOG_INDEX.compareAndSet(Long.MAX_VALUE, 0);
    }
}
