package us.zoom.infra.notification.channel.zoomchat;

import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import com.zoom.op.monitor.domain.alarm.AlarmExtensionRelation;
import com.zoom.op.monitor.domain.alarm.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.common.AlarmMonitorTypeEnum;
import us.zoom.cube.sdk.model.MonitorLog;
import us.zoom.infra.influx.model.alarm.AlarmMatchRecord;
import us.zoom.infra.loader.PiiTableLoader;
import us.zoom.infra.model.LogResult;
import us.zoom.infra.model.alarm.AlarmLevel;
import us.zoom.infra.notification.NotificationHelper;
import us.zoom.infra.notification.channel.ChannelEngine;
import us.zoom.infra.notification.channel.ChannelSendModel;
import us.zoom.infra.utils.CommonSplitConstants;
import us.zoom.infra.utils.DateUtils;
import us.zoom.infra.utils.IpUtils;
import us.zoom.cube.lib.utils.JsonUtils;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static us.zoom.infra.utils.DateUtils.FORMART1;

@Component
@Slf4j
@Order(1)
public class IMChannelEngine implements ChannelEngine {

    public static final String ZOOM_CHAT = "Zoom Chat";
    private Logger monitorLog = LoggerFactory.getLogger("Monitor");

    public static final String IM_MESSAGE_LINE_END_CHAR = "\u2029";

    private static final AtomicLong LOG_INDEX = new AtomicLong();

    private static final long LOG_COUNT = 100L;

    public static final String PARAM_NAME_ENDPOINT = "Endpoint";

    public static final String PARAM_NAME_VERIFICATION_TOKEN = "Verification Token";

    public static final String PARAM_NAME_OWNER = "Owner";

    public static final String IS_DEAULT = "Is Default";

    private CloseableHttpClient httpClient;

    private NotificationHelper notificationHelper;

    public static final String SWITCH_RETRY = "switchRetry";

    public static final String RETRY_SLEEP_TIME = "retrySleepTime";

    public static final String RETRY_COUNT = "retryCount";

    public static final String VERSION = "1.0.0";

    public static final String MEASURE = "alarm_send_fail_record";

    public static final String REGEX_PATTERN_URL = "(https?)://[-A-Za-z0-9+&@#/%?*=~_|!:,.;]+[-A-Za-z0-9+&*@#/%=~_|]";

    private final Pattern pattern = Pattern.compile(REGEX_PATTERN_URL);

    private static final String COLON = ":";

    private static final String NEW_LINE = "\n";

    @Autowired
    public void setNotificationHelper(NotificationHelper notificationHelper) {
        this.notificationHelper = notificationHelper;
    }

    @Autowired(required = false)
    public void setHttpClient(CloseableHttpClient httpClient) {
        this.httpClient = httpClient;
    }

    //required = false for cube-site, cube-alarm will import this instance
    @Autowired(required = false)
    private PiiTableLoader piiTableLoader;

    @Override
    public String getName() {
        return ZOOM_CHAT;
    }

    @Override
    public List<Parameter> getParameters() {

        return Arrays.asList(

                Parameter.builder()
                        .name(PARAM_NAME_ENDPOINT)
                        .placeHolder("https://inbots.zoom.us/incoming/hook/{channel-identity}")
                        .build(),

                Parameter.builder()
                        .name(PARAM_NAME_VERIFICATION_TOKEN)
                        .isSecret(true)
                        .placeHolder("")
                        .build(),

                Parameter.builder()
                        .name(IS_DEAULT)
                        .type("checkbox")
                        .build(),

                Parameter.builder()
                        .name(PARAM_NAME_OWNER)
                        .isRequired(false)
                        .explain("The owner must be a member of the notice channel")
                        .placeHolder("Please enter an email address")
                        .build()
        );
    }

    private String alarmDashboardEndpoint;

    @Value("${alarm.dashboard.endpoint:}")
    public void setAlarmDashboardEndpoint(String alarmDashboardEndpoint) {
        this.alarmDashboardEndpoint = alarmDashboardEndpoint;
    }

    @Value("${alarm.pii-redirect.dashboard.endpoint:}")
    private String piiRedirectDashboardEp;

    private void retrySendIM(HttpPost post, String imRequest, long retrySleepTime, int retryCount, AlarmMatchRecord record, Channel channel) throws Exception {
        int times = 1;
        while (times <= retryCount) {
            try {
                StringEntity stringEntity = new StringEntity(imRequest, "UTF-8");
                stringEntity.setContentType("application/json;charset=utf-8");
                post.setEntity(stringEntity);

                long startTime = System.currentTimeMillis();
                CloseableHttpResponse result = httpClient.execute(post);
                long endTime = System.currentTimeMillis();
                if (null == result || null == result.getStatusLine() || 200 != result.getStatusLine().getStatusCode()) {
                    sendIMFailLog(result, channel);
                    if (null != result && null != result.getStatusLine() && 401 == result.getStatusLine().getStatusCode()) {
                        Map<String, Object> success = Maps.newHashMap();
                        success.put("statusCode", 401);
                        addPublicParams(success, record, times);
                        monitorLog.info(LogResult.getSuccessLogResult(AlarmMonitorTypeEnum.retrySendIm.name(), "", success));
                        return;
                    }
                    throw new RuntimeException("Send Fail " + (result != null && result.getStatusLine() != null ? JsonUtils.toJsonStringIgnoreExp(result.getStatusLine()) : ""));
                }
                if (times > 1) {
                    Map<String, Object> success = Maps.newHashMap();
                    addPublicParams(success, record, times);
                    monitorLog.info(LogResult.getSuccessLogResult(AlarmMonitorTypeEnum.retrySendIm.name(), "", success));
                }
                result.getStatusLine().getStatusCode();
                printToLog(post, result, endTime - startTime);
                return;
            } catch (RuntimeException e) {
                Map<String, Object> result = Maps.newHashMap();
                addPublicParams(result, record, times);
                monitorLog.info(LogResult.getErrorLogResult(AlarmMonitorTypeEnum.retrySendIm.name(), e, result, ""));
                Thread.sleep(retrySleepTime);
                if (times >= retryCount) {
                    throw new RuntimeException(e);
                }
            } catch (UnsupportedEncodingException e) {
                log.error("[IMChannelEngine#encoding] error! ", e);
            } catch (ClientProtocolException e) {
                log.error("[IMChannelEngine#clientProtocol] error! ", e);
            } catch (IOException e) {
                log.error("[IMChannelEngine#send] error! ", e);
            } finally {
                times++;
                post.releaseConnection();
            }
        }
    }

    private void addPublicParams(Map<String, Object> result, AlarmMatchRecord record, int times) {
        result.put("tenantName", record.getTenantName());
        result.put("alarmName", record.getAlarmName());
        result.put("retryCount", times);
    }

    @Override
    public void send(ChannelSendModel channelSendModel) throws Exception  {

        Map<String, String> parameters = channelSendModel.getParameters();
        String title = channelSendModel.getTitle();
        String message = channelSendModel.getMessage();
        AlarmMatchRecord record = channelSendModel.getRecord();
        Map retrySendIM = channelSendModel.getRetrySendIM();
        String newEndpoint = channelSendModel.getNewEndpoint();
        String mentions = channelSendModel.getMentions();
        Boolean isMarkdown = channelSendModel.getIsMarkdown();
        Boolean includeAiResult = channelSendModel.getIncludeAiResult();
        Channel channel = channelSendModel.getChannel();
        String runbook = channelSendModel.getRunBook();

        String imRequest = buildIMRequest(message, title, record, newEndpoint, mentions, isMarkdown, includeAiResult, runbook);

        String endpoint = toFullFormat(parameters.get(PARAM_NAME_ENDPOINT));

        String verificationToken = parameters.get(PARAM_NAME_VERIFICATION_TOKEN);

        HttpPost post = new HttpPost(deleteInvalidEndChar(endpoint));

        post.addHeader("Authorization", deleteInvalidEndChar(verificationToken));
        // Add groupKey header for alarm notification grouping
        post.addHeader("groupKey", record.getNotifyGroupKey());

        boolean switchRetrySendIM = Objects.nonNull(retrySendIM) && !retrySendIM.isEmpty() && Objects.nonNull(retrySendIM.get(SWITCH_RETRY)) && (Boolean) retrySendIM.get(SWITCH_RETRY);
        if (switchRetrySendIM) {
            long retrySleepTime = Objects.nonNull(retrySendIM.get(RETRY_SLEEP_TIME)) ? Long.parseLong(String.valueOf(retrySendIM.get(RETRY_SLEEP_TIME))) : 1000L;
            int retryCount = Objects.nonNull(retrySendIM.get(RETRY_COUNT)) ? (int) retrySendIM.get(RETRY_COUNT) : 3;
            retrySendIM(post, imRequest, retrySleepTime, retryCount, record, channel);
        } else {
            try {
                StringEntity stringEntity = new StringEntity(imRequest, "UTF-8");
                stringEntity.setContentType("application/json;charset=utf-8");
                post.setEntity(stringEntity);

                long startTime = System.currentTimeMillis();
                CloseableHttpResponse result = httpClient.execute(post);
                long endTime = System.currentTimeMillis();
                if (null == result || null == result.getStatusLine() || 200 != result.getStatusLine().getStatusCode()) {
                    sendIMFailLog(result, channel);
                    throw new RuntimeException("Send Fail " + (result != null && result.getStatusLine() != null ? JsonUtils.toJsonStringIgnoreExp(result.getStatusLine()) : ""));
                }
                result.getStatusLine().getStatusCode();
                printToLog(post, result, endTime - startTime);

            } catch (UnsupportedEncodingException e) {
                log.error("[IMChannelEngine#encoding] error! ", e);
            } catch (ClientProtocolException e) {
                log.error("[IMChannelEngine#clientProtocol] error! ", e);
            } catch (IOException e) {
                log.error("[IMChannelEngine#send] error! ", e);
            } finally {
                post.releaseConnection();
            }
        }

    }

    public void sendIMFailLog(CloseableHttpResponse result, Channel channel) {
        try {
            String trackingId = Optional.of(result).map(r -> r.getFirstHeader("x-zm-trackingid")).map(Header::getValue).orElse(null);
            String responseBody = EntityUtils.toString(result.getEntity(), "UTF-8");
            String reasonPhrase = JsonUtils.toJsonStringIgnoreExp(result.getStatusLine().getReasonPhrase());
            MonitorLog log = MonitorLog.builder()
                    .withCubeVer(VERSION)
                    .withMeasure(MEASURE)
                    .withTs(System.currentTimeMillis())
                    .addTag("ip", IpUtils.getLocalIP())
                    .addTag("channelName", channel.getName())
                    .addTag("channelId", channel.getId())
                    .addTag("engineName", channel.getEngineName())
                    .addField("reasonPhrase", reasonPhrase)
                    .addField("responseBody", responseBody)
                    .addField("trackingId", trackingId)
                    .build();
            monitorLog.info(JsonUtils.toJsonStringIgnoreExp(log));
        } catch (Exception e) {
            log.error("Parsing http error: ", e);
        }
    }

    private String buildIMRequest(String srcMessage, String title, AlarmMatchRecord record, String newEndpoint, String mentions, Boolean isMarkdown, Boolean includeAiResult, String runbook) {

        long time = record.getTime();
        String timeString = DateUtils.format(new Date(time), FORMART1);
        String message = "[UTC Time: " + timeString + "]\n" + srcMessage;
        Map<String, Object> alarmInfo = Maps.newHashMap();
        Map<String, Object> content = Maps.newHashMap();
        Map<String, Object> head = setHead(title, record);
        //build aiContent with pii mask
        String aiContent = buildAiAppendContent(record);
        List<Map<String, Object>> body = setBody(record.getAlarmLevel(), message, aiContent, record.getAlarmName(), record.getTenantName(), mentions, isMarkdown, includeAiResult);
        setLink(body, record);
        setLink(notificationHelper.buildAlarmDetailUrl(newEndpoint, record), body);
        setRunbookLink(runbook, body);
        content.put("body", body);
        content.put("head", head);
        alarmInfo.put("content", content);

        return JsonUtils.toJsonStringIgnoreExp(alarmInfo);
    }

    private String buildAiAppendContent(AlarmMatchRecord record) {
        Map<String, Object> aiResult = record.getAiResult();
        if (CollectionUtils.isEmpty(aiResult)) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        Set<String> thisMetricsPiiColumns = piiTableLoader.getPiiField(record.getTenantName(), record.getMetricsName());
        for (Map.Entry<String, Object> entry : aiResult.entrySet()) {
            String key = entry.getKey();
            String value = thisMetricsPiiColumns.contains(key) ? CommonSplitConstants.MASKING : String.valueOf(entry.getValue());
            sb.append(key).append(COLON).append(value).append(NEW_LINE);
        }
        return sb.toString();
    }

    private void setLink(List<Map<String, Object>> body, AlarmMatchRecord record) {
        List<Map<String, Object>> sections = new ArrayList<>();
        Map<String, String> dashBoardLinkMap = new HashMap<>();
        Map<String, String> userLinkMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(record.getAlarmExtensionRelations())) {
            for (AlarmExtensionRelation alarmExtensionRelation : record.getAlarmExtensionRelations()) {
                switch (alarmExtensionRelation.getRelationType()) {
                    case dashboard:
                        String dashboardUrl = buildDashboardLinkParamsAndMaskPiiField(record, record.getMetricsTags(), alarmExtensionRelation.getRelationId());
                        Matcher matcherUrl = pattern.matcher(dashboardUrl);
                        if (matcherUrl.matches()) {
                            dashBoardLinkMap.put(alarmExtensionRelation.getRelationName(), dashboardUrl);
                        }
                        break;
                    case userlink:
                        String userLink = buildCustomLink(alarmExtensionRelation.getConfigValue());
                        Matcher matcher = pattern.matcher(userLink);
                        if (matcher.matches()) {
                            userLinkMap.put(alarmExtensionRelation.getRelationName(), userLink);
                        }
                        break;
                    default:
                }
            }
            if (!dashBoardLinkMap.isEmpty()) {
                setFont("", sections, "Dashboard links: ");
                dashBoardLinkMap.forEach((k, v) -> {
                    setAddLink(v, sections, k);
                });
            }

            if (!userLinkMap.isEmpty()) {
                setFont("", sections, "Customize links: ");
                userLinkMap.forEach((k, v) -> {
                    setAddLink(v, sections, k);
                });
            }
        }

        if (!CollectionUtils.isEmpty(sections)) {
            Map<String, Object> bodySection = new HashMap<>();
            bodySection.put("type", "section");
            bodySection.put("sections", sections);
            body.add(bodySection);
        }
    }

    public String buildCustomLink(String originUrl) {
        if (StringUtils.isBlank(originUrl)) {
            return originUrl;
        }
        String encodeUrl = NotificationHelper.encodeUrlQueryString(NotificationHelper.decode(originUrl));
        return StringUtils.isBlank(encodeUrl) ? originUrl : encodeUrl;
    }

    public String buildDashboardLinkParamsAndMaskPiiField(AlarmMatchRecord alarmMatchRecord , Map<String, String> metricsTags, String dashboardId) {
        StringBuilder requestUriBuilder = new StringBuilder();
        Map<String, String> maybeIncludeMaskTags = new HashMap<>();
        boolean existPiiTags = false;
        if (!metricsTags.isEmpty()) {
            Map<String, String> cleanedMetricsTags = metricsTags.entrySet().stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> entry.getValue().replaceAll("\\s+", "")
                    ));

            //to masking pii tag's value
            Set<String> thisMetricsPiiColumns = piiTableLoader.getPiiField(alarmMatchRecord.getTenantName(), alarmMatchRecord.getMetricsName());
            if(CollectionUtils.isEmpty(thisMetricsPiiColumns)) {
                maybeIncludeMaskTags.putAll(cleanedMetricsTags);
            } else {
                for(Map.Entry<String,String> tagValus : cleanedMetricsTags.entrySet()) {
                    String tagColumn = tagValus.getKey();
                    if(thisMetricsPiiColumns.contains(tagColumn)) {
                        existPiiTags = true;
                        maybeIncludeMaskTags.put("__alarmRecordId__", alarmMatchRecord.getId());
                        maybeIncludeMaskTags.put(tagColumn, CommonSplitConstants.MASKING);
                    }else {
                        maybeIncludeMaskTags.put(tagColumn, NotificationHelper.encode(tagValus.getValue()));
                    }
                }
            }
        }
        //build request URL
        requestUriBuilder.append(existPiiTags ? piiRedirectDashboardEp : alarmDashboardEndpoint).append(CommonSplitConstants.SLASH).append(dashboardId);
        //build request parameters
        if(!maybeIncludeMaskTags.isEmpty()) {
            requestUriBuilder.append("?").append(Joiner.on("&")
                    .useForNull("")
                    .withKeyValueSeparator("=")
                    .join(maybeIncludeMaskTags));
        }

        return requestUriBuilder.toString();
    }

    private String toFullFormat(String endpoint) {

        return StringUtils.substringBefore(endpoint, "?") + "?format=full";
    }

    private void setLink(String msgUrl, List<Map<String, Object>> body) {

        if (StringUtils.isNotBlank(msgUrl)) {

            Map<String, Object> record = new HashMap<>();
            record.put("type", "message");
            record.put("text", "Click here for details.");
            record.put("link", msgUrl);
            body.add(record);
        }
    }

    private void setAddLink(String msgUrl, List<Map<String, Object>> sections, String text) {
        if (StringUtils.isNotBlank(msgUrl)) {
            Map<String, Object> record = new HashMap<>();
            record.put("type", "message");
            record.put("text", text);
            if (StringUtils.isNotEmpty(msgUrl)) {
                record.put("link", msgUrl);
            }
            sections.add(record);
        }
    }

    private void setRunbookLink(String runbookLink, List<Map<String, Object>> body) {
        if(StringUtils.isNotBlank(runbookLink)){
            Map<String, Object> record = new HashMap<>();
            record.put("type", "message");
            record.put("text", "Runbook");
            record.put("link", runbookLink);
            body.add(record);
        }
    }

    private void setFont(String msgUrl, List<Map<String, Object>> sections, String text) {
        Map<String, Object> record = new HashMap<>();
        record.put("type", "message");
        record.put("text", text);
        record.put("style", fontMap);
        sections.add(record);
    }

    static Map<String, Object> fontMap = Maps.newHashMap();
    static {
        fontMap.put("color", "#868683");
        fontMap.put("bold", false);
        fontMap.put("italic", false);
    }

    static Map<Integer, String> alarmLevelColorMap = Maps.newHashMap();
    static {
        alarmLevelColorMap.put(AlarmLevel.FATAL.getValue(), "#FF00FF");
        alarmLevelColorMap.put(AlarmLevel.ERROR.getValue(), "#F27E29");
        alarmLevelColorMap.put(AlarmLevel.WARN.getValue(), "#FFC000");
        alarmLevelColorMap.put(AlarmLevel.INFO.getValue(), "#4793F1");
        alarmLevelColorMap.put(-1, "#FF00FF");
    }

    static Map<Integer, String> barColorMap = Maps.newHashMap();
    static {
        barColorMap.put(AlarmLevel.FATAL.getValue(), "#FF00FF");
        barColorMap.put(AlarmLevel.ERROR.getValue(), "#F27E29");
        barColorMap.put(AlarmLevel.WARN.getValue(), "#FFC000");
        barColorMap.put(AlarmLevel.INFO.getValue(), "#4793F1");
        barColorMap.put(-1, "#FF00FF");
    }

    private List<Map<String, Object>> setBody(AlarmLevel alarmLevel, String alarmContent, String aiContent, String alarmName, String serviceName, String mentions, Boolean isMarkdown, Boolean includeAiResult) {

        String alarmLevelPeril = Optional.ofNullable(alarmLevel).map(AlarmLevel::getPeril).orElse("No Value");
        int alarmLevelValue = Optional.ofNullable(alarmLevel).map(AlarmLevel::getValue).orElse(-1);
        String alarmColor = alarmLevelColorMap.getOrDefault(alarmLevelValue, "#FF00FF");

        List<Map<String, Object>> body = new ArrayList<>();
        Map<String, Object> bodySection = new HashMap<>(3);
        bodySection.put("type", "section");
        bodySection.put("sidebar_color", alarmColor);

        List<Map<String, Object>> sections = new ArrayList<>();

        if (Boolean.TRUE.equals(isMarkdown)) {
            //AlarmLevel Style
            Map<String, Object> alarmLevelKeyStyle = new HashMap<>(3);
            Map<String, Object> alarmLevelValStyle = new HashMap<>(3);
            alarmLevelKeyStyle.put("bold", false);
            alarmLevelKeyStyle.put("italic", false);
            alarmLevelKeyStyle.put("color", "#868683");
            alarmLevelValStyle.put("bold", true);
            alarmLevelValStyle.put("italic", false);
            alarmLevelValStyle.put("color", alarmColor);

            //AlarmLevel
            Map<String, Object> alarmLevelKey = new HashMap<>(3);
            alarmLevelKey.put("type", "message");
            alarmLevelKey.put("text", "Alarm Level");
            alarmLevelKey.put("style", alarmLevelKeyStyle);
            sections.add(alarmLevelKey);
            Map<String, Object> alarmLevelVal = new HashMap<>(3);
            alarmLevelVal.put("type", "message");
            alarmLevelVal.put("text", alarmLevelPeril);
            alarmLevelVal.put("style", alarmLevelValStyle);
            sections.add(alarmLevelVal);

            if (StringUtils.isNotBlank(alarmName)) {
                //AlarmName Style
                Map<String, Object> alarmNameKeyStyle = new HashMap<>(3);
                alarmNameKeyStyle.put("bold", false);
                alarmNameKeyStyle.put("italic", false);
                alarmNameKeyStyle.put("color", "#868683");

                //Alarm Name
                Map<String, Object> alarmNameKey = new HashMap<>(3);
                alarmNameKey.put("type", "message");
                alarmNameKey.put("text", "Alarm Name");
                alarmNameKey.put("style", alarmNameKeyStyle);
                sections.add(alarmNameKey);
                Map<String, Object> alarmNameVal = new HashMap<>(3);
                alarmNameVal.put("type", "message");
                alarmNameVal.put("text", alarmName);
                sections.add(alarmNameVal);
            }

            if (StringUtils.isNotBlank(serviceName)) {
                //serviceName Style
                Map<String, Object> serviceNameKeyStyle = new HashMap<>(3);
                serviceNameKeyStyle.put("bold", false);
                serviceNameKeyStyle.put("italic", false);
                serviceNameKeyStyle.put("color", "#868683");

                //Service Name
                Map<String, Object> serviceNameKey = new HashMap<>(3);
                serviceNameKey.put("type", "message");
                serviceNameKey.put("text", "Service Name");
                serviceNameKey.put("style", serviceNameKeyStyle);
                sections.add(serviceNameKey);
                Map<String, Object> serviceNameVal = new HashMap<>(3);
                serviceNameVal.put("type", "message");
                serviceNameVal.put("text", serviceName);
                sections.add(serviceNameVal);
            }

            //AlarmContent Style
            Map<String, Object> alarmContentKeyStyle = new HashMap<>(3);
            alarmContentKeyStyle.put("bold", false);
            alarmContentKeyStyle.put("italic", false);
            alarmContentKeyStyle.put("color", "#868683");

            //Alarm Content
            String contentVal = StringUtils.isEmpty(alarmContent) ? "No Content" : alarmContent;
            Map<String, Object> alarmContentKey = new HashMap<>(3);
            alarmContentKey.put("type", "message");
            alarmContentKey.put("text", "Alarm Content");
            alarmContentKey.put("style", alarmContentKeyStyle);
            sections.add(alarmContentKey);
            Map<String, Object> alarmContentVal = new HashMap<>(3);
            alarmContentVal.put("type", "message");
            alarmContentVal.put("text", contentVal);
            alarmContentVal.put("is_markdown_support", true);
            sections.add(alarmContentVal);

            if (Boolean.TRUE.equals(includeAiResult) && StringUtils.isNotBlank(aiContent)) {
                //AiContent Style
                Map<String, Object> aiContentKeyStyle = new HashMap<>(3);
                aiContentKeyStyle.put("bold", false);
                aiContentKeyStyle.put("italic", false);
                aiContentKeyStyle.put("color", "#868683");
                //AI Content
                Map<String, Object> aiContentKey = new HashMap<>(3);
                aiContentKey.put("type", "message");
                aiContentKey.put("text", "AI Check Result");
                aiContentKey.put("style", aiContentKeyStyle);
                sections.add(aiContentKey);
                Map<String, Object> aiContentVal = new HashMap<>(3);
                aiContentVal.put("type", "message");
                aiContentVal.put("text", aiContent);
                sections.add(aiContentVal);
            }
        } else {
            Map<String, Object> record = new HashMap<>(2);
            record.put("type", "fields");
            List<Map<String, Object>> fields = new ArrayList<>();
            record.put("items", fields);

            //Alarm Level
            Map<String, Object> level = new HashMap<>(2);
            Map<String, Object> style = new HashMap<>(3);
            style.put("bold", true);
            style.put("italic", false);
            style.put("color", alarmColor);
            level.put("key", "Alarm  Level");
            level.put("value", alarmLevelPeril);
            level.put("style", style);
            fields.add(level);

            //Alarm Name
            if (StringUtils.isNotBlank(alarmName)) {
                Map<String, Object> name = new HashMap<>(3);
                name.put("key", "Alarm Name");
                name.put("value", alarmName);
                fields.add(name);
            }

            //Service Name
            if (StringUtils.isNotBlank(serviceName)) {
                Map<String, Object> serviceNameMap = new HashMap<>(3);
                serviceNameMap.put("key", "Service Name");
                serviceNameMap.put("value", serviceName);
                fields.add(serviceNameMap);
            }

            //AlarmContent
            String content = StringUtils.isEmpty(alarmContent) ? "No Content" : alarmContent;
            HashMap<String, Object> contentMap = new HashMap<>(5);
            contentMap.put("key", "Alarm Content");
            contentMap.put("value", content);
            fields.add(contentMap);

            //AiContent
            if (Boolean.TRUE.equals(includeAiResult) && StringUtils.isNotBlank(aiContent)) {
                HashMap<String, Object> aiContentMap = new HashMap<>(5);
                aiContentMap.put("key", "AI Check Result");
                aiContentMap.put("value", aiContent);
                fields.add(aiContentMap);
            }

            sections.add(record);
        }

        //add mention info
        if (StringUtils.isNotBlank(mentions)) {
            Map<String, Object> mentionGroup = new HashMap<>(2);
            mentionGroup.put("type", "message");
            mentionGroup.put("is_markdown_support", true);
            mentionGroup.put("text", mentions);
            sections.add(mentionGroup);
        }
        bodySection.put("sections", sections);
        body.add(bodySection);
        return body;
    }


    private static Map<String, Object> setHead(String title, AlarmMatchRecord record) {

        Map<String, Object> head = new HashMap<>(5);

        Map<String, Object> style = new HashMap<>(5);
        style.put("color", getStyleColor(record.getAlarmLevel().getValue()));
        style.put("bold", true);
        style.put("italic", false);
        head.put("style", style);
        head.put("text", StringUtils.isNotBlank(title) ? title : "No Title");
        return head;
    }

    private static String getBarColor(int level) {
        switch (level) {
            case 1:
                return "#FF0000";
            case 2:
                return "#F27E29";
            case 3:
                return "#FFC000";
            case 4:
                return "#4793F1";
            default:
                return "#330000";
        }
    }

    private static String getStyleColor(int level) {
        switch (level) {
            case 1:
                return "#FF0000";
            case 2:
                return "#F27E29";
            case 4:
                return "#4793F1";
            default:
                return "#FFC000";
        }
    }

    private String deleteInvalidEndChar(String str) {

        if (str.endsWith(IM_MESSAGE_LINE_END_CHAR)) {

            return str.substring(0, str.length() - 1);
        }

        return str;
    }

    public static void printToLog(HttpPost post, CloseableHttpResponse result, long costTime) {

        if (LOG_INDEX.incrementAndGet() % LOG_COUNT == 0) {
            log.info("[IMChannelEngine#send] request is: {}, result is: {}, cost time : {} ms",
                    JsonUtils.toJsonStringIgnoreExp(post), JsonUtils.toJsonStringIgnoreExp(result), costTime);
        }
        LOG_INDEX.compareAndSet(Long.MAX_VALUE, 0);
    }
}
