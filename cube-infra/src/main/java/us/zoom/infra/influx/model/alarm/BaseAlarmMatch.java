package us.zoom.infra.influx.model.alarm;

import com.google.common.collect.Maps;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.apache.commons.collections.MapUtils;
import us.zoom.infra.enums.AlarmRecordStatusEnum;
import us.zoom.infra.enums.AlarmSourceTypeEnum;
import us.zoom.infra.model.alarm.AlarmLevel;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2020/4/24 4:17 PM
 */
@Data
@FieldDefaults(level = AccessLevel.PROTECTED)
public class BaseAlarmMatch {

    AlarmSourceTypeEnum alarmSourceType;

    String tenantName;

    String metricsId;

    String metricsName;

    /**
     * @see us.zoom.infra.enums.MetricsTypeEnum
     */
    Integer metricsType;

    Integer aggPeriod;

    Integer subPeriod;

    String aggTimeZone;

    String alarmName;

    String title;

    String alarmId;

    String alarmRuleId;

    AlarmLevel alarmLevel;

    String tagKey;

    String groupTagKey;

    String notifyGroupKey;

    /**
     *
     */
    Map<String, String> metricsTags = Maps.newHashMap();

    Map<String,Object> metricFields;

    Boolean isLastCountMatched = false;

    /**
     * IM
     */
    String noticeContent;

    /**
     * M
     */
    Boolean isNoticed;

    Integer lastCount;

    String tenantId;

    /**
     * Alarm Status
     */
    AlarmRecordStatusEnum status;

    /**
     * Alarm ack expire time
     */
    Long ackExpireTime;

    /**
     * pre AlarmRecord id, used in suppressed
     */
    String preRecordId;


    HashMap<String, String> tags;

    public Map<String, String> fetchTags() {
        if(MapUtils.isNotEmpty(this.tags)){
            return this.tags;
        }
        this.tags=createFetchTags(alarmLevel != null ? alarmLevel.getLevel(): null,alarmId,alarmRuleId,metricsTags);
        return this.tags;
    }

    public static HashMap<String, String> createFetchTags(String alarmLevel, String alarmId, String ruleId, Map<String, String> tags) {

        HashMap<String, String> fetchTags = Maps.newHashMap();
        if (null != alarmLevel) {
            fetchTags.put("alarmLevel", alarmLevel);
        }

        if (null != alarmId) {
            fetchTags.put("alarmId", alarmId);
        }

        if (null != ruleId) {
            fetchTags.put("alarmRuleId", ruleId);
        }

        if (null != tags) {
            fetchTags.putAll(tags);
        }

        return  fetchTags;
    }


    public Map<String, Object> fetchFields() {

        Map<String, Object>  fields = new HashMap<>(16);
        if (null != alarmSourceType) {
            fields.put("alarmSourceType", alarmSourceType.getValue());
        }
        if (null != tenantName) {
            fields.put("tenantName", tenantName);
        }
        if (null != metricsId) {
            fields.put("metricsId", metricsId);
        }
        if(null != metricsName) {
            fields.put("metricsName", metricsName);
        }
        if (null != aggPeriod) {
            fields.put("aggPeriod", aggPeriod);
        }
        if (null != subPeriod) {
            fields.put("subPeriod", subPeriod);
        }
        if(null != aggTimeZone) {
            fields.put("aggTimeZone", aggTimeZone);
        }
        if (null != alarmName) {
            fields.put("alarmName", alarmName);
        }
        if (null != title) {
            fields.put("title", title);
        }
        if (null != isLastCountMatched) {
            fields.put("isLastCountMatched", String.valueOf(isLastCountMatched));
        }
        if (null != noticeContent) {
            fields.put("noticeContent", noticeContent);
        }

        if (null != isNoticed) {
            fields.put("isNoticed", String.valueOf(isNoticed));
        }

        return fields;

    }

}
