package us.zoom.infra.influx.model.alarm;

import com.zoom.op.monitor.domain.alarm.AlarmExtensionRelation;
import com.zoom.op.monitor.domain.alarm.AlarmRule;
import com.zoom.op.monitor.domain.alarm.Notification;
import com.zoom.op.monitor.domain.alarm.TagCondition;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.apache.commons.codec.digest.DigestUtils;
import us.zoom.infra.enums.AlarmEventTypeEnum;
import us.zoom.infra.influx.util.TagUtils;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2020/4/24 10:43 AM
 * <p>
 * 
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AlarmMatchRecord extends BaseAlarmMatch {

    String id;

    /**
     * ms
     */
    long time;

    long inhibitInterval;

    int inhibitThreshold;

    private List<TagCondition> tagConditions;

    private List<AlarmRule> rules;

    private AlarmRule hittedRule;

    private AlarmRuleHittedValue hittedValue;

    private List<Notification> notifications;

    private String allTags;

    private List<AlarmExtensionRelation> alarmExtensionRelations;

    private boolean containsPii;

    private AlarmEventTypeEnum alarmEventType;

    private Date eventGmtCreate;

    private Date recordGmtCreate;

    private String pdGroupTags;

    private Map<String, Object> aiResult;

    private Map<String, Object> histogramResult;

    private Map<String, Object> aiVarMap;

    private String alarmSilenceId;

    private String alarmSilenceName;

    private boolean isNoData = false;

    private RecordExtendInfo recordExtendInfo;

    @Override
    public String toString() {
        return JsonUtils.toJsonStringIgnoreExp(this);
    }


    public void generateId() {
        String tagKey = TagUtils.createKey(metricsTags);
        String builder = alarmId + "_" + alarmRuleId + "_" + tagKey + "_" + time;
        this.id = DigestUtils.md5Hex(builder);
    }

    public String generateDedupKey(Map<String, String> tags) {
        String tagKey = TagUtils.createKey(tags);
        String builder = alarmId + "_" + alarmRuleId + "_" + tagKey;
        return DigestUtils.md5Hex(builder);
    }

    /**
     * Set tag keys (tagKey, groupTagKey, and notifyGroupKey) in one method
     * 
     * @param tags metric tags map
     * @param groupTagsList group tags list for generating groupTagKey
     */
    public void setKeys(Map<String, String> tags, List<String> groupTagsList) {
        // 1. Set tagKey
        this.tagKey = TagUtils.createKey(tags);
        
        // 2. Set groupTagKey
        this.groupTagKey = TagUtils.createKey(tags, groupTagsList);
        
        // 3. Generate and set notifyGroupKey
        this.notifyGroupKey = generateNotifyGroupKey();
    }

    /**
     * Generate notification group key for aggregating similar alarms
     * 
     * Generation rules:
     * 1. Use groupTagKey first, if empty then use tagKey
     * 2. Use the alarm record's time field, if empty then use current system time
     * 3. Aggregate time by minute (time/60s) to group alarms within the same minute
     * 4. Format: alarmId:tagKey:alarmLevel:timeWindow
     * 5. If both tagKey and groupTagKey are empty, return a random value
     * 
     * @return notification group key
     */
    public String generateNotifyGroupKey() {
        // 1. Use groupTagKey first, otherwise use tagKey
        String tagKey = org.apache.commons.lang3.StringUtils.isNotBlank(groupTagKey) ? 
                groupTagKey : this.tagKey;
        
        // If both are empty, generate a random value
        if (org.apache.commons.lang3.StringUtils.isBlank(tagKey)) {
            tagKey = "random_" + java.util.UUID.randomUUID();
        }
        
        // 2. Get timestamp
        long timestamp = this.time;
        if (timestamp <= 0) {
            timestamp = System.currentTimeMillis();
        }
        
        // 3. Aggregate by minute (time / 60 seconds)
        long timeWindow = timestamp / 60000;
        
        // 4. Get alarm level
        String alarmLevelStr = alarmLevel != null ? 
                alarmLevel.getLevel() : "UNKNOWN";
        
        // 5. Generate notifyGroupKey: alarmId:tagKey:alarmLevel:timeWindow
        return String.format("%s:%s:%s:%d", 
                alarmId != null ? alarmId : "unknown", 
                tagKey, 
                alarmLevelStr, 
                timeWindow);
    }

    @Data
    public static class RecordExtendInfo {
        private Long inputTime;
    }

    public static void main(String[] args) {
        String builder = DigestUtils.md5Hex("builder");
        System.out.println(builder);
        System.out.println(builder.length());
    }
}
