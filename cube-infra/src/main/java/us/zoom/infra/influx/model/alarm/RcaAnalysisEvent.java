package us.zoom.infra.influx.model.alarm;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * RCA (Root Cause Analysis) analysis event record class
 * Used to record detailed information of RCA analysis tasks, including information needed for alarm message replies
 *
 * <AUTHOR>
 * @date 2024/12/28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RcaAnalysisEvent {
    
    /**
     * Task type, e.g. "smartRCA-alarm"
     */
    @JsonProperty("task")
    private String task;
    
    /**
     * Service name, e.g. "Infra_WebArch_dsp-manager"
     */
    @JsonProperty("service")
    private String service;
    
    /**
     * Alarm ID, e.g. "62bca488-0cde-45f6-83be-c42c69ab1eec"
     */
    @JsonProperty("alarmId")
    private String alarmId;
    
    /**
     * Begin timestamp (milliseconds)
     */
    @JsonProperty("beginTs")
    private Long beginTs;
    
    /**
     * End timestamp (milliseconds)
     */
    @JsonProperty("endTs")
    private Long endTs;
    
    /**
     * Alarm message group key, used for replying to alarm messages
     */
    @JsonProperty("groupKey")
    private String groupKey;
    
    /**
     * Channel ID, corresponding to the specific channel for alarm notifications
     */
    @JsonProperty("channelId")
    private String channelId;

    /**
     * Endpoint, used for replying to alarm messages
     */
    @JsonProperty("endpoint")
    private String endpoint;

    /**
     * Verify token, used for replying to alarm messages
     */
    @JsonProperty("verifyToken")
    private String verifyToken;
    
    /**
     * Create an RCA analysis event instance
     * 
     * @param task task type
     * @param service service name
     * @param alarmId alarm ID
     * @param beginTs begin timestamp
     * @param endTs end timestamp
     * @param groupKey alarm group key
     * @param channelId channel ID
     * @param endpoint endpoint
     * @param verifyToken verify token
     * @return RCA analysis event instance
     */
    public static RcaAnalysisEvent create(String task, String service, String alarmId, 
                                          Long beginTs, Long endTs, String groupKey, String channelId,
                                          String endpoint, String verifyToken) {
        return RcaAnalysisEvent.builder()
                .task(task)
                .service(service)
                .alarmId(alarmId)
                .beginTs(beginTs)
                .endTs(endTs)
                .groupKey(groupKey)
                .channelId(channelId)
                .endpoint(endpoint)
                .verifyToken(verifyToken)
                .build();
    }
} 