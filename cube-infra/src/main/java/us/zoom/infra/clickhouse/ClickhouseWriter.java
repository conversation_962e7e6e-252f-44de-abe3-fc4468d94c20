package us.zoom.infra.clickhouse;

import cn.hutool.core.thread.BlockPolicy;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListeningExecutorService;
import com.google.common.util.concurrent.MoreExecutors;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.infra.enums.FieldTypeEnum;
import us.zoom.infra.thread.NamedThreadFactory;
import us.zoom.infra.thread.SlientSleep;

import java.sql.*;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static us.zoom.infra.clickhouse.ClickhouseFlushType.*;
import static us.zoom.infra.clickhouse.ClickhouseSchema.TIME_COLUMN_NAME;
import static us.zoom.infra.clickhouse.ClickhouseWritingStatistic.*;

/**
 * <AUTHOR> Wang
 * @date 2022-01-25 10:11
 */
public class ClickhouseWriter {

    protected ScheduledExecutorService scheduler;

    protected volatile long flushMajorIntervalMillSecond;

    private volatile long flushMinorIntervalMillSecond;

    private volatile int flushMaxRecordCount;

    private volatile int flushMinRecordCountPerTable;

    private volatile int queueSize;

    protected volatile long queueMemoryLimit;

    private volatile long singleTableMemoryLimit;

    protected volatile int threadPoolSize;

    private volatile int flushPoolSize;

    protected volatile MemoryLimitQueue<ClickhouseData> queue;

    protected volatile MemoryLimitQueue<ClickhouseData> cacheQueue;

    private Set<String>allEnvs=new HashSet<>();

    private Set<String>noWritable=new HashSet<>();

    private volatile int flushParallelism;

    private boolean waitAvaliableConnection;

    private boolean flushWhenHoldTooLong;

    private boolean memFlushFlag;

    private Random random=new Random();

    private AtomicLong lastMinorFlushScheduleTime =new AtomicLong();
    protected AtomicLong lastMajorFlushScheduleTime =new AtomicLong();

    protected AtomicLong lastMajorFlushTime =new AtomicLong();

    private static final Logger monitorLog = LoggerFactory.getLogger("Monitor");
    private static final Logger log = LoggerFactory.getLogger(ClickhouseWriter.class);


    protected static final long MIN_TIME=1000000000* 1000L;
    protected static final long MAX_TIME=MIN_TIME* 10L;
    protected static int queueSizeMax=100 * 10000;
    protected static int DEFAULT_FLUSH_TASK_POOL_SIZE = 100;
    protected static int DEFAULT_SCHEDULE_DELAY_MS = 500;

    private int tryTimes = 5;

    private int sleepTime = 30;

    protected ClickhouseEnvProxy clickhouseEnvProxy;

    protected final List<ExecutorService>flushPoolList=new LinkedList<>();

    protected volatile ThreadPoolExecutor prepareFlushPool;

    protected volatile ThreadPoolExecutor currentFlushPool;

    ListeningExecutorService reloadExecutor = MoreExecutors.listeningDecorator(
            new ThreadPoolExecutor(
                    16,
                    32,
                    60L, TimeUnit.SECONDS,
                    new LinkedBlockingQueue<>(10000),
                    new ThreadPoolExecutor.CallerRunsPolicy()
            )
    );
//    Cache<String, ClickhouseSchema> schemaCache= CacheBuilder.newBuilder()
//            .expireAfterWrite(10, TimeUnit.MINUTES).build();

    LoadingCache<String, ClickhouseSchema> schemaCache = CacheBuilder.newBuilder()
            .refreshAfterWrite(5, TimeUnit.MINUTES)
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build(new CacheLoader<>() {
                @Override
                public ClickhouseSchema load(String table) {
                    String[] parts = table.split("\\.", 2);
                    return getSchema(parts[0], parts[1]);
                }

                @Override
                public ListenableFuture<ClickhouseSchema> reload(String key, ClickhouseSchema oldValue) {
                    String[] parts = key.split("\\.", 2);
                    return reloadExecutor.submit(() -> {
                        try {
                            return getSchema(parts[0], parts[1]);
                        } catch (Exception e) {
                            log.warn("Reload schema failed for {}", key, e);
                            return oldValue;
                        }
                    });
                }
            });

    private static String unitTag=null;

    private static String targetEnv=null;

    private static ClickHouseShuntConfig shuntConfig=new ClickHouseShuntConfig();

    private static Set<String> bufferTables=new HashSet<>();

    private static Set<String> writingToDistributed=new HashSet<>();

    private static String runningEnv=null;

    public ClickhouseWriter(ClickhouseEnvProxy clickhouseEnvProxy) {
        this.clickhouseEnvProxy = clickhouseEnvProxy;
    }

    //main writing point
    public boolean write(String db, String table, Map<String,Object> row, Timestamp ts, long memorySize, boolean blocking){
        return write(db,table,row,ts,memorySize,blocking,false);
    }

    //braodcast writing point
    public boolean write(String db, String table, Map<String,Object> row, Timestamp ts, long memorySize, boolean blocking,boolean broadcast){
        if(org.springframework.util.CollectionUtils.isEmpty(row)){
            return true;
        }
        long now=System.currentTimeMillis();
        row.put(TIME_COLUMN_NAME,ts);
        row.put(ARRIVE_IN_CLICKHOUSE_WRITER,now);
        if(!row.containsKey(DELAY_UPON_ARRIVAL)) {
            row.put(DELAY_UPON_ARRIVAL, now - ts.getTime());
        }
        if(!row.containsKey(KAFKA_CREATE_TIME)){
            row.put(KAFKA_CREATE_TIME,now);
        }
        return appendData(new ClickhouseData(new ClickhouseKey(db, table), row, memorySize, broadcast), blocking);
    }

    public synchronized void refreshQueue() {
        flushCacheQueue();
        int currentSize = this.queue.getCapacity();
        long currentByteSize = this.queue.getByteCapacity();
        log.info("refreshQueue start {} {}, {} {}", queueSize, currentSize, queueMemoryLimit, currentByteSize);
        if (this.queueSize != currentSize || this.queueMemoryLimit != currentByteSize) {
            if (CollectionUtils.isEmpty(cacheQueue)) {
                cacheQueue = this.queue;
                this.queue = new MemoryLimitQueue<>(queueSize, queueMemoryLimit);
                flushCacheQueue();
            }
        }
        log.info("refreshQueue finish {} {}, {} {}", queueSize, this.queue.getCapacity(), queueMemoryLimit, this.queue.getByteCapacity());
    }

    public void flushCacheQueue() {
        while (CollectionUtils.isNotEmpty(cacheQueue)) {
            try {
                ClickhouseData data = cacheQueue.take();
                this.queue.put(data);
            } catch (InterruptedException e) {
                log.error("refresh queue finish", e);
            }
        }
    }
    public ClickhouseWriter start() {
        this.queue = new MemoryLimitQueue<>(queueSizeMax, queueMemoryLimit);
        Runnable scheduleFlush= () -> {
            try {
                long current = System.currentTimeMillis();
                long lastMajor = lastMajorFlushScheduleTime.get();
                long lastMinor = lastMinorFlushScheduleTime.get();
                if (current-lastMajor > flushMajorIntervalMillSecond) {
                    if (lastMajorFlushScheduleTime.compareAndSet(lastMajor, current)) {
                        log.info("Triggering major flush due to time interval: {}ms", current - lastMajor);
                        addTaskIfNotExist(QUARTZ_FORCEALL);
                        return;
                    }
                }
                if (current-lastMinor  > flushMinorIntervalMillSecond) {
                    if (lastMinorFlushScheduleTime.compareAndSet(lastMinor, current)) {
                        log.info("Triggering minor flush due to time interval: {}ms", current - lastMinor);
                        addTaskIfNotExist(QUARTZ_SUGGEST);
                        return;
                    }
                }
                if(queue.byteSize()>queueMemoryLimit*.8){
                    log.info("Triggering flush due to queue memory size: current={}B, limit={}B",
                            queue.byteSize(), queueMemoryLimit);
                    addTaskIfNotExist(QUEUE_MEMORY_SIZE_SUGGEST);
                    return;
                }
                if(queue.size()>= flushMaxRecordCount){
                    log.info("Triggering flush due to queue record count: current={}, limit={}",
                            queue.size(), flushMaxRecordCount);
                    addTaskIfNotExist(QUEUE_LENGTH_LIMIT);
                }
            }catch (Exception e) {
                log.error("Error when schedule flush task",e);
            }
        };
        prepareFlushPool = new ThreadPoolExecutor(threadPoolSize, threadPoolSize,
                60, TimeUnit.SECONDS, new ArrayBlockingQueue(DEFAULT_FLUSH_TASK_POOL_SIZE),
                new NamedThreadFactory("clickhouse_flush_main_task"), loggingHandler);
        scheduler = Executors.newSingleThreadScheduledExecutor();
        scheduler.scheduleWithFixedDelay(scheduleFlush,0,DEFAULT_SCHEDULE_DELAY_MS,TimeUnit.MILLISECONDS);
        Runtime.getRuntime().addShutdownHook(new FinalFlush());
        return this;
    }

    RejectedExecutionHandler loggingHandler = (r, executor) -> {
        log.warn("Task {} was rejected from executor {}", r, executor);
    };

    private void addTaskIfNotExist(ExecTask task) {
        if (!prepareFlushPool.getQueue().contains(task)) {
            prepareFlushPool.submit(task);
        }else {
            log.info("Flush task already in queue, skipping: {} | QueueSize={}",
                    task,
                    prepareFlushPool.getQueue().size());
        }
    }

    class ExecTask implements Runnable {

        private ClickhouseFlushType type;

        public ExecTask(ClickhouseFlushType type) {
            this.type = type;
        }

        @Override
        public void run() {
            if (type.equals(ClickhouseFlushType.QUARTZ_FORCEALL) || type.equals(QUEUE_MEMORY_SIZE_SUGGEST)) {
                flush(true, type);
            } else if(memFlushFlag && (type.equals(QUEUE_MEMORY_SIZE_SUGGEST) || type.equals(QUEUE_LENGTH_LIMIT))) {
                flush(true, type);
            } else {
                flush(false, type);
            }
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) {
                return true;
            }
            if (obj == null || getClass() != obj.getClass()) {
                return false;
            }
            ExecTask other = (ExecTask) obj;
            return type == other.type;
        }

        @Override
        public int hashCode() {
            return 31 + (type != null ? type.hashCode() : 0);
        }

        @Override
        public String toString() {
            return "ExecTask{type=" + type + "}";
        }
    }

    private final Set<ClickhouseFlushType> inFlightTypes = ConcurrentHashMap.newKeySet();

    private void addTaskIfNotExist(ClickhouseFlushType type) {
        if (inFlightTypes.add(type)) {
            prepareFlushPool.submit(() -> {
                try {
                    new ExecTask(type).run();
                } finally {
                    inFlightTypes.remove(type);
                }
            });
        } else {
            log.info("Flush task already scheduled or running: {}", type);
        }
    }

    class FinalFlush extends Thread {
        public void run() {
            log.info("final flush start...");
            scheduler.shutdown();
            prepareFlushPool.shutdown();
            forceTriggerFlush();
            try {
                prepareFlushPool.awaitTermination(10, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
                log.error("final flush, prepareFlushPool final flush interrupted", e);
            }
            flushPoolList.forEach(ExecutorService::shutdown);
            flushPoolList.forEach(u-> {
                try {
                    u.awaitTermination(10, TimeUnit.SECONDS);
                } catch (InterruptedException e) {
                    log.error("final flush, flushPool final flush interrupted", e);
                }
            });
            log.info("final flush end...");
        }
    }


    /**
     *
     * @param data
     * @return the remaining if any
     */
    public List<ClickhouseData> putback(List<ClickhouseData>data) {
        int i=0;
        for(;i< data.size();i++){
            if(!appendData(data.get(i), false)){
                //indicating the queue is full, need to flush the remaining
                break;
            }
        }
        return data.subList(i,data.size());
    }

    public boolean appendData(ClickhouseData data,boolean block){
        boolean ret=true;
//        long totalSize = GraphLayout.parseInstance(data).totalSize();
        try {
            if(block) {
                queue.put(data);
            }else{
                ret=queue.offer(data);
            }
        } catch (InterruptedException e) {
            log.error("clickhouse_writing_queue_interrupted data to clickhouse {}.{} is drop due to interruption."
                    ,data.getKey().getDbName(),data.getKey().getTableName());
        }

        return ret;
    }

    public void suggestQueueMemoryLimitFlush( ){
        flush(false, QUEUE_MEMORY_SIZE_SUGGEST);
    }

    public void suggestQueueLengthLimitFlush( ) {
        flush(false, QUEUE_LENGTH_LIMIT);
    }

    public void suggestMinorFlush( ){
        flush(false, QUARTZ_SUGGEST);
    }

    public void forceMajorFlush( ){
        flush(true, QUARTZ_FORCEALL);
    }

    public void forceTriggerFlush( ){
        flush(true,TRIGGER_FORCEALL);
    }

    private <T>Future<T> submitFlush(Callable<T> c) {
        int retry=10;
        while(retry-->0) {
            try {
                return currentFlushPool.submit(c);
            } catch (Exception e) {
                SlientSleep.sleep(100);
            }
        }
        log.error("Submit flush task after retries");
        return null;
    }

    public boolean syncBatchWrite(List<ClickhouseData> clickhouseDataList) {
        boolean result = true;
        ClickhouseWritingStatistic statistic = new ClickhouseWritingStatistic().setFlushType(SYNC_FLUSH.name()).setQueueType(getQueueType());
        Map<ClickhouseKey, List<ClickhouseData>> batches = clickhouseDataList.stream().collect(Collectors.groupingBy(ClickhouseData::getKey));
        List<Future<ClickhouseWritingStatistic>> futures = new LinkedList<>();
        for (Map.Entry<ClickhouseKey, List<ClickhouseData>> batch : batches.entrySet()) {
            Set<String> envToWrite = new HashSet<>();
            String targetEnv = clickhouseEnvProxy.getEnvironment();
            if (getTargetEnv() != null) {
                targetEnv = getTargetEnv();
            }
            envToWrite.add(targetEnv);
            for (String env : envToWrite) {
                Future<ClickhouseWritingStatistic> f = submitFlush(new InsertCallable(true, batch, env));
                if (f != null) {
                    futures.add(f);
                } else {
                    result = false;
                    statistic.addFailed(batch.getKey().getDbName(), batch.getKey().getTableName(), batch.getValue().size());
                }
            }
        }

        for (Future<ClickhouseWritingStatistic> f : futures) {
            try {
                ClickhouseWritingStatistic st = f.get();
                if (st.getFailCount() > 0) {
                    result = false;
                }
                statistic.mergeFrom(st);
                ClickhouseWritingStatisticDetail detail = ClickhouseWritingStatisticDetail.fromClickhouseWritingStatistic(st.setFlushType(SYNC_FLUSH.name()));
                if (detail.getTotalCount() > 0) {
                    monitorLog.info(JsonUtils.toJsonString(detail));
                }

            } catch (Exception e) {
                log.error("Error when getting writing result", e);
            }
        }

        statistic = statistic.setActiveThreadCount((prepareFlushPool).getActiveCount())
                .setCorePoolSize((prepareFlushPool).getCorePoolSize())
                .setQueueCapacity(queueSize).setQueueCurrentLength(queue.size())
                .setQueueCurrentSize(queue.byteSize())
                .setCurrentPoolSize((currentFlushPool).getCorePoolSize())
                .finish(clickhouseDataList);
        if (statistic.getTotalCount() > 0) {
            monitorLog.info(JsonUtils.toJsonString(statistic));
        }

        return result;
    }

    private boolean shouldFlush(boolean force, ClickhouseFlushType flushType, long now) {
        long lastFlush = lastMajorFlushTime.get();
        boolean frequent = now - lastFlush < flushMinorIntervalMillSecond;
        boolean underThreshold = queue.size() < flushMaxRecordCount / 3 &&
                queue.byteSize() < queueMemoryLimit / 3;

        if (frequent) return false;
        if ((flushType.equals(QUEUE_MEMORY_SIZE_SUGGEST) || flushType.equals(QUEUE_LENGTH_LIMIT)) && underThreshold) return false;
        if (!force && underThreshold) return false;
        if (force) lastMajorFlushTime.set(now);

        return true;
    }

    private void flush(boolean force,ClickhouseFlushType flushType){
        try {
            long current = System.currentTimeMillis();
            if (!shouldFlush(force, flushType, current)) return;
            List<ClickhouseData> list;
            if(memFlushFlag && (flushType.equals(QUEUE_MEMORY_SIZE_SUGGEST) || flushType.equals(QUEUE_LENGTH_LIMIT))){
                list = new ArrayList<>(Math.round((queueSizeMax / 3) * 1.1f));
                queue.drainTo(list, queueSizeMax / 3);
            }else{
                list = new ArrayList<>(Math.round(queue.size() * 1.1f));
                queue.drainTo(list);
            }

            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            Map<ClickhouseKey, List<ClickhouseData>> batches = list.stream().collect(Collectors.groupingBy(ClickhouseData::getKey));
            ClickhouseWritingStatistic statistic = new ClickhouseWritingStatistic().setFlushType(flushType.name()).setQueueType(getQueueType());
            List<Future<ClickhouseWritingStatistic>> futures = new LinkedList<>();
            for (Map.Entry<ClickhouseKey, List<ClickhouseData>> batch : batches.entrySet()) {
                Set<String> envToWrite = new HashSet<>();
                String targetEnv = clickhouseEnvProxy.getEnvironment();
                if (getTargetEnv() != null) {
                    targetEnv = getTargetEnv();
                }
                envToWrite.add(targetEnv);
                if (batch.getValue().get(0).isBroadcast()) {
                    envToWrite.addAll(allEnvs);
                }
                for (String env : envToWrite) {
                    Future<ClickhouseWritingStatistic> f = submitFlush(new InsertCallable(force, batch, env));
                    if (f != null) {
                        futures.add(f);
                    } else {
                        statistic.addFailed(batch.getKey().getDbName(), batch.getKey().getTableName(), batch.getValue().size());
                    }
                }
            }
            for (Future<ClickhouseWritingStatistic> f : futures) {
                try {
                    ClickhouseWritingStatistic st = f.get();
                    statistic.mergeFrom(st);
                    ClickhouseWritingStatisticDetail detail = ClickhouseWritingStatisticDetail.fromClickhouseWritingStatistic(st.setFlushType(flushType.name()));
                    if (detail.getTotalCount() > 0) {
                        monitorLog.info(JsonUtils.toJsonString(detail));
                    }
                } catch (Exception e) {
                    log.error("Error when getting writing result", e);
                }
            }

            statistic = statistic.setActiveThreadCount((prepareFlushPool).getActiveCount())
                    .setCorePoolSize((prepareFlushPool).getCorePoolSize())
                    .setQueueCapacity(queueSize).setQueueCurrentLength(queue.size())
                    .setQueueCurrentSize(queue.byteSize())
                    .setCurrentPoolSize((currentFlushPool).getCorePoolSize())
                    .finish(list);
            if (statistic.getTotalCount() > 0) {
                monitorLog.info(JsonUtils.toJsonString(statistic));
            }
        } catch (Exception ex) {
            log.error("flush error", ex);
        }
    }


    class InsertCallable implements Callable<ClickhouseWritingStatistic> {

        ClickhouseWritingStatistic statistic=new ClickhouseWritingStatistic();
        boolean force;
        Map.Entry<ClickhouseKey, List<ClickhouseData>> batch;

        String env=null;
        public InsertCallable(boolean force,Map.Entry<ClickhouseKey, List<ClickhouseData>> batch){
            this.force=force;
            this.batch=batch;
        }

        public InsertCallable(boolean force,Map.Entry<ClickhouseKey, List<ClickhouseData>> batch,String env){
            this.force=force;
            this.batch=batch;
            this.env=env;
        }

        @Override
        public ClickhouseWritingStatistic call() throws Exception {
            statistic.setTid(Thread.currentThread().getId());
            statistic.setTs(System.currentTimeMillis());
            int tryCount = 0;
            boolean flag = false;
            while (!flag && tryCount < tryTimes) {
                tryCount++;
                if (tryCount > 1) {
                    Thread.sleep(sleepTime);
                    statistic.addRetryCount(1);
                    flag = invoke();
                } else {
                    flag = invoke();
                }
            }

            if (flag) {
                log.info("flush clickhouse success, try count {}", tryCount);
            } else {
                List<ClickhouseData> data = batch.getValue();
                String db = batch.getKey().getDbName();
                String table = batch.getKey().getTableName();
                statistic.addFailed(db,table,data.size());
            }
            return statistic;
        }

        private boolean holdData(List<ClickhouseData> data){
            long now = System.currentTimeMillis();

            if (data.isEmpty()) return true;

            long byteSum = 0L;
            long earliest = Long.MAX_VALUE;

            for (ClickhouseData d : data) {
                byteSum += d.getMemorySize();
                earliest = Math.min(earliest, d.getArriveInClickHouseWriter());
            }

            if (data.size() < flushMinRecordCountPerTable && byteSum < singleTableMemoryLimit && !force) {
                if (now - earliest > flushMajorIntervalMillSecond && isFlushWhenHoldTooLong()) {
                    return false;
                }
                if (data.size() > queue.remainingCapacity()) {
                    return false;
                }
                return true;
            }
            return false;
        }

        private boolean invoke() {
            statistic.clearDirtyDataCount();
            statistic.clearRecordSizeToBoxing();
            List<ClickhouseData> data = batch.getValue();
            if (holdData(data)) {
                List<ClickhouseData> remaining = putback(data);
                statistic.addRestoreToNextTimeCount(data.size()-remaining.size());
                if(remaining.isEmpty()) {
                    return true;
                }
                data=remaining;
                batch.setValue(remaining);
            }
            long step2StartTime = System.currentTimeMillis();
            String db = batch.getKey().getDbName();
            String table = batch.getKey().getTableName();
            long averageByte=Math.round(data.stream().mapToLong(ClickhouseData::getMemorySize).average().orElse(0));
            statistic.setMaxDelayTable(batch.getKey().getDbTableName());
            statistic.setMaxDelayMillSec(Math.round(data.stream().mapToLong(ClickhouseData::getTime).average().orElse(0)));
            statistic.setLargestRecordSizeTableName(batch.getKey().getDbTableName());
            statistic.setLargestRecordSizeForOneTable(averageByte);
            statistic.setTotalMemorySize( data.stream().mapToLong(ClickhouseData::getMemorySize).sum());
            statistic.setTaskQueueLength((currentFlushPool).getQueue().size());
            statistic.setQueueType(getQueueType());
            statistic.setKafkaRetryCount(getMaxRetryCount(data));
            ClickhouseMultiHandler handlerByEnv = clickhouseEnvProxy.getHandlerByEnv(env);
            if(handlerByEnv==null){
                log.warn("This environment={} has no ClickHouse configuration",env);
                return false;
            }
            ClickhouseSchema schema = getSchemaFromCache(db, table);
            if(schema==null){
                statistic.addDirtyDataCount(db,table,data.size());
                return false;
            }
            Map<String, FieldTypeEnum> colDef = schema.getColDef();
            Map<String, Object>  colDefaultValues = schema.getColDefaultValues();
            List<String> columnsList = new ArrayList<>(colDef.keySet());
            String sql = "INSERT INTO `%s`.`%s` (%s) VALUES(%s)";
            ClickHouseDataSourceProxy anyWritable = getAnyWritable(handlerByEnv.getByServiceName(ChNameDecoder.decode(db)),db,table);
            if (anyWritable == null) {
                log.warn("This environment={} and service={} has no ClickHouse configuration",env,ChNameDecoder.decode(db));
                return false;
            }
            statistic.setServerIp(anyWritable.getHost());
            String writingTable = schema.getLocalTable();
            if(bufferTables.contains(ClickhouseSqlUtil.concatFullTableName(db,table))){
                writingTable = ClickhouseSqlUtil.fromLocalToBuffer(writingTable);
            }else if(writingToDistributed.contains(db)){
                writingTable=table;
            }
            long startTime = System.currentTimeMillis();
            long step3Cost = startTime - step2StartTime;
            try (Connection conn = anyWritable.getConnection()) {
                long getConnectionTime = System.currentTimeMillis();
                PreparedStatement stmt
                        = conn.prepareStatement(
                        String.format(sql, db, writingTable
                                , columnsList.stream().map(u->String.format("`%s`",u)).collect(Collectors.joining(","))
                                ,String.join(",", Collections.nCopies(columnsList.size(), "?"))));
                for (ClickhouseData d : data) {
                    Map<String, Object> vMap=d.getRow();
                    statistic.addRecordSizeToBoxing(d);
                    boolean success = true;// success in record level
                    for (int i = 0; i < columnsList.size(); i++) {
                        String columnName = columnsList.get(i);
                        FieldTypeEnum type = colDef.get(columnName);
                        Object o = vMap.get(columnName);
                        if (!vMap.containsKey(columnName)) {
                            o = colDefaultValues.get(columnName);
                        }
                        if (!setValueInStatement(columnName, stmt, i + 1, o, type)) {
                            success = false;
                            statistic.addDirtyDataCount(db,table);
                            statistic.setDirtyDataSample(String.format("columnName=%s value=%s type=%s",columnName,o==null?"nullObj":o.toString(),type.name()));
                            break;
                        }
                    }
                    if (success) {
                        stmt.addBatch();
                    }
                }

                int[] execRets = stmt.executeBatch();
                long endTime = System.currentTimeMillis();
                statistic.finshOneTable(execRets.length);
                long getConnectionCost = getConnectionTime - startTime;
                long executeCostTimeCost = endTime - getConnectionTime;
                statistic.setGetConnectionTime(getConnectionCost);
                statistic.setExecuteConnectionTime(executeCostTimeCost);
                statistic.setGetSchemaTime(step3Cost);
                log.info("flush success to clickhouse {}.{} with {},{} records in {}, get_connection:{}ms, execute_cost:{}ms, step3: {}ms, total_cost:{}ms", db,table, execRets.length, data.size(), getQueueType(),
                        getConnectionCost, executeCostTimeCost,  step3Cost, endTime - statistic.getTs());
            } catch (Exception e) {
                log.error("write to clickhouse {}.{} error with data {} ",db,table,peekAtClickhouseData(data,2000),e);
                statistic.finish(data);
                return false;
            }
            statistic.finish(data);
            return true;
        }
    }

    public int getMaxRetryCount(List<ClickhouseData> datas) {
        try {
            int maxCount = 0;
            for (ClickhouseData data : datas) {
                Object o = data.getRow().get(KAFKA_RETRY_TIME);
                if (Objects.nonNull(o) && o instanceof Number && ((Number) o).intValue() > maxCount) {
                    maxCount = ((Number) o).intValue();
                }
            }
            return maxCount;
        } catch (Exception e) {
            log.error("get max retry count error", e);
            return 0;
        }
    }

    public String peekAtClickhouseData(List<ClickhouseData>data,int limit){
        try {
            return StringUtils.left(JsonUtils.toJsonStringIgnoreExp(data.stream().map(ClickhouseData::getRow).findFirst().get()), limit);
        }catch (Exception e){
            return "";
        }
    }

    public boolean setValueInStatement(String column,PreparedStatement stmt, int pIndex,Object o,FieldTypeEnum type)   {
        boolean success = true;
        try {
            switch (type) {
                case datetime64:
                    long time;
                    if (o == null) {
                        time = System.currentTimeMillis();
                    } else if (o instanceof Timestamp) {
                        time = ((Timestamp) o).getTime();
                    } else if (o instanceof Long) {
                        time = (long) o;
                    } else if (o instanceof Double) {
                        time = Math.round((Double) o);
                    } else if (o instanceof Integer) {
                        time = (Integer) o;
                    } else {
                        time = Long.parseLong(o.toString());
                    }
                    stmt.setLong(pIndex, time);
                    break;
                case datetime:
                    if(o == null) {
                        o = new Timestamp(System.currentTimeMillis());
                    } else if (o instanceof Double) {
                        o = new Timestamp(Math.round((Double) o));
                    } else if (o instanceof Long) {
                        o = new Timestamp((Long) o);
                    } else if (!(o instanceof Timestamp)) {
                        o = new Timestamp(Long.parseLong(o.toString()));
                    }
                    if(TIME_COLUMN_NAME.equals(column)) {
                        //Clickhouse requires timestamp to be at least 10 digits in unix form
                        if (((Timestamp) o).getTime() - MIN_TIME < 0 || ((Timestamp) o).getTime() - (MAX_TIME) > 0) {
                            success = false;
                        } else {
                            stmt.setTimestamp(pIndex, (Timestamp) o);
                        }
                    } else {
                        stmt.setTimestamp(pIndex, (Timestamp) o);
                    }
                    break;
                case nullable_datetime:
                    if(o instanceof Timestamp) {
                        stmt.setTimestamp(pIndex, (Timestamp) o);
                    } else if(o instanceof Double){
                        stmt.setTimestamp(pIndex,new Timestamp(Math.round((Double) o)));
                    } else if(o instanceof Long){
                        stmt.setTimestamp(pIndex,new Timestamp((Long) o));
                    } else if (o instanceof Integer) {
                        stmt.setTimestamp(pIndex,new Timestamp((Integer) o));
                    } else {
                        stmt.setNull(pIndex,Types.TIMESTAMP);
                    }
                    break;
                case string:
                    if (o != null) {
                        stmt.setString(pIndex, o.toString());
                    } else {
                        stmt.setNull(pIndex, Types.VARCHAR);
                    }
                    break;
                case number:
                    if (o != null) {
                        if(o instanceof Number) {
                            stmt.setDouble(pIndex, ((Number) o).doubleValue());
                        }else if(o instanceof String){
                            stmt.setDouble(pIndex, Double.parseDouble(((String) o)));
                        }
                    } else {
                        stmt.setNull(pIndex, Types.FLOAT);
                    }
                    break;
                case int32:
                    if (o != null) {
                        if(o instanceof Integer) {
                            stmt.setInt(pIndex, (Integer) o);
                        }else {
                            stmt.setInt(pIndex, Math.toIntExact(Math.round((Double) o)));
                        }
                    } else {
                        stmt.setNull(pIndex, Types.INTEGER);
                    }
                    break;
                case int64:
                    if (o != null) {
                        if(o instanceof Integer || o instanceof Long) {
                            stmt.setLong(pIndex, Long.parseLong(o.toString()));
                        }else if(o instanceof Double){
                            stmt.setLong(pIndex,  Math.round((Double) o)) ;
                        }
                    } else {
                        stmt.setNull(pIndex, Types.BIGINT);
                    }
                    break;
                case histogram:case summary:
                    if (o != null) {
                        stmt.setObject(pIndex, o);
                    } else {
                        stmt.setNull(pIndex, Types.JAVA_OBJECT);
                    }
                    break;
                case bool:
                    if (o != null) {
                        stmt.setBoolean(pIndex, (Boolean) o);
                    } else {
                        stmt.setNull(pIndex, Types.BOOLEAN);
                    }
                    break;
                case enum8:
                    if (o != null) {
                        stmt.setInt(pIndex, (Integer) o);
                    } else {
                        stmt.setNull(pIndex, Types.TINYINT);
                    }
                    break;
                case mapString:case mapNumber:case mapBool:
                    if (o != null) {
                        stmt.setObject(pIndex,o);
                    } else {
                        stmt.setNull(pIndex, Types.JAVA_OBJECT);
                    }
                    break;
            }
        } catch (Exception e) {
            log.warn("jdbc set value", e);
            success = false;
        }
        return success;
    }

    public ClickhouseSchema getSchemaFromCache(String db,String table){
        try {
//            return schemaCache.get(db+"."+table, () -> getSchema(db,table));
            return schemaCache.get(db+"."+table);
        } catch (Exception e) {
            return getSchema(db,table);
        }
    }

    /**
     * Get schema from CH
     * @param db
     * @param table
     * @return null if no schema is available or has local table conflict. Column definition is the minimum set of all schemas
     */
    public ClickhouseSchema getSchema(String db,String table){
        Map<String, ClickhouseSchema> viewSchemas = clickhouseEnvProxy.getHandlerByEnv().getTableSchema(db, table);
        if(viewSchemas.size()==0){
            return null;
        }
        Set<String> localTableNames = viewSchemas.values().stream().map(ClickhouseSchema::getLocalTable).collect(Collectors.toSet());
        if(localTableNames.size()!=1){
            return null;
        }
        String localTableName = localTableNames.stream().findFirst().get();

        Map<String, ClickhouseSchema> tableSchemas = clickhouseEnvProxy.getHandlerByEnv().getTableSchema(db, localTableName);
        if(tableSchemas.size()==0){
            return null;
        }
        Map<ClickhouseColDef, Long> colMap = tableSchemas.values().stream()
                .map(u -> u.getColDef().entrySet().stream().map(ClickhouseColDef::new).collect(Collectors.toList()))
                .flatMap(List::stream)
                .collect(Collectors.groupingBy(e -> e, Collectors.counting()));
        ClickhouseSchema ret=new ClickhouseSchema().setLocalTable(localTableName);
        colMap.entrySet().stream().filter(u->u.getValue()==tableSchemas.size())
                .forEach(u->ret.addCol(u.getKey().getColName(),u.getKey().getColType()));
        return ret;
    }


    /**
     * create new thread pool with new size if necessary
     * @param flushParallelism
     */
    public ClickhouseWriter setFlushParallelism(int flushParallelism) {
        if(this.flushParallelism==flushParallelism) {
            return this;
        }
        synchronized (this) {
            if(this.flushParallelism==flushParallelism) {
                return this;
            }
            this.flushParallelism = flushParallelism;
            currentFlushPool = new ThreadPoolExecutor(flushParallelism, flushParallelism,
                    60L, TimeUnit.SECONDS,
                    new LinkedBlockingQueue<>(1000),
                    new NamedThreadFactory("clickhouse_flush_sub_task"), new BlockPolicy());
            flushPoolList.add(currentFlushPool);
            ListIterator<ExecutorService>iterator=flushPoolList.listIterator();
            while(iterator.hasNext()){
                ExecutorService next = iterator.next();
                if(next!=currentFlushPool){
                    next.shutdown();
                    if(!next.isTerminated()){
                        iterator.remove();
                    }
                }
            }
        }
        return this;
    }


    public ClickhouseWriter setFlushMajorIntervalMillSecond(long flushMajorIntervalMillSecond) {
        this.flushMajorIntervalMillSecond = flushMajorIntervalMillSecond;
        return this;
    }

    public ClickhouseWriter setFlushMinorIntervalMillSecond(long flushMinorIntervalMillSecond) {
        this.flushMinorIntervalMillSecond = flushMinorIntervalMillSecond;
        return this;
    }

    public ClickhouseWriter setFlushMaxRecordCount(int flushMaxRecordCount) {
        this.flushMaxRecordCount = flushMaxRecordCount;
        return this;
    }

    public ClickhouseWriter setFlushMinRecordCountPerTable(int flushMinRecordCountPerTable) {
        this.flushMinRecordCountPerTable = flushMinRecordCountPerTable;
        return this;
    }

    public long getQueueMemoryLimit() {
        return queueMemoryLimit;
    }

    public ClickhouseWriter setQueueMemoryLimit(long queueMemoryLimit) {
        this.queueMemoryLimit = queueMemoryLimit;
        return this;
    }

    public Set<String> getAllEnvs() {
        return allEnvs;
    }

    public ClickhouseWriter setAllEnvs(Set<String> allEnvs) {
        this.allEnvs = allEnvs;
        return this;
    }

    public int getThreadPoolSize() {
        return threadPoolSize;
    }

    public ClickhouseWriter setThreadPoolSize(int threadPoolSize) {
        log.info("set threadPoolSize: {}", threadPoolSize);
        if(this.threadPoolSize==threadPoolSize) {
            return this;
        }
        synchronized (this) {
            if(this.threadPoolSize==threadPoolSize) {
                return this;
            }
            this.threadPoolSize = threadPoolSize;
            if (Objects.nonNull(prepareFlushPool)) {
                prepareFlushPool.shutdown();
                prepareFlushPool.getQueue().clear();
                prepareFlushPool = new ThreadPoolExecutor(threadPoolSize, threadPoolSize,
                        60, TimeUnit.SECONDS, new ArrayBlockingQueue(DEFAULT_FLUSH_TASK_POOL_SIZE),
                        new NamedThreadFactory("clickhouse_flush_main_task"), new ThreadPoolExecutor.DiscardPolicy());
            }
        }
        return this;
    }


    public int getFlushPoolSize() {
        return flushPoolSize;
    }

    public ClickhouseWriter setFlushPoolSize(int flushPoolSize) {
        this.flushPoolSize = flushPoolSize;
        return this;
    }


    public long getSingleTableMemoryLimit() {
        return singleTableMemoryLimit;
    }

    public ClickhouseWriter setSingleTableMemoryLimit(long singleTableMemoryLimit) {
        this.singleTableMemoryLimit = singleTableMemoryLimit;
        return this;
    }

    public ClickhouseWriter setMemFlushTag(boolean memFlushTag) {
        this.memFlushFlag = memFlushTag;
        return this;
    }

    public int getQueueSize() {
        return queueSize;
    }

    public ClickhouseWriter setQueueSize(int queueSize) {
        this.queueSize = queueSize;
        return this;
    }

    public int getFlushMaxRecordCount() {
        return flushMaxRecordCount;
    }

    public String getQueueType(){
        return "normal";
    }

    public void setNoWriteInstances(Set<String>instances){
        noWritable=instances;
    }

    public boolean isWaitAvaliableConnection() {
        return waitAvaliableConnection;
    }

    public ClickhouseWriter setWaitAvaliableConnection(boolean waitAvaliableConnection) {
        this.waitAvaliableConnection = waitAvaliableConnection;
        return this;
    }

    public ClickHouseDataSourceProxy getAnyWritable(ClickhouseHandler h, String db, String table){
        //first try
//        return h.getDataSource();
        ClickHouseDataSourceProxy clickHouseDataSource = tryGetWritable(h,db,table);
        if(clickHouseDataSource!=null){
            return clickHouseDataSource;
        }else{
            while (isWaitAvaliableConnection()){
                log.warn("db:{}, table:{}, waitAvaliableConnection is null", db, table);
                SlientSleep.sleep(1000);
                clickHouseDataSource = tryGetWritable(h,db,table);
                if(clickHouseDataSource!=null){
                    return clickHouseDataSource;
                }
            }
        }
        return null;
    }

    private ClickHouseDataSourceProxy tryGetWritable(ClickhouseHandler h, String db, String table) {
        try {
            if (h==null || MapUtils.isEmpty(h.getAllDataSource())) {
                return null;
            }
            List<String> active = h.getDataSource().getEnabledClickHouseUrls();
            List<ClickHouseDataSourceProxy> writable = h.getAllDataSource().entrySet().stream()
                    .filter(u -> active.contains(u.getKey()) && !noWritable.contains(u.getKey())).map(Map.Entry::getValue).collect(Collectors.toList());
            if(writable.size()==0){
                return null;
            }
            List<String> writableUrl = writable.stream().map(ClickHouseDataSourceProxy::getUrl).collect(Collectors.toList());

            boolean inShuntMode = shuntConfig.clusterInShuntMode(writableUrl);
            if(!inShuntMode) {
                return writable.get(random.nextInt(writable.size()));
            }
            String fullTableName=ClickhouseSqlUtil.concatFullTableName(db,table);
            List<String> ipAfterShunt = shuntConfig.getIpAfterShunt(fullTableName, writableUrl);
            if(CollectionUtils.isEmpty(ipAfterShunt)){
                return writable.get(random.nextInt(writable.size()));
            }
            writable=writable.stream().filter(u->ipAfterShunt.contains(u.getUrl())).collect(Collectors.toList());
            return writable.get(random.nextInt(writable.size()));
        } catch (Exception e) {
            log.error("try get writable datasource failed", e);
            return null;
        }

    }

    public int getQueueSizeMax() {
        return queueSizeMax;
    }

    public ClickhouseWriter setQueueSizeMax(int queueSizeMax) {
        ClickhouseWriter.queueSizeMax = queueSizeMax;
        return this;
    }

    public static String getUnitTag() {
        return unitTag;
    }

    public static void setUnitTag(String unitTag) {
        ClickhouseWriter.unitTag = unitTag;
    }

    public static String getTargetEnv() {
        return targetEnv;
    }

    public static void setTargetEnv(String targetEnv) {
        ClickhouseWriter.targetEnv = targetEnv;
    }

    public static void setShuntConfig(ClickHouseShuntConfig shuntConfig) {
        ClickhouseWriter.shuntConfig = shuntConfig;
    }

    public static Set<String> getBufferTables() {
        return bufferTables;
    }

    public void setBufferTables(Map<String,String> map) {
        Set<String> bufferTables = ParaUtil.getBufferTables(map, clickhouseEnvProxy.getEnvironment());
        if(bufferTables!=null) {
            ClickhouseWriter.bufferTables = bufferTables;
        }
    }

    public static Set<String> getWritingToDistributed() {
        return writingToDistributed;
    }

    public void setWritingToDistributed(Map<String,String> map) {
        Set<String> writeToDistributed = ParaUtil.getWriteToDistributed(map );
        if(writeToDistributed!=null) {
            ClickhouseWriter.writingToDistributed = writeToDistributed;
        }
    }

    public int getTryTimes() {
        return tryTimes;
    }

    public void setTryTimes(int tryTimes) {
        this.tryTimes = tryTimes;
    }

    public int getSleepTime() {
        return sleepTime;
    }

    public void setSleepTime(int sleepTime) {
        this.sleepTime = sleepTime;
    }

    public static String getRunningEnv() {
        return runningEnv;
    }

    public static void setRunningEnv(String runningEnv) {
        ClickhouseWriter.runningEnv = runningEnv;
    }

    public boolean isFlushWhenHoldTooLong() {
        return flushWhenHoldTooLong;
    }

    public ClickhouseWriter setFlushWhenHoldTooLong(boolean flushWhenHoldTooLong) {
        this.flushWhenHoldTooLong = flushWhenHoldTooLong;
        return this;
    }
}
