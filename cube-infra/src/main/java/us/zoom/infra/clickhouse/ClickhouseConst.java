package us.zoom.infra.clickhouse;

import us.zoom.infra.enums.FieldTypeEnum;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-05-24 10:11
 * https://zoomvideo.atlassian.net/wiki/spaces/WA/pages/2346714492/Clickhouse
 */
public class ClickhouseConst {

    public static final String NUMBER_TABLE_NAME_PREFIX="_cube_num_h_";
    public static final String SPECIAL_ENCODE_TOKEN="_cube_ch_env1_";
    public static final String CLICKHOUSE_DB_TYPE="clickhouse";
    public static final String CLICKHOUSE_QUERY_MODE="IN2CH";

    public static final String NO_WIRTE_SYS_KEY="cube.clickhouse.nowritable";

    public static final String WAIT_UNITL_AVAILABLE="cube.clickhouse.wait.available.connection";

    public static final String BUFFER_TABLE="cube.clickhouse.write.buffer";
    public static final String WRITE_TO_DISTRIBUTED="cube.clickhouse.write.to.distributed";

    public static final String LOW_CARDINALITY_STRING = "LowCardinality(String)";

    public static final String NULLABLE_STRING = "Nullable(String)";
    public static final String INT32 = "Int32";
    public static final String NULLABLE_INT32 = "Nullable(Int32)";
    public static final String INT64 = "Int64";
    public static final String NULLABLE_INT64 = "Nullable(Int64)";
    public static final String ARRAY_UINT_64 = "Array(UInt64)";
    public static final String ARRAY_UINT_8 = "Array(UInt8)";
    public static final String ARRAY_FLOAT_64 = "Array(Float64)";
    public static final String ARRAY_STRING = "Array(String)";
    public static final String NULLABLE_DATETIME = "Nullable(DateTime)";
    public static final String NULLABLE_DATETIME_64 = "Nullable(DateTime64)";
    public static final String DATETIME = "DateTime";
    public static final String DATETIME_64 = "DateTime64";

    public static final String BOOL = "Bool";

    public static final String ENUM_8 = "Enum8";

    public static final String MAP_STRING = "Map(String, String)";
    public static final String MAP_FLOAT = "Map(String, Float64)";
    public static final String MAP_BOOL = "Map(String, Bool)";

    public static final Map<String, FieldTypeEnum> ALARM_RECORDS_SCHEMA;

    public static final Map<String, FieldTypeEnum> ALL_ALARM_RECORDS_SCHEMA;

    public static final Map<String, FieldTypeEnum> AI_ORIGIN_ALL_RESULT_SCHEMA;

    public static final Map<String, FieldTypeEnum> AI_ORIGIN_RESULT_SCHEMA;

    public static final Map<String, FieldTypeEnum> ALARM_RECORDS_EVENT_SCHEMA;
    public static final Map<String, FieldTypeEnum>ALARM_RECORD_FIXED_SCHEMA;
    public static final Map<String, FieldTypeEnum> ALARM_RECORD_NOTIFICATION_SCHEMA;
    public static final Map<String, FieldTypeEnum> ALARM_RECORD_EXTENSION_RELATION_SCHEMA;

    public static final String CLICK_LOCAL_SUFFIX = "_click_local";
    public static final String CLICK_DESENSITIZED_SUFFIX = "_click_desensitized";

    public static final String CLICK_BUFFER_SUFFIX = "_click_buffer";

    public static final String CLICK_BUFFER_CONF_DEFAULT = "1,60,60,20000,1000000,10000000,200000000";

    //Alarm record related table (New)
    public static final String ALARM_CUBE_MATCH_SERVICE_RECORD_TABLE = "alarm_cube_match_service_record_table";
    public static final String ALARM_CUBE_MATCH_ALL_SERVICE_RECORD_TABLE = "alarm_cube_match_all_service_record_table";
    public static final String ALARM_CUBE_MATCH_RECORD_EVENT_TABLE = "alarm_cube_match_record_event_table";
    public static final String ALARM_MATCH_RECORD_NOTIFICATION_TABLE_NAME ="alarm_match_record_notification";
    public static final String ALARM_MATCH_RECORD_EXTENSION_RELATION_TABLE_NAME = "alarm_match_record_extension_relation";

    public static final String AI_ORIGIN_DETECT_RESULT_ALL_SERVICE_RECORD_TABLE = "ai_origin_detect_result_all_service_record_table";
    public static final String AI_ORIGIN_DETECT_RESULT_SERVICE_RECORD_TABLE = "ai_origin_detect_result_service_record_table";

    public static final String ALARM_CUBE_MATCH_PREFIX = "alarm_cube_match_";
    public static final String ALARM_RECORD_TABLE_PREFIX="cube_alarm_match_record_";
    public static final String ALARM_MATCH_RECORD_TABLE_PREFIX = "alarm_match_record_";
    //Deprecated soon below
    public static final String ALARM_EACH_SERCICE_RECORD_TABLE_NAME = "alarm_match_record_each_service";
    public static final String ALARM_ALL_RECORD_TABLE_NAME = "alarm_match_record_all";
    public static final String ALARM_MATCH_RECORD_STATUS_TABLE_NAME ="alarm_match_record_status";

    public static final Map<String, Map<String, FieldTypeEnum>> COMMON_ALARM_TABLE_SCHEMAS = new HashMap<>();

    public static final Map<String,String>ALARM_RECORD_FIELD_NAME_TRANSFER;

    public static final String WRITE_TRY_TIMES = "cube.clickhouse.write.retry.times";

    public static final int WRITE_TRY_TIMES_VALUE = 5;

    public static final String WRITE_RETRY_INTERVAL = "cube.clickhouse.write.retry.interval";

    public static final int WRITE_RETRY_INTERVAL_VALUE = 30;

    public static final long ONE_DAY_MS = 24 * 3600 * 1000L;

    public static final long ONE_MONTH_MS = 30 * 24 * 3600 * 1000L;

    public static final int DB_INDEX_IN_PARTS_ITEM=1;

    public static final int TABLE_INDEX_IN_PARTS_ITEM=0;

    public static final String SMART_REC_SCHEMA = "infra_DataIntelligence_smart_rca_cube_ch_env1_1c2d";

    public final static String TIME_SERIES_DATA_QUERY = "time_series_data_query";

    public final static String TOPN_ROOT_CAUSE_TABLE = "topn_root_cause";

    public static String toV2Name(String v1){
        if(ALARM_RECORD_FIELD_NAME_TRANSFER.containsKey(v1)){
            return ALARM_RECORD_FIELD_NAME_TRANSFER.get(v1);
        }
        return v1;
    }

    static {
        //alarm record table schema (decrypted)
        ALARM_RECORD_FIXED_SCHEMA=new HashMap<>();
        ALARM_RECORD_FIXED_SCHEMA.put("__id",FieldTypeEnum.string);
        ALARM_RECORD_FIXED_SCHEMA.put("__alarmId",FieldTypeEnum.string);
        ALARM_RECORD_FIXED_SCHEMA.put("__alarmName",FieldTypeEnum.string);
        ALARM_RECORD_FIXED_SCHEMA.put("__alarmLevel",FieldTypeEnum.string);
        ALARM_RECORD_FIXED_SCHEMA.put("__alarmRuleId",FieldTypeEnum.string);
        ALARM_RECORD_FIXED_SCHEMA.put("__alarmSourceType",FieldTypeEnum.int32);
        ALARM_RECORD_FIXED_SCHEMA.put("__isLastCountMatched",FieldTypeEnum.string);
        ALARM_RECORD_FIXED_SCHEMA.put("__isNoticed",FieldTypeEnum.string);
        ALARM_RECORD_FIXED_SCHEMA.put("__metricsId",FieldTypeEnum.string);
        ALARM_RECORD_FIXED_SCHEMA.put("__metricsName",FieldTypeEnum.string);
        ALARM_RECORD_FIXED_SCHEMA.put("__aggPeriod",FieldTypeEnum.int32);
        ALARM_RECORD_FIXED_SCHEMA.put("__subPeriod",FieldTypeEnum.int32);
        ALARM_RECORD_FIXED_SCHEMA.put("__serviceName",FieldTypeEnum.string);
        ALARM_RECORD_FIXED_SCHEMA.put("__title",FieldTypeEnum.string);
        ALARM_RECORD_FIXED_SCHEMA.put("__noticeContent",FieldTypeEnum.string);
        ALARM_RECORD_FIXED_SCHEMA.put("__gmtCreate",FieldTypeEnum.datetime);
        ALARM_RECORD_FIXED_SCHEMA.put("__tags",FieldTypeEnum.string);
        ALARM_RECORD_FIXED_SCHEMA.put("__hittedRule",FieldTypeEnum.string);
        ALARM_RECORD_FIELD_NAME_TRANSFER=new HashMap<>();
        for(String u:ALARM_RECORD_FIXED_SCHEMA.keySet()){
            if(u.equals("__serviceName")) {
                ALARM_RECORD_FIELD_NAME_TRANSFER.put("tenantName",u);
            }else{
                ALARM_RECORD_FIELD_NAME_TRANSFER.put(u.substring(2), u);
            }
        }

        //alarm_match_record_notification
        ALARM_RECORD_NOTIFICATION_SCHEMA =new HashMap<>();
        ALARM_RECORD_NOTIFICATION_SCHEMA.put("id",FieldTypeEnum.string);
        ALARM_RECORD_NOTIFICATION_SCHEMA.put("channelId",FieldTypeEnum.string);
        ALARM_RECORD_NOTIFICATION_SCHEMA.put("channelName",FieldTypeEnum.string);
        ALARM_RECORD_NOTIFICATION_SCHEMA.put("channelPriority",FieldTypeEnum.int32);
        ALARM_RECORD_NOTIFICATION_SCHEMA.put("status",FieldTypeEnum.int32);
        ALARM_RECORD_NOTIFICATION_SCHEMA.put("alarmMatchRecordId",FieldTypeEnum.string);
        ALARM_RECORD_NOTIFICATION_SCHEMA.put("title",FieldTypeEnum.string);
        ALARM_RECORD_NOTIFICATION_SCHEMA.put("content",FieldTypeEnum.string);
        ALARM_RECORD_NOTIFICATION_SCHEMA.put("gmtCreate",FieldTypeEnum.datetime);
        COMMON_ALARM_TABLE_SCHEMAS.put(ALARM_MATCH_RECORD_NOTIFICATION_TABLE_NAME, ALARM_RECORD_NOTIFICATION_SCHEMA);
        //alarm_extension_relation
        ALARM_RECORD_EXTENSION_RELATION_SCHEMA =new HashMap<>();
        ALARM_RECORD_EXTENSION_RELATION_SCHEMA.put("id",FieldTypeEnum.string);
        ALARM_RECORD_EXTENSION_RELATION_SCHEMA.put("relatationId",FieldTypeEnum.string);
        ALARM_RECORD_EXTENSION_RELATION_SCHEMA.put("relatationName",FieldTypeEnum.string);
        ALARM_RECORD_EXTENSION_RELATION_SCHEMA.put("configValue",FieldTypeEnum.string);
        ALARM_RECORD_EXTENSION_RELATION_SCHEMA.put("alarmMatchRecordId",FieldTypeEnum.string);
        ALARM_RECORD_EXTENSION_RELATION_SCHEMA.put("alarmId",FieldTypeEnum.string);
        ALARM_RECORD_EXTENSION_RELATION_SCHEMA.put("gmtCreate",FieldTypeEnum.datetime);
        ALARM_RECORD_EXTENSION_RELATION_SCHEMA.put("type",FieldTypeEnum.string);
        COMMON_ALARM_TABLE_SCHEMAS.put(ALARM_MATCH_RECORD_EXTENSION_RELATION_TABLE_NAME, ALARM_RECORD_EXTENSION_RELATION_SCHEMA);
        //alarm_cube_match_service_record_table
        ALARM_RECORDS_SCHEMA = new HashMap<>();
        ALARM_RECORDS_SCHEMA.put("id", FieldTypeEnum.string);
        ALARM_RECORDS_SCHEMA.put("alarmId", FieldTypeEnum.string);
        ALARM_RECORDS_SCHEMA.put("alarmName", FieldTypeEnum.string);
        ALARM_RECORDS_SCHEMA.put("alarmLevel", FieldTypeEnum.string);
        ALARM_RECORDS_SCHEMA.put("alarmLevelPriority", FieldTypeEnum.int32);
        ALARM_RECORDS_SCHEMA.put("alarmRuleId", FieldTypeEnum.string);
        ALARM_RECORDS_SCHEMA.put("alarmSourceType", FieldTypeEnum.int32);
        ALARM_RECORDS_SCHEMA.put("metricsId", FieldTypeEnum.string);
        ALARM_RECORDS_SCHEMA.put("metricsName", FieldTypeEnum.string);
        ALARM_RECORDS_SCHEMA.put("serviceName", FieldTypeEnum.string);
        ALARM_RECORDS_SCHEMA.put("gmtCreate", FieldTypeEnum.datetime);
        ALARM_RECORDS_SCHEMA.put("hittedRule", FieldTypeEnum.string);
        ALARM_RECORDS_SCHEMA.put("preRecordId", FieldTypeEnum.string);
        ALARM_RECORDS_SCHEMA.put("tagNames", FieldTypeEnum.string);
        ALARM_RECORDS_SCHEMA.put("fieldNames", FieldTypeEnum.string);
        ALARM_RECORDS_SCHEMA.put("tagMap", FieldTypeEnum.mapString);
        ALARM_RECORDS_SCHEMA.put("fieldStringMap", FieldTypeEnum.mapString);
        ALARM_RECORDS_SCHEMA.put("fieldNumberMap", FieldTypeEnum.mapNumber);
        ALARM_RECORDS_SCHEMA.put("tagKey", FieldTypeEnum.string);
        ALARM_RECORDS_SCHEMA.put("initialStatus", FieldTypeEnum.string);
        ALARM_RECORDS_SCHEMA.put("hittedValue", FieldTypeEnum.string);
        ALARM_RECORDS_SCHEMA.put("alarmSilenceId", FieldTypeEnum.string);
        ALARM_RECORDS_SCHEMA.put("alarmSilenceName", FieldTypeEnum.string);
        ALARM_RECORDS_SCHEMA.put("extendInfo", FieldTypeEnum.string);
        COMMON_ALARM_TABLE_SCHEMAS.put(ALARM_CUBE_MATCH_SERVICE_RECORD_TABLE, ALARM_RECORDS_SCHEMA);
        // alarm_cube_match_all_service_record_table
        ALL_ALARM_RECORDS_SCHEMA = new HashMap<>();
        ALL_ALARM_RECORDS_SCHEMA.put("id", FieldTypeEnum.string);
        ALL_ALARM_RECORDS_SCHEMA.put("alarmId", FieldTypeEnum.string);
        ALL_ALARM_RECORDS_SCHEMA.put("alarmName", FieldTypeEnum.string);
        ALL_ALARM_RECORDS_SCHEMA.put("alarmLevel", FieldTypeEnum.string);
        ALL_ALARM_RECORDS_SCHEMA.put("metricsId", FieldTypeEnum.string);
        ALL_ALARM_RECORDS_SCHEMA.put("metricsName", FieldTypeEnum.string);
        ALL_ALARM_RECORDS_SCHEMA.put("serviceName", FieldTypeEnum.string);
        ALL_ALARM_RECORDS_SCHEMA.put("gmtCreate", FieldTypeEnum.datetime);
        ALL_ALARM_RECORDS_SCHEMA.put("initialStatus", FieldTypeEnum.string);
        ALL_ALARM_RECORDS_SCHEMA.put("metricsType", FieldTypeEnum.int32);
        ALL_ALARM_RECORDS_SCHEMA.put("tagKey", FieldTypeEnum.string);
        //alarm_cube_match_record_event
        ALARM_RECORDS_EVENT_SCHEMA = new HashMap<>();
        ALARM_RECORDS_EVENT_SCHEMA.put("id", FieldTypeEnum.string);
        ALARM_RECORDS_EVENT_SCHEMA.put("gmtCreate", FieldTypeEnum.datetime);
        ALARM_RECORDS_EVENT_SCHEMA.put("alarmStatus", FieldTypeEnum.string);
        ALARM_RECORDS_EVENT_SCHEMA.put("ackExpireTime", FieldTypeEnum.datetime);
        ALARM_RECORDS_EVENT_SCHEMA.put("alarmId", FieldTypeEnum.string);
        ALARM_RECORDS_EVENT_SCHEMA.put("alarmName", FieldTypeEnum.string);
        ALARM_RECORDS_EVENT_SCHEMA.put("alarmLevel", FieldTypeEnum.string);
        ALARM_RECORDS_EVENT_SCHEMA.put("metricsId", FieldTypeEnum.string);
        ALARM_RECORDS_EVENT_SCHEMA.put("metricsName", FieldTypeEnum.string);
        ALARM_RECORDS_EVENT_SCHEMA.put("event", FieldTypeEnum.string);
        ALARM_RECORDS_EVENT_SCHEMA.put("tagKey", FieldTypeEnum.string);
        COMMON_ALARM_TABLE_SCHEMAS.put(ALARM_CUBE_MATCH_RECORD_EVENT_TABLE, ALARM_RECORDS_EVENT_SCHEMA);
        //ai_origin_detect_result_all_service_record_table
        AI_ORIGIN_ALL_RESULT_SCHEMA = new HashMap<>();
        AI_ORIGIN_ALL_RESULT_SCHEMA.put("metricsId", FieldTypeEnum.string);
        AI_ORIGIN_ALL_RESULT_SCHEMA.put("metricsName", FieldTypeEnum.string);
        AI_ORIGIN_ALL_RESULT_SCHEMA.put("tenantId", FieldTypeEnum.string);
        AI_ORIGIN_ALL_RESULT_SCHEMA.put("tenantName", FieldTypeEnum.string);
        AI_ORIGIN_ALL_RESULT_SCHEMA.put("fieldName", FieldTypeEnum.string);
        AI_ORIGIN_ALL_RESULT_SCHEMA.put("id", FieldTypeEnum.string);
        AI_ORIGIN_ALL_RESULT_SCHEMA.put("adName", FieldTypeEnum.string);
        AI_ORIGIN_ALL_RESULT_SCHEMA.put("aiAlarmConditionCfgList", FieldTypeEnum.string);
        AI_ORIGIN_ALL_RESULT_SCHEMA.put("gmtCreate", FieldTypeEnum.datetime);
        //ai_origin_detect_result_service_record_table
        AI_ORIGIN_RESULT_SCHEMA = new HashMap<>();
        AI_ORIGIN_RESULT_SCHEMA.put("metricsId", FieldTypeEnum.string);
        AI_ORIGIN_RESULT_SCHEMA.put("metricsName", FieldTypeEnum.string);
        AI_ORIGIN_RESULT_SCHEMA.put("tenantId", FieldTypeEnum.string);
        AI_ORIGIN_RESULT_SCHEMA.put("tenantName", FieldTypeEnum.string);
        AI_ORIGIN_RESULT_SCHEMA.put("fieldName", FieldTypeEnum.string);
        AI_ORIGIN_RESULT_SCHEMA.put("id", FieldTypeEnum.string);
        AI_ORIGIN_RESULT_SCHEMA.put("adName", FieldTypeEnum.string);
        AI_ORIGIN_RESULT_SCHEMA.put("latestTime", FieldTypeEnum.datetime);
        AI_ORIGIN_RESULT_SCHEMA.put("anomalyScore", FieldTypeEnum.number);
        AI_ORIGIN_RESULT_SCHEMA.put("absPredRatio", FieldTypeEnum.number);
        AI_ORIGIN_RESULT_SCHEMA.put("anomalyDirection", FieldTypeEnum.string);
        AI_ORIGIN_RESULT_SCHEMA.put("tagKey", FieldTypeEnum.string);
        AI_ORIGIN_RESULT_SCHEMA.put("alarmId", FieldTypeEnum.string);
        AI_ORIGIN_RESULT_SCHEMA.put("columns", FieldTypeEnum.mapString);
        AI_ORIGIN_RESULT_SCHEMA.put("adTagCfgDataList", FieldTypeEnum.mapString);
        AI_ORIGIN_RESULT_SCHEMA.put("fieldMap", FieldTypeEnum.mapString);
        AI_ORIGIN_RESULT_SCHEMA.put("gmtCreate", FieldTypeEnum.datetime);
        COMMON_ALARM_TABLE_SCHEMAS.put(AI_ORIGIN_DETECT_RESULT_SERVICE_RECORD_TABLE, AI_ORIGIN_RESULT_SCHEMA);
    }

}
