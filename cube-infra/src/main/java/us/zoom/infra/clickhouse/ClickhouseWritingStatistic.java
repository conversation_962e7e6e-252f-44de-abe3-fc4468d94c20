package us.zoom.infra.clickhouse;

import us.zoom.infra.utils.IpUtils;

import java.util.*;
import java.util.function.Supplier;
import java.util.stream.LongStream;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2022-02-09 10:11
 */
public class ClickhouseWritingStatistic {

    private long ts;
    private long totalCount;
    private long successCount;
    private long failCount;
    private List<String> failTables =new LinkedList<>();
    private String failTablesString="";
    private long failTableCount;
    private long costMillSec;
    private double rps;
    private long delayMillSec;
    private long tid;
    private String ip;
    private String serverIp;
    private String runningEnv;
    private long totalTableCount;
    private double avgTableFlushSize;
    private double stdOfTableFlushSize;
    private List<Integer>flushSizeForEveryTable=new LinkedList<>();
    private int corePoolSize;
    private int currentPoolSize;
    private int activeThreadCount;
    private int restoreToNextTimeCount;
    private Set<String> dirtyDataTableSet =new HashSet<>();
    private int dirtyRecordCount;
    private String dirtyTablesString="";
    private int queueCapacity;
    private int queueCurrentLength;
    private long queueCurrentSize;
    private int taskQueueLength;
    private long getConnectionTime;
    private long executeConnectionTime;
    private long getSchemaTime;
    private long delayUponArrivalMillSec;
    private long delayFromKafkaCreateMillSec;
    private long delayFromKafkaCreateMillSec95;
    private long delayFromKafkaCreateMillSecMax;
    private String flushType;
    public static String DELAY_UPON_ARRIVAL ="DELAY_UPON_ARRIVAL";
    public static String KAFKA_CREATE_TIME ="KAFKA_CREATE_TIME";
    public static String KAFKA_RETRY_TIME ="KAFKA_RETRY_TIME";
    public static String ARRIVE_IN_CLICKHOUSE_WRITER ="ARRIVE_IN_CLICKHOUSE_WRITER";
    private long largestRecordSizeForOneTable;
    private String largestRecordSizeTableName;
    private String recordSizeBoxing;
    private static int[] recordSizeBoxPoint=new int[15];
    private int[] sizeBoxing=new int[recordSizeBoxPoint.length+1];
    private long totalMemorySize;
    private long retryCount;
    private long kafkaRetryCount;
    private long maxDelayMillSec;
    private String maxDelayTable="";
    private String system ="clickhouse_flush_statistic";
    private String dirtyDataSample="";
    public static String localIP=IpUtils.getLocalIP();

    private String queueType="normal";

    static {
        recordSizeBoxPoint[0]=3000;
        recordSizeBoxPoint[1]=5000;
        //Follow by Fibonacci sequence, max will be 233k
        for(int i=2;i<recordSizeBoxPoint.length;i++){
            recordSizeBoxPoint[i]=recordSizeBoxPoint[i-2]+recordSizeBoxPoint[i-1];
        }
    }



    public long getDelayFromKafkaCreateMillSec() {
        return delayFromKafkaCreateMillSec;
    }

    public void setDelayFromKafkaCreateMillSec(long delayFromKafkaCreateMillSec) {
        this.delayFromKafkaCreateMillSec = delayFromKafkaCreateMillSec;
    }

    public long getDelayUponArrivalMillSec() {
        return delayUponArrivalMillSec;
    }

    public void setDelayUponArrivalMillSec(long delayUponArrivalMillSec) {
        this.delayUponArrivalMillSec = delayUponArrivalMillSec;
    }

    public long getGetConnectionTime() {
        return getConnectionTime;
    }

    public void setGetConnectionTime(long getConnectionTime) {
        this.getConnectionTime = getConnectionTime;
    }

    public long getExecuteConnectionTime() {
        return executeConnectionTime;
    }

    public void setExecuteConnectionTime(long executeConnectionTime) {
        this.executeConnectionTime = executeConnectionTime;
    }

    public long getGetSchemaTime() {
        return getSchemaTime;
    }

    public void setGetSchemaTime(long getSchemaTime) {
        this.getSchemaTime = getSchemaTime;
    }

    public void addDirtyDataCount(String db, String table){
        dirtyDataTableSet.add(db+"."+table);
        dirtyRecordCount++;
    }

    public void addDirtyDataCount(String db, String table,int count){
        dirtyDataTableSet.add(db+"."+table);
        dirtyRecordCount+=count;
    }

    public void clearDirtyDataCount(){
        dirtyDataTableSet.clear();
        dirtyRecordCount = 0;
    }

    public void finshOneTable(int size){
        flushSizeForEveryTable.add(size);
    }

    public void addRestoreToNextTimeCount(int c){
        restoreToNextTimeCount+=c;
    }

    public int getRestoreToNextTimeCount() {
        return restoreToNextTimeCount;
    }

    public ClickhouseWritingStatistic setRestoreToNextTimeCount(int restoreToNextTimeCount) {
        this.restoreToNextTimeCount = restoreToNextTimeCount;
        return this;
    }

    public int getCorePoolSize() {
        return corePoolSize;
    }

    public ClickhouseWritingStatistic setCorePoolSize(int corePoolSize) {
        this.corePoolSize = corePoolSize;
        return this;
    }

    public int getActiveThreadCount() {
        return activeThreadCount;
    }

    public ClickhouseWritingStatistic setActiveThreadCount(int activeThreadCount) {
        this.activeThreadCount = activeThreadCount;
        return this;
    }

    public long getTotalTableCount() {
        return totalTableCount;
    }

    public ClickhouseWritingStatistic setTotalTableCount(long totalTableCount) {
        this.totalTableCount = totalTableCount;
        return this;
    }

    public double getAvgTableFlushSize() {
        return avgTableFlushSize;
    }

    public ClickhouseWritingStatistic setAvgTableFlushSize(double avgTableFlushSize) {
        this.avgTableFlushSize = avgTableFlushSize;
        return this;
    }

    public ClickhouseWritingStatistic addFailed(String dbName, String tableName, long failRecordSize){
        failTables.add(String.format("%s.%s",dbName,tableName));
        failCount+=failRecordSize;
        return this;
    }

    public ClickhouseWritingStatistic setServerIp(String serverIp){
        this.serverIp = serverIp;
        return this;
    }

    public ClickhouseWritingStatistic mergeFrom(ClickhouseWritingStatistic other){
        restoreToNextTimeCount+= other.restoreToNextTimeCount;
        dirtyDataTableSet.addAll(other.dirtyDataTableSet);
        dirtyRecordCount+= other.dirtyRecordCount;
        flushSizeForEveryTable.addAll(other.flushSizeForEveryTable);
//        getConnectionTime+=other.getConnectionTime;
//        executeConnectionTime+=other.executeConnectionTime;
//        getSchemaTime+=other.getSchemaTime;
        failTables.addAll(other.failTables);
        failCount+=other.failCount;
        totalMemorySize+=other.totalMemorySize;
        retryCount+=other.retryCount;
        if(other.largestRecordSizeForOneTable > largestRecordSizeForOneTable){
            largestRecordSizeForOneTable =other.largestRecordSizeForOneTable;
            largestRecordSizeTableName =other.largestRecordSizeTableName;
        }
        if(other.maxDelayMillSec > maxDelayMillSec){
            maxDelayMillSec =other.maxDelayMillSec;
            maxDelayTable =other.maxDelayTable;
        }
        if(other.getConnectionTime > getConnectionTime){
            getConnectionTime =other.getConnectionTime;
        }
        if(other.executeConnectionTime > executeConnectionTime){
            executeConnectionTime =other.executeConnectionTime;
        }
        if(other.getSchemaTime > getSchemaTime){
            getSchemaTime =other.getSchemaTime;
        }
        for(int i=0;i<sizeBoxing.length;i++){
            sizeBoxing[i]+=other.sizeBoxing[i];
        }
        if (kafkaRetryCount < other.getKafkaRetryCount()) {
            kafkaRetryCount = other.getKafkaRetryCount();
        }
        return this;
    }



    public ClickhouseWritingStatistic finish(List<ClickhouseData> list){
        long finishTime=System.currentTimeMillis();
        costMillSec=finishTime- ts;
        int listSize=list.size();
        totalCount=listSize-restoreToNextTimeCount;
        successCount=totalCount-failCount-dirtyRecordCount;
        failTableCount= failTables.size();
        rps=(1000.0*successCount)/costMillSec;
        long avgLogTime = Math.round(
                list.stream().mapToLong(ClickhouseData::getTime).sorted()
                        .skip((listSize-1)/2).limit(2-listSize%2).average().orElse(finishTime));
        delayMillSec =finishTime-avgLogTime;
        maxDelayMillSec=delayMillSec;
        delayUponArrivalMillSec = Math.round(
                list.stream().mapToLong(ClickhouseData::getDelayUponArrival).sorted()
                        .skip((listSize-1)/2).limit(2-listSize%2).average().orElse(0));
        //Delay from kafka
        Supplier<LongStream> sortedKafkaCreateTimeSupplier = () -> list.stream().mapToLong(ClickhouseData::getKafkaCreateTime).sorted();
        long kafkaCreateTime = Math.round(sortedKafkaCreateTimeSupplier.get().skip((listSize-1)/2).limit(2-listSize%2).average().orElse(finishTime));
        delayFromKafkaCreateMillSec=finishTime-kafkaCreateTime;
        long kafkaCreateTime95=Math.round(sortedKafkaCreateTimeSupplier.get().skip(Math.round(listSize*.05)).limit(1).average().orElse(finishTime));
        delayFromKafkaCreateMillSec95=finishTime-kafkaCreateTime95;
        delayFromKafkaCreateMillSecMax=finishTime- sortedKafkaCreateTimeSupplier.get().findFirst().orElse(finishTime);
        failTablesString=String.join(",",failTables);
        dirtyTablesString=String.join(",",dirtyDataTableSet);
        totalTableCount=flushSizeForEveryTable.size();
        avgTableFlushSize=flushSizeForEveryTable.stream().mapToInt(u->u).average().orElse(0);
        stdOfTableFlushSize=Math.sqrt(flushSizeForEveryTable.stream().mapToDouble(u->Math.pow(u-avgTableFlushSize,2)).average().orElse(0));
        recordSizeBoxing=Arrays.toString(sizeBoxing);
        system="clickhouse_flush_statistic";
        ts=System.currentTimeMillis();
        return this;
    }

    public void addRecordSizeToBoxing(ClickhouseData d){
        sizeBoxing[getBoxIndex(d.getMemorySize())]++;
    }

    public void clearRecordSizeToBoxing(){
        sizeBoxing = new int[recordSizeBoxPoint.length+1];
    }


    public static int getBoxIndex(long size){
        int index=recordSizeBoxPoint.length;
        for(int i=0; i<recordSizeBoxPoint.length; i++){
            if(size<recordSizeBoxPoint[i]){
                index=i;
                break;
            }
        }
        return index;
    }

    public double getStdOfTableFlushSize() {
        return stdOfTableFlushSize;
    }

    public ClickhouseWritingStatistic setStdOfTableFlushSize(double stdOfTableFlushSize) {
        this.stdOfTableFlushSize = stdOfTableFlushSize;
        return this;
    }

    public long getTs() {
        return ts;
    }

    public ClickhouseWritingStatistic setTs(long ts) {
        this.ts = ts;
        return this;
    }

    public String getServerIp() {
        return serverIp;
    }

    public long getTotalCount() {
        return totalCount;
    }

    public ClickhouseWritingStatistic setTotalCount(long totalCount) {
        this.totalCount = totalCount;
        return this;
    }

    public long getSuccessCount() {
        return successCount;
    }

    public ClickhouseWritingStatistic setSuccessCount(long successCount) {
        this.successCount = successCount;
        return this;
    }

    public long getFailCount() {
        return failCount;
    }

    public ClickhouseWritingStatistic setFailCount(long failCount) {
        this.failCount = failCount;
        return this;
    }


    public long getFailTableCount() {
        return failTableCount;
    }

    public ClickhouseWritingStatistic setFailTableCount(long failTableCount) {
        this.failTableCount = failTableCount;
        return this;
    }

    public long getCostMillSec() {
        return costMillSec;
    }

    public ClickhouseWritingStatistic setCostMillSec(long costMillSec) {
        this.costMillSec = costMillSec;
        return this;
    }

    public double getRps() {
        return rps;
    }

    public ClickhouseWritingStatistic setRps(double rps) {
        this.rps = rps;
        return this;
    }

    public long getDelayMillSec() {
        return delayMillSec;
    }

    public ClickhouseWritingStatistic setDelayMillSec(long delayMillSec) {
        this.delayMillSec = delayMillSec;
        return this;
    }

    public long getTid() {
        return tid;
    }

    public ClickhouseWritingStatistic setTid(long tid) {
        this.tid = tid;
        return this;
    }

    public String getIp() {
        return ip;
    }

    public ClickhouseWritingStatistic setIp(String ip) {
        this.ip = ip;
        return this;
    }

    public String getSystem() {
        return system;
    }

    public ClickhouseWritingStatistic setSystem(String system) {
        this.system = system;
        return this;
    }

    public ClickhouseWritingStatistic(){

        ts =System.currentTimeMillis();
        tid = Thread.currentThread().getId();
        ip= localIP;
        runningEnv=ClickhouseWriter.getRunningEnv();
    }

    public String getFailTablesString() {
        return failTablesString;
    }

    public ClickhouseWritingStatistic setFailTablesString(String failTablesString) {
        this.failTablesString = failTablesString;
        return this;
    }

    public int getDirtyRecordCount() {
        return dirtyRecordCount;
    }

    public ClickhouseWritingStatistic setDirtyRecordCount(int dirtyRecordCount) {
        this.dirtyRecordCount = dirtyRecordCount;
        return this;
    }

    public String getDirtyTablesString() {
        return dirtyTablesString;
    }

    public ClickhouseWritingStatistic setDirtyTablesString(String dirtyTablesString) {
        this.dirtyTablesString = dirtyTablesString;
        return this;
    }

    public int getQueueCapacity() {
        return queueCapacity;
    }

    public ClickhouseWritingStatistic setQueueCapacity(int queueCapacity) {
        this.queueCapacity = queueCapacity;
        return this;
    }

    public int getQueueCurrentLength() {
        return queueCurrentLength;
    }

    public ClickhouseWritingStatistic setQueueCurrentLength(int queueCurrentLength) {
        this.queueCurrentLength = queueCurrentLength;
        return this;
    }

    public int getCurrentPoolSize() {
        return  currentPoolSize;
    }

    public ClickhouseWritingStatistic setCurrentPoolSize(int currentPoolSize) {
        this.currentPoolSize = currentPoolSize;
        return this;
    }

    public long getQueueCurrentSize() {
        return queueCurrentSize;
    }

    public ClickhouseWritingStatistic setQueueCurrentSize(long queueCurrentSize) {
        this.queueCurrentSize = queueCurrentSize;
        return this;
    }

    public String getFlushType() {
        return flushType;
    }

    public ClickhouseWritingStatistic setFlushType(String flushType) {
        this.flushType = flushType;
        return this;
    }

    public String getRecordSizeBoxing() {
        return recordSizeBoxing;
    }

    public ClickhouseWritingStatistic setRecordSizeBoxing(String recordSizeBoxing) {
        this.recordSizeBoxing = recordSizeBoxing;
        return this;
    }

    public long getLargestRecordSizeForOneTable() {
        return largestRecordSizeForOneTable;
    }

    public ClickhouseWritingStatistic setLargestRecordSizeForOneTable(long largestRecordSizeForOneTable) {
        this.largestRecordSizeForOneTable = largestRecordSizeForOneTable;
        return this;
    }

    public String getLargestRecordSizeTableName() {
        return largestRecordSizeTableName;
    }

    public ClickhouseWritingStatistic setLargestRecordSizeTableName(String largestRecordSizeTableName) {
        this.largestRecordSizeTableName = largestRecordSizeTableName;
        return this;
    }

    public long getTotalMemorySize() {
        return totalMemorySize;
    }

    public ClickhouseWritingStatistic setTotalMemorySize(long totalMemorySize) {
        this.totalMemorySize = totalMemorySize;
        return this;
    }

    public long getDelayFromKafkaCreateMillSec95() {
        return delayFromKafkaCreateMillSec95;
    }

    public ClickhouseWritingStatistic setDelayFromKafkaCreateMillSec95(long delayFromKafkaCreateMillSec95) {
        this.delayFromKafkaCreateMillSec95 = delayFromKafkaCreateMillSec95;
        return this;
    }

    public long getDelayFromKafkaCreateMillSecMax() {
        return delayFromKafkaCreateMillSecMax;
    }

    public ClickhouseWritingStatistic setDelayFromKafkaCreateMillSecMax(long delayFromKafkaCreateMillSecMax) {
        this.delayFromKafkaCreateMillSecMax = delayFromKafkaCreateMillSecMax;
        return this;
    }

    public void addRetryCount(int c){
        retryCount+=c;
    }

    public long getRetryCount() {
        return retryCount;
    }

    public ClickhouseWritingStatistic setRetryCount(long retryCount) {
        this.retryCount = retryCount;
        return this;
    }

    public long getMaxDelayMillSec() {
        return maxDelayMillSec;
    }

    public ClickhouseWritingStatistic setMaxDelayMillSec(long maxDelayMillSec) {
        this.maxDelayMillSec = maxDelayMillSec;
        return this;
    }

    public String getMaxDelayTable() {
        return maxDelayTable;
    }

    public ClickhouseWritingStatistic setMaxDelayTable(String maxDelayTable) {
        this.maxDelayTable = maxDelayTable;
        return this;
    }

    public String getQueueType() {
        return queueType;
    }

    public ClickhouseWritingStatistic setQueueType(String queueType) {
        this.queueType = queueType;
        return this;
    }

    public String getDirtyDataSample() {
        return dirtyDataSample;
    }

    public ClickhouseWritingStatistic setDirtyDataSample(String dirtyDataSample) {
        this.dirtyDataSample = dirtyDataSample;
        return this;
    }

    public String getUnitTag() {
        return ClickhouseWriter.getUnitTag();
    }

    public String getRunningEnv() {
        return runningEnv;
    }

    public ClickhouseWritingStatistic setRunningEnv(String runningEnv) {
        this.runningEnv = runningEnv;
        return this;
    }

    public int getTaskQueueLength() {
        return taskQueueLength;
    }

    public ClickhouseWritingStatistic setTaskQueueLength(int taskQueueLength) {
        this.taskQueueLength = taskQueueLength;
        return this;
    }
    public long getKafkaRetryCount() {
        return kafkaRetryCount;
    }

    public void setKafkaRetryCount(long kafkaRetryCount) {
        this.kafkaRetryCount = kafkaRetryCount;
    }

}
