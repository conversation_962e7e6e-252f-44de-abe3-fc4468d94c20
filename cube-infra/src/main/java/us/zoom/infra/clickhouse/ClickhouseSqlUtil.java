package us.zoom.infra.clickhouse;

import com.clickhouse.jdbc.ClickHouseArray;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import us.zoom.cube.lib.common.CubeConstants;
import us.zoom.infra.enums.QueryStatus;
import us.zoom.infra.utils.CommonSplitConstants;
import us.zoom.infra.utils.DateUtils;
import us.zoom.jsqlparser.JSQLParserException;
import us.zoom.jsqlparser.expression.*;
import us.zoom.jsqlparser.expression.operators.conditional.AndExpression;
import us.zoom.jsqlparser.expression.operators.relational.*;
import us.zoom.jsqlparser.parser.CCJSqlParserManager;
import us.zoom.jsqlparser.schema.Column;
import us.zoom.jsqlparser.schema.Table;
import us.zoom.jsqlparser.statement.Statement;
import us.zoom.jsqlparser.statement.select.*;
import us.zoom.jsqlparser.statement.values.ValuesStatement;
import us.zoom.jsqlparser.util.InfluxToClickhouseVisitor;

import java.io.StringReader;
import java.lang.reflect.Array;
import java.time.Instant;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAccessor;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static us.zoom.infra.clickhouse.ClickhouseSchema.TIME_COLUMN_NAME;

/**
 * <AUTHOR> Wang
 * @date 2022/01/28 1:46 PM
 */
@Slf4j
public class ClickhouseSqlUtil {

    public static CCJSqlParserManager parserManager = new CCJSqlParserManager();
    private static final Pattern p = Pattern.compile("(\\d+)");
    private static final int SNIPPET_TEXT_BEGIN_INDEX = 2;
    private static final String DUMMY_CONDITION = "1=1";
    private static final Pattern SNIPPET_PATTERN = Pattern.compile("(WHERE|where)*(AND|and|OR|or)*\\s+\\$\\{[A-Za-z0-9]+\\}");
    private static final Pattern VARIABLE_PATTERN = Pattern.compile("_(.*?)_");

    private static final String TO_DATA_TIME = "toDateTime";


    public static String toClickhouseName(String ori){
        try {
            return ChNameEncoder.encode(ori);
        }catch (Exception e){
            log.error("Error to encode {} with exception {}",ori,e);
            return "";
        }
    }

    static public String tableNameEncoding(String sql){
        try {
            us.zoom.jsqlparser.statement.Statement statement = parserManager.parse(new StringReader(sql));
            if(statement instanceof Select){
                SelectBody selectBody = ((Select) statement).getSelectBody();
                encodeTableItem(selectBody);
                sql=statement.toString();
            }
        }catch (Exception e){
            log.info("Encounter error when doing table encoding for {}",sql);
        }
        return sql;
    }

    static private void encodeTableItem(SelectBody selectBody) {
        if (selectBody instanceof PlainSelect) {
            FromItem fromItem = ((PlainSelect) selectBody).getFromItem();
            if (fromItem instanceof SubSelect) {
                encodeTableItem(((SubSelect) fromItem).getSelectBody());
            } else if(fromItem instanceof Table){
                List<String> partItems = ((Table) fromItem).getPartItems();
                for(int i=0;i<partItems.size();i++){
                    String s = partItems.get(i);
                    s = encodeClickhouseName(s);
                    partItems.set(i,s);
                }
            }
        }
    }

    public static void main(String[] args) {
        System.out.println(toClickhouseName("Infra_Monitor_Cube-Alarm"));
    }

    public static String encodeClickhouseName(String s) {
        boolean enclosed= s.startsWith("\"") && s.endsWith("\"");
        s =enclosed? s.substring(1, s.length()-1): s;
        s = ClickhouseSqlUtil.toClickhouseName(s);
        s =enclosed?"\""+ s +"\"": s;
        return s;
    }

    public static Integer getGapMinute(String sql){
        try {
            us.zoom.jsqlparser.statement.Statement statement = parserManager.parse(new StringReader(sql));
            if(statement instanceof Select){
                SelectBody selectBody = ((Select) statement).getSelectBody();
                if(selectBody instanceof PlainSelect){
                    Map<String, List<String>> whereTags = getWhereTags(((PlainSelect) selectBody).getWhere());
                    if(whereTags.containsKey(TIME_COLUMN_NAME)){
                        OptionalInt min = whereTags.get(TIME_COLUMN_NAME).stream().map(u -> u.replaceAll("[^0-9]", ""))
                                .filter(u -> u.matches("\\d+"))
                                .mapToInt(Integer::valueOf)
                                .min();
                        return min.isPresent()?min.getAsInt():null;
                    }
                }
            }
        }catch (Exception e){
            log.info("Encounter error when getGapMinute for sql {}",sql);
        }
        return null;
    }

    static public String adjustSelectOrderAndAddLimit(String sql,int limit){
        try {
            us.zoom.jsqlparser.statement.Statement statement = parserManager.parse(new StringReader(sql));
            if(statement instanceof Select){
                SelectBody selectBody = ((Select) statement).getSelectBody();
                if(selectBody instanceof PlainSelect){
                    PlainSelect plainSelect = (PlainSelect) selectBody;
                    List<SelectItem> selectItems = plainSelect.getSelectItems();
                    //SQL limit
                    if(((PlainSelect) selectBody).getLimit()==null){
                        Limit l=new Limit();
                        l.setRowCount(new LongValue(limit));
                        ((PlainSelect) selectBody).setLimit(l);
                    }
                    //move time column to the first
                    int timeIndex= findTimeIndex(selectItems);
                    if(timeIndex>0 && timeIndex<selectItems.size()){
                        List<SelectItem> timeFirstSelectItems = new ArrayList<>();
                        SelectItem timeItem = selectItems.remove(timeIndex);
                        timeFirstSelectItems.add(timeItem);
                        timeFirstSelectItems.addAll(selectItems);
                        plainSelect.setSelectItems(timeFirstSelectItems);
                    }
                    return statement.toString();
                }
            }
        }catch (Exception e){
            log.info("Encounter error when adjust select order for sql {}",sql);
        }
        return sql;
    }


    public static String concatFullTableName(String db,String table){
        return String.format("%s.%s",db,table);
    }

    public static String getLocalTableNaming(String tableName) {
        return tableName + ClickhouseConst.CLICK_LOCAL_SUFFIX;
    }

    public static String getBufferTableNaming(String tableName) {
        return tableName + ClickhouseConst.CLICK_BUFFER_SUFFIX;
    }

    public static String fromLocalToOriginalNaming(String localTable) {
        return localTable.substring(0, localTable.lastIndexOf(ClickhouseConst.CLICK_LOCAL_SUFFIX));
    }

    public static String fromLocalToBuffer(String localName){
        return localName.replace(ClickhouseConst.CLICK_LOCAL_SUFFIX,ClickhouseConst.CLICK_BUFFER_SUFFIX);
    }

    public static String fromTableToDesensitized(String table){
        return table+ClickhouseConst.CLICK_DESENSITIZED_SUFFIX;
    }

    public static String encodeThenToDesensitized(String table){
        return fromTableToDesensitized(encodeClickhouseName(table));
    }

    public static String fromItemsToDesensitized(String s) {
        boolean enclosed= s.startsWith("\"") && s.endsWith("\"");
        s =enclosed? s.substring(1, s.length()-1): s;
        s = s+ClickhouseConst.CLICK_DESENSITIZED_SUFFIX;
        s =enclosed?"\""+ s +"\"": s;
        return s;
    }

    private static int findTimeIndex(List<SelectItem> selectItems ) {
        int timeIndex=0;
        for(; timeIndex < selectItems.size(); timeIndex++){
            SelectItem selectItem = selectItems.get(timeIndex);
            if(!(selectItem instanceof SelectExpressionItem)){
                continue;
            }
            Alias alias = ((SelectExpressionItem) selectItem).getAlias();
            if(alias!=null){
                if(TIME_COLUMN_NAME.equals(alias.getName())) {
                    break;
                }
            }else{
                Expression expression = ((SelectExpressionItem) selectItem).getExpression();
                if(expression instanceof Column){
                    if(TIME_COLUMN_NAME.equals(expression.toString())) {
                        break;
                    }
                }
            }
        }
        return timeIndex;
    }

    static public String doPlaceholder(String sql){
        return doPlaceholder(sql,new SqlPlaceholder());
    }


    static public String doPlaceholder(String sql,SqlPlaceholder sqlPlaceholderMap){
        boolean singleEnclosing=false;
        boolean doubleEnclosing=false;
        if(StringUtils.isEmpty(sql)||sql.length()==1){
            return sql;
        }
        StringBuilder ret=new StringBuilder(sql);
        int colonStart=-1;
        for(int i=0;i<sql.length();i++){
            singleEnclosing=isQuote(sql,i,'\'')?!singleEnclosing:singleEnclosing;
            doubleEnclosing=isQuote(sql,i,'\"')?!doubleEnclosing:doubleEnclosing;
            if(singleEnclosing||doubleEnclosing){
                continue;
            }
            if(sql.charAt(i)==':'){
                if(colonStart==-1){
                    colonStart=i;
                }else{
                    String ori=ret.substring(colonStart,i+1);
                    ret.setCharAt(i,'_');
                    ret.setCharAt(colonStart,'_');
                    sqlPlaceholderMap.add(ret.substring(colonStart,i+1),ori);
                    colonStart=-1;
                }
            }
        }
        return ret.toString();
    }

    static public String toTranslatable(String s){
        return String.format(":%s:",s);
    }


    static public String trimDoubleQuote(String s){
        return trimQuote(s,'\"');
    }

    static public String trimAllQuotes(String s){
        return trimQuote(trimQuote(s,'\"'),'\'');
    }

    static public String trimQuote(String s, char quote){
        if(!StringUtils.isEmpty(s) && s.length()>1){
            if(s.charAt(0)==quote && s.charAt(s.length()-1)==quote){
                return s.substring(1,s.length()-1);
            }
        }
        return s;
    }

    static public boolean isQuote(String sql,int index, char quote){
        if(!StringUtils.isEmpty(sql) && sql.length()>1){
            return sql.charAt(index)==quote && sql.charAt(index-1)!='\\';
        }
        return false;
    }


    static public String removeGroupByIfNotAggrFuncs(String sql){
        boolean hasFuncs=false;
        try {
            Statement parse = parserManager.parse(new StringReader(sql));
            if (parse instanceof Select) {
                SelectBody selectBody = ((Select) parse).getSelectBody();
                if (selectBody instanceof PlainSelect) {
                    for(SelectItem selectItem: ((PlainSelect) selectBody).getSelectItems()){
                        hasFuncs = hasFuncs || isHasFuncs(selectItem);
                    }
                    if(hasFuncs){
                        return sql;
                    }
                    if(((PlainSelect) selectBody).getGroupBy()!=null && !hasFuncs){
                        ((PlainSelect) selectBody).setGroupByElement(null);
                        return parse.toString();
                    }
                }
            }
        }catch (Exception e){
            log.error("Error when remove group by when aggr function is not presented, sql={} e={}",sql,e);
        }
        return sql;
    }


    private static boolean isHasFuncs(Expression expression) {
        if(expression instanceof Function){
            return true;
        }else if(expression instanceof Parenthesis){
            Expression inParenthesis = ((Parenthesis) expression).getExpression();
            return isHasFuncs(inParenthesis);
        }else if(expression instanceof BinaryExpression){
            return isHasFuncs(((BinaryExpression) expression).getRightExpression())
                    || isHasFuncs(((BinaryExpression) expression).getLeftExpression());
        } else if (expression instanceof ParenthesesExpression) {
            Expression inParenthesis = ((ParenthesesExpression) expression).getObjExpression();
            return isHasFuncs(inParenthesis);
        }
        return false;
    }


    private static boolean isHasFuncs(SelectItem selectItem) {
        boolean hasFuncs=false;
        if(selectItem instanceof SelectExpressionItem){
            Expression expression = ((SelectExpressionItem) selectItem).getExpression();
            return isHasFuncs(expression);
        }
        return hasFuncs;
    }


    static public Map<String,Object> parseSql(String sql,boolean useRawTest){
        Map<String,Object>ret=new HashMap<>();
        List<Map<String,Object>>fields=new LinkedList<>();
        ret.put("fields",fields);
        Map<String,Object>groupBy=new HashMap<>();
        ret.put("groupBy",groupBy);
        ret.put("rawText",sql);
        if(useRawTest ||sql.startsWith("_DO_NOT_PAESE_THIS_")||sql.startsWith("_DO_NOT_PARSE_THIS_") || !sql.contains("time")){
            ret.put("rawText",sql.replace("_DO_NOT_PAESE_THIS_","").replace("_DO_NOT_PARSE_THIS_",""));
            return ret;
        }

        //{"sql":" SELECT avg(\"irq\") AS \"avg_irq1231123\", toStartOfInterval(time, INTERVAL :INTERVAL:) AS time FROM \"ds_web\".\"cpu\" WHERE time > :dashboardTime: AND time < :upperDashboardTime: ${snippet} ${snippet}  GROUP BY time ORDER BY time  ","dbType":"clickhouse"}
        //parse snippet and remove
        String sqlWithoutSnippet = parseSnippetAndRemove(sql, ret);

        String sqlWithoutPlaceHolder = doPlaceholder(sqlWithoutSnippet);
        try {
            Statement parse = parserManager.parse(new StringReader(sqlWithoutPlaceHolder));
            if (parse instanceof Select) {
                SelectBody selectBody = ((Select) parse).getSelectBody();
                if(selectBody instanceof PlainSelect){
                    FromItem fromItem = ((PlainSelect) selectBody).getFromItem();
                    if(fromItem instanceof Table && ((Table) fromItem).getPartItems().size()==2){
                        List<String> partItems = ((Table) fromItem).getPartItems();
                        ret.put("database", trimDoubleQuote(partItems.get(1)));
                        ret.put("measurement",trimDoubleQuote(partItems.get(0)));
                    }else{
                        return ret;
                    }
                    GroupByElement groupByElement = ((PlainSelect) selectBody).getGroupBy();

                    Expression where = ((PlainSelect) selectBody).getWhere();
                    Map<String,List<String>>whereTags=new HashMap<>();
                    if(where!=null){
                        whereTags=getWhereTags(where);
                        whereTags.remove(TIME_COLUMN_NAME);
                    }
                    ret.put("tags",whereTags);
                    Set<String>groupbyTags=new HashSet<>();
                    if(groupByElement!=null){
                        ExpressionList groupByExpressionList = groupByElement.getGroupByExpressionList();
                        groupbyTags.addAll(groupByExpressionList.getExpressions().stream().map(u->trimDoubleQuote(u.toString()))
                                .filter(u->!u.equals(TIME_COLUMN_NAME)).collect(Collectors.toList()));
                    }
                    groupBy.put("tags",groupbyTags);
                    List<SelectItem> selectItems = ((PlainSelect) selectBody).getSelectItems();
                    for(SelectItem selectItem:selectItems){
                        if(selectItem instanceof SelectExpressionItem){
                            Map<String,Object>field=new HashMap<>();

                            Expression expression = ((SelectExpressionItem) selectItem).getExpression();

                            if(expression instanceof Function){
                                String oriFieldName = trimDoubleQuote(((Function) expression).getName());
                                field.put("value", oriFieldName);
                                field.put("type", "func");
                                Map<String, Object> arg = new HashMap<>();
                                List<Map<String, Object>> args = new LinkedList<>();
                                field.put("args", args);
                                args.add(arg);
                                arg.put("value", trimDoubleQuote(((Function) expression).getParameters().toString()));
                                //set field/tag
                                arg.put("type", groupbyTags.contains(oriFieldName) ? "tag" : "field");
                                arg.put("alias", "");
                            }else if(expression instanceof Column){
                                field.put("value", trimDoubleQuote(((Column) expression).getColumnName()));
                                field.put("type", "field");
                            }

                            Alias alias = ((SelectExpressionItem) selectItem).getAlias();
                            String aliasName=null;
                            if(alias!=null){
                                aliasName = trimDoubleQuote(alias.getName());
                                field.put("alias", aliasName);
                                //indicating this column is time, and this column can be subject to time functions
                                if(TIME_COLUMN_NAME.equals(aliasName)){
                                    if(expression instanceof Function){
                                        groupBy.put(TIME_COLUMN_NAME,getTimeGroupBy((Function) expression));
                                    }
                                }
                            }
                            if(!TIME_COLUMN_NAME.equals(aliasName)) {
                                fields.add(field);
                            }
                        }
                    }
                }
                ret.put("rawText",null);
            }
        }catch (Exception e){
            log.warn("Parse SQL error, sql={}, error={}",sqlWithoutPlaceHolder,e);
        }
        return ret;
    }

    public static String parseSnippetAndRemove(String sql, Map<String, Object> ret) {

        Matcher matcher = SNIPPET_PATTERN.matcher(sql);
        List<String> snippets = new ArrayList<>();
        if (matcher.find()) {
            String group = matcher.group();

            if (group.startsWith(CommonSplitConstants.SPACE)) {
                sql = sql.replace(group, CommonSplitConstants.SPACE);
            } else if (group.toLowerCase().startsWith(CubeConstants.AND) || group.toLowerCase().startsWith(CubeConstants.OR)) {
                sql = sql.replace(group, CommonSplitConstants.SPACE);
            } else if (group.toLowerCase().startsWith(CubeConstants.WHERE)) {
                sql = sql.replace(group, CubeConstants.WHERE + CommonSplitConstants.SPACE + DUMMY_CONDITION);
            }

            String snippet = group.substring(group.indexOf(CommonSplitConstants.DOLLAR) + 2, group.length() - 1);
            if (StringUtils.isNotBlank(snippet)) {
                snippets.add(snippet);
            }
        }

        ret.put(CubeConstants.SNIPPET, snippets);
        return sql;

    }



    public static Map<String,List<String>> merge(Map<String,List<String>>m1,Map<String,List<String>>m2){
        for(Map.Entry<String,List<String>> en:m1.entrySet()){
            if(m2.containsKey(en.getKey())){
                m2.get(en.getKey()).addAll(en.getValue());
            }else{
                m2.put(en.getKey(),en.getValue());
            }
        }
        return m2;
    }

    public void findWhere(){

    }

    public static Map<String,List<String>>getWhereTags(Expression exp){
        Map<String,List<String>> ret=new HashMap<>();
        if(exp instanceof Parenthesis){
            exp=((Parenthesis) exp).getExpression();
        }
        if(exp instanceof BinaryExpression){
            if(exp instanceof ComparisonOperator) {
                if (((ComparisonOperator) exp).getLeftExpression() instanceof Column) {
                    String columnName = ((Column) ((ComparisonOperator) exp).getLeftExpression()).getColumnName();
                    List<String> vs = new ArrayList<>();
                    ret.put(trimDoubleQuote(columnName), vs);
                    if (((ComparisonOperator) exp).getRightExpression() instanceof Column) {
                        String v = ((Column) ((ComparisonOperator) exp).getRightExpression()).getColumnName();
                        if (v.startsWith("_") && v.endsWith("_")) {
                            StringBuilder vBuf = new StringBuilder(v);
                            vBuf.setCharAt(0,':');
                            vBuf.setCharAt(vBuf.length()-1,':');
                            v=vBuf.toString();
                        }
                        vs.add(v);
                    }else{
                        String v =((ComparisonOperator) exp).getRightExpression().toString();
                        vs.add(trimAllQuotes(v));
                    }
                }
            }
            else {
                merge(getWhereTags(((BinaryExpression) exp).getLeftExpression()),ret);
                merge(getWhereTags(((BinaryExpression) exp).getRightExpression()),ret);
            }
        }
        return ret;
    }

    public static String getTimeGroupBy(Function f){
        String ret="auto";
        String functionName = f.getName();
        if("toStartOfInterval".equals(functionName)){
            List<Expression> expressions = f.getParameters().getExpressions();
            if(expressions.size()==2){
                Expression expression = expressions.get(1);
                if(expression instanceof IntervalExpression){
                    String parameter = ((IntervalExpression) expression).getParameter();
                    if(parameter==null || parameter.toLowerCase().contains("interval")) {
                        return ret;
                    }else{
                        return ((IntervalExpression) expression).getParameter() + toInfluxTimeInterval(((IntervalExpression) expression).getIntervalType());
                    }
                }
            }
        }
        return ret;
    }

    static String toInfluxTimeInterval(String type){
        switch (type.toLowerCase()){
            case "second":return "s";
            case "minute":return "m";
            case "hour":return "h";
            case "day":return "d";
            case "week":return "w";
            default:return "unknown";
        }
    }

    static public long approximateSize(Map<String,?> m){
        if(m==null || m.size()==0){
            return 0;
        }
        long ret= m.keySet().stream().filter(Objects::nonNull).mapToLong(key -> key.length() * 2L).sum();
        ret+=m.values().stream().filter(Objects::nonNull).mapToLong(key -> key.toString().length() * 2L).sum();
        return ret;
    }

    public static List<String> getGroupByFromSQL(String sql){
        try {
            us.zoom.jsqlparser.statement.Statement statement = parserManager.parse(new StringReader(sql));
            if(statement instanceof Select){
                SelectBody selectBody = ((Select) statement).getSelectBody();
                return findGroupByFromSelect(selectBody);
            }
        }catch (Exception e){
            log.info("Encounter error when parse group by for {}",sql);
        }
        return new LinkedList<>();
    }

    public static List<String>findGroupByFromSelect(SelectBody selectBody){
        if(selectBody instanceof PlainSelect){
            GroupByElement groupBy = ((PlainSelect) selectBody).getGroupBy();
            if(groupBy!=null){
                return groupBy.getGroupByExpressionList().getExpressions()
                        .stream().filter(u->u instanceof Column).map(u-> ClickhouseSqlUtil.trimDoubleQuote(((Column) u).getColumnName())).collect(Collectors.toList());
            }else if(((PlainSelect) selectBody).getFromItem() instanceof SubSelect){
                return findGroupByFromSelect(((SubSelect) ((PlainSelect) selectBody).getFromItem()).getSelectBody());
            }
        }
        return new LinkedList<>();
    }



    public static String revertPlaceholder(String sql,SqlPlaceholder sqlPlaceholder){
        for(String s:sqlPlaceholder.getAllKeys()){
            sql=sql.replace(s,sqlPlaceholder.search(s));
        }
        sql=sql.replace("INTERVAL _interval","INTERVAL :interval:");
        return sql;
    }



    public static String influxQL2ChSql(String sql,QueryMonitor monitor)  {
        monitor.appendInputSql(sql);
        try {
            us.zoom.jsqlparser.statement.Statement statement = parserManager.parse(new StringReader(sql));
            if (statement instanceof Select) {
                SelectBody selectBody = ((Select) statement).getSelectBody();
                selectBody.accept(new InfluxToClickhouseVisitor());
                sql = statement.toString();
                monitor.appendTranslatedSql(sql);
            }
        }catch (JSQLParserException e){
            monitor.setStatus(QueryStatus.TRANSLATE_FAIL.name());
        }
        return sql;

    }

    public static void replaceTimeInterval(SelectExpressionItem ps,IntervalExpression replaceIn){
        Expression expression = ps.getExpression();
        if(!(expression instanceof Function)){
            return;
        }
        if(!"toStartOfInterval".equals(((Function) expression).getName())){
            return;
        }
        List<Expression> expressions = ((Function) expression).getParameters().getExpressions();
        if(expressions.size()!=2){
            return;
        }
        Expression intervalParam = expressions.get(1);
        if(!(intervalParam instanceof IntervalExpression)){
            return;
        }
        if(!"_INTERVAL_".equals(((IntervalExpression) intervalParam).getExpression().toString())){
            return;
        }
        expressions.set(1,replaceIn);
    }

    public static Statement getStatementThrowException(String sql) throws JSQLParserException {
        return  parserManager.parse(new StringReader(sql));
    }

    public static Statement getStatement(String sql){
        try {
            return    parserManager.parse(new StringReader(sql));
        } catch (JSQLParserException e) {
            return null;
        }
    }

    public static PlainSelect getPlainSelect(String sql){
        try {
            us.zoom.jsqlparser.statement.Statement statement = parserManager.parse(new StringReader(sql));
            if (statement instanceof Select) {
                SelectBody selectBody = ((Select) statement).getSelectBody();
                if (selectBody instanceof PlainSelect) {
                    return (PlainSelect) selectBody;
                }
            }
        }catch (Exception e){
            log.error("Error when parse plain sql",e);
        }
        return null;
    }

    public static EqualsTo getEqualto(){
        EqualsTo equalsTo=new EqualsTo();
        equalsTo.setLeftExpression(new LongValue(1));
        equalsTo.setRightExpression(new LongValue(1));
        return equalsTo;
    }

    public static String toFullVarableName(String s){
        return ":"+s+":";
    }

    public static Function makeRelativeTimeFunciton(long minute){
        Function f=new Function();
        f.setName("subtractMinutes");
        ExpressionList el=new ExpressionList();
        f.setParameters(el);
        Function now=new Function();
        now.setName("now");
        el.addExpressions(now);
        LongValue l=new LongValue(minute);
        el.addExpressions(l);
        return f;
    }

    public static void searchAndSetValue(BinaryExpression b, String key, Object value) {

        if (b.getLeftExpression() instanceof Column) {

            if (((Column) b.getLeftExpression()).getColumnName().replace(CommonSplitConstants.ESCAPE_QUOTATION, CommonSplitConstants.EMPTY_STRING).equals(key)) {
                if (value instanceof String) {
                    b.setRightExpression(new StringValue(value.toString()));
                } else if (value instanceof Long || value instanceof Integer
                        || value instanceof Double || value instanceof Float) {
                    b.setRightExpression(new DoubleValue(value.toString()));
                }
            }

            return;
        }

        searchAndSetValue((BinaryExpression)b.getLeftExpression(), key, value);
        searchAndSetValue((BinaryExpression)b.getRightExpression(), key, value);
    }

    private static Expression replaceLikeWithTrueCondition(Expression where, Expression target) {
        if (where == null || target == null) return where;

        if (where.equals(target)) {
            return new EqualsTo(new LongValue(1), new LongValue(1));
        }

        if (where instanceof AndExpression andExpr) {
            Expression left = replaceLikeWithTrueCondition(andExpr.getLeftExpression(), target);
            Expression right = replaceLikeWithTrueCondition(andExpr.getRightExpression(), target);
            return new AndExpression(left, right);
        }

        return where;
    }

    public static void replaceWhereCondition(SelectBody selectBody,BinaryExpression b,Map<String,ClickhouseVariable>variables){
        replaceWhereCondition(selectBody,b,variables,false);
    }

    public static void replaceWhereCondition(SelectBody selectBody,BinaryExpression b,Map<String,ClickhouseVariable>variables, boolean replaceVariableRecursively){
        if(b instanceof LikeExpression && b.getRightExpression() instanceof StringValue){
            StringValue rightExpression = (StringValue) b.getRightExpression();
            for(Map.Entry<String,ClickhouseVariable> v:variables.entrySet()){
                Object replaceTo = v.getValue().getValue();
                if(replaceTo instanceof List<?>){
                    ClickhouseVariable value = v.getValue();
                    Object valueWithType = value.getValueWithType();
                    if(valueWithType instanceof ExpressionList){
                        ListVariableReplacer r = new ListVariableReplacer((ExpressionList) valueWithType);
                        r.replaceListVariable(v.getKey(), (LikeExpression) b, selectBody);
                    }
                }else{
                    if(replaceTo==null ){
                        replaceTo="";
                    }
                    rightExpression.setValue(rightExpression.getValue().replace(toFullVarableName(v.getKey()), replaceTo.toString()));
//                    if(rightExpression.getValue().contains(toFullVarableName(v.getKey()))){
//                        if(!replaceTo.equals("")){
//                            rightExpression.setValue(rightExpression.getValue().replace(toFullVarableName(v.getKey()), replaceTo.toString()));
//                        }else{
//                            if (selectBody instanceof PlainSelect plainSelect) {
//                                Expression where = plainSelect.getWhere();
//                                Expression newWhere = replaceLikeWithTrueCondition(where, b);
//                                plainSelect.setWhere(newWhere);
//                            }
//                        }
//                    }
                }
            }
            return;
        }
        String rightVariable=getVariableName(b.getRightExpression());
        String leftVariable=getVariableName(b.getLeftExpression());
        if (b.getRightExpression() instanceof ArrayExpression) {
            Expression objExpression = ((ArrayExpression) b.getRightExpression()).getObjExpression();
            if (objExpression instanceof Function) {
                ExpressionList parameters = ((Function) objExpression).getParameters();
                for (Expression expression : parameters.getExpressions()) {
                    if (expression instanceof StringValue) {
                        String variableName = getVariableName(expression);
                        if (variables.containsKey(variableName)) {
                            Object valueWithType = variables.get(variableName).getValueWithType();
                            if (valueWithType instanceof StringValue) {
                                ((StringValue) expression).setValue(((StringValue) valueWithType).getValue());
                            }
                        }
                    }
                }
            }
        }
        if (replaceVariableRecursively) {
            Expression expression = replaceColumnVariablesInExpression(b.getLeftExpression(), variables);
            if (expression != null) {
                b.setLeftExpression(expression);
            } else {
                ConditionDisabler.disableCondition(b,selectBody);
                return;
            }
            expression = replaceColumnVariablesInExpression(b.getRightExpression(), variables);
            if (expression != null) {
                b.setRightExpression(expression);
            } else {
                ConditionDisabler.disableCondition(b,selectBody);
            }
            return;
        }
        String variableName=null;
        Boolean varialbeOnRight=null;
        if(variables.containsKey(rightVariable)){
            variableName=rightVariable;
            varialbeOnRight=true;
        }else if(variables.containsKey(leftVariable)){
            variableName=leftVariable;
            varialbeOnRight=false;
        }
        if(StringUtils.isEmpty(variableName)){
            return;
        }
        ClickhouseVariable clickhouseVariable = variables.get(variableName);
        if(clickhouseVariable==null){
            return;
        }
        Object valueWithType = clickhouseVariable.getValueWithType();
        if(valueWithType ==null){
            ConditionDisabler.disableCondition(b,selectBody);
            return;
        }

        if(valueWithType instanceof ExpressionList){
            ListVariableReplacer r=new ListVariableReplacer((ExpressionList) valueWithType);
            r.replaceListVariable(b,selectBody);
            return;
        }
        if(varialbeOnRight) {
            b.setRightExpression((Expression) valueWithType);
        }else{
            b.setLeftExpression((Expression) valueWithType);
        }
    }

    /**
     * Recursively replace column variables in an expression
     */
    private static Expression replaceColumnVariablesInExpression(Expression expression, Map<String, ClickhouseVariable> variables) {
        if (expression == null) {
            return null;
        }

        if (expression instanceof Column || expression instanceof StringValue) {
            String variableName = getVariableName(expression);
            if (variableName != null) {
                return getVarValue(variableName, variables);
            }
        } else if (expression instanceof BinaryExpression binaryExpr) {
            // Recursively process left and right expressions
            Expression newExpression = replaceColumnVariablesInExpression(binaryExpr.getLeftExpression(), variables);
            if (newExpression != null) {
                binaryExpr.setLeftExpression(newExpression);
            }
            newExpression = replaceColumnVariablesInExpression(binaryExpr.getRightExpression(), variables);
            if (newExpression != null) {
                binaryExpr.setRightExpression(newExpression);
            }
        } else if (expression instanceof Function function) {
            // Recursively process function parameters
            ExpressionList parameters = function.getParameters();
            List<Expression> newExpressions = Lists.newArrayList();
            if (parameters != null) {
                for (Expression param : parameters.getExpressions()) {
                    Expression newExpression = replaceColumnVariablesInExpression(param, variables);
                    if (newExpression != null) {
                        newExpressions.add(newExpression);
                    }
                }
                parameters.setExpressions(newExpressions);
            }
        } else if (expression instanceof Parenthesis parenthesis) {
            // Recursively process expression inside parentheses
            Expression newExpression = replaceColumnVariablesInExpression(parenthesis.getExpression(), variables);
            parenthesis.setExpression(newExpression);
        } else if (expression instanceof ParenthesesExpression parenthesesExpr) {
            // Recursively process expression inside parentheses
            Expression newExpression = replaceColumnVariablesInExpression(parenthesesExpr.getObjExpression(), variables);
            parenthesesExpr.setObjExpression(newExpression);
        }
        return expression;
    }

    private static Expression getVarValue(String variableName, Map<String, ClickhouseVariable> variables) {
        if (variableName != null && variables.containsKey(variableName)) {
            ClickhouseVariable var = variables.get(variableName);
            Object varValue = var.getValueWithType();
            if (varValue != null) {
                return (Expression) varValue;
            }
        }
        return null;
    }

    public static String getVariableName(Expression expression){

        String ori=expression instanceof StringValue?((StringValue) expression).getValue():expression.toString();
        return getVariableName(ori);
    }

    public static String getVariableName(String ori){
        if(ori.length()<2){
            return null;
        }
        if(ori.charAt(0)==ori.charAt(ori.length()-1) && ori.charAt(0)=='_'){
            return ori.substring(1,ori.length()-1);
        }
        if(ori.charAt(0)==ori.charAt(ori.length()-1) && ori.charAt(0)==':'){
            return ori.substring(1,ori.length()-1);
        }
        return null;
    }

    public static Long getNumber(String from){
        try {
            Matcher m = p.matcher(from);
            if (m.find()) {
                return Long.valueOf(m.group(1));
            }
        }catch (Exception e){
          log.warn("Parse number wrong from {} error={}",from,e);
        }
        return null;
    }

    public static Long getMinute(String time){
        Long number = getNumber(time);
        if(number==null){
            return null;
        }
        if(time.endsWith("h")){
            return number*60;
        }
        if(time.endsWith("d")){
            return number*60*24;
        }
        return null;
    }

    public static String getCompareSql(String sql,String compare){
        Long minute=ClickhouseSqlUtil.getMinute(compare);
        if(minute==null){
            return null;
        }
        PlainSelect plainSelect = ClickhouseSqlUtil.getPlainSelect(sql);
        if(plainSelect==null){
            return null;
        }
        for(SelectItem si:plainSelect.getSelectItems()){
            if(si instanceof SelectExpressionItem){
                Alias alias = ((SelectExpressionItem) si).getAlias();
                if(alias==null){
                    continue;
                }
                if(TIME_COLUMN_NAME.equals(alias.getName())){
                    continue;
                }
                String newAlias=ClickhouseSqlUtil.trimAllQuotes(alias.getName());
                newAlias=String.format("\"%s\"",newAlias+"_"+compare);
                alias.setName(newAlias);
            }
        }
        SelectSqlPartFinder sspf=new SelectSqlPartFinder(plainSelect);
        sspf.parse();
        List<BinaryExpression> conditions = sspf.getConditions();
        boolean haveUpperBound1=false;
        boolean haveUpperBound2=false;
        for(BinaryExpression c:conditions){
            String colName = c.getLeftExpression().toString();
            if(!StringUtils.equals(colName,TIME_COLUMN_NAME)){
                continue;
            }
            Expression rightExpression = c.getRightExpression();
            if(rightExpression instanceof Function){
                if(((Function) rightExpression).getName().equals("subtractMinutes")) {
                    ExpressionList parameters = ((Function) rightExpression).getParameters();
                    List<Expression> expressions = parameters.getExpressions();
                    if(expressions.size()==2){
                        Expression expression = expressions.get(1);
                        if(expression instanceof LongValue){
                            long value = ((LongValue) expression).getValue();
                            value+=minute;
                            ((LongValue) expression).setValue(value);
                        }
                    }else if(expressions.size()==1){
                        LongValue longValue=new LongValue(minute);
                        expressions.add(longValue);
                    }
                    if(c instanceof MinorThan){
                        haveUpperBound1=true;
                    }
                } else if (((Function) rightExpression).getName().equals(TO_DATA_TIME)) {
                    ExpressionList parameters = ((Function) rightExpression).getParameters();
                    List<Expression> expressions = parameters.getExpressions();
                    if (expressions.size()==1) {
                        if (expressions.get(0) instanceof StringValue) {
                            StringValue expression = (StringValue) expressions.get(0);
                            String value = DateUtils.minusMinute(expression.getValue(), minute);
                            if (Objects.nonNull(value)) {
                                expression.setValue(value);
                            }
                        }
                    }
                    if (expressions.size()==2) {
                        if (expressions.get(0) instanceof LongValue) {
                            LongValue expression = (LongValue) expressions.get(0);
                            Long value = DateUtils.addMinute(expression.getValue(), (int)-minute);
                            if (Objects.nonNull(value)) {
                                expression.setValue(value);
                            }
                        }
                    }
                    if(c instanceof MinorThan){
                        haveUpperBound2=true;
                    }
                }
            }
        }

        if(haveUpperBound1 || haveUpperBound2){
            return plainSelect.toString();
        }

        addUpperBoundThan2(plainSelect, minute);

        return plainSelect.toString();
    }

    public static String addCondition2Sql(String ori , Map<String, Object> conditionMap){
        SqlPlaceholder placeholder = new SqlPlaceholder();
        String sql = ClickhouseSqlUtil.doPlaceholder(ori, placeholder);
        Statement statement = null;
        try {
            statement = parserManager.parse(new StringReader(sql));
        } catch (JSQLParserException e) {
            log.error("addCondition2SqlPara error", e);

            return null;
        }

        if (statement instanceof Select) {
            SelectBody selectBody = ((Select) statement).getSelectBody();
            if (selectBody instanceof PlainSelect) {
                ClickhouseSqlUtil.addEquals((PlainSelect) selectBody, conditionMap);
            }
        } else {
            return null;
        }

        String ret = statement.toString();

        for (Map.Entry<String, String> entry : placeholder.getMap().entrySet()) {
            ret = ret.replace(entry.getKey(), entry.getValue());
        }

        return ret;
    }

    public static void addEquals(PlainSelect plainSelect, Map<String, Object> conditionMap) {

        SelectSqlPartFinder spf = new SelectSqlPartFinder(plainSelect);
        spf.parse();

        for (Map.Entry<String, Object> entry : conditionMap.entrySet()) {
            EqualsTo equalsTo = new EqualsTo();

            Map<String, List<String>> whereTags = getWhereTags(plainSelect.getWhere());

            List<String> strings = whereTags.get(entry.getKey());
            if (CollectionUtils.isNotEmpty(strings)) {

                for (BinaryExpression b : spf.getConditions()) {
                    ClickhouseSqlUtil.searchAndSetValue(b, entry.getKey(), entry.getValue());
                }

                continue;
            }

            if (entry.getValue() instanceof String) {
                equalsTo.setRightExpression(new StringValue(entry.getValue().toString()));
            } else if (entry.getValue() instanceof Long || entry.getValue() instanceof Integer
                    || entry.getValue() instanceof Double || entry.getValue() instanceof Float) {
                equalsTo.setRightExpression(new DoubleValue(entry.getValue().toString()));
            } else {
                continue;
            }

            equalsTo.setLeftExpression(new Column(entry.getKey()));
            AndExpression and = new AndExpression(equalsTo, plainSelect.getWhere());
            plainSelect.setWhere(and);
        }
    }

    public static void addUpperBoundThan1(PlainSelect plainSelect, Long minute) {
        MinorThan upperBound=new MinorThan();
        upperBound.setLeftExpression(new Column("time"));
        Function f=new Function();
        upperBound.setRightExpression(f);
        f.setName("subtractMinutes");
        ExpressionList el=new ExpressionList();
        f.setParameters(el);
        Function now=new Function();
        now.setName("now");
        el.addExpressions(now);
        el.addExpressions(new LongValue(minute));
        AndExpression and=new AndExpression(upperBound,plainSelect.getWhere());
        plainSelect.setWhere(and);
    }

    public static void addUpperBoundThan2(PlainSelect plainSelect, Long minute) {
        MinorThan upperBound=new MinorThan();
        upperBound.setLeftExpression(new Column("time"));
        Function f=new Function();
        upperBound.setRightExpression(f);
        f.setName("toDateTime");
        ExpressionList el=new ExpressionList();
        f.setParameters(el);
        String value = DateUtils.minusMinute(DateUtils.getCurrentDate(DateUtils.formatter1), minute);
        el.addExpressions(new StringValue(value));
        AndExpression and=new AndExpression(upperBound,plainSelect.getWhere());
        plainSelect.setWhere(and);
    }


    public static Long getTimeFromCondition(BinaryExpression b){
        if(b.getRightExpression() instanceof LongValue){
            return 1000*((LongValue) b.getRightExpression()).getValue();
        }else if(b.getRightExpression() instanceof Function){
            if(StringUtils.equals("subtractMinutes",((Function) (b).getRightExpression()).getName())){
                long now = System.currentTimeMillis();
                Long number = ClickhouseSqlUtil.getNumber(b.getRightExpression().toString());
                if(number!=null){
                    return now-number*60*1000;
                }
            }
            if(StringUtils.equals("toDateTime",((Function) (b).getRightExpression()).getName())){
                String dateStr = ((Function) b.getRightExpression()).getParameters().getExpressions().get(0).toString();
                dateStr = dateStr.replace("'", "");
                Date time = DateUtils.parse(dateStr);
                if (Objects.nonNull(time)) {
                    return time.getTime();
                }
            }
        }else{
            DateTimeFormatter f=DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            try{
                TemporalAccessor parse = f.parse(((StringValue) b.getRightExpression()).toString());
                Instant instant = Instant.from( parse );
                return instant.toEpochMilli();
            }catch (Exception e){}
        }
        return null;
    }

    public static Set<String> extractTables(String sql) throws Exception {
        Statement statement = parserManager.parse(new StringReader(sql));
        TableCollector visitor = new TableCollector();
        if (statement instanceof Select) {
            SelectBody selectBody = ((Select) statement).getSelectBody();
            selectBody.accept(visitor);
        }
        return visitor.getTables();
    }

    public static class TableCollector implements SelectVisitor, FromItemVisitor {

        private final Set<String> tables = new HashSet<>();

        public Set<String> getTables() {
            return tables;
        }

        @Override
        public void visit(PlainSelect plainSelect) {
            if (plainSelect.getFromItem() != null) {
                plainSelect.getFromItem().accept(this);
            }
            if (plainSelect.getJoins() != null) {
                for (Join join : plainSelect.getJoins()) {
                    if (join.getRightItem() != null) {
                        join.getRightItem().accept(this);
                    }
                }
            }
        }

        @Override
        public void visit(SetOperationList setOpList) {
            for (SelectBody selectBody : setOpList.getSelects()) {
                selectBody.accept(this);
            }
        }

        @Override
        public void visit(WithItem withItem) {
            if (withItem.getSubSelect() != null) {
                withItem.getSubSelect().accept(this);
            }
        }

        @Override
        public void visit(ValuesStatement aThis) {

        }

        @Override
        public void visit(Table table) {
            tables.add(table.getFullyQualifiedName());
        }

        @Override
        public void visit(SubSelect subSelect) {
            if (subSelect.getSelectBody() != null) {
                subSelect.getSelectBody().accept(this);
            }
        }

        @Override
        public void visit(SubJoin subjoin) {
            if (subjoin.getLeft() != null) {
                subjoin.getLeft().accept(this);
            }
            if (subjoin.getJoinList() != null) {
                for (Join join : subjoin.getJoinList()) {
                    join.getRightItem().accept(this);
                }
            }
        }

        @Override
        public void visit(LateralSubSelect lateralSubSelect) {
            if (lateralSubSelect.getSubSelect() != null) {
                lateralSubSelect.getSubSelect().accept(this);
            }
        }

        @Override
        public void visit(ValuesList valuesList) {

        }

        @Override
        public void visit(TableFunction tableFunction) {

        }

        @Override
        public void visit(ParenthesisFromItem aThis) {
            if (aThis.getFromItem() != null) {
                aThis.getFromItem().accept(this);
            }
        }
    }


    public static Object[] toJavaArray(ClickHouseArray a){
        try {
            int length = Array.getLength(a.getArray());
            Object[] myArray  = new Object[length];
            for (int i = 0; i < length; i++) {
                myArray[i] = Array.get(a.getArray(), i);
            }
            return myArray;
        } catch (Exception throwables) {
            return new Object[0];
        }
    }
}
