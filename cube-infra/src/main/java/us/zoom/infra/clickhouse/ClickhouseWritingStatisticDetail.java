package us.zoom.infra.clickhouse;


/**
 * <AUTHOR>
 * @date 2022-03-30 10:11
 */
public class ClickhouseWritingStatisticDetail {

    private long ts;
    private long totalCount;
    private String table;

    private String queueType;

    private String flushType;
    private long costMillSec;
    private long delayFromKafkaCreateMillSec;

    private long retryCount;

    private long delayMillSec;

    private String ip;

    private String serverIp;

    private String runningEnv;

    private String service;

    private long failCount;

    private String dirtyDataSample="";

    private int dirtyRecordCount;

    private String unitTag="";

    private int taskQueueLength;

    private long totalMemorySize;

    private long kafkaRetryCount;

    private long getConnectionTime;

    private long executeConnectionTime;

    private long getSchemaTime;

    public long getGetConnectionTime() {
        return getConnectionTime;
    }

    public void setGetConnectionTime(long getConnectionTime) {
        this.getConnectionTime = getConnectionTime;
    }

    public long getExecuteConnectionTime() {
        return executeConnectionTime;
    }

    public void setExecuteConnectionTime(long executeConnectionTime) {
        this.executeConnectionTime = executeConnectionTime;
    }

    public long getGetSchemaTime() {
        return getSchemaTime;
    }

    public void setGetSchemaTime(long getSchemaTime) {
        this.getSchemaTime = getSchemaTime;
    }

    public long getKafkaRetryCount() {
        return kafkaRetryCount;
    }

    public void setKafkaRetryCount(long kafkaRetryCount) {
        this.kafkaRetryCount = kafkaRetryCount;
    }

    public long getTotalMemorySize() {
        return totalMemorySize;
    }

    public void setTotalMemorySize(long totalMemorySize) {
        this.totalMemorySize = totalMemorySize;
    }

    public long getFailCount() {
        return failCount;
    }

    public void setFailCount(long failCount) {
        this.failCount = failCount;
    }

    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getServerIp() {
        return serverIp;
    }

    public void setServerIp(String serverIp) {
        this.serverIp = serverIp;
    }

    public long getDelayMillSec() {
        return delayMillSec;
    }

    public void setDelayMillSec(long delayMillSec) {
        this.delayMillSec = delayMillSec;
    }

    public String getQueueType() {
        return queueType;
    }

    public void setQueueType(String queueType) {
        this.queueType = queueType;
    }

    public String getFlushType() {
        return flushType;
    }

    public void setFlushType(String flushType) {
        this.flushType = flushType;
    }

    public long getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(long retryCount) {
        this.retryCount = retryCount;
    }

    private String system ="clickhouse_flush_statistic_detail";

    public String getSystem() {
        return system;
    }

    public void setSystem(String system) {
        this.system = system;
    }

    public long getTs() {
        return ts;
    }

    public void setTs(long ts) {
        this.ts = ts;
    }

    public long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(long totalCount) {
        this.totalCount = totalCount;
    }

    public String getTable() {
        return table;
    }

    public void setTable(String table) {
        this.table = table;
        this.service=table.split("\\.")[0];
    }

    public long getCostMillSec() {
        return costMillSec;
    }

    public void setCostMillSec(long costMillSec) {
        this.costMillSec = costMillSec;
    }

    public long getDelayFromKafkaCreateMillSec() {
        return delayFromKafkaCreateMillSec;
    }

    public void setDelayFromKafkaCreateMillSec(long delayFromKafkaCreateMillSec) {
        this.delayFromKafkaCreateMillSec = delayFromKafkaCreateMillSec;
    }

    public String getDirtyDataSample() {
        return dirtyDataSample;
    }

    public ClickhouseWritingStatisticDetail setDirtyDataSample(String dirtyDataSample) {
        this.dirtyDataSample = dirtyDataSample;
        return this;
    }

    public int getDirtyRecordCount() {
        return dirtyRecordCount;
    }

    public ClickhouseWritingStatisticDetail setDirtyRecordCount(int dirtyRecordCount) {
        this.dirtyRecordCount = dirtyRecordCount;
        return this;
    }

    public String getUnitTag() {
        return unitTag;
    }

    public ClickhouseWritingStatisticDetail setUnitTag(String unitTag) {
        this.unitTag = unitTag;
        return this;
    }

    public String getRunningEnv() {
        return runningEnv;
    }

    public ClickhouseWritingStatisticDetail setRunningEnv(String runningEnv) {
        this.runningEnv = runningEnv;
        return this;
    }

    public int getTaskQueueLength() {
        return taskQueueLength;
    }

    public ClickhouseWritingStatisticDetail setTaskQueueLength(int taskQueueLength) {
        this.taskQueueLength = taskQueueLength;
        return this;
    }

    public static ClickhouseWritingStatisticDetail fromClickhouseWritingStatistic(ClickhouseWritingStatistic statistic){
        ClickhouseWritingStatisticDetail detail=new ClickhouseWritingStatisticDetail();
        detail.setTotalCount(statistic.getTotalCount());
        detail.setDelayFromKafkaCreateMillSec(statistic.getDelayFromKafkaCreateMillSec());
        detail.setDelayMillSec(statistic.getDelayMillSec());
        detail.setCostMillSec(statistic.getCostMillSec());
        detail.setTable(statistic.getMaxDelayTable());
        detail.setTs(statistic.getTs());
        detail.setRetryCount(statistic.getRetryCount());
        detail.setQueueType(statistic.getQueueType());
        detail.setFlushType(statistic.getFlushType());
        detail.setIp(statistic.getIp());
        detail.setServerIp(statistic.getServerIp());
        detail.setFailCount(statistic.getFailCount());
        detail.setDirtyDataSample(statistic.getDirtyDataSample());
        detail.setDirtyRecordCount(statistic.getDirtyRecordCount());
        detail.setUnitTag(statistic.getUnitTag());
        detail.setRunningEnv(statistic.getRunningEnv());
        detail.setTaskQueueLength(statistic.getTaskQueueLength());
        detail.setTotalMemorySize(statistic.getTotalMemorySize());
        detail.setKafkaRetryCount(statistic.getKafkaRetryCount());
        detail.setExecuteConnectionTime(statistic.getExecuteConnectionTime());
        detail.setGetConnectionTime(statistic.getGetConnectionTime());
        detail.setGetSchemaTime(statistic.getGetSchemaTime());
        return detail;
    }
}
