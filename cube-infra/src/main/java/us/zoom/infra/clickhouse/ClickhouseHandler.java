package us.zoom.infra.clickhouse;

import com.clickhouse.client.config.ClickHouseClientOption;
import com.clickhouse.client.config.ClickHouseDefaults;
import com.clickhouse.client.http.config.HttpConnectionProvider;
import com.clickhouse.jdbc.JdbcConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.sdk.util.JsonUtils;
import us.zoom.cube.sdk.util.MonitorLogReporter;
import us.zoom.infra.clickhouse.monitor.ChColumnDropJdbcMetric;
import us.zoom.infra.clickhouse.monitor.ChTableDropJdbcMetric;
import us.zoom.infra.enums.FieldTypeEnum;
import us.zoom.infra.enums.QueryStatus;
import us.zoom.infra.enums.StatusEnum;
import us.zoom.infra.thread.ThreadLocalStore;
import us.zoom.infra.utils.CommonSplitConstants;

import javax.sql.DataSource;
import java.sql.*;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.lang.reflect.Array;
import java.util.concurrent.CompletableFuture;

import static com.clickhouse.client.http.config.ClickHouseHttpOption.CONNECTION_PROVIDER;
import static us.zoom.infra.clickhouse.ClickhouseConst.*;
import static us.zoom.infra.utils.TimeUtils.MILLIS_SECOND_MULTIPLIER_INT;

/**
 * <AUTHOR> Wang
 * @date 2022-01-13 10:11
 */
@Slf4j
public class ClickhouseHandler {

    private Logger monitorLog = LoggerFactory.getLogger("Monitor");

    /**
     * This datasource includes all IPs.
     * And it will return \*random\* ip connection if invoke getConnection() method
     */
    private ClickHouseDataSourceProxy dataSource;

    /**
     * Every single ip mapping a single datasource in this Map, even if they're all in the same cluster.
     */
    private Map<String, ClickHouseDataSourceProxy> allDataSource = new HashMap<>();

    public static final int DEFAULT_PORT = 8123;

    public static final int DEFAULT_HTTPS_PORT = 8443;

    private static int DEFAULT_MAX_LIMIT = 0;

    private Properties properties = new Properties();

    private Random random = new Random();

    private String name;

    final static Pattern COLUMN_NAME_PATTERN = Pattern.compile("^`(.*)`(.*)");

    public ClickhouseHandler(String username, String password, String[] serverUrls) {
        this(username, password, serverUrls, false, new Properties());
    }

    public ClickhouseHandler(String username, String password, String[] serverUrls, boolean useHttps) {
        this(username, password, serverUrls, useHttps, new Properties());
    }

    public ClickhouseHandler(String username, String password, String[] serverUrls, boolean useHttps, Properties properties) {
        try {
            properties.putAll(properties);
            properties.setProperty(ClickHouseDefaults.USER.getKey(), username);
            properties.setProperty(ClickHouseDefaults.PASSWORD.getKey(), password);
            properties.setProperty(ClickHouseClientOption.SOCKET_TIMEOUT.getKey(), MILLIS_SECOND_MULTIPLIER_INT*70 + "");
            properties.setProperty(ClickHouseClientOption.LOAD_BALANCING_POLICY.getKey(), "random");
            properties.setProperty(CONNECTION_PROVIDER.getKey(), HttpConnectionProvider.APACHE_HTTP_CLIENT.name());
            properties.setProperty(ClickHouseClientOption.CHECK_ALL_NODES.getKey(), "true");
            properties.setProperty(JdbcConfig.PROP_NULL_AS_DEFAULT, "1");

            List<String> urlList = new LinkedList<>();
            for (String serverUrl : serverUrls) {
                String urlPort = formatIpWithPort(serverUrl, useHttps);
                String singleUrl = createJdbcUrlWithScheme(urlPort, useHttps);
                urlList.add(urlPort);
                ClickHouseDataSourceProxy clickHouseDataSourceProxy = new ClickHouseDataSourceProxy(singleUrl, properties);
                allDataSource.put(clickHouseDataSourceProxy.getUrl(), clickHouseDataSourceProxy);
            }
            String urls = createJdbcUrlWithScheme(String.join(",",urlList), useHttps);
            dataSource = new ClickHouseDataSourceProxy(urls, properties);
        } catch (SQLException e) {
            log.error("create clickhouse datasource {} error", Arrays.toString(serverUrls), e);
        }
    }

    public Properties getProperties() {
        return properties;
    }

    public ClickhouseHandler setProperties(Properties properties) {
        this.properties = properties;
        return this;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public ClickHouseDataSourceProxy getDataSource(){
        return dataSource;
    }

    public void createDbOnCluster(String dbName){
        List<ClickHouseDataSourceProxy> enableDataSources = getEnableDataSources();
        for(ClickHouseDataSourceProxy dataSource:enableDataSources) {
            try (Connection conn = dataSource.getConnection()) {
                Statement stmt = conn.createStatement();
                stmt.executeUpdate(String.format("create database IF NOT EXISTS %s;", dbName));
            } catch (Exception e) {
                log.error("Create Clickhouse database {} in {} error", dbName, dataSource.getUrl(), e);
            }
        }
    }

    public List<ClickHouseDataSourceProxy> getEnableDataSources() {
        return allDataSource.entrySet().stream().filter(u -> getDataSource().getEnabledClickHouseUrls().contains(u.getKey())).map(u -> u.getValue()).collect(Collectors.toList());

//        return allDataSource.entrySet().stream().filter(u -> getDataSource().getConnection().getConfig()..contains(u.getKey())).map(u -> u.getValue()).collect(Collectors.toList());
    }

    public Map<String, ClickHouseDataSourceProxy> getAllDataSource() {
        return allDataSource;
    }

    public boolean createTableOnCluster(String dbName, String tableName, String localTableName
            , Map<String, FieldTypeEnum> colDef, int ttl,Set<String> piiFields, boolean batchTable){
        // Optimization 1: Batch create local tables and distributed tables
        createLocalTableBatch(dbName, localTableName, colDef, ttl, batchTable);
        createDistributeTableBatch(dbName, tableName);
        createDesensitizedTableBatch(dbName, tableName, colDef, piiFields);
        return true;
    }

    /**
     * Batch create local table to reduce database connection overhead
     */
    public void createLocalTableBatch(String dbName, String tableName, Map<String, FieldTypeEnum> colDef, int ttl, boolean batchTable) {
        String partitionDayOffset=String.format(" + toIntervalDay(%d)",random.nextInt(7));
        String sqlFormat;
        
        if(batchTable){
            sqlFormat="create table if not exists %1$s.%2$s  (time DateTime,%3$s" +
                    ")ENGINE =ReplicatedMergeTree('/clickhouse/tables/{shard}/%1$s/%2$s.%5$s', '{replica}')\n" +
                    "PARTITION BY toYYYYMMDD(time)\n" +
                    "ORDER BY (`time`) \n" +
                    "TTL time + toIntervalYear(%4$d) SETTINGS ttl_only_drop_parts = 1;";
        }else{
            sqlFormat="create table if not exists %1$s.%2$s  (time DateTime,%3$s" +
                    ")ENGINE =ReplicatedMergeTree('/clickhouse/tables/{shard}/%1$s/%2$s.%5$s', '{replica}')\n" +
                    "PARTITION BY toMonday(time %6$s)\n" +
                    "ORDER BY (`time`) \n" +
                    "TTL time + INTERVAL %4$d DAY SETTINGS ttl_only_drop_parts = 1;";
        }

        String cols = colDef.entrySet().stream().map(u -> {
            String type = FieldTypeEnum.toClickhouseType(u.getValue());
            return String.format("`%s` %s",u.getKey(),type);
        }).collect(Collectors.joining(","));
        
        String sql;
        if(batchTable){
            sql=String.format(sqlFormat,dbName,tableName,cols, ttl
                    ,UUID.randomUUID().toString().replace("-",""));
        }else{
            sql=String.format(sqlFormat,dbName,tableName,cols,ttl
                    ,UUID.randomUUID().toString().replace("-",""),partitionDayOffset);
        }
        
        // Optimization 2: Use batch execution to reduce connection overhead
        // For table creation, we want to know if any operation failed
        executeBatchOnAllDataSources(sql, "Create Clickhouse table", true);
    }

    /**
     * Batch create distributed table
     */
    public void createDistributeTableBatch(String dbName, String tableName){
        String sqlFormat="CREATE TABLE if not exists %1$s.%2$s AS %1$s.%3$s \n" +
                "ENGINE = Distributed('{cluster}', %1$s, %3$s,rand() )  ;";
        String sql=String.format(sqlFormat,dbName,tableName, ClickhouseSqlUtil.getLocalTableNaming(tableName));
        
        // Use batch execution for distributed table creation
        executeBatchOnAllDataSources(sql, "Create Clickhouse distributed table", false);
    }

    /**
     * Batch create desensitized table
     */
    public void createDesensitizedTableBatch(String dbName, String tableName, Map<String, FieldTypeEnum> colDef,Set<String> piiFields){
        if(piiFields.size()==0){
            return;
        }
        String sqlFormat="CREATE OR REPLACE VIEW %1$s.%2$s_click_desensitized  as \n" +
                "select  `time`,%3$s from %1$s.%2$s;";
        List<String>fields=new ArrayList<>();
        for(Map.Entry<String,FieldTypeEnum>en:colDef.entrySet()){
            if(piiFields.contains(en.getKey())){
                if( FieldTypeEnum.int32.equals(en.getValue()) ||  FieldTypeEnum.number.equals(en.getValue())){
                    fields.add(String.format("0 as `%s`",en.getKey()));
                }else if( FieldTypeEnum.string.equals(en.getValue())){
                    fields.add(String.format("'*' as `%s`",en.getKey()));
                }
            }else{
                fields.add(String.format("`%s`",en.getKey()));
            }
        }
        String sql=String.format(sqlFormat,dbName,tableName,String.join(",",fields));
        
        // Use batch execution for desensitized table creation
        executeBatchOnAllDataSources(sql, "Create Clickhouse desensitized table", false);
    }

    /**
     * Batch execute SQL on all data sources with error propagation control
     * @param sql SQL to execute
     * @param operationName Name of the operation for logging
     * @param throwOnError Whether to throw exception if any operation fails
     */
    public void executeBatchOnAllDataSources(String sql, String operationName, boolean throwOnError) {
        List<ClickHouseDataSourceProxy> enableDataSources = getEnableDataSources();
        // Optimization 3: Use CompletableFuture for concurrent execution
        List<CompletableFuture<Boolean>> futures = enableDataSources.stream()
                .map(dataSource -> CompletableFuture.supplyAsync(() -> {
                    try (Connection conn = dataSource.getConnection()) {
                        Statement stmt = conn.createStatement();
                        stmt.executeUpdate(sql);
                        return true; // Success
                    } catch (Exception e) {
                        log.error("{} failed on {} with SQL: {}. Error: {}",
                                operationName, dataSource.getUrl(), sql, e.getMessage(), e);
                        return false; // Failure
                    }
                }))
                .collect(Collectors.toList());
        
        // Wait for all operations to complete and collect results
        List<Boolean> results = futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());
        
        // Check if any operation failed
        boolean allSucceeded = results.stream().allMatch(Boolean::booleanValue);
        
        if (!allSucceeded) {
            long failureCount = results.stream().filter(result -> !result).count();
            log.error("{} failed on {} out of {} data sources", operationName, failureCount, enableDataSources.size());
            
            // Only throw exception for critical operations (like table creation)
            // For non-critical operations (like distributed table creation), just log the error
            if (throwOnError && operationName.contains("Create Clickhouse table")) {
                throw new RuntimeException(String.format("%s failed on %d out of %d data sources", 
                        operationName, failureCount, enableDataSources.size()));
            }
        } else {
            log.debug("{} succeeded on all {} data sources", operationName, enableDataSources.size());
        }
    }

    public void createBufferTable(String dbName, String tableName,String localName, String conf){
        String sqlFormat="CREATE TABLE %1$s.%2$s AS %1$s.%3$s ENGINE = Buffer(%1$s, %3$s, %4$s);";

        String sql=String.format(sqlFormat,dbName,tableName,localName,conf);

        for(ClickHouseDataSourceProxy dataSource:getEnableDataSources()) {
            try (Connection conn = dataSource.getConnection()) {
                Statement stmt = conn.createStatement();
                stmt.executeUpdate(sql);
            } catch (Exception e) {
                log.error("Create Clickhouse buffer table {} in {} error", sql, dataSource.getUrl(), e);
            }
        }
    }

    public void executeOnInstance(String sql, ClickHouseDataSourceProxy dataSource){
        if(dataSource==null){
            log.error("Try to execute SQL {} error, because datasource is null", sql );
            return;
        }
        try (Connection conn = dataSource.getConnection()) {
            Statement stmt = conn.createStatement();
            stmt.executeUpdate(sql);
        } catch (Exception e) {
            log.error("Execute SQL {} in {} error", sql, dataSource.getUrl(), e);
        }
    }


    public boolean dropTableOnCluster(String dbName, String tableName){
        if(!dropTable(dbName, ClickhouseSqlUtil.getLocalTableNaming(tableName) )){
            return false;
        }
        return dropTable(dbName, tableName);
    }

    public boolean dropTable(String dbName, String tableName){
        try (Connection conn = getDataSource().getConnection()) {
            Statement stmt = conn.createStatement();
            stmt.executeUpdate(String.format("drop table %s.%s ON CLUSTER '{cluster}'",dbName,tableName));
            return true;
        }catch (Exception e) {
            log.error("Drop table {}.{} error",dbName,tableName, e);
            return false;
        }
    }

    public void dropTableAndRelated(String dbName, String tableName) {
        LocalDateTime startTime = LocalDateTime.now();
        boolean noException = true;
        String sql = String.format("DROP TABLE IF EXISTS %s.%s", dbName, tableName);
        for(ClickHouseDataSourceProxy dataSource:getEnableDataSources()) {
            try (Connection conn = dataSource.getConnection();
                 Statement stmt = conn.createStatement()) {
                stmt.executeUpdate(sql);
            } catch (Exception e) {
                noException = false;
                log.error("Error when dropping Clickhouse useless table,sql: {} in {} ", sql, dataSource.getUrl(), e);
            }
        }
        MonitorLogReporter.report(monitorLog, ChTableDropJdbcMetric.builder().db(dbName).table(tableName)
                .status(noException ? StatusEnum.SUCCESS.getStatus() : StatusEnum.FAIL.getStatus())
                .costMs(Duration.between(startTime, LocalDateTime.now()).toMillis()).build());
    }


    public void addColumnOnInstance(String dbName, String tableName, String fieldName, FieldTypeEnum type,String url){
        String typeString=FieldTypeEnum.toClickhouseType(type);
        String format="ALTER TABLE %s.%s  ADD COLUMN IF NOT EXISTS `%s` %s;";
        String sql=String.format(format,dbName,tableName,fieldName,typeString);
        executeOnInstance(sql,allDataSource.get(url));
    }

    public void dropColumnOnInstance(String dbName, String tableName, String fieldName, String url) {
        String sql = String.format("ALTER TABLE %s.%s DROP COLUMN IF EXISTS `%s`; ", dbName, tableName, fieldName);
        executeOnInstance(sql,allDataSource.get(url));
    }

    private static final String ALTER_TABLE_SQL_PREFIX_TMP = "ALTER TABLE `%s`.`%s` %s ;";
    private static final String DROP_COLUMN_TMP = "DROP COLUMN IF EXISTS `%s`";

    public void dropColumn(String dbName, String tableName, String... fieldNames) {
        if(fieldNames.length == 0) {
            return;
        }
        LocalDateTime startTime = LocalDateTime.now();
        boolean noException = true;
        String dropColumnSqlSnippet = Arrays.stream(fieldNames)
                .map(fieldName -> String.format(DROP_COLUMN_TMP, fieldName))
                .collect(Collectors.joining(CommonSplitConstants.COMMA_SPLIT));
        String sql = String.format(ALTER_TABLE_SQL_PREFIX_TMP, dbName, tableName, dropColumnSqlSnippet);
        for(ClickHouseDataSourceProxy dataSource:getEnableDataSources()) {
            try (Connection conn = dataSource.getConnection();
                Statement stmt = conn.createStatement()) {
                stmt.executeUpdate(sql);
            } catch (Exception e) {
                noException = false;
                log.error("Error when dropping Clickhouse useless column,sql: {} in {} ", sql, dataSource.getUrl(), e);
            }
        }
        MonitorLogReporter.report(monitorLog, ChColumnDropJdbcMetric.builder().db(dbName).table(tableName)
                .status(noException ? StatusEnum.SUCCESS.getStatus() : StatusEnum.FAIL.getStatus())
                .columnSnippet(dropColumnSqlSnippet).costMs(Duration.between(startTime, LocalDateTime.now()).toMillis()).build());
    }

    public Map<String,String>showCreateTables(String dbName, String tableName, boolean logError){
        List<ClickHouseDataSourceProxy> enableDataSources = getEnableDataSources();
        Map<String,String>ret=new HashMap<>();
        for(ClickHouseDataSourceProxy ds : enableDataSources){
            try (Connection conn = ds.getConnection()) {
                Statement stmt = conn.createStatement();
                ResultSet resultSet = stmt.executeQuery(String.format("show create table %s.%s;",dbName,tableName));
                boolean next = resultSet.next();
                String sql = resultSet.getString(1);
                ret.put(ds.getUrl(),sql);
            } catch (Exception e) {
                if(logError) {
                    log.error("show {}.{} create sql error.", dbName, tableName, e);
                }
            }
        }
        return ret;
    }


    public Map<String,ClickhouseSchema> getTableSchema(String dbName, String tableName){
        Map<String,String> sqls = showCreateTables(dbName, tableName,false);
        Map<String,ClickhouseSchema> ret=new HashMap<>();
        if(CollectionUtils.isEmpty(sqls)){
            log.warn("{}.{} table does not exist at all nodes.", dbName, tableName);
            return ret;
        }
        for(Map.Entry<String,String> en:sqls.entrySet()) {
            String sql=en.getValue();
            ClickhouseSchema schema = parseCreateSql(dbName, tableName, sql);
            ret.put(en.getKey(),schema);
        }
        return ret;
    }

    public static Integer extractTTLDays(String s) {
        Pattern pattern = Pattern.compile("\\((\\d+)\\)");
        Matcher matcher = pattern.matcher(s);
        Integer result = null;
        while (matcher.find()) {
            result = Integer.parseInt(matcher.group(1));
        }
        return result;
    }

    public static ClickhouseSchema parseCreateSql(String dbName, String tableName, String sql) {
        ClickhouseSchema schema=new ClickhouseSchema();
        schema.setDbName(dbName);
        schema.setTableName(tableName);
        schema.setRawSql(sql);
        String columnString = StringUtils.substringBetween(sql, "(", ")\nENGINE");

        List<String> sortingkey = new LinkedList<>();
        for (String s : sql.split("\n")) {
            if (s.startsWith("ORDER BY")) {
                for (String sort : s.replace("ORDER BY", "").replaceAll("[()]", "").split(",")) {
                    sortingkey.add(sort.trim());
                }
            }
            if (s.startsWith("TTL")) {
                schema.setTtl(extractTTLDays(s));
            }
            if (s.startsWith("ENGINE = Distributed")) {
                String[] split = StringUtils.substringBetween(s, "(", ")").split(",");
                String localNameString=split[2].trim();
                schema.setLocalTable(localNameString.substring(1,localNameString.length()-1));
            }
            if(s.startsWith("ENGINE = ReplicatedReplacingMergeTree") || s.startsWith("ENGINE = ReplicatedMergeTree")){
                String[] split = StringUtils.substringBetween(s, "(", ")").split(",");
                schema.setZkPath(split[0].substring(1,split[0].length()-1));
            }
        }
        schema.setSortingKey(sortingkey);
        if(columnString==null){
            return schema;
        }
        for (String c : columnString.split(",\n")) {
            c = c.trim();
            if (!c.startsWith("`")) {
                continue;
            }
            try {
                Matcher matcher = COLUMN_NAME_PATTERN.matcher(c);
                if(matcher.matches()) {
                    String colName = matcher.group(1).trim();
                    String colType = matcher.group(2).trim();
                    Object defaultValue = SqlDefaultValueExtractor.extractDefaultValue(colType);
                    boolean isMap = colType.startsWith("Map");
                    if(colType.contains(" ") && !isMap){
                        colType = colType.split(" ")[0];
                    }
                    if(LOW_CARDINALITY_STRING.equals(colType) || NULLABLE_STRING.equals(colType)){
                        colType= FieldTypeEnum.string.name();
                    }else if(ARRAY_UINT_64.equals(colType) || ARRAY_UINT_8.equals(colType)){
                        colType=FieldTypeEnum.histogram.name();
                    }else if(ARRAY_FLOAT_64.equals(colType)){
                        colType=FieldTypeEnum.summary.name();
                    }else if(ARRAY_STRING.equals(colType)){
                        colType=FieldTypeEnum.summary.name();
                    }else if(MAP_STRING.equals(colType)){
                        colType=FieldTypeEnum.mapString.name();
                    }else if(MAP_FLOAT.equals(colType)){
                        colType=FieldTypeEnum.mapNumber.name();
                    }else if(MAP_BOOL.equals(colType)){
                        colType=FieldTypeEnum.mapBool.name();
                    }else if(INT32.equals(colType)||NULLABLE_INT32.equals(colType)){
                        colType=FieldTypeEnum.int32.name();
                    }else if(INT64.equals(colType)||NULLABLE_INT64.equals(colType)){
                        colType=FieldTypeEnum.int64.name();
                    }else if(DATETIME.equals(colType)){
                        colType=FieldTypeEnum.datetime.name();
                    }else if(colType.startsWith(DATETIME_64)){
                        colType=FieldTypeEnum.datetime64.name();
                    }else if(NULLABLE_DATETIME.equals(colType)){
                        colType=FieldTypeEnum.nullable_datetime.name();
                    }else if(NULLABLE_DATETIME_64.equals(colType)){
                        colType=FieldTypeEnum.nullable_datetime.name();
                    }else if(BOOL.equals(colType)){
                        colType=FieldTypeEnum.bool.name();
                    }else if(colType.startsWith(ENUM_8)){
                        colType=FieldTypeEnum.enum8.name();
                    }
                    schema.addCol(colName, FieldTypeEnum.getIngoreCase(colType, FieldTypeEnum.number));

                    schema.addDefaultValue(colName, defaultValue);
                }else {
                    log.error("Parse {} from {}.{} encouter error", c, dbName, tableName);
                }
            } catch (Exception e) {
                log.error("Parse {} from {}.{} encouter error", c, dbName, tableName);
            }
        }
        return schema;
    }

    public List<Map<String,Object>> query(String sql){
        return query(sql,null);
    }

    public List<Map<String,Object>> query(String sql,QueryMonitor qb){
        List<Map<String,Object>> list = new ArrayList<>();
        ClickHouseDataSourceProxy dataSource = getDataSource();
        try (Connection conn = dataSource.getConnection()) {
            Statement stmt = conn.createStatement();
            stmt.setMaxRows(DEFAULT_MAX_LIMIT);
            ResultSet rs = stmt.executeQuery(sql);
            ResultSetMetaData md = rs.getMetaData();
            int columns = md.getColumnCount();
            while (rs.next()){
                HashMap<String,Object> row = new LinkedHashMap<>(columns);
                for(int i=1; i<=columns; ++i) {
                    row.put(md.getColumnName(i), typeTransfer(rs.getObject(i)));
                }
                list.add(row);
            }
        } catch (Exception e) {
            log.error("user {} Query sql {} in Clickhouse database {} error", ThreadLocalStore.getUserNameLocal(), sql,dataSource.getEnabledClickHouseUrls(), e);
            if(qb!=null){
                qb.setStatus(QueryStatus.QUERY_FAIL.name());
                qb.setMsg(e.getMessage());
            }
        }
        return list;
    }

    public OriginResultData queryOri(String sql, QueryMonitor qb) throws SQLException {
        OriginResultData result = new OriginResultData();
        List<Map<String,Object>> meta = new ArrayList<>();
        List<Map<String,Object>> data = new ArrayList<>();
        DataSource dataSource = getDataSource();
        Connection conn = dataSource.getConnection();
        Statement stmt = conn.createStatement();
        stmt.setMaxRows(DEFAULT_MAX_LIMIT);
        ResultSet rs = stmt.executeQuery(sql);
        ResultSetMetaData md = rs.getMetaData();
        int columns = md.getColumnCount();
        for (int j = 1; j<= columns;j++){
            HashMap<String,Object> metaColumnsTypeMap = new LinkedHashMap<>(2);
            metaColumnsTypeMap.put("name",md.getColumnName(j));
            metaColumnsTypeMap.put("type",md.getColumnTypeName(j));
            meta.add(metaColumnsTypeMap);
        }
        while (rs.next()){
            HashMap<String,Object> row = new LinkedHashMap<>(columns);
            for(int i=1; i<=columns; ++i) {
                row.put(md.getColumnName(i), typeTransfer(rs.getObject(i)));
            }
            data.add(row);
        }
        result.setMeta(meta);
        result.setData(data);
        result.setRows(data.size());
        return result;
    }

    private Object typeTransfer(Object input) {
        if (Objects.isNull(input)) {
            return null;
        }
        if (input instanceof LocalDateTime) {
            return Timestamp.valueOf((LocalDateTime)input);
        }

        if (input instanceof OffsetDateTime) {
            return Timestamp.from(((OffsetDateTime) input).toInstant());
        }

        if (input instanceof ZonedDateTime) {
            return Timestamp.from(((ZonedDateTime) input).toInstant());
        }

        if (input instanceof Map<?, ?> mapInput) {
            Map<String, Object> converted = new LinkedHashMap<>();
            for (Map.Entry<?, ?> entry : mapInput.entrySet()) {
                Object key = entry.getKey();
                Object value = entry.getValue();
                if (key != null) {
                    converted.put(key.toString(), typeTransfer(value));
                }
            }
            return converted;
        }

        if (input.getClass().isArray()) {
            int len = Array.getLength(input);
            List<Object> list = new ArrayList<>(len);
            for (int i = 0; i < len; i++) {
                Object element = Array.get(input, i);
                list.add(typeTransfer(element));
            }
            return list;
        }

        return input;
    }

    private String createJdbcUrlWithScheme(String ip, boolean useHttps) {
        if (useHttps) {
            return String.format("jdbc:clickhouse:https://%s", ip);
        } else {
            return String.format("jdbc:clickhouse://%s", ip);
        }
    }

    private String formatIpWithPort(String ip, boolean useHttps) {
        if (useHttps) {
            return String.format("%s:%d", ip,DEFAULT_HTTPS_PORT);
        } else {
            return String.format("%s:%d", ip, DEFAULT_PORT);
        }
    }

    public LocalDateTime queryModificationTimeOnRandomInstance(String dbName, String tableName) {
        String sqlFormat = "SELECT `metadata_modification_time` FROM system.tables where `database`='%s' and `name`='%s' LIMIT 1";
        String sql = String.format(sqlFormat, dbName, tableName);

        try (Connection randomIpConnection = this.dataSource.getConnection();
            Statement stmt = randomIpConnection.createStatement();
            ResultSet resultSet = stmt.executeQuery(sql)) {
            while (resultSet.next()) {
                LocalDateTime modificationTime = (LocalDateTime) resultSet.getObject(1);
                return modificationTime;
            }
        } catch (Exception e) {
            log.error("Execute SQL {} in {} error", sql, this.dataSource.getUrl(), e);
        }
        return null;
    }

    public Map<String, LocalDateTime> queryModificationTimeOnRandomInstance(String dbName) {
        String sqlFormat = "SELECT `name`, `metadata_modification_time` FROM system.tables where `database`='%s'";
        String sql = String.format(sqlFormat, dbName);

        Map<String, LocalDateTime> tableAndModTimeMap = new HashMap<>();
        try (Connection randomIpConnection = this.dataSource.getConnection();
             Statement stmt = randomIpConnection.createStatement();
             ResultSet resultSet = stmt.executeQuery(sql)) {
            while (resultSet.next()) {
                String tableName = resultSet.getString(1);
                LocalDateTime modificationTime = (LocalDateTime) resultSet.getObject(2);
                tableAndModTimeMap.put(tableName, modificationTime);
            }
        } catch (Exception e) {
            log.error("Execute SQL {} in {} error", sql, this.dataSource.getUrl(), e);
        }
        return tableAndModTimeMap;
    }

    public static void setQueryLimit(int limit) {
        if (limit < 0 || limit == DEFAULT_MAX_LIMIT) {
            return;
        }
        DEFAULT_MAX_LIMIT = limit;
    }

}
