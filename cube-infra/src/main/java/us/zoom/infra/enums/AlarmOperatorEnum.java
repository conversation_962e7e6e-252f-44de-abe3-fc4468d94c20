package us.zoom.infra.enums;

import lombok.Getter;
import org.springframework.util.Assert;
import us.zoom.cube.lib.integrations.MetricsFieldTypeEnum;

import java.util.Arrays;
import java.util.Objects;

public enum AlarmOperatorEnum {
    /**
     *
     */
    ne(MetricsFieldTypeEnum.number, "!="),
    eq(MetricsFieldTypeEnum.number, "=="),
    lt(MetricsFieldTypeEnum.number, "<"),
    lte(MetricsFieldTypeEnum.number, "<="),
    gt(MetricsFieldTypeEnum.number, ">"),
    gte(MetricsFieldTypeEnum.number, ">="),
    /**
     *
     */
    crIncrease(MetricsFieldTypeEnum.number, "crIncrease"),
    /**
     *
     */
    crDecrease(MetricsFieldTypeEnum.number, "crDecrease"),

    IN(MetricsFieldTypeEnum.string, "in"),
    NOT_IN(MetricsFieldTypeEnum.string, "not-in"),
    ;

    private MetricsFieldTypeEnum metricsFieldType;

    @Getter
    private String operation;

    AlarmOperatorEnum(MetricsFieldTypeEnum metricsFieldType, String operation) {
        this.metricsFieldType = metricsFieldType;
        this.operation = operation;
    }

    public static boolean containsOperation(String operation, MetricsFieldTypeEnum metricsFieldType) {
        Assert.notNull(metricsFieldType, "Can't find any matched field type!");
        if (Objects.equals(metricsFieldType, MetricsFieldTypeEnum.histogram)
                || Objects.equals(metricsFieldType, MetricsFieldTypeEnum.mapNumber)) {
            metricsFieldType = MetricsFieldTypeEnum.number;
        } else if (Objects.equals(metricsFieldType, MetricsFieldTypeEnum.mapString)) {
            metricsFieldType = MetricsFieldTypeEnum.string;
        }
        final MetricsFieldTypeEnum finalMetricsFieldType = metricsFieldType;
        return Arrays
                .stream(values())
                .anyMatch(op -> op.metricsFieldType == finalMetricsFieldType && op.operation.equals(operation));
    }


}
