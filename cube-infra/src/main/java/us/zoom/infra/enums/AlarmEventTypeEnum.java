package us.zoom.infra.enums;


/**
 *
 * @author: canyon.li
 * @date: 2023/05/29
 **/
public enum AlarmEventTypeEnum {

    /**
     * alarm record acknowledged by user confirm
     */
    user_acknowledged(1),

    /**
     * alarm record acknowledged by user confirm
     */
    user_acknowledged_done(2),

    /**
     * alarm record acknowledged by user confirm
     */
    user_resolved(3),

    /**
     * alarm record acknowledged by user confirm
     */
    user_resolved_done(4),

    /**
     * alarm record acknowledged by user confirm
     */
    sys_pending(5),

    /**
     * alarm record acknowledged by user confirm
     */
    sys_alarming(6),

    /**
     * alarm record acknowledged by user confirm
     */
    sys_triggered(7),

    /**
     * alarm record acknowledged by user confirm
     */
    sys_suppressed(8),

    /**
     * alarm record acknowledged by user confirm
     */
    sys_acknowledged(9),

    /**
     * alarm record acknowledged by user confirm
     */
    sys_resolved(10),

    sys_suppressed_by_acknowledged(11),

    sys_suppressed_by_triggered(12),

    sys_silenced(13),

    sys_suppressed_by_rate_limit(14),

    sys_suppressed_by_not_get_notice_permission(15);

    private int code;

    public int getValue() {
        return code;
    }

    AlarmEventTypeEnum(int code) {
        this.code = code;
    }

    public static AlarmEventTypeEnum fromValue(int code) {
        for (AlarmEventTypeEnum type : AlarmEventTypeEnum.values()) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }
}
