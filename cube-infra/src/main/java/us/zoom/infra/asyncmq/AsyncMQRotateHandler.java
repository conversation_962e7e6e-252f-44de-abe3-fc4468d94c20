package us.zoom.infra.asyncmq;

import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import us.zoom.cloud.secrets.rotate.handler.CurrentVersionKVSecretHandler;
import us.zoom.cloud.secrets.rotate.handler.RotateHandlerRegister;
import us.zoom.cloud.secrets.rotate.vo.Status;
import us.zoom.cloud.secrets.vo.KVSecret;
import us.zoom.infra.utils.Instance;
import us.zoom.mq.client.AsyncMQ;
import us.zoom.mq.client.exception.AsyncMQClientException;

import jakarta.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;

@Service
public class AsyncMQRotateHandler {
    private final static Logger LOG = LoggerFactory.getLogger(AsyncMQRotateHandler.class);

    @Value("${asyncmq.csms.key.listen.list:}")
    private String csmsKeyListenList;

    private static Map<String, List<MqAndUserName>> mqListMap = new HashMap();

    public static synchronized void addMQListener(AsyncMQ mq, String appKeyName, String userName) {
        List<MqAndUserName> asyncMQS = mqListMap.get(appKeyName);
        if (CollectionUtils.isEmpty(asyncMQS)) {
            asyncMQS = new ArrayList<>();
        }
        MqAndUserName mun = new MqAndUserName(mq, userName);

        asyncMQS.add(mun);
        mqListMap.put(appKeyName, asyncMQS);
    }

    @PostConstruct
    public void registerRotateHandler() {
        if (StringUtils.isBlank(csmsKeyListenList)) {
            LOG.error("AsyncMQRotateHandler, csmsKeyListenList is empty");

            return;
        }

        Arrays.stream(csmsKeyListenList.split(",")).forEach(key -> {
            if (StringUtils.isBlank(key)) {
                return;
            }
            RotateHandlerRegister.registerCurrentVersionHandler(Lists.newArrayList(key), new CurrentVersionKVSecretHandler() {
                @Override
                public Map<String, Status> preVerify(Map<String, KVSecret> kvSecretMap) {
                    if (MapUtils.isEmpty(kvSecretMap) || !kvSecretMap.containsKey(key)) {
                        LOG.error("mq rotate key map is empty or the map not contains key");
                        return Status.fail(kvSecretMap, "mq rotate key map is empty or the map not contains specified key");
                    }
                    KVSecret secret = kvSecretMap.get(key);

                    List<MqAndUserName> asyncMQS = mqListMap.get(key);
                    AtomicBoolean error = new AtomicBoolean(false);
                    Instance.ofNullable(asyncMQS).stream().forEach(mun -> {
                        try {
                            if (mun.getMq().verifyCredential(mun.getUserName(), secret.getValue())) {
                                LOG.info("mq preVerify success");
                            }
                        } catch (AsyncMQClientException e) {
                            LOG.error("mq verify key failed");
                            error.set(true);

                        }
                    });

                    if (error.get()) {
                        return Status.fail(kvSecretMap, "mq preVerify failed");
                    }

                    return Status.success(kvSecretMap);
                }

                @Override
                public Map<String, Status> handle(Map<String, KVSecret> kvSecretMap) {
                    if (MapUtils.isEmpty(kvSecretMap) || !kvSecretMap.containsKey(key)) {
                        LOG.error("mq rotate key map is empty or the map not contains key");
                        return Status.fail(kvSecretMap, "mq rotate key map is empty or the map not contains specified key");
                    }
                    KVSecret secret = kvSecretMap.get(key);

                    List<MqAndUserName> asyncMQS = mqListMap.get(key);
                    AtomicBoolean error = new AtomicBoolean(false);
                    Instance.ofNullable(asyncMQS).stream().forEach(mun -> {
                        try {

                            if (mun.getMq().updatePassword(secret.getValue())) {
                                LOG.info("mq handle success");
                            }
                        } catch (AsyncMQClientException e) {
                            LOG.error("mq update key failed");
                            error.set(true);
                        }
                    });

                    if (error.get()) {
                        return Status.fail(kvSecretMap, "mq handler failed");
                    }

                    return Status.success(kvSecretMap);
                }
            });
        });


    }


    private static class MqAndUserName {
        private AsyncMQ mq;
        private String userName;

        public AsyncMQ getMq() {
            return mq;
        }

        public void setMq(AsyncMQ mq) {
            this.mq = mq;
        }

        public String getUserName() {
            return userName;
        }

        public void setUserName(String userName) {
            this.userName = userName;
        }

        public MqAndUserName(AsyncMQ mq, String userName) {
            this.mq = mq;
            this.userName = userName;
        }
    }
}