package us.zoom.infra.dao.service;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;
import us.zoom.infra.dao.model.OutageActionDO;
import us.zoom.infra.dao.model.OutageAlarmDO;
import us.zoom.infra.dao.model.OutageCommentDO;
import us.zoom.infra.dao.model.OutageDO;
import us.zoom.infra.dao.model.OutageEventDO;
import us.zoom.infra.dao.model.OutageLinkDO;
import us.zoom.infra.dao.model.OutageOwnerDO;

import java.io.Serializable;
import java.util.List;

@Mapper
@Repository
public interface OutageDAO extends Serializable {

    @Select(value = "<script>" +
            " select * from outage" +
            " where  is_deleted = 0 " +
            " <if test=\"title != null and title != ''\"> and title LIKE CONCAT('%', #{title}, '%')</if>" +
            " <if test=\"level !=null and level !=''\"> and   level = #{level}</if>" +
            " <if test=\"status !=null and status !=''\"> and status = #{status}</if>" +
            " <if test=\"type !=null and type !=''\"> and type = #{type}</if>" +
            " <if test=\"startTime != null\"> and create_time &gt;= #{startTime}</if>" +
            " <if test=\"endTime != null\"> and create_time &lt;= #{endTime}</if>" +
            " order by create_time desc limit #{pageIndex},#{pageSize}" +
            "</script>")
    List<OutageDO> searchOutageList(@Param("title") String title, @Param("level") String level, @Param("status") String status, @Param("type") String type, @Param("startTime") String startTime,@Param("endTime") String endTime,  @Param("pageIndex") int pageIndex, @Param("pageSize") int pageSize);


    @Select(value = "<script>" +
            " select count(id) from outage" +
            " where  is_deleted = 0 " +
            " <if test=\"title != null and title != ''\"> and title LIKE CONCAT('%', #{title}, '%')</if>" +
            " <if test=\"level !=null and level !=''\"> and   level = #{level}</if>" +
            " <if test=\"status !=null and status !=''\"> and status = #{status}</if>" +
            " <if test=\"type !=null and type !=''\"> and type = #{type}</if>" +
            " <if test=\"startTime != null\"> and create_time &gt;= #{startTime}</if>" +
            " <if test=\"endTime != null\"> and create_time &lt;= #{endTime}</if>" +
            "</script>")
    Integer getOutageListCount(@Param("title") String title, @Param("level") String level, @Param("status") String status, @Param("type") String type, @Param("startTime") String startTime,@Param("endTime") String endTime,@Param("pageIndex") int pageIndex, @Param("pageSize") int pageSize);


    @Insert("Insert into outage(id,title,level,type,creator,last_modified_by,summary,customer_impact,do_well,do_poorly,root_cause,five_why,edit_source)values(#{id},#{title},#{level},#{type},#{creator},#{lastModifiedBy},#{summary},#{customerImpact},#{doWell},#{doPoorly},#{rootCause},#{fiveWhy},#{editSource})")
    void add(OutageDO outageDO);


    @Insert("INSERT INTO outage (" +
            "id, title, level, type, creator, last_modified_by, summary, customer_impact, do_well, do_poorly, " +
            "root_cause, five_why, start_time, detect_time, process_time, recovery_time, edit_source, service_ids, " +
            "outage_handler, impacted_service, customer_impact_flag, detect_desc, detect_channel, recovery_mode, " +
            "cover_by_ta, early_detected_by,rca_wiki_url" +
            ") VALUES (" +
            "#{id}, #{title}, #{level}, #{type}, #{creator}, #{lastModifiedBy}, #{summary}, #{customerImpact}, " +
            "#{doWell}, #{doPoorly}, #{rootCause}, #{fiveWhy}, #{startTime}, #{detectTime}, #{processTime}, " +
            "#{recoveryTime}, #{editSource}, #{serviceIds}, #{outageHandler}, #{impactedService}, " +
            "#{customerImpactFlag}, #{detectDesc}, #{detectChannel}, #{recoveryMode}, #{coverByTa}, #{earlyDetectedBy},#{rcaWikiUrl}" +
            ")")
    void addByRca(OutageDO outageDO);


    @Update("<script>" +
            "UPDATE outage " +
            "<set>" +
            "<if test='title != null'> title = #{title}, </if>" +
            "<if test='summary != null'> summary = #{summary}, </if>" +
            "<if test='level != null'> level = #{level}, </if>" +
            "<if test='status != null'> status = #{status}, </if>" +
            "<if test='customerImpact != null'> customer_impact = #{customerImpact}, </if>" +
            "<if test='serviceIds != null'> service_ids = #{serviceIds}, </if>" +
            "<if test='rootCause != null'> root_cause = #{rootCause}, </if>" +
            "<if test='outageHandler != null'> outage_handler = #{outageHandler}, </if>" +
            "<if test='doWell != null'> do_well = #{doWell}, </if>" +
            "<if test='doPoorly != null'> do_poorly = #{doPoorly}, </if>" +
            "<if test='fiveWhy != null'> five_why = #{fiveWhy}, </if>" +
            "<if test='type != null'> type = #{type}, </if>" +
            "<if test='impactedService != null'> impacted_service = #{impactedService}, </if>" +
            "<if test='customerImpactFlag != null'> customer_impact_flag = #{customerImpactFlag}, </if>" +
            "<if test='detectDesc != null'> detect_desc = #{detectDesc}, </if>" +
            "<if test='detectChannel != null'> detect_channel = #{detectChannel}, </if>" +
            "<if test='recoveryMode != null'> recovery_mode = #{recoveryMode}, </if>" +
            "<if test='coverByTa != null'> cover_by_ta = #{coverByTa}, </if>" +
            "<if test='earlyDetectedBy != null'> early_detected_by = #{earlyDetectedBy}, </if>" +
            "<if test='startTime != null'> start_time = #{startTime}, </if>" +
            "<if test='detectTime != null'> detect_time = #{detectTime}, </if>" +
            "<if test='processTime != null'> process_time = #{processTime}, </if>" +
            "<if test='recoveryTime != null'> recovery_time = #{recoveryTime}, </if>" +
            "<if test='editSource != null'> edit_source = #{editSource}, </if>" +
            "<if test='lastModifiedBy != null'> last_modified_by = #{lastModifiedBy}, </if>" +
            "</set>" +
            "WHERE id = #{id}" +
            "</script>")
    void edit(OutageDO outageDO);


    @Update("update outage set is_deleted = 1  where id=#{id}")
    void delete(@Param("id") String id);

    @Select("select * from outage where id=#{id}")
    OutageDO getById(@Param("id") String id);

    @Select("select * from outage where rca_wiki_url=#{rcaWikiUrl}")
    List<OutageDO> listByRcaWikiUrl(@Param("rcaWikiUrl") String rcaWikiUrl);


    @Delete("delete from outage where rca_wiki_url = #{rcaWikiUrl}")
    int deleteByRcaWikiUrl(@Param("rcaWikiUrl") String rcaWikiUrl);


    @Update("update outage set status=#{status},last_modified_by = #{lastModifiedBy} where id =#{id} and status=#{fromStatus}")
    void updateStatus(@Param("status") String status,@Param("lastModifiedBy") String lastModifiedBy,@Param("id") String id, @Param("fromStatus") String fromStatus);


    @Select("select *  FROM outage_link WHERE outage_id = #{id}")
    List<OutageLinkDO> selectLinks(@Param("id") String id);

    @Delete("DELETE FROM outage_link WHERE outage_id = #{id}")
    void deleteLinks(@Param("id") String id);


    @Insert({
            "<script>",
            "INSERT INTO outage_link (id, outage_id, link_desc, link_url) VALUES ",
            "<foreach collection='outageLinkDOS' item='item' separator=','>",
            "(#{item.id}, #{item.outageId}, #{item.linkDesc}, #{item.linkUrl})",
            "</foreach>",
            "</script>"
    })
    void addLinks(@Param("outageLinkDOS") List<OutageLinkDO> outageLinkDOS);

    @Insert("Insert into outage_comment(id,outage_id,comment_id,parent_comment_id,content,creator,mentioned_user_ids,type)values(#{id},#{outageId},#{commentId},#{parentCommentId},#{content},#{creator},#{mentionedUserIds},#{type})")
    void comment(OutageCommentDO outageCommentDO);

    @Update("UPDATE outage_comment " +
            "SET content = #{content} " +
            "WHERE id = #{id}")
    void updateContentById(@Param("id") String id, @Param("content") String content);



    @Select("select * from outage_comment where outage_id=#{outageId}  and is_deleted = 0  order by create_time desc ")
    List<OutageCommentDO> searchOutageCommentList(@Param("outageId") String outageId);


    @Update("update outage_comment set is_deleted = 1  where outage_id=#{outageId} and comment_id=#{commentId}")
    void deleteParentComment(@Param("outageId") String outageId, @Param("commentId") String commentId);


    @Update("update outage_comment set is_deleted = 1  where outage_id=#{outageId} and parent_comment_id=#{commentId}")
    void deleteChildComment(@Param("outageId") String outageId, @Param("commentId") String commentId);

    @Select("select * from outage_event where outage_id=#{outageId}  and  is_deleted = 0  order by operation_time asc")
    List<OutageEventDO> searchOutageEventList(@Param("outageId") String outageId);


    @Insert("Insert into outage_event(id,outage_id,content,creator,operator,operation_time,mentioned_user_ids)values(#{id},#{outageId},#{content},#{creator},#{operator},#{operationTime},#{mentionedUserIds})")
    void addEvent(OutageEventDO outageEventDO);

    @Insert({
            "<script>",
            "INSERT INTO outage_event (id, outage_id, content, creator, operator, operation_time, mentioned_user_ids,edit_source)",
            "VALUES",
            "<foreach collection='list' item='item' separator=','>",
            "(#{item.id}, #{item.outageId}, #{item.content}, #{item.creator}, #{item.operator}, #{item.operationTime}, #{item.mentionedUserIds},#{item.editSource})",
            "</foreach>",
            "</script>"
    })
    void addEvents(@Param("list") List<OutageEventDO> outageEventDOList);


    @Update("update outage_event set content=#{content},operator=#{operator},operation_time=#{operationTime},mentioned_user_ids=#{mentionedUserIds},edit_source=#{editSource}  where outage_id=#{outageId} and id=#{id}")
    void editEvent(OutageEventDO outageEventDO);

    @Update("update outage_event set is_deleted = 1  where outage_id=#{outageId} and id=#{id}")
    void deleteEvent(OutageEventDO outageEventDO);

    @Select("select * from outage_alarm where outage_id=#{outageId}  and  is_deleted = 0  order by create_time desc limit #{pageIndex},#{pageSize}")
    List<OutageAlarmDO> searchOutageAlarmList(@Param("outageId") String outageId, @Param("pageIndex") int pageIndex, @Param("pageSize") int pageSize);


    @Select("select count(id) from outage_alarm where outage_id=#{outageId}  and  is_deleted = 0")
    Integer getOutageAlarmListCount(@Param("outageId") String outageId, @Param("pageIndex") int pageIndex, @Param("pageSize") int pageSize);


    @Insert({
            "<script>",
            "INSERT INTO outage_alarm (id, outage_id, name, level, trigger_time, tags, creator, alarm_definition_id, alarm_record_id,service_id,service_name,date_time)",
            "VALUES",
            "<foreach collection='outageAlarmDOs' item='item' index='index' separator=','>",
            "(#{item.id}, #{item.outageId}, #{item.name}, #{item.level}, #{item.triggerTime}, #{item.tags}, #{item.creator}, #{item.alarmDefinitionId}, #{item.alarmRecordId},#{item.serviceId},#{item.serviceName},#{item.dateTime})",
            "</foreach>",
            "</script>"
    })
    void addAlarms(@Param("outageAlarmDOs") List<OutageAlarmDO> outageAlarmDOs);



    @Update("<script>" +
            "UPDATE outage_alarm " +
            "SET is_deleted = 1 " +
            "WHERE outage_id = #{outageId} " +
            "AND id IN " +
            "<foreach item='id' collection='ids' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    void deleteAlarms(@Param("outageId") String outageId, @Param("ids") List<String> ids);



    @Select("select * from outage_action where outage_id=#{outageId}  and  is_deleted = 0  order by create_time desc")
    List<OutageActionDO> searchOutageActionList(@Param("outageId") String outageId);


    @Insert({
            "<script>",
            "INSERT INTO outage_action (id, outage_id, type, link, owner, due_date, creator,title,severity,complexity)",
            "VALUES",
            "<foreach collection='addActions' item='item' index='index' separator=','>",
            "(#{item.id}, #{item.outageId}, #{item.type}, #{item.link}, #{item.owner}, #{item.dueDate}, #{item.creator},#{item.title},#{item.severity},#{item.complexity})",
            "</foreach>",
            "</script>"
    })
    void batchInsertAction(@Param("addActions") List<OutageActionDO> addActions);


    @Delete("delete from outage_action where outage_id = #{outageId}")
    void batchDeleteAction(@Param("outageId") String outageId);

    @Select("select * from outage where title=#{title}  and  is_deleted = 0 LIMIT 1")
    OutageDO searchOutageByTitle(@Param("title") String title);

    @Delete("DELETE FROM outage_owner WHERE outage_id = #{id}")
    void deleteOwners(String id);


    @Select("select *  FROM outage_owner WHERE outage_id = #{id}")
    List<OutageOwnerDO> selectOwners(@Param("id") String id);

    @Insert({
            "<script>",
            "INSERT INTO outage_owner (id, outage_id, owner, managers) VALUES ",
            "<foreach collection='outageOwnerDOS' item='item' separator=','>",
            "(#{item.id}, #{item.outageId}, #{item.owner}, #{item.managers})",
            "</foreach>",
            "</script>"
    })
    void addOwners(@Param("outageOwnerDOS") List<OutageOwnerDO> outageOwnerDOS);
}
