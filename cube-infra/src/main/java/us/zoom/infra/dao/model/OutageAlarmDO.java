package us.zoom.infra.dao.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * OutageDO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OutageAlarmDO {
    private String id;
    private String outageId;
    private String alarmDefinitionId;
    private String alarmRecordId;
    private String name;
    private String level;
    /**
     * alarmTime
     */
    private Long triggerTime;
    /**
     * redirectTime
     */
    private Long dateTime;
    private String tags;
    private String serviceId;
    private String serviceName;
    private Date createTime;
    private Date modifyTime;
    private String creator;
}
