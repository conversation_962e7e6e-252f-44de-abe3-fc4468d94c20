package us.zoom.infra.dao.service;

import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;
import us.zoom.infra.dao.model.MetricsSyncTaskDO;

import java.util.List;

/**
 * @authoer: tobey.zhu
 * @createDate: 2022/9/16
 * @description: table:
 * cube_metrics_sync_task
 */
@Mapper
@Repository
public interface MetricsSyncTaskDAO {

    @Update("<script>" +
            "    update `cube_metrics_sync_task`" +
            "    <set>" +
            "        `name`=#{name}," +
            "        `service`=#{service}," +
            "        `interval`=#{interval}," +
            "        `status`=#{status}," +
            "        `task_type`=#{taskType}," +
            "        `task_config`=#{taskConfig}," +
            "        `last_modified_user`=#{lastModifiedUser}," +
            "        `remark`=#{remark}," +
            "        `unit_tag`=#{unitTag}," +
            "        `send_topic`=#{sendTopic}" +
            "        `ref_id`=#{refId}" +
            "        `ref_type`=#{refType}" +
            "    </set>" +
            "    where id = #{id}" +
            "</script>")
    void modify(MetricsSyncTaskDO metricsSyncTaskDO);

    @Update("<script>" +
            "    update `cube_metrics_sync_task`" +
            "    <set>" +
            "        `sub_tasks`=#{subTasks}," +
            "    </set>" +
            "    where id = #{id}" +
            "</script>")
    void updateSubTasks(@Param("id") String id, @Param("subTasks") String subTasks);

    @Select("select * from `cube_metrics_sync_task` where `id` = #{id}")
    MetricsSyncTaskDO getById(@Param("id") String id);

    @Select("select * from `cube_metrics_sync_task` where `name` = #{name}")
    MetricsSyncTaskDO getByName(@Param("name") String name);


    @Insert("insert into `cube_metrics_sync_task`(`id`,`name`,`interval`,`status`,`task_type`,`task_config`,`gmt_create`,`gmt_modify`,`service`,`created_user`,`last_modified_user`,`remark`,`unit_tag`,`send_topic`,`ref_id`,`ref_type`) " +
            "values(#{id},#{name},#{interval},#{status},#{taskType},#{taskConfig}, now(),now(), #{service},#{createdUser},#{lastModifiedUser},#{remark},#{unitTag},#{sendTopic}, #{refId},#{refType})")
    void add(MetricsSyncTaskDO metricsSyncTaskDO);

    @Delete("delete from `cube_metrics_sync_task` where `id` = #{id}")
    void deleteById(@Param("id") String id);


    @Select("<script>" +
            "    select * from `cube_metrics_sync_task`" +
            "    <where>" +
            "        <if test=\"serviceNames != null and serviceNames.size > 0\">and `service` in (" +
            "           <foreach collection=\"serviceNames\" item=\"serviceName\" separator=\",\">" +
            "                   #{serviceName}" +
            "           </foreach> " +
            "           )" +
            "        </if>" +
            "        <if test=\"name != null and name != ''\">and `name` like concat('%', #{name} , '%') </if>" +
            "        <if test=\"taskType != null and taskType != ''\">and `task_type` = #{taskType} </if>" +
            "        <if test=\"status != null and status != ''\">and `status` = #{status} </if>" +
            "    </where>" +
            "    order by `gmt_modify` desc limit #{offset},#{pageSize}" +
            "</script>")
    List<MetricsSyncTaskDO> getList(@Param("serviceNames") List<String> serviceNames,
                                    @Param("name") String name,
                                    @Param("taskType") String taskType,
                                    @Param("status") String status,
                                    @Param("offset") int offset,
                                    @Param("pageSize") int pageSize);

    @Select("<script>" +
            "    select * from `cube_metrics_sync_task`" +
            "    <where>" +
            "        <if test=\"taskType != null and taskType != ''\">and `task_type` = #{taskType} </if>" +
            "        and `status` = 'enable' " +
            "    </where>" +
            "</script>")
    List<MetricsSyncTaskDO> getAllEnabledTasks(@Param("taskType") String taskType);

    @Select("<script>" +
            "    select count(1) from `cube_metrics_sync_task`" +
            "    <where>" +
            "        <if test=\"serviceNames != null and serviceNames.size > 0\">and `service` in (" +
            "           <foreach collection=\"serviceNames\" item=\"serviceName\" separator=\",\">" +
            "                   #{serviceName}" +
            "           </foreach> " +
            "           )" +
            "        </if>" +
            "        <if test=\"name != null and name != ''\">and `name` like concat('%', #{name} , '%') </if>" +
            "        <if test=\"taskType != null and taskType != ''\">and `task_type` = #{taskType} </if>" +
            "        <if test=\"status != null and status != ''\">and `status` = #{status} </if>" +
            "    </where>" +
            "</script>")
    int getCount(@Param("serviceNames") List<String> serviceNames,
                 @Param("name") String name,
                 @Param("taskType") String taskType,
                 @Param("status") String status);
}