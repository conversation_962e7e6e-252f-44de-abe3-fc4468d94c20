package us.zoom.infra.dao.config;

import com.zaxxer.hikari.HikariDataSource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.jdbc.metadata.HikariDataSourcePoolMetadata;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;
import us.zoom.cube.sdk.util.MonitorLogReporter;
import us.zoom.infra.dao.model.SysParaDO;
import us.zoom.infra.model.routingdb.RoutingDB;
import us.zoom.infra.monitor.DatabaseAAMonitor;
import us.zoom.infra.thread.NamedThreadFactory;
import us.zoom.infra.utils.IpUtils;
import us.zoom.infra.utils.JdbcHelper;
import us.zoom.cube.lib.utils.JsonUtils;

import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * @Author: Toby Fang
 * @ModuleOwner: Toby Fang
 * @Date:04/10/2023 13:46
 * @Description:
 */
@Slf4j
public class MultipleDataSource extends AbstractRoutingDataSource {
    private final Logger monitorLog = LoggerFactory.getLogger("Monitor");

    private final static int MAX_INACTIVE_COUNT = 3;
    private final static int SYS_PARA_CHECK_INTERVAL = 300000;
    private final static int MAX_SWITCH_INTERVAL = 300000;
    private final long startUpTime;

    private volatile DataSourceType currentDataSource = DataSourceType.MAIN;
    private volatile long lastCheckSysPara;
    private volatile int inactiveCount = 0;
    private volatile long lastCheckTs = 0;

    // Map<"MAIN"/"STANDBY", datasource>
    private Map<Object, Object> cacheDatasourceMap = new HashMap<>();

    // Map<"MAIN"/"STANDBY", hikariDataSourcePoolMetadata>
    private Map<Object, HikariDataSourcePoolMetadata> hikariDataSourcePoolMetadataMap = new HashMap<>();

    private static ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1, new NamedThreadFactory("multipleDataSource scheduler"));

    @Override
    public void setTargetDataSources(Map<Object, Object> targetDataSources) {
        super.setTargetDataSources(targetDataSources);
        this.cacheDatasourceMap = targetDataSources;

        for (Map.Entry entry : targetDataSources.entrySet()) {
            HikariDataSourcePoolMetadata metadata = new HikariDataSourcePoolMetadata((HikariDataSource) entry.getValue());
            hikariDataSourcePoolMetadataMap.put(entry.getKey(), metadata);
        }
    }

    @Override
    public Object determineCurrentLookupKey() {
        return currentDataSource.name();
    }

    @NotNull
    private void doGetKey() throws SQLException {
        log.info("start_try_get_db_key,  currentDataSource[{}]", currentDataSource);

        DatabaseAAMonitor.DatabaseAAMonitorBuilder builder = DatabaseAAMonitor.builder();
        builder.currentDB(currentDataSource.name());
        builder.privateIp(IpUtils.getLocalIP());
        HikariDataSourcePoolMetadata metadata = hikariDataSourcePoolMetadataMap.get(currentDataSource.name());
        if (metadata != null) {
            builder.minPoolIdle(metadata.getMin())
                    .maxPoolSize(metadata.getMax());
            if (metadata.getActive() != null
                    && metadata.getUsage() != null
                    && metadata.getIdle() != null) {
                builder.poolUsage(metadata.getUsage())
                        .poolActive(metadata.getActive())
                        .poolIdle(metadata.getIdle());
            }
        }

        // current db is active
        if (JdbcHelper.detectDBActive((DataSource) cacheDatasourceMap.get(currentDataSource.name()))) {
            //active, reset counter
            inactiveCount = 0;
            lastCheckTs = System.currentTimeMillis();

            ForceDBSetting forceDBSetting = checkForceDBSetting();
            if (forceDBSetting != null && forceDBSetting.getDataSourceType() != null) {
                currentDataSource = forceDBSetting.getDataSourceType();

                //force setting value modified later than startup time, print switch db log
                if (forceDBSetting.getLastModifyTime() > startUpTime) {
                    builder.switchDB(1).switchDBTo(currentDataSource.name())
                            .switchReason(DatabaseAAMonitor.Reason.SWITCH_BY_SYSPARA.name());
                }
                log.info("try_get_db_key,  db is active, force db[{}]", currentDataSource);
            }

            builder.inactiveTimes(inactiveCount).lastCheckTs(lastCheckTs);
            log.info("try_get_db_key, db is active, active db[{}]", currentDataSource);
        } else {

            //increase counter
            inactiveCount++;

            log.info("try_get_db_key, db is inactive[{}], current db[{}]", inactiveCount, currentDataSource);
            builder.inactiveTimes(inactiveCount).lastCheckTs(lastCheckTs);

            //counter reaches threshold or time is long enough to do the switch
            if (inactiveCount >= MAX_INACTIVE_COUNT || System.currentTimeMillis() - lastCheckTs > MAX_SWITCH_INTERVAL) {
                inactiveCount = 0;
                lastCheckTs = System.currentTimeMillis();
                //switch to the other db
                currentDataSource = DataSourceType.getTheOther(currentDataSource);
                builder.switchDB(1).switchDBTo(currentDataSource.name())
                        .switchReason(DatabaseAAMonitor.Reason.SWITCH_BY_CONNECTION_FAIL.name());
                log.info("try_get_db_key, db is inactive, counter reach MAX_INACTIVE_COUNT or inactive is enough to switch, switch to db[{}]", currentDataSource);
            }

        }
        MonitorLogReporter.report(monitorLog, builder.build());
    }

    public MultipleDataSource() {
        scheduler.scheduleAtFixedRate(() -> {
            try {
                doGetKey();
            } catch (Exception e) {
                log.error("Exception when getKey, ", e);
            }
        }, 0, 1, TimeUnit.MINUTES);

        startUpTime = System.currentTimeMillis();
    }

    @Nullable
    private ForceDBSetting checkForceDBSetting() throws SQLException {
        //check interval is not enough
        if (System.currentTimeMillis() - lastCheckSysPara < SYS_PARA_CHECK_INTERVAL) {
            return null;
        }

        lastCheckSysPara = System.currentTimeMillis();
        SysParaDO sysPara = JdbcHelper.selectAaDBPara((DataSource) cacheDatasourceMap.get(currentDataSource.name()));
        if (null == sysPara) {
            return null;
        }

        RoutingDB routingDB = JsonUtils.toObject(sysPara.getValue(), RoutingDB.class);
        // db is specified and not equal to current one
        DataSourceType currentCluster = DataSourceType.getByName(routingDB.getDbCluster());
        if (StringUtils.isNotBlank(routingDB.getDbCluster())
                && null != currentCluster
                && !routingDB.getDbCluster().equals(currentDataSource.name())) {
            boolean success = JdbcHelper.detectDBActive((DataSource) cacheDatasourceMap.get(routingDB.getDbCluster()));
            if (success) {
                ForceDBSetting forceDBSetting = new ForceDBSetting();
                forceDBSetting.setDataSourceType(currentCluster);
                forceDBSetting.setLastModifyTime(sysPara.getGmtModify().getTime());

                return forceDBSetting;
            }

        }
        return null;
    }

    @Data
    static class ForceDBSetting {
        private DataSourceType dataSourceType = DataSourceType.MAIN;
        private long lastModifyTime;
    }

}
