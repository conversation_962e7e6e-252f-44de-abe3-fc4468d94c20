package us.zoom.infra.dao.service;


import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;
import us.zoom.infra.dao.model.MenuItemDO;
import us.zoom.infra.dao.model.UserDO;
import us.zoom.infra.dao.model.UserRoleDO;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@Repository
public interface UserRoleDAO {

    @Insert("insert into user_role(id,role_desc,role,create_time,modify_time,cross_service,pii_access,can_operate, creator)" +
            "values(#{id},#{roleDesc},#{role},now(),now(),#{crossService},#{piiAccess},#{canOperate}, #{creator})")
    void addRole(UserRoleDO userRoleDO);

    @Delete(value="<script>" +
            "delete   from user_role  " +
            " where role =#{name}  " +
            "</script>")
    void deleteRoleByName(@Param("name") String name);

    @Select(value="<script>" +
            "select *  from user_role  " +
            " where role like \"%\"#{name}\"%\"  " +
            " order by create_time desc limit #{pageIndex},#{pageSize}" +
            "</script>")
    List<UserRoleDO> findByNameLike(@Param("name") String name,  @Param("pageIndex") int pageIndex, @Param("pageSize") int pageSize);

    @Select(value="<script>" +
            "select count(id) from user_role " +
            " where  role like \"%\"#{name}\"%\" "+
            "</script>")
    int getCountByNameLike(@Param("name") String name);



    @Update("update user_role set role_desc=#{roleDesc},cross_service=#{crossService},modifier=#{modifier},modify_time = now() where id=#{id} ")
    void editRole(UserRoleDO userRoleDO);

    @Update("update user_role set pii_access=#{piiAccess},modifier=#{modifier},modify_time = now() where role=#{role} ")
    void editPiiAccess(@Param("role") String role, @Param("piiAccess") Boolean piiAccess, @Param("modifier") String modifier);

    @Update("update user_role set can_operate=#{canOperate},modifier=#{modifier},modify_time = now() where role=#{role} ")
    void editCanOperate(@Param("role") String role, @Param("canOperate") Boolean canOperate, @Param("modifier") String modifier);

    @Select("select * from user_role where role=#{role}")
    UserRoleDO getRoleByName(@Param("role")String role);

    @Select("select * from user_role")
    List<UserRoleDO> listAll();

    @Select("select * from user_role where pii_access=1")
    List<UserRoleDO> listPiiRoles();


}
