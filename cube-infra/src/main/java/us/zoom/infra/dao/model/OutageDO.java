package us.zoom.infra.dao.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * OutageDO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OutageDO {
    private String id;
    private String title;
    private String summary;
    /**
     * @see us.zoom.cube.site.lib.outage.OutageLevelEnum
     */
    private String level;
    /**
     * @see us.zoom.cube.site.lib.outage.OutageStatusEnum
     */
    private String status;
    private String customerImpact;
    private String serviceIds;
    private String rootCause;
    private String outageHandler;
    private String doWell;
    private String doPoorly;
    /**
     * @see us.zoom.cube.site.lib.outage.OutageTypeEnum
     */
    private String type;
    private String impactedService;
    private boolean customerImpactFlag;
    private String detectDesc;
    private String detectChannel;
    private String recoveryMode;
    private boolean coverByTa;
    private String earlyDetectedBy;
    private boolean isDeleted;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date detectTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date processTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date recoveryTime;
    private String creator;
    private String lastModifiedBy;
    private String fiveWhy;
    /***
     * @see us.zoom.cube.site.lib.outage.OutageEditTypeEnum
     */
    private String editSource;
    private List<OutageLinkDO> outageLinks;
    private List<OutageOwnerDO> outageOwners;
    private String rcaWikiUrl;
}
