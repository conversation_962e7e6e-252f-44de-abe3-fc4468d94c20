package us.zoom.infra.dao.model;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/1/5
 */
public class DataParserPipelineQueryDO {

    private String id;
    private String name;
    private String filterRule;
    private Date gmtCreate;
    private Date gmtModify;
    private String dataParserId;
    private String collectorId;
    private String parentId;
    private Integer order;
    private String tenantId;
    /**
     * use status , 0 stop ,1 use
     */
    private Integer useStatus;

    private String topic;

    // query field
    private String tenantName;
    private String dataParserName;
    private String newDataParserName;
    private Integer sourceType;
    private String sourceName;
    private String calcQueueId;
    private String calcQueueName;
    private String alarmQueueId;
    private String alarmQueueName;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getFilterRule() {
        return filterRule;
    }

    public void setFilterRule(String filterRule) {
        this.filterRule = filterRule;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModify() {
        return gmtModify;
    }

    public void setGmtModify(Date gmtModify) {
        this.gmtModify = gmtModify;
    }

    public String getDataParserId() {
        return dataParserId;
    }

    public void setDataParserId(String dataParserId) {
        this.dataParserId = dataParserId;
    }

    public String getCollectorId() {
        return collectorId;
    }

    public void setCollectorId(String collectorId) {
        this.collectorId = collectorId;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public Integer getUseStatus() {
        return useStatus;
    }

    public void setUseStatus(Integer useStatus) {
        this.useStatus = useStatus;
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public String getTenantName() {
        return tenantName;
    }

    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }

    public String getDataParserName() {
        return dataParserName;
    }

    public void setDataParserName(String dataParserName) {
        this.dataParserName = dataParserName;
    }

    public String getNewDataParserName() {
        return newDataParserName;
    }

    public void setNewDataParserName(String newDataParserName) {
        this.newDataParserName = newDataParserName;
    }

    public Integer getSourceType() {
        return sourceType;
    }

    public void setSourceType(Integer sourceType) {
        this.sourceType = sourceType;
    }

    public String getSourceName() {
        return sourceName;
    }

    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }

    public String getCalcQueueId() {
        return calcQueueId;
    }

    public void setCalcQueueId(String calcQueueId) {
        this.calcQueueId = calcQueueId;
    }

    public String getCalcQueueName() {
        return calcQueueName;
    }

    public void setCalcQueueName(String calcQueueName) {
        this.calcQueueName = calcQueueName;
    }

    public String getAlarmQueueId() {
        return alarmQueueId;
    }

    public void setAlarmQueueId(String alarmQueueId) {
        this.alarmQueueId = alarmQueueId;
    }

    public String getAlarmQueueName() {
        return alarmQueueName;
    }

    public void setAlarmQueueName(String alarmQueueName) {
        this.alarmQueueName = alarmQueueName;
    }
}
