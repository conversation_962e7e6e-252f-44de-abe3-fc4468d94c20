package us.zoom.infra.dao.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * OutageDO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OutageActionDO {
    private String id;
    private String outageId;
    private String type;
    private String link;
    private String owner;
    private String dueDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;
    private String creator;
    private String title;
    private String severity;
    private String complexity;
}
