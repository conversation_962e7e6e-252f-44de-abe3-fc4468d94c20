package us.zoom.infra.dao.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * OutageDO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OutageEventDO {
    private String id;
    private String outageId;
    private String content;
    private Date createTime;
    private Date modifyTime;
    private String creator;
    private String lastModifiedBy;
    private String operator;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operationTime;
    private String mentionedUserIds;
    /***
     * @see us.zoom.cube.site.lib.outage.OutageEditTypeEnum
     */
    private String editSource;
}
