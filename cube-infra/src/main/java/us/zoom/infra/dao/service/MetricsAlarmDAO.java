package us.zoom.infra.dao.service;

import com.zoom.op.monitor.domain.alarm.AlarmDefinition;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;
import us.zoom.infra.dao.model.AlarmDO;
import us.zoom.infra.dao.model.MetricsAlarmDO;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2020/7/19 9:43 AM
 */
@Mapper
@Repository
public interface MetricsAlarmDAO {


    @Select("select distinct alarm.* from alarm left join metrics_alarm_relation on metrics_alarm_relation.alarm_id = alarm.id where metrics_alarm_relation.metrics_id=#{metricsId}")
    List<AlarmDO> listAlarmsByMetricsId(@Param("metricsId") String metricsId);

    @Select("<script>" +
            "select * from alarm_definition where metric_id in " +
            "<foreach collection=\"metricsIdList\" item=\"item\" index=\"index\" open=\"(\" close=\")\" separator=\",\">" +
            "  #{item}" +
            "</foreach>" +
            "</script>")
    List<AlarmDefinition> listAlarmsByMetricsIdList(@Param("metricsIdList") List<String> metricsIdList);


    @Select("select distinct alarm_definition.id as id ,alarm_definition.name as name  from alarm_definition  where metric_id=#{metricsId}")
    List<AlarmDO> listAlarmIdNames(@Param("metricsId") String metricsId);

    @Select({
            "<script>",
            "SELECT DISTINCT alarm_definition.id AS id, alarm_definition.name AS name",
            "FROM alarm_definition",
            "WHERE metric_id IN",
            "<foreach item='id' collection='metricsIds' open='(' separator=',' close=')'>",
            "#{id}",
            "</foreach>",
            "</script>"
    })
    List<AlarmDO> listAlarmIdNamesBatch(@Param("metricsIds") List<String> metricsIds);

    @Select("select distinct alarm_definition.id as id ,alarm_definition.name as name  from alarm_definition  where metric_id=#{metricsId} AND enabled = 1")
    List<AlarmDO> listEnabledAlarmIdNames(@Param("metricsId") String metricsId);

    @Insert("insert into metrics_alarm_relation(id, metrics_id, alarm_id, create_time, modify_time) values(#{id}, #{metricsId}, #{alarmId}, #{createTime}, #{modifyTime})")
    int insertMetricsAlarm(MetricsAlarmDO metricsAlarmDO);

    @Update("update metrics_alarm_relation set metrics_id=#{metricsId} where alarm_id=#{alarmId}")
    void updateMetricsIdByAlarmId(@Param("alarmId") String alarmId, @Param("metricsId") String metricsId);

    @Delete("delete from metrics_alarm_relation where alarm_id=#{alarmId}")
    void delByAlarmId(@Param("alarmId") String alarmId);

    @Select("SELECT * FROM metrics_alarm_relation")
    List<MetricsAlarmDO> listAll();

    @Select("select metrics_id from metrics_alarm_relation  where alarm_id=#{alarmId}")
    String getMetricsIdByAlarmId(@Param("alarmId") String alarmId);

    @Select("<script>" +
            "select * from metrics_alarm_relation where metrics_id in " +
            "<foreach collection=\"metricsIdList\" item=\"item\" index=\"index\" open=\"(\" close=\")\" separator=\",\">" +
            "  #{item}" +
            "</foreach>" +
            "</script>")
    List<MetricsAlarmDO> listMetricsAlarmsByMetricsIdList(@Param("metricsIdList") List<String> metricsIdList);
}
