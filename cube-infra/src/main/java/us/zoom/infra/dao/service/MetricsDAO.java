package us.zoom.infra.dao.service;

import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;
import us.zoom.infra.dao.model.MetricsDO;
import us.zoom.infra.dao.model.MetricsFieldDO;
import us.zoom.infra.dao.model.MetricsQueryDO;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2020/7/16 4:57 PM
 */
@Mapper
@Repository
public interface MetricsDAO {

    @Select(value = "<script>" +
            "select * from metrics where metrics_name like \"%\"#{metricsName}\"%\" and tenant_id in " +
            "<foreach collection=\"tenantIds\" item=\"item\" index=\"index\" open=\"(\" close=\")\" separator=\",\">" +
            "  #{item}" +
            "</foreach>" +
            " order by create_time desc limit #{pageIndex}, #{pageSize} </script>")
    List<MetricsDO> findByMetricsNameLike(@Param("metricsName") String metricsName, @Param("tenantIds") List<String> tenantIds, @Param("pageIndex") int pageIndex, @Param("pageSize") int pageSize);

    @Select("select * from metrics where metrics_name like \"%\"#{metricsName}\"%\" and tenant_id=#{tenantId}")
    List<MetricsDO> findAllByMetricsNameLike(@Param("metricsName") String metricsName, @Param("tenantId") String tenantId);

    @Select(value = "<script>" +
            "select * from metrics where metrics_name like \"%\"#{metricsName}\"%\" " +
            "<if test= \"type != 3\"> and type = #{type} </if>" +
            "and tenant_id in " +
            "<foreach collection=\"tenantIds\" item=\"item\" index=\"index\" open=\"(\" close=\")\" separator=\",\">" +
            "  #{item}" +
            "</foreach>" +
            " order by modify_time desc limit #{pageIndex}, #{pageSize} </script>")
    List<MetricsDO> findByMetricsNameLikeAndType(@Param("metricsName") String metricsName, @Param("type") Integer type, @Param("tenantIds") List<String> tenantIds, @Param("pageIndex") int pageIndex, @Param("pageSize") int pageSize);

    @Select(value = "<script>" +
            "select DISTINCT m.* " +
            "<if test= \"dataParserName != null and dataParserName != ''\"> , COALESCE(dp.name, dps.name) as dataParserName </if>" +
            "from metrics m " +
            "left join collector_metrics cm ON m.id = cm.metrics_id " +
            "left join collector c ON cm.collector_id = c.id " +
            "<if test= \"dataParserName != null and dataParserName != ''\">" +
            "left join data_parser_pipeline dpp ON cm.collector_id = dpp.collector_id " +
            "left join data_parser dp ON dpp.data_parser_id = dp.id " +
            "left join data_parser_source dps ON dpp.data_parser_id = dps.id " +
            "</if>" +
            "<if test= \"labelIds != null and labelIds.size() > 0 and labelMatchType != 'not'\">" +
            " left join resource_tag rt on m.id = rt.resource " +
            "</if>" +
            "where m.metrics_name like \"%\"#{metricsName}\"%\" " +
            "<if test= \"collectorName != null and collectorName != ''\"> and c.collector_name = #{collectorName} </if>" +
            "<if test= \"dataParserName != null and dataParserName != ''\"> and (dp.name = #{dataParserName} or dps.name = #{dataParserName}) </if>" +
            "<if test= \"type != 3\"> and m.type = #{type} </if>" +
            "<if test= \"creator != null and creator != ''\"> and m.creator like \"%\"#{creator}\"%\"  </if>" +
            "<if test= \"enabled != 2\"> and m.enabled = #{enabled} </if>" +
            "and m.tenant_id in " +
            "<foreach collection=\"tenantIds\" item=\"item\" index=\"index\" open=\"(\" close=\")\" separator=\",\">" +
            "  #{item}" +
            "</foreach>" +
            "<if test= \"labelIds != null and labelIds.size() > 0\">" +
            " <choose>" +
            "   <when test='labelMatchType == \"or\"'>" +
            "     and rt.tag_id in " +
            "     <foreach collection=\"labelIds\" item=\"item\" open=\"(\" separator=\",\" close=\")\">" +
            "         #{item} " +
            "     </foreach>" +
            "   </when>" +
            "   <when test='labelMatchType == \"and\"'>" +
            "     and rt.tag_id in " +
            "     <foreach collection=\"labelIds\" item=\"item\" open=\"(\" separator=\",\" close=\")\">" +
            "         #{item} " +
            "     </foreach>" +
            "    GROUP BY m.id " +
            "     HAVING COUNT(DISTINCT rt.tag_id) = #{labelSize} " +
            "   </when>" +
            "   <when test='labelMatchType == \"not\"'>" +
            "     and NOT EXISTS ( " +
            "         select 1 from resource_tag rtt " +
            "         where rtt.resource = m.id and rtt.tag_id in " +
            "         <foreach collection=\"labelIds\" item=\"item\" open=\"(\" separator=\",\" close=\")\">" +
            "             #{item} " +
            "         </foreach>" +
            "     )" +
            "   </when>" +
            " </choose>" +
            "</if>" +
            " order by m.modify_time desc, m.id desc limit #{pageIndex}, #{pageSize} </script>")
    List<MetricsDO> findByMetricsNameLikeWithTypeAndStatus(@Param("metricsName") String metricsName, @Param("type") Integer type, @Param("enabled") Integer enabled, @Param("collectorName") String collectorName, @Param("dataParserName") String dataParserName, @Param("tenantIds") List<String> tenantIds, @Param("creator") String creator,
                                                           @Param("labelIds") List<String> labelIds, @Param("labelSize") Integer labelSize,
                                                           @Param("labelMatchType") String labelMatchType,  @Param("pageIndex") int pageIndex, @Param("pageSize") int pageSize);


    @Select(value = "<script>" +
            "select count(id) from metrics where metrics_name like \"%\"#{metricsName}\"%\"  " +
            "<if test= \"type != 3\"> and type = #{type} </if>" +
            "and tenant_id in " +
            "<foreach collection=\"tenantIds\" item=\"item\" index=\"index\" open=\"(\" close=\")\" separator=\",\">" +
            "  #{item}" +
            "</foreach>" +
            "</script>")
    int getCountByMetricsNameLikeAndType(@Param("metricsName") String metricsName, @Param("type") Integer type, @Param("tenantIds") List<String> tenantIds);

    @Select(value = "<script>" +
            "select count(*) from (" +
            "select DISTINCT m.id from metrics m " +
            "left join collector_metrics cm ON m.id = cm.metrics_id " +
            "left join collector c ON cm.collector_id = c.id " +
            "<if test= \"dataParserName != null and dataParserName != ''\">" +
            "left join data_parser_pipeline dpp ON cm.collector_id = dpp.collector_id " +
            "left join data_parser dp ON dpp.data_parser_id = dp.id " +
            "left join data_parser_source dps ON dpp.data_parser_id = dps.id " +
            "</if>" +
            "<if test= \"labelIds != null and labelIds.size() > 0 and labelMatchType != 'not'\">" +
            " left join resource_tag rt on m.id = rt.resource " +
            "</if>" +
            "where m.metrics_name like \"%\"#{metricsName}\"%\" " +
            "<if test= \"collectorName != null and collectorName != ''\"> and c.collector_name = #{collectorName} </if>" +
            "<if test= \"dataParserName != null and dataParserName != ''\"> and (dp.name = #{dataParserName} or dps.name = #{dataParserName}) </if>" +
            "<if test= \"type != 3\"> and m.type = #{type} </if>" +
            "<if test= \"creator != null and creator != ''\"> and m.creator like  \"%\"#{creator}\"%\" </if>" +
            "<if test= \"enabled != 2\"> and m.enabled = #{enabled} </if>" +
            "and m.tenant_id in " +
            "<foreach collection=\"tenantIds\" item=\"item\" index=\"index\" open=\"(\" close=\")\" separator=\",\">" +
            "  #{item}" +
            "</foreach>" +
            "<if test= \"labelIds != null and labelIds.size() > 0\">" +
            " <choose>" +
            "   <when test='labelMatchType == \"or\"'>" +
            "     and rt.tag_id in " +
            "     <foreach collection=\"labelIds\" item=\"item\" open=\"(\" separator=\",\" close=\")\">" +
            "         #{item} " +
            "     </foreach>" +
            "   </when>" +
            "   <when test='labelMatchType == \"and\"'>" +
            "     and rt.tag_id in " +
            "     <foreach collection=\"labelIds\" item=\"item\" open=\"(\" separator=\",\" close=\")\">" +
            "         #{item} " +
            "     </foreach>" +
            " GROUP BY m.id " +
            "     HAVING COUNT(DISTINCT rt.tag_id) = #{labelSize} " +
            "   </when>" +
            "   <when test='labelMatchType == \"not\"'>" +
            "     and NOT EXISTS ( " +
            "         select 1 from resource_tag rtt " +
            "         where rtt.resource = m.id and rtt.tag_id in " +
            "         <foreach collection=\"labelIds\" item=\"item\" open=\"(\" separator=\",\" close=\")\">" +
            "             #{item} " +
            "         </foreach>" +
            "     )" +
            "   </when>" +
            " </choose>" +
            "</if>" +
            ") as temp" +
            "</script>")
    int getCountByMetricsNameLikeAndTypeNew(@Param("metricsName") String metricsName, @Param("type") Integer type, @Param("enabled") Integer enabled, @Param("collectorName") String collectorName, @Param("dataParserName") String dataParserName, @Param("tenantIds") List<String> tenantIds,
                                            @Param("creator") String creator,@Param("labelIds") List<String> labelIds , @Param("labelSize") Integer labelSize, @Param("labelMatchType") String labelMatchType);


    @Select(value = "<script>" +
            "select count(id) from metrics where metrics_name like \"%\"#{metricsName}\"%\"   and tenant_id in " +
            "<foreach collection=\"tenantIds\" item=\"item\" index=\"index\" open=\"(\" close=\")\" separator=\",\">" +
            "  #{item}" +
            "</foreach>" +
            "</script>")
    int getCountByMetricsNameLike(@Param("metricsName") String metricsName, @Param("tenantIds") List<String> tenantIds);


    @Select(value = "<script>" +
            "select * from metrics where metrics_name = #{metricsName}   and tenant_id in " +
            "<foreach collection=\"tenantIds\" item=\"item\" index=\"index\" open=\"(\" close=\")\" separator=\",\">" +
            "  #{item}" +
            "</foreach>" +
            " limit #{start},#{pageSize} " +
            "</script>")
    List<MetricsDO> queryExistTenantsByMetricsNameAndTenantIds(MetricsQueryDO metricsQueryDO);

    @Select(value = "<script>" +
            "select count(id) from metrics where metrics_name = #{metricsName} and tenant_id in " +
            "<foreach collection=\"tenantIds\" item=\"item\" index=\"index\" open=\"(\" close=\")\" separator=\",\">" +
            "  #{item}" +
            "</foreach>" +
            "</script>")
    int getCountByMetricsName(@Param("metricsName") String metricsName, @Param("tenantIds") List<String> tenantIds);

    @Insert("insert into metrics(id, metrics_name, tenant_id, tag_names, type, create_time, modify_time, origin_period, creator, editor, enabled, document_link) " +
            "values(#{id}, #{metricsName}, #{tenantId}, #{tagNames}, #{type}, #{createTime}, #{modifyTime}, #{originPeriod}, #{creator}, #{editor}, #{enabled}, #{documentLink})")
    int insertMetrics(MetricsDO metricsDO);


    @Insert(value = "<script>" +
            "insert into metrics(id, metrics_name, tenant_id, tag_names, type, create_time, modify_time, origin_period, creator, editor, enabled) " +
            "values" +
            "<foreach collection=\"metricsDOS\" item=\"metricsDO\" index=\"index\" separator=\",\">" +
            "(#{metricsDO.id}, #{metricsDO.metricsName}, #{metricsDO.tenantId}, #{metricsDO.tagNames}, #{metricsDO.type}, " +
            "#{metricsDO.createTime},#{metricsDO.modifyTime},#{metricsDO.originPeriod},#{metricsDO.creator},#{metricsDO.editor},#{metricsDO.enabled})" +
            "</foreach>" +
            "</script>")
    int batchInsertMetrics(@Param("metricsDOS") List<MetricsDO> metricsDOS);


    @Update("update metrics set tag_names = #{tagNames}, origin_period=#{originPeriod}, modify_time=#{modifyTime}, editor=#{editor}, enabled=#{enabled} where id = #{id}")
    void updateMetrics(MetricsDO metricsDO);

    @Select("<script>" +
            "select id from metrics  where tenant_id  in " +
            "<foreach collection=\"tenantIds\" item=\"item\" index=\"index\" open=\"(\" close=\")\" separator=\",\">" +
            "  #{item}" +
            "</foreach>" +
            "</script>")
    List<String> listMetricsIdsByTenantIds(@Param("tenantIds") List<String> tenantIds);

    @Select(value = "select * from metrics where  id = #{id}  ")
    MetricsDO getMetricsById(String id);

    @Select(value = "select * from metrics where tenant_id = #{tenantId}  ")
    List<MetricsDO> getMetricsByTenant(@Param("tenantId") String tenantId);

    @Delete(value = "delete from metrics where id = #{metricsId}")
    void deleteMetricsById(@Param("metricsId") String metricsId);

    @Delete(value = "<script>" +
            "delete from metrics where id  in " +
            "<foreach collection=\"metricsIds\" item=\"item\" index=\"index\" open=\"(\" close=\")\" separator=\",\">" +
            "  #{item}" +
            "</foreach>" +
            "</script>")
    void deleteMetricsByIds(@Param("metricsIds") List<String> metricsIds);

    @Select(value = "select * from metrics where tenant_id = #{tenantId} and metrics_name=#{metricsName}  ")
    MetricsDO getMetricsByNameOfTenant(@Param("tenantId") String tenantId, @Param("metricsName") String metricsName);

    @Select(value = "select * from metrics where tenant_id = #{tenantId} and metrics_name=#{metricsName}  ")
    MetricsDO findMetricsByNameAndTenantId(@Param("metricsName") String metricsName, @Param("tenantId") String tenantId);

    @Select(value = "<script>" +
            "select *  from metrics where enabled = 1 and id  in " +
            "<foreach collection=\"metricsIds\" item=\"item\" index=\"index\" open=\"(\" close=\")\" separator=\",\">" +
            "  #{item}" +
            "</foreach>" +
            "</script>"
    )
    List<MetricsDO> listEnableMetricsByMetricsIds(@Param("metricsIds") List<String> metricsIds);

    @Select(value = "<script>" +
            "select *  from metrics where id  in " +
            "<foreach collection=\"metricsIds\" item=\"item\" index=\"index\" open=\"(\" close=\")\" separator=\",\">" +
            "  #{item}" +
            "</foreach>" +
            "</script>"
    )
    List<MetricsDO> listMetricsByMetricsIds(@Param("metricsIds") List<String> metricsIds);

    @Select(value = "<script>" +
            "select *  from metrics_field where metrics_id  in " +
            "<foreach collection=\"metricsIds\" item=\"item\" index=\"index\" open=\"(\" close=\")\" separator=\",\">" +
            "  #{item}" +
            "</foreach>" +
            "</script>"
    )
    List<MetricsFieldDO> listMetricsFieldByMetricsIds(@Param("metricsIds") List<String> metricsIds);

    @Select("SELECT * FROM metrics")
    List<MetricsDO> listAll();

    @Select("SELECT count(id) FROM metrics where id = #{id}")
    int getCountById(@Param("id") String id);

    @Update("update metrics set tag_names=#{tagNames} where id = #{id} ")
    void editMetricsTags(MetricsDO targetMetricsDO);

    @Update("update metrics set enabled = #{enabled}, modify_time=#{modifyTime}, editor = #{editor} where id = #{id}")
    void editMetricsStatus(MetricsDO metricsDO);

    @Update(value = "<script>" +
            "update metrics " +
            "SET document_link = CASE " +
            "  <foreach collection=\"metricsLinks\" item=\"metric\" separator=\" \">" +
            "    WHEN metrics_name = #{metric.metricsName} THEN #{metric.documentLink} " +
            "  </foreach> " +
            "END " +
            "WHERE tenant_id IN" +
            "<foreach collection=\"tenantIds\" item=\"tenantId\" open=\"(\" close=\")\" separator=\",\">" +
            "  #{tenantId}" +
            "</foreach>" +
            "AND metrics_name IN" +
            "<foreach collection=\"metricsLinks\" item=\"metric\" open=\"(\" close=\")\" separator=\",\">" +
            "  #{metric.metricsName}" +
            "</foreach>" +
            "</script>"
    )
    void editMetricsDocumentLink(@Param("metricsLinks") List<MetricsDO> metricsLinks, @Param("tenantIds")List<String> tenantIds);

    @Select("SELECT m.* FROM metrics m LEFT JOIN collector_metrics cm ON m.id=cm.metrics_id WHERE cm.collector_id=#{collectorId}")
    List<MetricsDO> listMetricsByCollectorId(@Param("collectorId") String collectorId);

    @Select("SELECT metrics_name FROM metrics WHERE tenant_id=#{tenantId}")
    List<String> listAllMetricsName(@Param("tenantId") String tenantId);

    @Update(value = "<script>" +
            "<foreach collection=\"metricsDOList\" item=\"item\" index=\"index\" open=\"\" close=\"\" separator=\";\">" +
            "update metrics " +
            "<set>" +
            "tag_names = #{item.tagNames}, modify_time=#{item.modifyTime}" +
            "</set>" +
            "where id=#{item.id}" +
            "</foreach>" +
            "</script>")
    int batchUpdateMetrics(@Param("metricsDOList") List<MetricsDO> metricsDOList);

    @Select(value = "<script>" +
            "select * from metrics " +
            " where tenant_id = #{tenantId} and metrics_name in " +
            "<foreach collection=\"metricsNames\" item=\"item\" index=\"index\" open=\"(\" close=\")\" separator=\",\">" +
            "  #{item}" +
            "</foreach>" +
            "</script>")
    List<MetricsDO> findMetricsByNames(@Param("tenantId") String tenantId, @Param("metricsNames") List<String> metricsNames);

    @Select("SELECT m.* FROM metrics m where m.id = (select ad.metric_id from alarm_definition ad where ad.name = #{alarmName} and ad.tenant_id =#{tenantId})")
    MetricsDO listMetricsByAlarm(@Param("tenantId") String tenantId, @Param("alarmName") String alarmName);

    @Select("<script>" +
            "select id, tenant_id, metrics_name from metrics  where tenant_id  in " +
            "<foreach collection=\"tenantIds\" item=\"item\" index=\"index\" open=\"(\" close=\")\" separator=\",\">" +
            "  #{item}" +
            "</foreach>" +
            "</script>")
    List<MetricsDO> listMetricsByTenantIds(@Param("tenantIds") List<String> tenantIds);

    @Select(value = """
            select * from metrics 
            where tenant_id = #{tenantId} and id=#{id}  
            """)
    MetricsDO findMetricsByIdAndTenantId(@Param("id") String id, @Param("tenantId") String tenantId);

    @Update("update metrics set tag_names = #{tagNames}, origin_period=#{originPeriod}, modify_time=#{modifyTime}, editor=#{editor}, enabled=#{enabled}, document_link = #{documentLink} where id = #{id}")
    void newUpdateMetrics(MetricsDO metricsDO);

    @Update("update metrics set tag_names = #{tagNames}, modify_time=#{modifyTime}, editor=#{editor} where id = #{id} ")
    int updateMetricsTags(MetricsDO metricsDO);

}
