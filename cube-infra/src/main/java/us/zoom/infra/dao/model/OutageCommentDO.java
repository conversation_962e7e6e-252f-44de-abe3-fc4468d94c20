package us.zoom.infra.dao.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * OutageDO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OutageCommentDO {
    private String id;
    private String outageId;
    private String commentId;
    private String parentCommentId;
    private List<OutageCommentDO> commentList;
    private String content;
    private Date createTime;
    private Date modifyTime;
    private String creator;
    private String lastModifiedBy;
    private String mentionedUserIds;
    private String type;
}
