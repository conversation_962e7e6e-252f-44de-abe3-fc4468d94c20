package us.zoom.infra.dao.service;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;
import us.zoom.infra.dao.model.RcaCommentDO;
import us.zoom.infra.dao.model.RcaDO;
import us.zoom.infra.dao.model.RcaLinkDO;

import java.io.Serializable;
import java.util.List;

@Mapper
@Repository
public interface RcaDAO extends Serializable {

    @Select(value = "<script>" +
            " select * from rca" +
            " where  is_deleted = 0 " +
            " <if test=\"title != null and title != ''\"> and title LIKE CONCAT('%', #{title}, '%')</if>" +
            " <if test=\"sevLevel !=null and sevLevel !=''\"> and   sev_level = #{sevLevel}</if>" +
            " <if test=\"status !=null and status !=''\"> and status = #{status}</if>" +
            " <if test=\"startTime != null\"> and create_time &gt;= #{startTime}</if>" +
            " <if test=\"endTime != null\"> and create_time &lt;= #{endTime}</if>" +
            " order by create_time desc limit #{pageIndex},#{pageSize}" +
            "</script>")
    List<RcaDO> searchRcaList(@Param("title") String title, @Param("sevLevel") String sevLevel, @Param("status") String status, @Param("startTime") String startTime, @Param("endTime") String endTime, @Param("pageIndex") int pageIndex, @Param("pageSize") int pageSize);


    @Select(value = "<script>" +
            " select count(id) from rca" +
            " where  is_deleted = 0 " +
            " <if test=\"title != null and title != ''\"> and title LIKE CONCAT('%', #{title}, '%')</if>" +
            " <if test=\"sevLevel !=null and sevLevel !=''\"> and   sev_level = #{sevLevel}</if>" +
            " <if test=\"status !=null and status !=''\"> and status = #{status}</if>" +
            " <if test=\"startTime != null\"> and create_time &gt;= #{startTime}</if>" +
            " <if test=\"endTime != null\"> and create_time &lt;= #{endTime}</if>" +
            "</script>")
    Integer getRcaListCount(@Param("title") String title, @Param("sevLevel") String sevLevel, @Param("status") String status, @Param("startTime") String startTime, @Param("endTime") String endTime, @Param("pageIndex") int pageIndex, @Param("pageSize") int pageSize);


    @Insert("Insert into rca(id,title,description,sev_level,product_type,root_cause)values(#{id},#{title},#{description},#{sevLevel},#{productType},#{rootCause})")
    void add(RcaDO rcaDO);

    @Update("<script>" +
            "UPDATE rca " +
            "<set>" +
            "<if test='title != null'> title = #{title}, </if>" +
            "<if test='description != null'> description = #{description}, </if>" +
            "<if test='sevLevel != null'> sev_level = #{sevLevel}, </if>" +
            "<if test='productType != null'> product_type = #{productType}, </if>" +
            "<if test='serviceProductImpact != null'> service_product_impact = #{serviceProductImpact}, </if>" +
            "<if test='failureType != null'> failure_type = #{failureType}, </if>" +
            "<if test='failureTypeClass != null'> failure_type_class = #{failureTypeClass}, </if>" +
            "<if test='rootCause != null'> root_cause = #{rootCause}, </if>" +
            "<if test='rootCauseClass != null'> root_cause_class = #{rootCauseClass}, </if>" +
            "<if test='problemOwnerTeam != null'> problem_owner_team = #{problemOwnerTeam}, </if>" +
            "<if test='environment != null'> environment = #{environment}, </if>" +
            "<if test='rcaOwner != null'> rca_owner = #{rcaOwner}, </if>" +
            "<if test='rcaWiki != null'> rca_wiki = #{rcaWiki}, </if>" +
            "<if test='zoomModule != null'> zoom_module = #{zoomModule}, </if>" +
            "<if test='seTicket != null'> se_ticket = #{seTicket}, </if>" +
            "<if test='status != null'> status = #{status}, </if>" +
            "<if test='creator != null'> creator = #{creator}, </if>" +
            "<if test='lastModifiedBy != null'> last_modified_by = #{lastModifiedBy}, </if>" +
            "<if test='assignee != null'> assignee = #{assignee}, </if>" +
            "<if test='assigneeManager != null'> assignee_manager = #{assigneeManager}, </if>" +
            "<if test='priority != null'> priority = #{priority}, </if>" +
            "<if test='rcaReviewed != null'> rca_reviewed = #{rcaReviewed}, </if>" +
            "<if test='impact != null'> impact = #{impact}, </if>" +
            "<if test='labels != null'> labels = #{labels}, </if>" +
            "<if test='dueDate != null'> due_date = #{dueDate}, </if>" +
            "<if test='startDate != null'> start_date = #{startDate}, </if>" +
            "</set>" +
            "WHERE id = #{id}" +
            "</script>")
    void updateRca(RcaDO rcaDO);



    @Update("update rca set is_deleted = 1  where id=#{id}")
    void delete(@Param("id") String id);

    @Select("select * from rca where id=#{id}")
    RcaDO getById(@Param("id") String id);

    @Update("update rca set status=#{status},last_modified_by = #{lastModifiedBy} where id =#{id} and status=#{fromStatus}")
    void updateStatus(@Param("status") String status,@Param("lastModifiedBy") String lastModifiedBy,@Param("id") String id, @Param("fromStatus") String fromStatus);


    @Select("select *  FROM rca_link WHERE rca_id = #{id}")
    List<RcaLinkDO> selectLinks(@Param("id") String id);

    @Delete("DELETE FROM rca_link WHERE rca_id = #{id}")
    void deleteLinks(@Param("id") String id);


    @Insert({
            "<script>",
            "INSERT INTO rca_link (id, rca_id, link_desc, link_url) VALUES ",
            "<foreach collection='rcaLinkDOS' item='item' separator=','>",
            "(#{item.id}, #{item.rcaId}, #{item.linkDesc}, #{item.linkUrl})",
            "</foreach>",
            "</script>"
    })
    void addLinks(@Param("rcaLinkDOS") List<RcaLinkDO> rcaLinkDOS);

    @Insert("Insert into rca_comment(id,rca_id,comment_id,parent_comment_id,content,creator,mentioned_user_ids,type)values(#{id},#{rcaId},#{commentId},#{parentCommentId},#{content},#{creator},#{mentionedUserIds},#{type})")
    void comment(RcaCommentDO rcaCommentDO);

    @Update("UPDATE rca_comment " +
            "SET content = #{content} " +
            "WHERE id = #{id}")
    void updateContentById(@Param("id") String id, @Param("content") String content);



    @Select("select * from rca_comment where rca_id=#{rcaId}  and is_deleted = 0  order by create_time desc ")
    List<RcaCommentDO> searchRcaCommentList(@Param("rcaId") String rcaId);


    @Update("update rca_comment set is_deleted = 1  where rca_id=#{rcaId} and comment_id=#{commentId}")
    void deleteParentComment(@Param("rcaId") String rcaId, @Param("commentId") String commentId);


    @Update("update rca_comment set is_deleted = 1  where rca_id=#{rcaId} and parent_comment_id=#{commentId}")
    void deleteChildComment(@Param("rcaId") String rcaId, @Param("commentId") String commentId);


    @Select("select * from rca where title=#{title}  and  is_deleted = 0 LIMIT 1")
    RcaDO searchByTitle(@Param("title") String title);
}
