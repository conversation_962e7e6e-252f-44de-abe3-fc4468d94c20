package us.zoom.infra.dao.model;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023-04-26 15:21
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AlarmDefinitionRuleConditionDO {
    private String id;
    private Integer conditionType;
    private String expression;
    private Integer conditionsOrder;
    private String name;
    private String mapKey;
    private String operator;
    private String alarmRuleId;
    private String threshold;
    /**
     * histogram&summary
     * histogram([1,2,10,30],[1,10,15,20],${histogramQuantile})>12
     */
    private String histogramQuantile;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime = new Date();
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime = new Date();

    private String alarmDefinitionId;

    private String extension;
}
