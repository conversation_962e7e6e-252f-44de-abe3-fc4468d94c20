package us.zoom.infra.dao.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * OutageDO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RcaDO {
    private String id;
    private String title;
    private String description;
    private String sevLevel;
    private String productType;
    private String serviceProductImpact;
    private String failureType;
    private String failureTypeClass;
    private String rootCause;
    private String rootCauseClass;
    private String problemOwnerTeam;
    private String environment;
    private String rcaOwner;
    private String rcaWiki;
    private String zoomModule;
    private String seTicket;
    private String status;
    private boolean isDeleted;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;
    private String creator;
    private String lastModifiedBy;
    private String assignee;
    private String assigneeManager;
    private String priority;
    private String rcaReviewed;
    private String impact;
    private String labels;
    private String dueDate;
    private String startDate;
    private List<RcaLinkDO> rcaLinkDOS;
}
