package us.zoom.infra.dao.service;

import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;
import us.zoom.infra.dao.model.ResourceTagDo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/27 15:19
 */
@Mapper
@Repository
public interface ResourceTagDAO {

    @Insert("insert into resource_tag (id, tag_id, resource,tenant_id, resource_type , gmt_create, creator, modifier ) values (#{id}, #{tagId}, #{resource}, #{tenantId}, #{resourceType}, #{gmtCreate}, #{creator},#{modifier})")
    void add(ResourceTagDo resourceTagDo);

    @Select("select * from resource_tag where id = #{id}")
    ResourceTagDo findById(@Param("id") String id);


    @Select("select * from resource_tag where tag_id = #{tagId}")
    List<ResourceTagDo> findByTagId(@Param("tagId") String tagId);

    @Select("<script>" +
            " SELECT * FROM resource_tag WHERE tag_id IN " +
            " <foreach item='tagId' collection='tagIds' open='(' separator=',' close=')'>" +
            "  #{tagId}" +
            " </foreach>" +
            " </script>")
    List<ResourceTagDo> findByTagIds(@Param("tagIds") List<String> tagIds);


    @Delete("delete from resource_tag where id = #{id}")
    int deleteById(@Param("id") String id);

    @Delete("delete from resource_tag where resource = #{resourceTag.resource} and resource_type = #{resourceTag.resourceType} and tag_id = #{resourceTag.tagId} and tenant_id = #{resourceTag.tenantId}")
    int deleteResourceTag(@Param("resourceTag") ResourceTagDo resourceTagDo);

    @Delete("delete from resource_tag where resource = #{resource} and resource_type = #{resourceType}")
    int deleteResourceTagByName(@Param("resource") String resource, @Param("resourceType") String resourceType);


    @Delete("<script>" +
            "DELETE FROM resource_tag WHERE resource IN " +
            "<foreach collection='resources' item='resource' open='(' separator=',' close=')'>" +
            "   #{resource} " +
            "</foreach> " +
            "AND resource_type = #{resourceType}" +
            "</script>")
    int deleteResourceTagByNames(@Param("resources") List<String> resource, @Param("resourceType") String resourceType);

    @Delete("<script>" +
            " DELETE FROM resource_tag WHERE " +
            " tag_id IN " +
            " <foreach collection='tagIds' item='tagId' open='(' separator=',' close=')'> " +
            "  #{tagId} " +
            " </foreach> " +
            " </script>")
    int deleteResourceByTagsId(@Param("tagIds") List<String> tagIds);


    @Delete("<script>" +
            " DELETE FROM resource_tag WHERE " +
            " tag_id IN " +
            " <foreach collection='resourceTags' item='item' open='(' separator=',' close=')'> " +
            "  #{item.tagId} " +
            " </foreach> " +
            " AND resource_type = #{resourceType} " +
            " AND tenant_id = #{tenantId} " +
            " </script>")
    int deleteResourceTags(@Param("resourceTags") List<ResourceTagDo> resourceTags,
                           @Param("resourceType") String resourceType,
                           @Param("tenantId") String tenantId);



    @Select(value ="<script>" +
            "select * from resource_tag where resource in "+
            "<foreach collection=\"resourceIds\" item=\"resource\"  open=\"(\" close=\")\" separator=\",\">" +
            "  #{resource}" +
            "</foreach>" +
            " and tenant_id = #{tenantId}" +
            " and resource_type = #{resourceType}" +
            "</script>"
    )
    List<ResourceTagDo> findByResourceIds(@Param("resourceIds") List<String> resourceIds,
                                          @Param("resourceType") String resourceType, @Param("tenantId") String tenantId);

    @Select(value ="<script>" +
            "select * from resource_tag rt " +
            "left join tag t on rt.tag_id = t.id " +
            "left join tag_type tt ON t.tag_type_id = tt.id " +
            "where rt.resource = #{resourceId} " +
            " and rt.tenant_id = #{tenantId} " +
            " and rt.resource_type = #{resourceType} " +
            " and tt.source = 1" +
            "</script>"
    )
    List<ResourceTagDo> findByResourceId(@Param("resourceId") String resourceId,
                                          @Param("resourceType") String resourceType, @Param("tenantId") String tenantId);

    @Select(value ="<script>" +
            "select tenant_id, resource, resource_type from resource_tag where tag_id in "+
            "<foreach collection=\"tagIds\" item=\"tagId\"  open=\"(\" close=\")\" separator=\",\">" +
            "  #{tagId}" +
            "</foreach>" +
            " <if test=\"resource !=null and resource !=''\"> and resource like \"%\"#{resource}\"%\"</if>" +
            " <if test=\"resourceType !=null and resourceType !=''\"> and resource_type = #{resourceType}</if>" +
            " <if test=\"tenantId != null and tenantId !=''\"> and tenant_id =  #{tenantId} </if>" +
            " group by resource, resource_type , tenant_id having count(distinct(tag_id)) = #{tagCount} " +
            " limit #{offset}, #{limit}" +
            "</script>"
    )
    List<ResourceTagDo> findResourceByTagIds(
            @Param("tenantId") String tenantId,
            @Param("tagIds") List<String> tagIds,
                                             @Param("resource") String resource,
                                             @Param("resourceType") String resourceType,
                                             @Param("tagCount") Integer tagCount,
                                             @Param("offset") Integer offset,
                                             @Param("limit")  Integer limit );

    @Select(value ="<script>" +
            "select tenant_id, resource, resource_type from resource_tag where tag_id in "+
            "<foreach collection=\"tagIds\" item=\"tagId\"  open=\"(\" close=\")\" separator=\",\">" +
            "  #{tagId}" +
            "</foreach>" +
            " <if test=\"resource !=null and resource !=''\"> and resource like \"%\"#{resource}\"%\"</if>" +
            " <if test=\"resourceType !=null and resourceType !=''\"> and resource_type = #{resourceType}</if>" +
            " <if test=\"tenantId != null and tenantId !=''\"> and tenant_id =  #{tenantId} </if>" +
            " group by resource, resource_type , tenant_id" +
            " limit #{offset}, #{limit}" +
            "</script>"
    )
    List<ResourceTagDo> findResourceByTagIdsOrPage(
            @Param("tenantId") String tenantId,
            @Param("tagIds") List<String> tagIds,
            @Param("resource") String resource,
            @Param("resourceType") String resourceType,
            @Param("offset") Integer offset,
            @Param("limit")  Integer limit );


    @Select(value ="<script>" +
            "select tenant_id, resource, resource_type from resource_tag where tag_id in "+
            "<foreach collection=\"tagIds\" item=\"tagId\"  open=\"(\" close=\")\" separator=\",\">" +
            "  #{tagId}" +
            "</foreach>" +
            " <if test=\"resource !=null and resource !=''\"> and resource like \"%\"#{resource}\"%\"</if>" +
            " <if test=\"resourceType !=null and resourceType !=''\"> and resource_type = #{resourceType}</if>" +
            " <if test=\"tenantId != null and tenantId !=''\"> and tenant_id =  #{tenantId} </if>" +
            " group by resource, resource_type , tenant_id " +
            "</script>"
    )
    List<ResourceTagDo> findResourceByTagIdsOr(
            @Param("tenantId") String tenantId,
            @Param("tagIds") List<String> tagIds,
            @Param("resource") String resource,
            @Param("resourceType") String resourceType);


    @Select(value ="<script>" +
            "select count(*) from (" +
            "select count(distinct (resource)) from resource_tag where tag_id in "+
            "<foreach collection=\"tagIds\" item=\"tagId\"  open=\"(\" close=\")\" separator=\",\">" +
            "  #{tagId}" +
            "</foreach>" +
            " <if test=\"resource != null and resource !=''\"> and resource like \"%\"#{resource}\"%\"</if>" +
            " <if test=\"resourceType != null and resourceType !=''\"> and resource_type = #{resourceType}</if>" +
            " <if test=\"tenantId != null and tenantId !=''\"> and tenant_id =  #{tenantId} </if>" +
            " group by resource, resource_type  having count(distinct(tag_id)) = #{tagCount} " +
            " ) as t1" +
            "</script>"
    )
    int countResourceByTagIds(
            @Param("tenantId") String tenantId,
            @Param("tagIds") List<String> tagIds,
                                             @Param("resource") String resource,
                                             @Param("resourceType") String resourceType,
                                             @Param("tagCount") Integer tagCount);


    @Select(value ="<script>" +
            "select count(*) from (" +
            "select count(distinct (resource)) from resource_tag where tag_id in "+
            "<foreach collection=\"tagIds\" item=\"tagId\"  open=\"(\" close=\")\" separator=\",\">" +
            "  #{tagId}" +
            "</foreach>" +
            " <if test=\"resource != null and resource !=''\"> and resource like \"%\"#{resource}\"%\"</if>" +
            " <if test=\"resourceType != null and resourceType !=''\"> and resource_type = #{resourceType}</if>" +
            " <if test=\"tenantId != null and tenantId !=''\"> and tenant_id =  #{tenantId} </if>" +
            " group by resource, resource_type " +
            " ) as t1" +
            "</script>"
    )
    int countResourceByTagIdsOr(
            @Param("tenantId") String tenantId,
            @Param("tagIds") List<String> tagIds,
            @Param("resource") String resource,
            @Param("resourceType") String resourceType);


    @Insert(value ="<script>" +
            "INSERT INTO resource_tag  (id, tag_id, resource,tenant_id, resource_type , gmt_create, creator, modifier) values "+
            "<foreach collection=\"resourceTags\" item=\"resourceTag\"  separator=\",\">" +
            " (#{resourceTag.id}, #{resourceTag.tagId}, #{resourceTag.resource}, #{resourceTag.tenantId}, #{resourceTag.resourceType}, #{resourceTag.gmtCreate}, #{resourceTag.creator}, #{resourceTag.modifier})" +
            "</foreach>" +
            " ON DUPLICATE KEY UPDATE modifier = VALUES(modifier) " +
            "</script>"
    )
    int batchAddResourceTag(@Param("resourceTags") List<ResourceTagDo> resourceTagDos);
}


