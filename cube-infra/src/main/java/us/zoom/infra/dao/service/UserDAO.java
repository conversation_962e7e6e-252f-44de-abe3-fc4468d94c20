package us.zoom.infra.dao.service;


import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;
import us.zoom.infra.dao.model.UserDO;

import java.util.List;
import java.util.Set;

@Mapper
@Repository
public interface UserDAO {

    @Select("select * from user where name=#{name}")
    UserDO getUserByName(@Param("name") String name);

    @Select("select * from user where id=#{userId} ")
    UserDO getUserById(@Param("userId") String userId);

    @Insert("insert into user(id,name,password_new,create_time,modify_time,role)values(#{id},#{name},#{passwordNew},now(),now(),#{role})")
    void add(UserDO userDO);

    @Update("update user set modify_time=now(),current_tenant=#{tenantId}  where id=#{userId}")
    void setTenant(@Param("userId") String userId,@Param("tenantId") String tenantId);

    @Select(value="<script>" +
            "select *  from user  " +
            " where name like \"%\"#{name}\"%\"  " +
            "   and status = 0 " +
            " order by create_time desc limit #{pageIndex},#{pageSize}" +
            "</script>")
    List<UserDO> findByNameLike(@Param("name") String name,  @Param("pageIndex") int pageIndex, @Param("pageSize") int pageSize);


    @Select(value="<script>" +
            "select * from user " +
            " where name is not null and name != '' " +
            " and status = 0 " +
            " <if test='name != null and name != \"\"'> " +
            "   and name like concat('%', #{name}, '%') " +
            " </if> " +
            " order by create_time desc limit #{pageIndex},#{pageSize}" +
            "</script>")
    List<UserDO> searchAlarmMentionUser(@Param("name") String name, @Param("pageIndex") int pageIndex, @Param("pageSize") int pageSize);

    @Select(value="<script>" +
            "select count(id) from user " +
            " where  name like \"%\"#{name}\"%\" "+
            "   and status = 0 " +
            "</script>")
    int getCountByNameLike(@Param("name") String name);

    @Update("<script>" +
            "update user set role=#{obj.role}" +
            " <if test=\"resetPass != null and resetPass==true \"> " +
            ", password_new = #{obj.passwordNew}" +
            "</if>" +
            ",modify_time=now() where id=#{obj.id}" +
            "</script>")
    void editUser(@Param("obj") UserDO userDO,@Param("resetPass") Boolean resetPass);

    @Delete("delete from user where id=#{id}")
    void delUser(@Param("id") String id);

    @Select("select * from user where status = 0")
    List<UserDO> listAll();

    @Select("select * from user where role=#{role} ")
    List<UserDO> findByRole(@Param("role") String role);

    @Select("<script>" +
            "select * from user where id in" +
            "<foreach collection=\"ids\" item=\"item\" index=\"index\" open=\"(\" close=\")\" separator=\",\">" +
            "  #{item}" +
            "</foreach>" +
            "</script>")
    List<UserDO> listByIds(@Param("ids") List<String> ids);

    @Update("<script>" +
            "update user set status=#{status} where id in" +
            "<foreach collection=\"userIds\" item=\"item\" index=\"index\" open=\"(\" close=\")\" separator=\",\">" +
            "  #{item}" +
            "</foreach>" +
            "</script>")
    void batchUpdateStatus(@Param("userIds") List<String> userIds, @Param("status") Integer status);

    @Select(value = "<script>" +
            "select * from user where id in " +
            "<foreach collection=\"ids\" item=\"item\" index=\"index\" open=\"(\" close=\")\" separator=\",\">" +
            "  #{item}" +
            "</foreach>" +
            "</script>")
    List<UserDO> getByIds(@Param("ids") Set<String> ids);

    @Select("select name from user where id=#{userId} ")
    String getNameById(@Param("userId") String userId);

    @Select(value = "<script>" +
            "select name from user where name in " +
            "<foreach collection=\"users\" item=\"item\" index=\"index\" open=\"(\" close=\")\" separator=\",\">" +
            "  #{item}" +
            "</foreach>" +
            "</script>")
    List<String> listExistUser(@Param("users") List<String> users);

    @Select("<script>" +
            "select * from user where name in" +
            "<foreach collection=\"ids\" item=\"item\" index=\"index\" open=\"(\" close=\")\" separator=\",\">" +
            "  #{item}" +
            "</foreach>" +
            "</script>")
    List<UserDO> listByName(@Param("ids") List<String> nameList);

}
