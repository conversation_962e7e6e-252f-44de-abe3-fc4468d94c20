package us.zoom.infra.dao.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.reflection.DefaultReflectorFactory;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;
import org.apache.ibatis.session.ResultHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import us.zoom.cube.sdk.util.MonitorLogReporter;
import us.zoom.infra.monitor.DatabaseSqlMonitor;
import us.zoom.infra.utils.IpUtils;

import java.sql.Statement;
import java.util.Properties;

@Slf4j
@Intercepts({@Signature(type = StatementHandler.class, method = "query", args = {Statement.class, ResultHandler.class}),
        @Signature(type = StatementHandler.class, method = "update", args = {Statement.class}),
        @Signature(type = StatementHandler.class, method = "batch", args = {Statement.class})})
@Component
public class MybatisInterceptor implements Interceptor {

    private final Logger monitorLog = LoggerFactory.getLogger("Monitor");
    // monitor switch
    @Value("${monitor.sql.enable:false}")
    private String enableMonitor;

    // threshold to monitor
    @Value("${monitor.sql.threshold:100}")
    private Long threshold;

    private static final int MAX_SQL_LENGTH = 200;
    private static final String MAPPED_STATEMENT = "delegate.mappedStatement";

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        Object target = invocation.getTarget();
        long begin = System.currentTimeMillis();
        StatementHandler statementHandler = (StatementHandler) target;
        Object proceed = invocation.proceed();
        monitorSql(begin, statementHandler);

        return proceed;
    }

    private void monitorSql(long begin, StatementHandler statementHandler) {
        if (!BooleanUtils.toBoolean(enableMonitor)) {
            return;
        }
        long end = System.currentTimeMillis();

        // if reach threshold
        if ((end - begin) > threshold) {
            try {
                // method and type
                MetaObject metaObject = MetaObject.forObject(statementHandler, SystemMetaObject.DEFAULT_OBJECT_FACTORY, SystemMetaObject.DEFAULT_OBJECT_WRAPPER_FACTORY, new DefaultReflectorFactory());
                MappedStatement mappedStatement = (MappedStatement) metaObject.getValue(MAPPED_STATEMENT);
                String methodName = mappedStatement.getId();
                String sqlType = mappedStatement.getSqlCommandType().toString();
                BoundSql boundSql = statementHandler.getBoundSql();
                String sql = boundSql.getSql();
                sql = StringUtils.substring(sql, 0, MAX_SQL_LENGTH);
                // parameters map
                //Object parameterObject = boundSql.getParameterObject();
                // parameters
                //List<ParameterMapping> parameterMappingList = boundSql.getParameterMappings();
                DatabaseSqlMonitor.DatabaseSqlMonitorBuilder builder = DatabaseSqlMonitor.builder();
                builder.cost(end - begin);
                builder.sql(sql);
                builder.sqlType(sqlType);
                builder.methodName(methodName);
                builder.privateIp(IpUtils.getLocalIP());

                MonitorLogReporter.report(monitorLog, builder.build());
            } catch (Exception e) {
                log.error("monitor sql error,", e);
            }
        }

    }


    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {

    }
}