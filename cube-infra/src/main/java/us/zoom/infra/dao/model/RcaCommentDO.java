package us.zoom.infra.dao.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * RcaCommentDO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RcaCommentDO {
    private String id;
    private String rcaId;
    private String commentId;
    private String parentCommentId;
    private List<RcaCommentDO> commentList;
    private String content;
    private Date createTime;
    private Date modifyTime;
    private String creator;
    private String lastModifiedBy;
    private String mentionedUserIds;
    private String type;
}
