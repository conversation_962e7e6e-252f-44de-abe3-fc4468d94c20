package us.zoom.infra.model.alarm;

import com.google.common.collect.Maps;
import com.zoom.op.monitor.domain.alarm.ConditionType;
import com.zoom.op.monitor.domain.alarm.RuleCondition;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;
import us.zoom.cube.lib.common.CubeConstants;
import us.zoom.infra.enums.FieldTypeEnum;
import us.zoom.cube.lib.utils.expression.CustomAviatorUtils;
import us.zoom.infra.influx.model.alarm.AiFilterVarV2;

import java.util.Map;
import java.util.Optional;
import java.util.StringJoiner;

import static us.zoom.infra.utils.AlarmConstants.*;

/**
 * <AUTHOR>
 * @create 2019/10/28 3:51
 * <p>
 * ，AlarmRuleSingleContent
 */

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AlarmRuleSingleContent {


    public static final String ATTACK_DATA_LIST_NAME = "_attack_data";
    private final static String REPLACE_REGEX = "\\(|\\)|\\.";

    /**
     * {@link CompareType}
     */
    CompareType type;

    String tag;

    String field;

    /**
     * number, string, mapString, mapNumber
     */
    FieldTypeEnum fieldType;

    /**
     * For map type fields, this field stores the key of the map
     */
    String mapKey;

    /**
     * !=, ==, >, >=, <, <=, crIncrease, crDecrease, in, not-in
     */
    String operator;

    /**
     * histogram
     */
    String histogramQuantile;
    /**
     * bucket [1,2,3,4]
     */
    String histogramBucket;

    /**
     * Comparing value with the field value using the operator
     */
    String cmpValue;

    /**
     * Aviator，：c<3， d==4
     */
    String expression;

    /**
     * extension
     */
    AlarmRuleConditionExtension extension;

    // replace the field or tag, except cmpValue
    private String buildTagExpression() {

        String tagName = Optional.ofNullable(tag).map(
                value -> value.replaceAll(REPLACE_REGEX, BRACKET_REPLACER)
        ).orElse(StringUtils.EMPTY);

        if (StringUtils.equalsIgnoreCase(operator, INCLUDE_EXPR)
                || StringUtils.equalsIgnoreCase(operator, EXCLUDE_EXPR)) {

            return (StringUtils.equalsIgnoreCase(operator, EXCLUDE_EXPR) ? "!" : "")
                    + buildIncludeExpression(tagName, cmpValue);
        }

        StringBuilder sb = new StringBuilder();
        if (StringUtils.isNotBlank(tagName)) {
            sb.append(tagName);
        }
        sb.append(" ");
        if (null != operator) {
            sb.append(getOp());
        }

        sb.append(" ");
        sb.append("'");
        sb.append(cmpValue);
        sb.append("'");

        return sb.toString();
    }

    private String buildIncludeExpression(String fieldName, String cmpValue) {

        StringBuilder sb = new StringBuilder();

        sb.append("include(seq.list('");
        sb.append(StringUtils.replace(cmpValue, ",", "','"));
        sb.append("'), ").append(fieldName).append(")");

        return sb.toString();
    }

    // replace the field or tag, except cmpValue
    private String buildExpression() {

        if (CompareType.FUNC_COMPARE.equals(type)) {
            return expression;
        }

        String tagOrFiled = Optional.ofNullable(CompareType.TAG_COMPARE.equals(type) ? tag : field)
                .map( value -> value.replaceAll(REPLACE_REGEX, BRACKET_REPLACER))
                .orElse(StringUtils.EMPTY);

        if (StringUtils.equalsIgnoreCase(operator, INCLUDE_EXPR)
                || StringUtils.equalsIgnoreCase(operator, EXCLUDE_EXPR)) {

            if (fieldType == FieldTypeEnum.mapString || fieldType == FieldTypeEnum.mapNumber) {
                String fieldAccess = tagOrFiled.replaceAll(REPLACE_REGEX, BRACKET_REPLACER);
                String keyAccess = fieldAccess + "['" + mapKey + "']";
                String includeExpr = buildIncludeExpression(keyAccess, cmpValue);
                String finalExpr = (StringUtils.equalsIgnoreCase(operator, EXCLUDE_EXPR) ? "!" : "") + includeExpr;

                return buildMapTypeExpression(tagOrFiled, finalExpr);
            } else {
                return (StringUtils.equalsIgnoreCase(operator, EXCLUDE_EXPR) ? "!" : "")
                        + buildIncludeExpression(tagOrFiled, cmpValue);
            }
        }

        if (fieldType == FieldTypeEnum.mapString || fieldType == FieldTypeEnum.mapNumber) {
            String fieldAccess = tagOrFiled.replaceAll(REPLACE_REGEX, BRACKET_REPLACER);
            String keyAccess = fieldAccess + "['" + mapKey + "']";

            // Build inner expression: map_field['key'] operator value
            StringBuilder innerExpr = new StringBuilder();
            innerExpr.append(keyAccess);
            if (null != operator) {
                innerExpr.append(" ").append(getOp()).append(" ");
            }
            if (fieldType.equals(FieldTypeEnum.mapString)) {
                innerExpr.append("'").append(cmpValue).append("'");
            } else {
                innerExpr.append(cmpValue);
            }

            return buildMapTypeExpression(tagOrFiled, innerExpr.toString());
        }

        StringBuilder sb = new StringBuilder();
        if (StringUtils.isNotBlank(tagOrFiled)) {
            sb.append(tagOrFiled.replaceAll(REPLACE_REGEX, BRACKET_REPLACER));
        }
        sb.append(" ");
        if (null != operator) {
            sb.append(getOp());
        }

        sb.append(" ");
        if (fieldType.equals(FieldTypeEnum.string)) {
            sb.append("'");
            sb.append(cmpValue);
            sb.append("'");
        } else {
            sb.append(cmpValue);
        }

        return sb.toString();
    }

    /**
     * Build expression for map type fields with null safety check
     * @param tagOrField the field or tag name (already processed)
     * @param innerExpression the inner comparison expression
     * @return complete expression with null check: (map_field['key'] != nil) && (inner_expression)
     */
    private String buildMapTypeExpression(String tagOrField, String innerExpression) {
        String fieldAccess = tagOrField.replaceAll(REPLACE_REGEX, BRACKET_REPLACER);
        String keyAccess = fieldAccess + "['" + mapKey + "']";
        return "(" + keyAccess + " != nil) && (" + innerExpression + ")";
    }

    private String getOp() {
        String op;
        switch (operator) {
            case "crIncrease":
            case "crDecrease":
                op = ">=";
                break;
            case "yoyIncrease":
            case "yoyDecrease":
                op = ">=";
                break;
            default:
                op = operator;
        }
        return op;
    }

    public boolean isMatchTag(Object tagValue) {
        Map<String, Object> env = Maps.newHashMap();
        // group by ， url == 'www.baidu.com'; status > 200
        if (CompareType.TAG_COMPARE.equals(type)) {
            String buildExpression = buildTagExpression();
            env.put(
                    Optional.ofNullable(tag).map(
                            value -> value.replaceAll(REPLACE_REGEX, BRACKET_REPLACER)
                    ).orElse(StringUtils.EMPTY),
                    tagValue);
            return (boolean) CustomAviatorUtils.execute(buildExpression, env, true);
        }
        return false;
    }

    public boolean isMatchMetrics(Map<String, Object> innerMap, String metricsId) {
        if (StringUtils.isBlank(expression)) {
            return false;
        }
        String buildExpression = buildExpression();

        innerMap.put(CubeConstants.METRICS_ID, metricsId);
        return (boolean) CustomAviatorUtils.execute(buildExpression, innerMap, true);
    }

    public boolean isMatchMetrics(Object fieldValue) {
        if (StringUtils.isBlank(field)) {
            return false;
        }
        Map<String, Object> env = Maps.newHashMap();

        String buildExpression = buildExpression();
        String replacedFiled = field.replaceAll(REPLACE_REGEX, BRACKET_REPLACER);

        //，alarm_rule_content{"expression":"max(cost) > 300","field":"cost","type":0}
        if (CompareType.THRESHOLD.equals(type)) {
            env.put(replacedFiled, fieldValue);
            return (boolean) CustomAviatorUtils.execute(buildExpression, env, true);
        }

        //， alarm_rule_content{"expression":"max(cost) increase 0.2","field":"cost","type":0}, {"expression":"max(cost) decrease 0.2","field":"cost","type":0}
        Double compareValue = (Double) fieldValue;
        if (expression.contains(CHAINED_RATIO_INCREASE_EXPR)) {
            // decimal increase value is transformed into percentage number ( * 100)
            env.put(replacedFiled, 100 * (compareValue - 1.0));
            return (boolean) CustomAviatorUtils.execute(buildExpression(), env, true);
        }


        if (expression.contains(CHAINED_RATIO_DECREASE_EXPR)) {
            env.put(replacedFiled, 100 * (1.0 - compareValue));
            return (boolean) CustomAviatorUtils.execute(buildExpression, env, true);
        }

        return false;
    }

    public RuleCondition toRuleCondition(){
        RuleCondition ruleCondition = new RuleCondition();
        if(CompareType.THRESHOLD.equals(type)){
            ruleCondition.setConditionType(ConditionType.FIELD);
            ruleCondition.setName(field);
        }else if(CompareType.TAG_COMPARE.equals(type)){
            ruleCondition.setConditionType(ConditionType.TAG);
            ruleCondition.setName(tag);
        } else if(CompareType.FUNC_COMPARE.equals(type)){
            ruleCondition.setConditionType(ConditionType.EXPRESSION);
            ruleCondition.setExpression(expression);
        } else {
            return ruleCondition;
        }
        ruleCondition.setOperator(operator);
        ruleCondition.setThreshold(cmpValue);
        return ruleCondition;
    }

    @Data
    public static class AlarmRuleConditionExtension {

        /**
         * AI detect mode, Both/Upper/Lower
         */
        private DetectModeEnum detectMode;
    }

    public enum DetectModeEnum {
        Both,
        Upper,
        Lower;

        // Judge Current DetectModeEnum matches the given AnomalyDirection
        public boolean isMatch(AiFilterVarV2.AnomalyDirection anomalyDirection) {
            if (anomalyDirection == null) {
                return false;
            }
            return switch (this) {
                case Both -> true; // Both mode matches all directions
                case Upper -> anomalyDirection == AiFilterVarV2.AnomalyDirection.up;
                case Lower -> anomalyDirection == AiFilterVarV2.AnomalyDirection.down;
            };
        }
    }

    public static AlarmRuleSingleContent.DetectModeEnum fromAnomalyDirectionType(String direction) {
        return switch (direction) {
            case "3" -> AlarmRuleSingleContent.DetectModeEnum.Both;
            case "2" -> AlarmRuleSingleContent.DetectModeEnum.Upper;
            case "1" -> AlarmRuleSingleContent.DetectModeEnum.Lower;
            default -> null;
        };
    }


    public static String buildUserDefinedThresholdExpression(String field, String upper, String lower) {
        if (StringUtils.isBlank(field)) {
            return "";
        }

        StringJoiner conditionJoiner = new StringJoiner(" || ");
        addThresholdCondition(conditionJoiner, field, ">", upper);
        addThresholdCondition(conditionJoiner, field, "<", lower);

        return conditionJoiner.toString();
    }

    private static void addThresholdCondition(StringJoiner joiner, String fieldName, String operator, String thresholdValue) {
        if (StringUtils.isNotBlank(thresholdValue)) {
            joiner.add(fieldName + operator + thresholdValue);
        }
    }
}
