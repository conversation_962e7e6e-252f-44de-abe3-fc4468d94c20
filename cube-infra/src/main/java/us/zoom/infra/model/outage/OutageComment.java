package us.zoom.infra.model.outage;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class OutageComment {
    private String id;
    private String outageId;
    private String commentId;
    private String parentCommentId;
    private String content;
    private Date createTime;
    private Date modifyTime;
    private String creator;
    private String lastModifiedBy;
}
